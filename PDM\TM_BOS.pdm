<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1447028732" ExtractionBranch="1" ExtractionDate="1447028732" ExtractionId="709091" ExtractionVersion="39" ID="{39215953-BF65-44D6-B70C-00C68A3E9B13}" Label="" LastModificationDate="1597200679" Name="TM_BOS" Objects="334" RepositoryId="{5C34CC33-BB05-4053-9571-E184DEFF2D78}" Symbols="29" Target="Microsoft SQL Server 2005" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>39215953-BF65-44D6-B70C-00C68A3E9B13</a:ObjectID>
<a:Name>TM_BOS</a:Name>
<a:Code>TM_BOS</a:Code>
<a:CreationDate>1384163659</a:CreationDate>
<a:Creator>weixy</a:Creator>
<a:ModificationDate>1597200679</a:ModificationDate>
<a:Modifier>RD_huazhu_ni</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=Yes
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=No
RefrMigrateReuse=No
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=1
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=0
RefrUpdateConstraint=0
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {5C34CC33-BB05-4053-9571-E184DEFF2D78}
MODEL_ID 709091
MODEL_VRSN 39
BRANCH_ID 1
EXT_DATE 1447028732
EXT_END 1447028732
OBJECTS 
BEG_BLOCK 
 709091 {39215953-BF65-44D6-B70C-00C68A3E9B13}
 709092 {E59AF34B-3C4E-48AE-92CC-360621EBB7F7}
 709093 {80FE38C3-EB7C-40C5-90FF-484DF5944672}
 709094 {2BD4836D-3EF5-4E71-AE5C-FA4EA6C8E592}
 709095 {39BFC30F-A7F5-4611-829D-47094C24D104}
 709096 {D0792F96-B5BE-4BD5-A7EB-6EC937CD1525}
 709097 {FBADCBBC-5D4C-4C9E-8933-66FF5E32C2E7}
 709794 {910229D0-0013-40D8-93BD-A6C45D0E957D}
 709795 {ACD076F5-8E12-4A50-A8C4-7412270C63D0}
 709796 {8157D39D-DEA8-4D45-B151-3B34E9FE0ADA}
 709797 {90AE4835-4753-414A-B850-D9B48E4F41DC}
 709798 {0766AE2F-9F9E-4319-981B-F19B8227B07F}
 709799 {D6A0AAD0-6530-485A-9924-040A0FE961C0}
 712929 {0B6FE878-56AB-4819-8B36-D600CD34D3FA}
 712930 {36058F1C-8600-4B66-8AA8-4F3A344CE9B3}
 712931 {7E2F0533-ABDA-49E1-A190-C35DB580E2C5}
 712932 {CAAEE34E-2164-4397-AB39-0DE2E407924C}
 714456 {FDD6FE73-01DA-4BF8-A2CA-DF1FD5F00AFE}
 714457 {4744CD7C-1318-4FB0-9B5F-842A0EBFC548}
 722900 {70E1793B-44A0-4236-8EED-C5F872C2375F}
 722901 {CBAAB921-0236-4BF0-BE60-B91C946C2554}
 723002 {0386F310-38BD-4285-AA9F-19F441F3110F}
 723003 {63C07D55-961D-4FBC-B46E-03215A4AA61F}
 723004 {0AFD76DF-47BF-4F22-996F-84B78BFDDE77}
 723005 {56F7B947-143C-45E7-ACA0-3489C8AA28E5}
 723006 {0BD61AF8-2888-4F99-ADCE-633FBD65B14B}
 723007 {A756B0E0-99E0-456B-977A-2FDEF785ACEC}
 723008 {C61BBB3B-D51E-48F8-8FAE-8C8852560019}
 723009 {EE222528-ADF1-47B6-9305-57215CD16F49}
 723010 {F2A8C51F-8C87-4140-ABA6-AF29BBAADEDF}
 723152 {08A40ED0-683D-4B75-94E1-1479F329413F}
 723153 {EDE9DA3D-430F-4889-8637-2F70B0AAD614}
 723155 {C5290F4C-403F-4089-9223-8144E8BD4550}
 723156 {A1B41BEA-76D6-4D55-8D96-B1A0FBB20DE7}
 723158 {DE1A89F0-439B-4154-B4BB-2A43397F7655}
 723159 {CAEE2708-9F41-41D3-A616-4D520BA0AD60}
 723160 {6E48CDB7-1858-424B-ADF7-5E9F4E87764B}
 723161 {CED41746-7FCE-4728-B643-A119846D7D59}
 723162 {F1DFC21D-7669-4E44-8EAA-0BEF80B009C6}
 723163 {3605C5FC-424F-430A-9A7B-35CA093F4A4B}
 723164 {EEC0566C-7FB8-4834-B3EF-C1089B01EDD2}
 723165 {477F51B2-CE4C-4BFE-A598-3E712DD61CED}
 723166 {0E173A88-8E86-4B10-AC79-E38A83A91C7B}
 723167 {750582F3-EE47-472F-B66B-CBB73F43E3FA}
 723168 {EEBB201A-C999-4F8E-B648-42D5B7E5EBDF}
 723169 {3DDD545E-7145-4029-9E80-A3381052E63F}
 723170 {66694C55-2251-477C-80B6-B17CC0E51AD9}
 723171 {DC66EE49-B4F4-4BFC-A1B7-535ACFE8F140}
 729241 {64385423-0A3B-4FB7-80FD-C4E14CAC8EC3}
 729242 {4DC0AFA9-22E1-4314-BB51-E4DDC81B4A8C}
 729243 {5D42FC17-2DBF-4D6E-BFCA-E3D75757276C}
 729244 {F701AAA9-62B2-4E83-B3A4-85F707DF2628}
 729245 {35E9816F-3909-43AE-A6EC-875D29176783}
 729246 {1767D6E6-C417-4FDF-A244-B131DD01FE7F}
 729247 {361C40DD-472C-4F3A-9CC5-9FAA8CFB94DF}
 731614 {8D687380-DFF3-43C2-B37C-68DF1D77602D}
 731615 {232F8776-AD22-4DA8-AF05-7AFD3AF27CCF}
 731616 {902E2CD0-0A23-490D-B00D-464E439B8212}
 731617 {D1AEB8F1-7A9A-4D15-BDFB-F08ED6424946}
 731618 {B394D4AC-97AF-4870-8F06-765C9AACA0C9}
 731619 {FF778382-95A9-4DE1-AC79-61F63E61B95F}
 731620 {FF530E6D-F884-48D0-B1CB-64B75099FCA8}
 731621 {F32C23F5-C099-44B6-8477-C3B9557C4237}
 731622 {1EF39E5F-7A71-4380-A6F4-ADCD1C085BD8}
 731623 {90C03629-CF7A-40AF-A414-46FD52253FEE}
 731624 {FA43ECF8-CA92-48EB-A3CA-5383EDC1BAFA}
 734572 {48DCD443-B7FE-4B12-850F-B0628A37DED7}
 734573 {FA29D9B0-6E9C-4A7F-A3AA-B88C9E699BE8}
 734574 {4DB19ED2-3C80-4823-92AC-E1FE7DA743A6}
 734575 {3EED744A-81D7-4107-ABE2-AF2151699B5C}
 734576 {B716915D-F28D-4788-846A-594238EECB40}
 734577 {277F818A-2218-43FE-A726-7FE5558D386D}
 734578 {294AB87F-D834-47BF-AC9C-EF82D713574B}
 753312 {7FC7862E-94DE-4B1C-A788-95E78AA17164}
 753313 {A1AFAD17-9990-4F57-95DA-BABBA9175CAD}
 753314 {1B6B8224-277F-4793-893A-88E4A6D7AE0E}
 753315 {4A46EA1A-BD5A-41B6-8C66-71E33C3D242F}
 799853 {0B1A161D-C304-45AB-A40C-8212C726CBFB}
 799854 {CDE29E40-CEBC-4E47-AB79-F532308DBA56}
 799855 {7A0695D3-A57B-407A-8F70-A45838175D4A}
 799856 {CA1DC31F-BC27-4E50-9217-8FEB75C3125C}
 799857 {7AADE4F6-09EA-4B48-B829-54F1D052605D}
 799858 {6E094DF9-7C0B-42DE-96B2-66E702D349EC}
 799859 {57624A8C-C055-44FA-9764-ED4E4916A02B}
 799860 {52002BCF-EE57-45D4-BBF9-B118EA5A47EC}
 799861 {376895FF-B84D-44E5-A607-9AF4F41E1F09}
 799862 {024549D9-4721-4A36-973A-1FA00716B7D3}
 799863 {AD08551E-84F6-492A-AD16-A7B195770312}
 799953 {75BE2CE4-1603-4F81-A50F-1C1552AB3AF0}
 799954 {FB90C7F0-2B15-4CD2-AC52-D506E321A193}
 799955 {52132ED6-2455-4061-99E6-483EC26633BE}
 799956 {E8031AE5-251D-4442-9728-45AFD87F95EC}
 799957 {0442D1EA-91CB-43B3-82EC-E05F5493D749}
 799958 {CB557A9A-FAF9-4D82-9537-E6AF4D51772D}
 799959 {ABBC71CF-AFB4-4F81-ABA3-9C1CCFC80F4B}
 799960 {FFFBA776-1C6C-496A-8ADC-AEAE9943056F}
 799961 {AD3A540A-2A9C-4B5F-96E2-4573D0D0E025}
 799962 {1C5C83E8-EC45-45BB-A366-128047569245}
 799963 {CEAC24C9-4F86-4968-8E79-74471B2A7849}
 799964 {75C6CD7C-2558-4424-AC6E-AB442DDA90E8}
 799965 {660BDFB4-D6E8-4556-B47F-660856D74702}
 799966 {447E390C-9231-47BA-87DF-023E06222A86}
 804839 {A63F5D6B-687E-438C-A8BF-DFAAF9232C77}
 804840 {86AFD687-FCC1-47AC-909E-66E1860C3A7E}
 804841 {D62400AF-EA04-407A-982E-F26F2AAAB61C}
 804842 {60B7C39D-A436-4DD7-9F74-1A0872AE9E04}
 804843 {264AFCB3-01E7-4817-A1B1-D42FFC8107B6}
 804844 {A16783CE-6588-47AA-BEEB-FC0C092F36A9}
 804845 {F27D7B07-2D6F-46ED-9B6D-90B00E5DEC3C}
 804846 {4F668CF6-7E1D-4B96-83B8-1D4F54B80799}
 804847 {B0823531-55AD-4642-B091-7FD01DC724E9}
 804848 {E261BA9A-C919-41BE-B6E1-A17712C63C5B}
 804849 {FEA3B0F9-BD56-47CF-BFFD-610FB9DFD00E}
 804850 {8A143868-3970-44A8-B395-3D7BBDD228C2}
 804851 {A819F66F-947F-4DAE-AFBD-E7FE137F54FA}
 804852 {DB6EB8E1-8F1B-49AB-8168-6F5D2572C79E}
 804853 {05F34C17-2720-470B-90B8-F20EAA71194B}
 804854 {C3496EFA-40A8-4BE2-B775-C9223144FABD}
 804855 {8B375193-E1D9-45FC-B8F0-1EBD7241B39D}
 804856 {CBE3C522-894B-4950-83E4-D0618D691884}
 804857 {36E65F0B-CE99-42D1-946B-D3BB00B463B7}
 804858 {10E87BD0-C9EC-4BC9-B588-2FA8436E9B15}
 804859 {654C558E-1816-4BD3-ABB3-BE2DE07BF09D}
 804860 {E65D1290-559A-4049-8D03-49D4070BA960}
 804861 {AA963F86-C87A-437F-B94D-3DEC80A12ADB}
 804862 {C43CCE90-683F-4860-9271-C3EDD9D3A999}
 804863 {D2F83B65-B6BE-4CBB-86AD-52EC573CE844}
 804864 {0FD4814C-C7CD-4FEB-A745-0BB636918309}
 804865 {30D72AEA-0FB6-495C-BC33-D5C3B0BC778B}
 804866 {96F0B1F4-7A16-4787-B959-4F697EB6FCC1}
 804867 {48D3B518-93FB-4F5A-8D77-2AB9F0A16E6E}
 804868 {0DE3D69A-56F7-4504-B731-FE60923B11AD}
 804869 {FD19E69A-CD06-48F5-ADB4-5CB262A75C25}
 804870 {0CE35CF3-2466-46F8-962E-0D69601C43C8}
 804871 {1D1E571B-8CAA-48DD-9570-F3361A851263}
 804872 {4F0C5CF3-DB51-4A81-9FBC-E3C6F506F612}
 804873 {8B01F4ED-5B83-416D-B59B-07C8C1F3ABA2}
 804874 {94467FF5-1A87-4F6E-A92D-62FB179D73C9}
 804875 {4FA88B58-3828-46B1-9F1D-136323DC988D}
 804876 {A6C7ABC0-6D08-45B3-821D-909EA1FA3381}
 804877 {F412F2E8-7D33-40BB-AAD6-D3D0BA55553B}
 804878 {6EA0852C-EC8D-4597-9526-F99C1D911ECA}
 804879 {D8650284-A8AB-4304-B20F-57C2C872B266}
 804880 {4EA0EFEE-60AA-46C6-8251-003D8B3A0DA2}
 804881 {EA27CAD4-D1EA-4E40-A17C-A81141D42A48}
 804882 {A4F88CFA-B548-4052-AD7B-414C4C6F35F5}
 804883 {16F00F9A-9DD4-4796-9639-0C8897E4667E}
 804884 {E1C1BDC3-7BA4-4ED4-9B7E-B6A442250BC9}
 804885 {3F3597E2-DB0A-45D3-851D-78EB97C50349}
 804886 {E9A35B1E-3AD9-4254-93F3-7B1051419F47}
 804887 {2A56E3CD-4D71-4CA3-81DA-D87BEFB287A5}
 804888 {C618C3A4-4301-4998-88A1-C1196F7D6CD3}
 804889 {06580541-6C0F-4555-B8EF-FD6E8EF1C4B7}
 804890 {CD42DB38-82BC-415F-B18C-D4053E559261}
 804891 {1CB424C2-E0BA-4D96-AC8B-B1C39BEAB2AD}
 804892 {D49FBD87-F381-4B71-A599-766E9652F23B}
 804893 {5F945C37-D865-4B72-8F60-6C7FCB4561AE}
 804894 {92465268-D9C8-46B9-BF9D-4C5F8D484F33}
 804895 {BF6FD6EC-F853-4140-8F31-236825E08DBC}
 804896 {B70E5EB9-DAB0-479C-944F-8B58CD442B47}
 804958 {5E5D24D3-0331-41B3-AFBC-A044C9548F59}
 804959 {84935EA1-392B-43A0-A76D-9BD19FD68C58}
 804960 {6D1461B1-8EA3-446C-8D99-B51061FF0DE5}
 804961 {DF1470AD-787D-4DA1-BE5F-277D88BDF33D}
 804962 {C731F02C-D308-4116-8E0E-8CA1D6BB31AA}
 804963 {0C657904-17A2-4061-BA6E-DAB8F992CE12}
 804964 {EFB96D43-EB3A-4AD0-B4C6-10CA96FBBAD2}
 804965 {BF0204BD-E912-4FCC-B0F6-049934A51696}
 804966 {27052276-74F7-4C64-86D7-906827DEFE7C}
 804967 {1364C9A6-9522-4D3C-8442-0D95ED8740D1}
 804968 {AC329359-CC03-4F9A-AA1A-BE6547854BCC}
 804969 {A0789A96-9386-4528-9874-9B07EE8AB782}
 810359 {8F131E51-E518-48EE-A7A2-BB6624B4BCA0}
 810360 {5B827071-A363-408C-A7A9-0060832A6F0B}
 810361 {2DAE8176-1C19-4DA9-ADCA-6245E23ACDF6}
 810362 {FF6D6ED3-DEB1-43C2-9F60-208CBA685A25}
 810363 {1DD5B7E9-BD17-4371-82E9-71D91C39FF86}
 810365 {E24B4F7A-EE40-4E49-AA55-BEF58A9D7EA5}
 810366 {9A3ECE5F-F53A-40C8-BDF0-B9D1C6FEEA6B}
 810367 {93F78DDC-B11E-46C7-8E2A-69D2CE040B9C}
 810368 {CD9BDB3B-8576-4F17-BCDA-726AAF5E3198}
 810369 {ED7247B1-79CF-4881-AB29-2D7F891FBAC5}
 810370 {FC4FE87D-EC98-4857-A92D-C91001DDE858}
 810371 {29974B6C-18E8-435D-BC0B-B9AC91DEA444}
 810372 {8ACC137F-3565-445D-98AA-5D835FF941E4}
 810373 {6BD2D5BF-9007-4BF4-8654-B9E309A7C960}
 810374 {DA74C662-CB64-432B-8806-1FA6660492BA}
 810375 {AA6068D7-4987-4E2A-9D44-6DB4256B56DB}
 810376 {A403C98B-3D9A-4D65-96BF-ECF14576F26F}
 810377 {AF5E5AEB-E882-44B4-AA80-FB6392DA83A8}
 810378 {1EAF7916-01F8-4259-B6DF-D799424614C9}
 810379 {7DAA79CE-5B21-4969-823A-A80AF1CADB8D}
 810380 {FA65EB76-2110-4F58-9FD5-4531849533D2}
 810381 {4BF34287-05C1-4E0E-B16B-C6F4FE66BF66}
 810382 {FC170857-CDEB-4FE7-92C5-A8260BD99320}
 810383 {12FB416A-152C-4ED2-BC6E-9FF41CB536A0}
 810412 {15C3E25E-750D-4111-9D69-0320F71CFDE7}
 810413 {EE6A29E5-75AD-40E1-8696-3D515615EFE2}
 811007 {53C871AC-C3D7-4BB3-A8AC-997291866BB6}
 811008 {93F0631D-5D18-45F7-BA28-3139D396E8BC}
 811009 {F3C9DAB7-AE4B-457E-96AF-B1DEE3770E68}
 811010 {C52BBE1C-65E2-468D-ABE4-D38658024A38}
 811470 {18FCD2F5-6576-4467-BAB0-CE69617A8FB7}
 811471 {FE8690CF-8A48-46DE-AC93-393E68FEE42D}
 811472 {52F9334C-6C22-4D1C-8919-6EE650DE6BEB}
 811473 {AA970DD3-10EB-4862-BB83-C9C90C62E04D}
 811474 {A3CA96C9-E519-4668-BBA3-5CAECC294180}
 811475 {C99B418A-1193-45F6-BE10-0431C1449F1C}
 811476 {CF589F2B-2875-4E79-9C94-E29955FA0124}
 811477 {9B9BC61A-FF2D-4ED0-96B2-03B6B62BF34D}
 811478 {BFEF8DD0-7B26-46F7-8BE4-0F67826139B8}
 811479 {4070C32A-30ED-4EB4-BB9E-F4A400B40037}
 811480 {F881BA43-3C27-4B02-917D-55570AA5432C}
 811481 {827BC2E1-19F2-4F91-9600-D546C860052A}
 811482 {5F663BB8-1F34-486B-8067-08B7ADE43ECA}
 811483 {6EB69061-DA57-4ED8-82D8-02FEB9AC4EA9}
 811484 {D371F09F-9455-4BBF-8186-58FC15B65D9B}
 811485 {40ADE9AA-6055-462D-AD38-834964913E45}
 811486 {6BE9273F-BCAA-405D-ACA4-4EA8812ECC72}
 811487 {B597239C-FF25-45C6-A430-D140E179CB9B}
 811488 {5F792F53-7103-448E-9095-4E29E2656726}
 811489 {B18CEC76-3A6B-4372-A2C4-3AC054207EAE}
 811490 {E104C53F-7DA6-4723-BDD1-529504BA556C}
 811491 {E31E8E7C-1D23-4269-96E0-B904E67224F4}
 811492 {860F7A40-51E4-4BF7-BF1C-FF4C9C4FFAC7}
 811493 {6CC9AA3A-D98B-41D6-B77D-A652CB217B18}
 811494 {F0C9684D-9B4E-4B50-919A-9CAB829169A7}
 811495 {322CC2DC-DD9F-409A-B2BE-C9F461FD5FAA}
 811496 {FCCBC536-E1D5-41D0-A9D6-577E530A94E2}
 811497 {3667C7F9-EE1B-4D0D-9260-4DB1DE399CCD}
 811498 {F918DC50-687A-44E9-BCB3-7454D5BB3264}
 811499 {A73B3DF9-1F33-489E-8474-48D726BF8F9A}
 811500 {135D59FD-655E-4DDE-A35E-C208849366F0}
 811501 {461F4551-1D1B-4E1E-9D23-A95EFE32471F}
 811502 {BC46D93D-4FDA-4DCE-97B0-A17CCBDEBBEF}
 811503 {CF2C1A2C-835A-404A-A37B-EF62603B255C}
 811504 {B02816D5-0170-4662-AB81-F68A94AD41EA}
 811505 {890C0CA7-B668-4BE9-974C-6F91D1AF599A}
 811506 {E341DE81-5619-48C0-9B27-D4F228B6A927}
 811507 {20485593-D4B6-410B-B196-3C161F9B6CC4}
 811508 {32FB76A1-ED14-4027-86E3-23A347318843}
 811509 {759C08DE-0414-48B9-8118-312DA1E7874B}
 811510 {4E07D197-537B-4835-9EC4-67F2D9065F45}
 811511 {E08412D7-D385-46C9-976C-19BB8BE3D3F5}
 811512 {3FF45B83-665D-4BCA-9607-E9B89D1305ED}
 811670 {86154E41-B019-4BB5-BAB1-EA22E9350B96}
 811671 {06C94968-F2A6-4D2C-93DF-2FFDE2FD0C96}
 812497 {E9255D82-F140-469C-84D6-871F2CD0B4D1}
 812498 {FCDC7548-1F1D-420A-BA9B-B0D3369FBF10}
 812499 {BA7ECCE4-9EEE-4212-8298-490C44DC47D3}
 812500 {BC7C3626-52B5-4B1F-8594-CA6252A5DD72}
 816577 {1BC21CBB-B27B-40E9-AED0-AA25260E8E4D}
 816578 {A09A1655-1B50-4136-9081-3C9EBB554DFA}
 816579 {50A3616D-C1D3-44E6-8D63-7F3A440C83D5}
 816580 {24AA8E0D-6588-4A68-9286-A9D9853C7017}
 816581 {ECCFAED4-EEBE-464F-BC2B-02578F4CC57F}
 816582 {CBE99B01-5B8F-49FC-A85C-B33CBE48B556}
 816583 {49DCDB78-77E0-4677-A508-193C9D1D27EB}
 816584 {7A2676EF-F005-4267-86D1-B853E13E5F37}
 816756 {374E7E09-BC6F-4B3A-A642-35D99B06BDA8}
 818450 {EF838DAE-240D-458C-8305-6113352AFD6C}
 818451 {241A07C3-B9F0-4DAC-B4C9-E1FB8D9B9638}
 818602 {4F4E3863-871A-496A-BF87-4FF6E2FBE290}
 833318 {4955C055-F5F0-461F-A88D-99A3BE55A5CD}
 833319 {5D885E88-F8CF-476F-83CA-1C7A53786986}
 833320 {B22FED39-AF54-4C87-AA11-8CE148A0D37C}
 833321 {19AD52C9-E549-4361-9AA9-7AA7B980FB2D}
 833322 {1BDEA7CB-B0A0-4138-9C6D-39510C1BAD62}
 833323 {64D29660-4944-49FD-9981-2C3C0E43015F}
 835617 {4281A400-F52F-467D-B5C7-A3C1F2BFEEA2}
 835618 {5A6D4D15-A301-4F21-9C6C-3B7022E365B3}
 835619 {5CA4D5A8-CFC4-4C5A-829F-B5F9DA9E5252}
 835620 {18DC4956-5ADC-4FEE-A84B-635BA68D5035}
 835621 {A473B4E4-5527-4DDC-A81A-FC53577BE925}
 835622 {02F029AD-F77C-474F-B518-8DEF29B7A661}
 835623 {318878C2-8FCD-467E-A73F-3B39872F1F19}
 835624 {F07B0F3D-80D2-4633-9726-ECDDF4FEFB3B}
 835625 {FC78B03D-C968-453E-B02E-7DC2E9494549}
 835626 {1C470E04-B3E2-4DB5-9D35-7EFD71734F8A}
 835627 {605B7456-678C-4264-B555-917A923A8C84}
 835628 {094722A1-2300-4447-A9B5-F64C00D61226}
 835629 {B7DBA664-1AFD-4065-8361-223F77B41FFC}
 835630 {3A8FE4F5-4C60-4609-B783-C68E1093C31F}
 835631 {C791C7AC-6431-428B-8CD1-2B60AB9BBC27}
 835632 {CDF8BBB9-0583-43DA-BA73-A3A635100138}
 835633 {BA937747-4CAE-496E-A117-76886FFB6DEA}
 835634 {206B73CF-14B3-4B94-B7BD-E549E680639D}
 835635 {954C047A-203B-4FFA-B88A-32603A2A17EA}
 835636 {0B0137CD-3846-4555-879C-867104DF411E}
 835637 {D9EEAEB3-2D6C-4557-A7B0-4A57450445D0}
 835638 {2CB5D7A9-EA46-4E68-9DB2-5643DB973543}
 835639 {32B011BC-A740-4E68-A410-700907A5F4CF}
 835640 {E84A3158-7002-4516-81A9-E664020B38EB}
 835641 {6FBDE9A9-6EE8-45F5-9E70-9AFA6C400142}
 835642 {EAFC01B0-24E6-44C5-A77B-F7DD0A984670}
 835643 {4714DDC3-30AA-4AEC-81AE-8220726524F7}
 864252 {3D3F610A-2CDE-423F-951E-45685277DA44}
 864253 {6D1C8662-6C0F-4707-A839-C6193A5A419B}
 864254 {D0EBE9F0-DD3F-44D5-A14C-820996BE825A}
 864255 {F7EF52BD-FD98-4390-85CC-50829DD3B343}
 864256 {88CCADE3-63FA-426A-949C-BBB17DE34263}
 864257 {54D3F193-B47E-4D85-84D6-3BA91B2EFE94}
 864258 {8AD028CE-DDFD-4000-ADAB-E0ECCFEDEE44}
 864259 {0BBD3BF6-8A1D-4FC4-9EAD-792CE4309336}
 864260 {F012A1A5-0027-42EC-9C64-04CD74C1E818}
 864261 {F04FEA63-6781-48D1-AFAA-9869D14D7532}
 864262 {E5E735E1-4E69-4544-B663-63B73C62E09D}
 864299 {37F9E993-52FE-4115-B8C4-C46603AAC496}
 864300 {E6B7BFC9-A4C9-4415-B367-EB40987A9594}
 864301 {361E71BC-FECD-4138-8A53-66FEDD2A3B35}
 864302 {55BBDA26-8243-4FE3-B8B0-DFB690F855D6}
 864303 {2DDBCC84-170E-4E98-8B55-4A49F3CF4A8D}
 864304 {BB22AA11-22AC-437D-87D1-0D37EB3FF42F}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>E59AF34B-3C4E-48AE-92CC-360621EBB7F7</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1384163659</a:CreationDate>
<a:Creator>weixy</a:Creator>
<a:ModificationDate>1384163659</a:ModificationDate>
<a:Modifier>weixy</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>80FE38C3-EB7C-40C5-90FF-484DF5944672</a:ObjectID>
<a:Name>TM_BOS_ALL</a:Name>
<a:Code>PhysicalDiagram_1</a:Code>
<a:CreationDate>1384163659</a:CreationDate>
<a:Creator>weixy</a:Creator>
<a:ModificationDate>1527301758</a:ModificationDate>
<a:Modifier>rd_jia_xiong</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:TextSymbol Id="o5">
<a:Text>NVARCHAR(36)</a:Text>
<a:CreationDate>1407750782</a:CreationDate>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Rect>((-33225,-28012), (33225,28013))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>新宋体,8,N</a:FontName>
</o:TextSymbol>
<o:TextSymbol Id="o6">
<a:Text>FFULLPARENTID</a:Text>
<a:CreationDate>1407750839</a:CreationDate>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Rect>((-33225,-28012), (33225,28013))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>新宋体,8,N</a:FontName>
</o:TextSymbol>
<o:TextSymbol Id="o7">
<a:Text>FLEFT</a:Text>
<a:CreationDate>1407750861</a:CreationDate>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Rect>((-33225,-28012), (33225,28013))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>新宋体,8,N</a:FontName>
</o:TextSymbol>
<o:TextSymbol Id="o8">
<a:Text>FNUMBER</a:Text>
<a:CreationDate>1407751197</a:CreationDate>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Rect>((-34500,-27937), (31950,28088))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>新宋体,8,N</a:FontName>
</o:TextSymbol>
<o:PackageSymbol Id="o9">
<a:CreationDate>1384163684</a:CreationDate>
<a:ModificationDate>1384163701</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-2549,-1798), (2549,1801))</a:Rect>
<a:LineColor>11711154</a:LineColor>
<a:FillColor>12648447</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Package Ref="o10"/>
</c:Object>
</o:PackageSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o10">
<a:ObjectID>2BD4836D-3EF5-4E71-AE5C-FA4EA6C8E592</a:ObjectID>
<a:Name>TM_BOS临时表</a:Name>
<a:Code>Package_1</a:Code>
<a:CreationDate>1384163684</a:CreationDate>
<a:Creator>weixy</a:Creator>
<a:ModificationDate>1527647923</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o11">
<a:ObjectID>FBADCBBC-5D4C-4C9E-8933-66FF5E32C2E7</a:ObjectID>
<a:Name>TM_BOS临时表</a:Name>
<a:Code>PhysicalDiagram_1</a:Code>
<a:CreationDate>1384163684</a:CreationDate>
<a:Creator>weixy</a:Creator>
<a:ModificationDate>1527647923</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:TableSymbol Id="o12">
<a:CreationDate>1384494703</a:CreationDate>
<a:ModificationDate>1384742007</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-19351,4227), (-2253,8226))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o13"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o14">
<a:CreationDate>1384741629</a:CreationDate>
<a:ModificationDate>1384742004</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-19684,12671), (-1088,16670))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o15"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o16">
<a:CreationDate>1384741645</a:CreationDate>
<a:ModificationDate>1384742089</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-20661,-4880), (-711,-880))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o17"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o18">
<a:CreationDate>1387614825</a:CreationDate>
<a:ModificationDate>1387615475</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-20138,-12386), (-4540,-8387))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o19"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o20">
<a:CreationDate>1409714075</a:CreationDate>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((4055,14013), (21899,18012))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o21"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o22">
<a:CreationDate>1409726182</a:CreationDate>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((4049,4239), (17397,13087))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o23"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o24">
<a:CreationDate>1409727630</a:CreationDate>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((24246,-19469), (38344,18253))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o25"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o26">
<a:CreationDate>1411541849</a:CreationDate>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((2513,-12148), (19985,824))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o27"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o28">
<a:CreationDate>1411542182</a:CreationDate>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((225,-26661), (18823,-12863))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o29"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o30">
<a:CreationDate>1411885554</a:CreationDate>
<a:ModificationDate>1411897948</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-20428,-37719), (-8580,-28871))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o31"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o32">
<a:CreationDate>1411886021</a:CreationDate>
<a:ModificationDate>1411897948</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-5315,-37552), (6907,-28704))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o33"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o34">
<a:CreationDate>1411886391</a:CreationDate>
<a:ModificationDate>1411897948</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((9946,-34949), (22544,-28577))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o35"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o36">
<a:CreationDate>1412823773</a:CreationDate>
<a:ModificationDate>1412828292</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((24325,-26223), (37673,-22224))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o37"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o38">
<a:CreationDate>1418868850</a:CreationDate>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-20405,-48683), (-8183,-39835))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o39"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o40">
<a:CreationDate>1418869854</a:CreationDate>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-5143,-48908), (7455,-40060))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o41"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o42">
<a:CreationDate>1418870115</a:CreationDate>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((10495,-46545), (23467,-40173))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o43"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o44">
<a:CreationDate>1429776384</a:CreationDate>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-21261,-60818), (-5139,-49506))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o45"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o46">
<a:CreationDate>1429843742</a:CreationDate>
<a:ModificationDate>1429843826</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-4124,-57558), (16124,-51116))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o47"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o48">
<a:CreationDate>1527301798</a:CreationDate>
<a:ModificationDate>1527301798</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((1118,-33334), (15964,-29335))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o49"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o50">
<a:CreationDate>1527647923</a:CreationDate>
<a:ModificationDate>1527648884</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-22237,-27786), (-2515,-13164))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o51"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o11"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o15">
<a:ObjectID>910229D0-0013-40D8-93BD-A6C45D0E957D</a:ObjectID>
<a:Name>TM_BOS_IMPORTNUMBERMAP(引入时基础资料表名与编码)</a:Name>
<a:Code>TM_BOS_IMPORTNUMBERMAP</a:Code>
<a:CreationDate>1384220277</a:CreationDate>
<a:Creator>RD_liang_wu</a:Creator>
<a:ModificationDate>1384239414</a:ModificationDate>
<a:Modifier>RD_liang_wu</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o52">
<a:ObjectID>ACD076F5-8E12-4A50-A8C4-7412270C63D0</a:ObjectID>
<a:Name>基础资料表名</a:Name>
<a:Code>FTableName</a:Code>
<a:CreationDate>1384220450</a:CreationDate>
<a:Creator>RD_liang_wu</a:Creator>
<a:ModificationDate>1384239414</a:ModificationDate>
<a:Modifier>RD_liang_wu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o53">
<a:ObjectID>8157D39D-DEA8-4D45-B151-3B34E9FE0ADA</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNumber</a:Code>
<a:CreationDate>1384220450</a:CreationDate>
<a:Creator>RD_liang_wu</a:Creator>
<a:ModificationDate>1384239414</a:ModificationDate>
<a:Modifier>RD_liang_wu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o54">
<a:ObjectID>90AE4835-4753-414A-B850-D9B48E4F41DC</a:ObjectID>
<a:Name>Index_BOS_IMPORTNUMBERMAP</a:Name>
<a:Code>Index_BOS_IMPORTNUMBERMAP</a:Code>
<a:CreationDate>1384220556</a:CreationDate>
<a:Creator>RD_liang_wu</a:Creator>
<a:ModificationDate>1384239414</a:ModificationDate>
<a:Modifier>RD_liang_wu</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o55">
<a:ObjectID>0766AE2F-9F9E-4319-981B-F19B8227B07F</a:ObjectID>
<a:CreationDate>1384220840</a:CreationDate>
<a:Creator>RD_liang_wu</a:Creator>
<a:ModificationDate>1384239414</a:ModificationDate>
<a:Modifier>RD_liang_wu</a:Modifier>
<c:Column>
<o:Column Ref="o52"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o56">
<a:ObjectID>D6A0AAD0-6530-485A-9924-040A0FE961C0</a:ObjectID>
<a:CreationDate>1384220840</a:CreationDate>
<a:Creator>RD_liang_wu</a:Creator>
<a:ModificationDate>1384239414</a:ModificationDate>
<a:Modifier>RD_liang_wu</a:Modifier>
<c:Column>
<o:Column Ref="o53"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o13">
<a:ObjectID>0B6FE878-56AB-4819-8B36-D600CD34D3FA</a:ObjectID>
<a:Name>TM_BF_TARGETENTITYIDS (目标单据分录内码集合)</a:Name>
<a:Code>TM_BF_TARGETENTITYIDS</a:Code>
<a:CreationDate>1384494703</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1384498505</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<a:Comment>临时存放，目标单据分录内码集合。</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o57">
<a:ObjectID>36058F1C-8600-4B66-8AA8-4F3A344CE9B3</a:ObjectID>
<a:Name>单据分录内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1384494836</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1384498505</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o58">
<a:ObjectID>7E2F0533-ABDA-49E1-A190-C35DB580E2C5</a:ObjectID>
<a:Name>IDX_BF_TARGETENTITYIDS_FID</a:Name>
<a:Code>IDX_BF_TARGETENTITYIDS_FID</a:Code>
<a:CreationDate>1384494885</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1384498505</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o59">
<a:ObjectID>CAAEE34E-2164-4397-AB39-0DE2E407924C</a:ObjectID>
<a:CreationDate>1384494920</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1384498505</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<c:Column>
<o:Column Ref="o57"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o60">
<a:ObjectID>DE1A89F0-439B-4154-B4BB-2A43397F7655</a:ObjectID>
<a:Name>TM_BAS_ISSUE(下发功能)</a:Name>
<a:Code>TM_BAS_ISSUE</a:Code>
<a:CreationDate>1383552475</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:Comment>下发临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o61">
<a:ObjectID>CAEE2708-9F41-41D3-A616-4D520BA0AD60</a:ObjectID>
<a:Name>组织编码</a:Name>
<a:Code>FOrgNumber</a:Code>
<a:CreationDate>1383552475</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>6E48CDB7-1858-424B-ADF7-5E9F4E87764B</a:ObjectID>
<a:Name>组织内码</a:Name>
<a:Code>FOrgID</a:Code>
<a:CreationDate>1383552534</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>CED41746-7FCE-4728-B643-A119846D7D59</a:ObjectID>
<a:Name>单据内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1383552534</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>F1DFC21D-7669-4E44-8EAA-0BEF80B009C6</a:ObjectID>
<a:Name>单据编号</a:Name>
<a:Code>FNumber</a:Code>
<a:CreationDate>1383552534</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>3605C5FC-424F-430A-9A7B-35CA093F4A4B</a:ObjectID>
<a:Name>单据名称</a:Name>
<a:Code>FName</a:Code>
<a:CreationDate>1383552534</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>EEC0566C-7FB8-4834-B3EF-C1089B01EDD2</a:ObjectID>
<a:Name>普通单据编号</a:Name>
<a:Code>FBillNo</a:Code>
<a:CreationDate>1383552787</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>477F51B2-CE4C-4BFE-A598-3E712DD61CED</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1383552787</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>char(2)</a:DataType>
<a:Length>2</a:Length>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>0E173A88-8E86-4B10-AC79-E38A83A91C7B</a:ObjectID>
<a:Name>组织名称</a:Name>
<a:Code>FOrgName</a:Code>
<a:CreationDate>1383552787</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o69">
<a:ObjectID>750582F3-EE47-472F-B66B-CBB73F43E3FA</a:ObjectID>
<a:Name>TM_BAS_LOGBACKUP(日记备份)</a:Name>
<a:Code>TM_BAS_LOGBACKUP</a:Code>
<a:CreationDate>1383562832</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:Comment>日记备份临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o70">
<a:ObjectID>EEBB201A-C999-4F8E-B648-42D5B7E5EBDF</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1383562832</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:Comment>上机日志内码</a:Comment>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o71">
<a:ObjectID>3DDD545E-7145-4029-9E80-A3381052E63F</a:ObjectID>
<a:Name>TM_BAS_WARNEXECUTERESULT(预警执行情况)</a:Name>
<a:Code>TM_BAS_WARNEXECUTERESULT</a:Code>
<a:CreationDate>1384217599</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:Comment>预警执行情况临时表，存放预警执行情况内码</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o72">
<a:ObjectID>66694C55-2251-477C-80B6-B17CC0E51AD9</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FEXECUTERESULTID</a:Code>
<a:CreationDate>1384217599</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o73">
<a:ObjectID>DC66EE49-B4F4-4BFC-A1B7-535ACFE8F140</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1384222000</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
</o:Key>
</c:Keys>
</o:Table>
<o:Table Id="o74">
<a:ObjectID>0386F310-38BD-4285-AA9F-19F441F3110F</a:ObjectID>
<a:Name>TM_BAS_BISCHANGE(基础资料组织变更临时表)</a:Name>
<a:Code>TM_BAS_BISCHANGE</a:Code>
<a:CreationDate>1386073007</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386118762</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o75">
<a:ObjectID>63C07D55-961D-4FBC-B46E-03215A4AA61F</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FEntryId</a:Code>
<a:CreationDate>1386073046</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386073242</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>VARCHAR(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>0AFD76DF-47BF-4F22-996F-84B78BFDDE77</a:ObjectID>
<a:Name>业务ID</a:Name>
<a:Code>FOID</a:Code>
<a:CreationDate>1386073046</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386073242</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>VARCHAR(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>56F7B947-143C-45E7-ACA0-3489C8AA28E5</a:ObjectID>
<a:Name>业务唯一标示</a:Name>
<a:Code>FMID</a:Code>
<a:CreationDate>1386073046</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386073242</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>VARCHAR(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>0BD61AF8-2888-4F99-ADCE-633FBD65B14B</a:ObjectID>
<a:Name>组织机构ID</a:Name>
<a:Code>FORG</a:Code>
<a:CreationDate>1386073046</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386073242</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>A756B0E0-99E0-456B-977A-2FDEF785ACEC</a:ObjectID>
<a:Name>是否删除</a:Name>
<a:Code>FISDEL</a:Code>
<a:CreationDate>1386073046</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386073242</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>CHAR(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o80">
<a:ObjectID>C61BBB3B-D51E-48F8-8FAE-8C8852560019</a:ObjectID>
<a:Name>业务编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1386073046</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386073242</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>VARCHAR(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o81">
<a:ObjectID>EE222528-ADF1-47B6-9305-57215CD16F49</a:ObjectID>
<a:Name>业务名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1386073046</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386073242</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>NVARCHAR(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o82">
<a:ObjectID>F2A8C51F-8C87-4140-ABA6-AF29BBAADEDF</a:ObjectID>
<a:Name>组织名称</a:Name>
<a:Code>FORGNAME</a:Code>
<a:CreationDate>1386073046</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1386073242</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>NVARCHAR(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o83">
<a:ObjectID>C5290F4C-403F-4089-9223-8144E8BD4550</a:ObjectID>
<a:Name>TM_BAS_BASEDATAREF_VC(基础资料内码)</a:Name>
<a:Code>TM_BAS_BASEDATAREF_VC</a:Code>
<a:CreationDate>1383015290</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:Comment>基础资料内码临时表 内码为varchar（36）</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o84">
<a:ObjectID>A1B41BEA-76D6-4D55-8D96-B1A0FBB20DE7</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1383015290</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:Comment>基础资料内码</a:Comment>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o85">
<a:ObjectID>08A40ED0-683D-4B75-94E1-1479F329413F</a:ObjectID>
<a:Name>TM_BAS_BASEDATAREF(基础资料内码)</a:Name>
<a:Code>TM_BAS_BASEDATAREF</a:Code>
<a:CreationDate>1383015233</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386074555</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:Comment>内码为Int</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o86">
<a:ObjectID>EDE9DA3D-430F-4889-8637-2F70B0AAD614</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1383015233</a:CreationDate>
<a:Creator>RD_rongchuang_cai</a:Creator>
<a:ModificationDate>1386205565</a:ModificationDate>
<a:Modifier>RD_rongchuang_cai</a:Modifier>
<a:Comment>基础资料内码</a:Comment>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o87">
<a:ObjectID>70E1793B-44A0-4236-8EED-C5F872C2375F</a:ObjectID>
<a:Name>TM_SEC_ROLEIDS（角色内码临时表）</a:Name>
<a:Code>TM_SEC_ROLEIDS</a:Code>
<a:CreationDate>1386072591</a:CreationDate>
<a:Creator>RD_ronghua_zhang</a:Creator>
<a:ModificationDate>1386072737</a:ModificationDate>
<a:Modifier>RD_ronghua_zhang</a:Modifier>
<a:Comment>角色内码临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o88">
<a:ObjectID>CBAAB921-0236-4BF0-BE60-B91C946C2554</a:ObjectID>
<a:Name>角色内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1386072638</a:CreationDate>
<a:Creator>RD_ronghua_zhang</a:Creator>
<a:ModificationDate>1386292752</a:ModificationDate>
<a:Modifier>RD_ronghua_zhang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o19">
<a:ObjectID>64385423-0A3B-4FB7-80FD-C4E14CAC8EC3</a:ObjectID>
<a:Name>TM_BF_READTRACKERROWS (读取业务流程路线)</a:Name>
<a:Code>TM_BF_READTRACKERROWS</a:Code>
<a:CreationDate>1387614825</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1387615475</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<a:Comment>读取业务流程路线使用的临时表，专用于IBusinessFlowTrackerDbService.ReadBusinessFlowTrackerRows函数。
按序号划分目标表格及其对应的分录主键</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o89">
<a:ObjectID>4DC0AFA9-22E1-4314-BB51-E4DDC81B4A8C</a:ObjectID>
<a:Name>表格序号</a:Name>
<a:Code>FTableId</a:Code>
<a:CreationDate>1387614928</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1387615475</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>5D42FC17-2DBF-4D6E-BFCA-E3D75757276C</a:ObjectID>
<a:Name>表格内码</a:Name>
<a:Code>FEntryId</a:Code>
<a:CreationDate>1387614928</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1387615475</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o91">
<a:ObjectID>F701AAA9-62B2-4E83-B3A4-85F707DF2628</a:ObjectID>
<a:Name>IDX_BF_READTRACKERROWS_TABLEID</a:Name>
<a:Code>IDX_BF_READTRACKERROWS_TABLEID</a:Code>
<a:CreationDate>1387615278</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1387615475</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o92">
<a:ObjectID>35E9816F-3909-43AE-A6EC-875D29176783</a:ObjectID>
<a:CreationDate>1387615382</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1387615475</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<c:Column>
<o:Column Ref="o89"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o93">
<a:ObjectID>1767D6E6-C417-4FDF-A244-B131DD01FE7F</a:ObjectID>
<a:Name>IDX_BF_READTRACKERROWS_ENTRYID</a:Name>
<a:Code>IDX_BF_READTRACKERROWS_ENTRYID</a:Code>
<a:CreationDate>1387615278</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1387615475</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o94">
<a:ObjectID>361C40DD-472C-4F3A-9CC5-9FAA8CFB94DF</a:ObjectID>
<a:CreationDate>1387615396</a:CreationDate>
<a:Creator>RD_johnnyding</a:Creator>
<a:ModificationDate>1387615475</a:ModificationDate>
<a:Modifier>RD_johnnyding</a:Modifier>
<c:Column>
<o:Column Ref="o90"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o95">
<a:ObjectID>8D687380-DFF3-43C2-B37C-68DF1D77602D</a:ObjectID>
<a:Name>TM_KDS_RptItem(Item临时表)</a:Name>
<a:Code>TM_KDS_RptItem</a:Code>
<a:CreationDate>1389597711</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o96">
<a:ObjectID>232F8776-AD22-4DA8-AF05-7AFD3AF27CCF</a:ObjectID>
<a:Name>FItemNumber</a:Name>
<a:Code>FItemNumber</a:Code>
<a:CreationDate>1389597789</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(60)</a:DataType>
<a:Length>60</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>902E2CD0-0A23-490D-B00D-464E439B8212</a:ObjectID>
<a:Name>FItemDataTypeNumber</a:Name>
<a:Code>FItemDataTypeNumber</a:Code>
<a:CreationDate>1389597823</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(60)</a:DataType>
<a:Length>60</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>D1AEB8F1-7A9A-4D15-BDFB-F08ED6424946</a:ObjectID>
<a:Name>FYear</a:Name>
<a:Code>FYear</a:Code>
<a:CreationDate>1389597852</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>B394D4AC-97AF-4870-8F06-765C9AACA0C9</a:ObjectID>
<a:Name>FPeriod</a:Name>
<a:Code>FPeriod</a:Code>
<a:CreationDate>1389597871</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>FF778382-95A9-4DE1-AC79-61F63E61B95F</a:ObjectID>
<a:Name>FRptDimension</a:Name>
<a:Code>FRptDimension</a:Code>
<a:CreationDate>1389597884</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o101">
<a:ObjectID>FF530E6D-F884-48D0-B1CB-64B75099FCA8</a:ObjectID>
<a:Name>TM_KDS_RptItemData(Item临时表)</a:Name>
<a:Code>TM_KDS_RptItemData</a:Code>
<a:CreationDate>1389597963</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o102">
<a:ObjectID>F32C23F5-C099-44B6-8477-C3B9557C4237</a:ObjectID>
<a:Name>FRPTITEMID</a:Name>
<a:Code>FRPTITEMID</a:Code>
<a:CreationDate>1389598019</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>1EF39E5F-7A71-4380-A6F4-ADCD1C085BD8</a:ObjectID>
<a:Name>FITEMDATATYPEID</a:Name>
<a:Code>FITEMDATATYPEID</a:Code>
<a:CreationDate>1389598043</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o104">
<a:ObjectID>90C03629-CF7A-40AF-A414-46FD52253FEE</a:ObjectID>
<a:Name>FYEAR</a:Name>
<a:Code>FYEAR</a:Code>
<a:CreationDate>1389598059</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>FA43ECF8-CA92-48EB-A3CA-5383EDC1BAFA</a:ObjectID>
<a:Name>FPERIOD</a:Name>
<a:Code>FPERIOD</a:Code>
<a:CreationDate>1389598073</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1389598145</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o106">
<a:ObjectID>48DCD443-B7FE-4B12-850F-B0628A37DED7</a:ObjectID>
<a:Name>TM_ORG_OrgInfo(组织信息临时表)</a:Name>
<a:Code>TM_ORG_OrgInfo</a:Code>
<a:CreationDate>1392024617</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1392025994</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o107">
<a:ObjectID>FA29D9B0-6E9C-4A7F-A3AA-B88C9E699BE8</a:ObjectID>
<a:Name>ForgID</a:Name>
<a:Code>ForgID</a:Code>
<a:CreationDate>1392024710</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1392025994</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>4DB19ED2-3C80-4823-92AC-E1FE7DA743A6</a:ObjectID>
<a:Name>FNumber</a:Name>
<a:Code>FNumber</a:Code>
<a:CreationDate>1392024749</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1392025994</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>3EED744A-81D7-4107-ABE2-AF2151699B5C</a:ObjectID>
<a:Name>FName</a:Name>
<a:Code>FName</a:Code>
<a:CreationDate>1392024792</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1392025994</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>B716915D-F28D-4788-846A-594238EECB40</a:ObjectID>
<a:Name>FDescription</a:Name>
<a:Code>FDescription</a:Code>
<a:CreationDate>1392024915</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1392025994</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>277F818A-2218-43FE-A726-7FE5558D386D</a:ObjectID>
<a:Name>FDataType</a:Name>
<a:Code>FDataType</a:Code>
<a:CreationDate>1392024940</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1392025994</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>294AB87F-D834-47BF-AC9C-EF82D713574B</a:ObjectID>
<a:Name>FStartDate</a:Name>
<a:Code>FStartDate</a:Code>
<a:CreationDate>1392024963</a:CreationDate>
<a:Creator>RD_qi_meng</a:Creator>
<a:ModificationDate>1392025994</a:ModificationDate>
<a:Modifier>RD_qi_meng</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o113">
<a:ObjectID>7FC7862E-94DE-4B1C-A788-95E78AA17164</a:ObjectID>
<a:Name>TM_ORG_ACROSSORGBD(跨组织数据处理临时表)</a:Name>
<a:Code>TM_ORG_ACROSSORGBD</a:Code>
<a:CreationDate>1395928301</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1395929720</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o114">
<a:ObjectID>A1AFAD17-9990-4F57-95DA-BABBA9175CAD</a:ObjectID>
<a:Name>表单ID</a:Name>
<a:Code>FFORMID</a:Code>
<a:CreationDate>1395928535</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1395929632</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>1B6B8224-277F-4793-893A-88E4A6D7AE0E</a:ObjectID>
<a:Name>源数据ID</a:Name>
<a:Code>FSOURCEID</a:Code>
<a:CreationDate>1395928535</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1395929720</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>4A46EA1A-BD5A-41B6-8C66-71E33C3D242F</a:ObjectID>
<a:Name>目标组织ID</a:Name>
<a:Code>FDESTORGID</a:Code>
<a:CreationDate>1395928535</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1395929632</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o117">
<a:ObjectID>0B1A161D-C304-45AB-A40C-8212C726CBFB</a:ObjectID>
<a:Name>TM_BOS_REPORTGROUPTABLE(报表分组汇总)</a:Name>
<a:Code>TM_BOS_REPORTGROUPTABLE</a:Code>
<a:CreationDate>1407749716</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o118">
<a:ObjectID>CDE29E40-CEBC-4E47-AB79-F532308DBA56</a:ObjectID>
<a:Name>ID</a:Name>
<a:Code>ID</a:Code>
<a:CreationDate>1407749781</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>7A0695D3-A57B-407A-8F70-A45838175D4A</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1407750696</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>CA1DC31F-BC27-4E50-9217-8FEB75C3125C</a:ObjectID>
<a:Name>FNUMBER</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1407750696</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>7AADE4F6-09EA-4B48-B829-54F1D052605D</a:ObjectID>
<a:Name>FGROUPID</a:Name>
<a:Code>FGROUPID</a:Code>
<a:CreationDate>1407750765</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>6E094DF9-7C0B-42DE-96B2-66E702D349EC</a:ObjectID>
<a:Name>FPARENTID</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>1407750765</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>57624A8C-C055-44FA-9764-ED4E4916A02B</a:ObjectID>
<a:Name>FFULLPARENTID</a:Name>
<a:Code>FFULLPARENTID</a:Code>
<a:CreationDate>1407750765</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>52002BCF-EE57-45D4-BBF9-B118EA5A47EC</a:ObjectID>
<a:Name>FLEFT</a:Name>
<a:Code>FLEFT</a:Code>
<a:CreationDate>1407750765</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>376895FF-B84D-44E5-A607-9AF4F41E1F09</a:ObjectID>
<a:Name>FRIGHT</a:Name>
<a:Code>FRIGHT</a:Code>
<a:CreationDate>1407750765</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>024549D9-4721-4A36-973A-1FA00716B7D3</a:ObjectID>
<a:Name>SEQ</a:Name>
<a:Code>SEQ</a:Code>
<a:CreationDate>1407750765</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>nvarchar(500)</a:DataType>
<a:Length>500</a:Length>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>AD08551E-84F6-492A-AD16-A7B195770312</a:ObjectID>
<a:Name>FORDERBY</a:Name>
<a:Code>FORDERBY</a:Code>
<a:CreationDate>1407750765</a:CreationDate>
<a:Creator>RD_ben_liu</a:Creator>
<a:ModificationDate>1407751358</a:ModificationDate>
<a:Modifier>RD_ben_liu</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o128">
<a:ObjectID>75BE2CE4-1603-4F81-A50F-1C1552AB3AF0</a:ObjectID>
<a:Name>TM_WF_OverallSysReport(流程总体状况分析报表临时表)</a:Name>
<a:Code>TM_WF_OverallSysReport</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:Comment>流程总体状况分析报表用的临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o129">
<a:ObjectID>FB90C7F0-2B15-4CD2-AC52-D506E321A193</a:ObjectID>
<a:Name>组织ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o130">
<a:ObjectID>52132ED6-2455-4061-99E6-483EC26633BE</a:ObjectID>
<a:Name>组织名称</a:Name>
<a:Code>FORG_ANME</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o131">
<a:ObjectID>E8031AE5-251D-4442-9728-45AFD87F95EC</a:ObjectID>
<a:Name>流程模板数量</a:Name>
<a:Code>FTEMPLATE_COUNT</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o132">
<a:ObjectID>0442D1EA-91CB-43B3-82EC-E05F5493D749</a:ObjectID>
<a:Name>当期新增流程实例数量</a:Name>
<a:Code>FPROCINST_COUNT</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o133">
<a:ObjectID>CB557A9A-FAF9-4D82-9537-E6AF4D51772D</a:ObjectID>
<a:Name>流程数量_运行中</a:Name>
<a:Code>FPROCINST_RUNNING</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>ABBC71CF-AFB4-4F81-ABA3-9C1CCFC80F4B</a:ObjectID>
<a:Name>运行中占比</a:Name>
<a:Code>FPERCENT_RUNNING</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>FFFBA776-1C6C-496A-8ADC-AEAE9943056F</a:ObjectID>
<a:Name>流程数量_已完成</a:Name>
<a:Code>FPROCINST_COMPLETE</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>AD3A540A-2A9C-4B5F-96E2-4573D0D0E025</a:ObjectID>
<a:Name>完成占比</a:Name>
<a:Code>FPERCENT_COMPLETE</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o137">
<a:ObjectID>1C5C83E8-EC45-45BB-A366-128047569245</a:ObjectID>
<a:Name>流程参与人数</a:Name>
<a:Code>FPERSON_COUNT</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>CEAC24C9-4F86-4968-8E79-74471B2A7849</a:ObjectID>
<a:Name>流程参与人次</a:Name>
<a:Code>FPERSON_TIMES</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o139">
<a:ObjectID>75C6CD7C-2558-4424-AC6E-AB442DDA90E8</a:ObjectID>
<a:Name>平均使用频率</a:Name>
<a:Code>FPERCENT_TIMES</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o140">
<a:ObjectID>660BDFB4-D6E8-4556-B47F-660856D74702</a:ObjectID>
<a:Name>总处理时长</a:Name>
<a:Code>FTOTAL_HOURS</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o141">
<a:ObjectID>447E390C-9231-47BA-87DF-023E06222A86</a:ObjectID>
<a:Name>实例平均处理时长</a:Name>
<a:Code>FWORKING_HOURS</a:Code>
<a:CreationDate>1407751506</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1407752590</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o25">
<a:ObjectID>DB6EB8E1-8F1B-49AB-8168-6F5D2572C79E</a:ObjectID>
<a:Name>TM_BF_INSTANCETRACK (业务流程追踪表)</a:Name>
<a:Code>TM_BF_INSTANCETRACK</a:Code>
<a:CreationDate>1409727630</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o142">
<a:ObjectID>05F34C17-2720-470B-90B8-F20EAA71194B</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>FCreateTime</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>C3496EFA-40A8-4BE2-B775-C9223144FABD</a:ObjectID>
<a:Name>创建批次</a:Name>
<a:Code>FCreateBatchNo</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>8B375193-E1D9-45FC-B8F0-1EBD7241B39D</a:ObjectID>
<a:Name>批内序号</a:Name>
<a:Code>FSeqInBatch</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>CBE3C522-894B-4950-83E4-D0618D691884</a:ObjectID>
<a:Name>全流程实例内码</a:Name>
<a:Code>FMasterId</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>36E65F0B-CE99-42D1-946B-D3BB00B463B7</a:ObjectID>
<a:Name>单据表格编码1</a:Name>
<a:Code>FTableNumber_1</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>10E87BD0-C9EC-4BC9-B588-2FA8436E9B15</a:ObjectID>
<a:Name>单据分录内码1</a:Name>
<a:Code>FEntityId_1</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>654C558E-1816-4BD3-ABB3-BE2DE07BF09D</a:ObjectID>
<a:Name>单据表格编码2</a:Name>
<a:Code>FTableNumber_2</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o149">
<a:ObjectID>E65D1290-559A-4049-8D03-49D4070BA960</a:ObjectID>
<a:Name>单据分录内码2</a:Name>
<a:Code>FEntityId_2</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o150">
<a:ObjectID>AA963F86-C87A-437F-B94D-3DEC80A12ADB</a:ObjectID>
<a:Name>单据表格编码3</a:Name>
<a:Code>FTableNumber_3</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>C43CCE90-683F-4860-9271-C3EDD9D3A999</a:ObjectID>
<a:Name>单据分录内码3</a:Name>
<a:Code>FEntityId_3</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o152">
<a:ObjectID>D2F83B65-B6BE-4CBB-86AD-52EC573CE844</a:ObjectID>
<a:Name>单据表格编码4</a:Name>
<a:Code>FTableNumber_4</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>0FD4814C-C7CD-4FEB-A745-0BB636918309</a:ObjectID>
<a:Name>单据分录内码4</a:Name>
<a:Code>FEntityId_4</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o154">
<a:ObjectID>30D72AEA-0FB6-495C-BC33-D5C3B0BC778B</a:ObjectID>
<a:Name>单据表格编码5</a:Name>
<a:Code>FTableNumber_5</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>96F0B1F4-7A16-4787-B959-4F697EB6FCC1</a:ObjectID>
<a:Name>单据分录内码5</a:Name>
<a:Code>FEntityId_5</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>48D3B518-93FB-4F5A-8D77-2AB9F0A16E6E</a:ObjectID>
<a:Name>单据表格编码6</a:Name>
<a:Code>FTableNumber_6</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o157">
<a:ObjectID>0DE3D69A-56F7-4504-B731-FE60923B11AD</a:ObjectID>
<a:Name>单据分录内码6</a:Name>
<a:Code>FEntityId_6</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>FD19E69A-CD06-48F5-ADB4-5CB262A75C25</a:ObjectID>
<a:Name>单据表格编码7</a:Name>
<a:Code>FTableNumber_7</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>0CE35CF3-2466-46F8-962E-0D69601C43C8</a:ObjectID>
<a:Name>单据分录内码7</a:Name>
<a:Code>FEntityId_7</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>1D1E571B-8CAA-48DD-9570-F3361A851263</a:ObjectID>
<a:Name>单据表格编码8</a:Name>
<a:Code>FTableNumber_8</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>4F0C5CF3-DB51-4A81-9FBC-E3C6F506F612</a:ObjectID>
<a:Name>单据分录内码8</a:Name>
<a:Code>FEntityId_8</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>8B01F4ED-5B83-416D-B59B-07C8C1F3ABA2</a:ObjectID>
<a:Name>单据表格编码9</a:Name>
<a:Code>FTableNumber_9</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>94467FF5-1A87-4F6E-A92D-62FB179D73C9</a:ObjectID>
<a:Name>单据分录内码9</a:Name>
<a:Code>FEntityId_9</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>4FA88B58-3828-46B1-9F1D-136323DC988D</a:ObjectID>
<a:Name>单据表格编码10</a:Name>
<a:Code>FTableNumber_10</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o165">
<a:ObjectID>A6C7ABC0-6D08-45B3-821D-909EA1FA3381</a:ObjectID>
<a:Name>单据分录内码10</a:Name>
<a:Code>FEntityId_10</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o166">
<a:ObjectID>F412F2E8-7D33-40BB-AAD6-D3D0BA55553B</a:ObjectID>
<a:Name>单据表格编码11</a:Name>
<a:Code>FTableNumber_11</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o167">
<a:ObjectID>6EA0852C-EC8D-4597-9526-F99C1D911ECA</a:ObjectID>
<a:Name>单据分录内码11</a:Name>
<a:Code>FEntityId_11</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>D8650284-A8AB-4304-B20F-57C2C872B266</a:ObjectID>
<a:Name>单据表格编码12</a:Name>
<a:Code>FTableNumber_12</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>4EA0EFEE-60AA-46C6-8251-003D8B3A0DA2</a:ObjectID>
<a:Name>单据分录内码12</a:Name>
<a:Code>FEntityId_12</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>EA27CAD4-D1EA-4E40-A17C-A81141D42A48</a:ObjectID>
<a:Name>单据表格编码13</a:Name>
<a:Code>FTableNumber_13</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o171">
<a:ObjectID>A4F88CFA-B548-4052-AD7B-414C4C6F35F5</a:ObjectID>
<a:Name>单据分录内码13</a:Name>
<a:Code>FEntityId_13</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o172">
<a:ObjectID>16F00F9A-9DD4-4796-9639-0C8897E4667E</a:ObjectID>
<a:Name>单据表格编码14</a:Name>
<a:Code>FTableNumber_14</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o173">
<a:ObjectID>E1C1BDC3-7BA4-4ED4-9B7E-B6A442250BC9</a:ObjectID>
<a:Name>单据分录内码14</a:Name>
<a:Code>FEntityId_14</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o174">
<a:ObjectID>3F3597E2-DB0A-45D3-851D-78EB97C50349</a:ObjectID>
<a:Name>单据表格编码15</a:Name>
<a:Code>FTableNumber_15</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o175">
<a:ObjectID>E9A35B1E-3AD9-4254-93F3-7B1051419F47</a:ObjectID>
<a:Name>单据分录内码15</a:Name>
<a:Code>FEntityId_15</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o176">
<a:ObjectID>2A56E3CD-4D71-4CA3-81DA-D87BEFB287A5</a:ObjectID>
<a:Name>单据表格编码16</a:Name>
<a:Code>FTableNumber_16</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>C618C3A4-4301-4998-88A1-C1196F7D6CD3</a:ObjectID>
<a:Name>单据分录内码16</a:Name>
<a:Code>FEntityId_16</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>06580541-6C0F-4555-B8EF-FD6E8EF1C4B7</a:ObjectID>
<a:Name>单据表格编码17</a:Name>
<a:Code>FTableNumber_17</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>CD42DB38-82BC-415F-B18C-D4053E559261</a:ObjectID>
<a:Name>单据分录内码17</a:Name>
<a:Code>FEntityId_17</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>1CB424C2-E0BA-4D96-AC8B-B1C39BEAB2AD</a:ObjectID>
<a:Name>单据表格编码18</a:Name>
<a:Code>FTableNumber_18</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>D49FBD87-F381-4B71-A599-766E9652F23B</a:ObjectID>
<a:Name>单据分录内码18</a:Name>
<a:Code>FEntityId_18</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>5F945C37-D865-4B72-8F60-6C7FCB4561AE</a:ObjectID>
<a:Name>单据表格编码19</a:Name>
<a:Code>FTableNumber_19</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>92465268-D9C8-46B9-BF9D-4C5F8D484F33</a:ObjectID>
<a:Name>单据分录内码19</a:Name>
<a:Code>FEntityId_19</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>BF6FD6EC-F853-4140-8F31-236825E08DBC</a:ObjectID>
<a:Name>单据表格编码20</a:Name>
<a:Code>FTableNumber_20</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>B70E5EB9-DAB0-479C-944F-8B58CD442B47</a:ObjectID>
<a:Name>单据分录内码20</a:Name>
<a:Code>FEntityId_20</a:Code>
<a:CreationDate>1409727678</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o21">
<a:ObjectID>A63F5D6B-687E-438C-A8BF-DFAAF9232C77</a:ObjectID>
<a:Name>TM_BF_CHANGEDINSTANCE (最近改动的业务流程实例)</a:Name>
<a:Code>TM_BF_CHANGEDINSTANCE</a:Code>
<a:CreationDate>1409714075</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o186">
<a:ObjectID>86AFD687-FCC1-47AC-909E-66E1860C3A7E</a:ObjectID>
<a:Name>流水内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1409714078</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>D62400AF-EA04-407A-982E-F26F2AAAB61C</a:ObjectID>
<a:Name>业务流程实例内码</a:Name>
<a:Code>FInstanceId</a:Code>
<a:CreationDate>1409714078</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o188">
<a:ObjectID>5E5D24D3-0331-41B3-AFBC-A044C9548F59</a:ObjectID>
<a:Name>IDX_TM_BF_CHANGEDINSTANCE_FID</a:Name>
<a:Code>IDX_TM_BF_CHANGEDINSTANCE_FID</a:Code>
<a:CreationDate>1410240473</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o189">
<a:ObjectID>84935EA1-392B-43A0-A76D-9BD19FD68C58</a:ObjectID>
<a:CreationDate>1410240484</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o186"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o190">
<a:ObjectID>6D1461B1-8EA3-446C-8D99-B51061FF0DE5</a:ObjectID>
<a:Name>IDX_TM_BF_CHANGEDINSTANCE_INST</a:Name>
<a:Code>IDX_TM_BF_CHANGEDINSTANCE_INST</a:Code>
<a:CreationDate>1410240484</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o191">
<a:ObjectID>DF1470AD-787D-4DA1-BE5F-277D88BDF33D</a:ObjectID>
<a:CreationDate>1410240559</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o187"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o23">
<a:ObjectID>60B7C39D-A436-4DD7-9F74-1A0872AE9E04</a:ObjectID>
<a:Name>TM_BF_INSTANCEENTRY (业务流程路线)</a:Name>
<a:Code>TM_BF_INSTANCEENTRY</a:Code>
<a:CreationDate>1409726182</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o192">
<a:ObjectID>264AFCB3-01E7-4817-A1B1-D42FFC8107B6</a:ObjectID>
<a:Name>路线内码</a:Name>
<a:Code>FRouteId</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o193">
<a:ObjectID>A16783CE-6588-47AA-BEEB-FC0C092F36A9</a:ObjectID>
<a:Name>流程实例内码</a:Name>
<a:Code>FInstanceId</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>F27D7B07-2D6F-46ED-9B6D-90B00E5DEC3C</a:ObjectID>
<a:Name>流程图路线</a:Name>
<a:Code>FLineId</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>4F668CF6-7E1D-4B96-83B8-1D4F54B80799</a:ObjectID>
<a:Name>来源单据表格Id</a:Name>
<a:Code>FSTableName</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>B0823531-55AD-4642-B091-7FD01DC724E9</a:ObjectID>
<a:Name>来源单据内码</a:Name>
<a:Code>FSId</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>E261BA9A-C919-41BE-B6E1-A17712C63C5B</a:ObjectID>
<a:Name>目标单据表格Id</a:Name>
<a:Code>FTTableName</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>FEA3B0F9-BD56-47CF-BFFD-610FB9DFD00E</a:ObjectID>
<a:Name>目标单据内码</a:Name>
<a:Code>FTId</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>8A143868-3970-44A8-B395-3D7BBDD228C2</a:ObjectID>
<a:Name>流程首节点</a:Name>
<a:Code>FFirstNode</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>A819F66F-947F-4DAE-AFBD-E7FE137F54FA</a:ObjectID>
<a:Name>发生时间</a:Name>
<a:Code>FCreateTime</a:Code>
<a:CreationDate>1409726333</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1409728498</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o201">
<a:ObjectID>C731F02C-D308-4116-8E0E-8CA1D6BB31AA</a:ObjectID>
<a:Name>IDX_TM_BF_INSTANCEENTRY_INST</a:Name>
<a:Code>IDX_TM_BF_INSTANCEENTRY_INST</a:Code>
<a:CreationDate>1410240612</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o202">
<a:ObjectID>0C657904-17A2-4061-BA6E-DAB8F992CE12</a:ObjectID>
<a:CreationDate>1410240636</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o193"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o203">
<a:ObjectID>EFB96D43-EB3A-4AD0-B4C6-10CA96FBBAD2</a:ObjectID>
<a:Name>IDX_TM_BF_INSTANCEENTRY_SID</a:Name>
<a:Code>IDX_TM_BF_INSTANCEENTRY_SID</a:Code>
<a:CreationDate>1410240636</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o204">
<a:ObjectID>BF0204BD-E912-4FCC-B0F6-049934A51696</a:ObjectID>
<a:CreationDate>1410240677</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o195"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o205">
<a:ObjectID>27052276-74F7-4C64-86D7-906827DEFE7C</a:ObjectID>
<a:CreationDate>1410240677</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o196"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o206">
<a:ObjectID>1364C9A6-9522-4D3C-8442-0D95ED8740D1</a:ObjectID>
<a:Name>IDX_TM_BF_INSTANCEENTRY_TID</a:Name>
<a:Code>IDX_TM_BF_INSTANCEENTRY_TID</a:Code>
<a:CreationDate>1410240677</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o207">
<a:ObjectID>AC329359-CC03-4F9A-AA1A-BE6547854BCC</a:ObjectID>
<a:CreationDate>1410240717</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o197"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o208">
<a:ObjectID>A0789A96-9386-4528-9874-9B07EE8AB782</a:ObjectID>
<a:CreationDate>1410240717</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1410240826</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o198"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o29">
<a:ObjectID>FC4FE87D-EC98-4857-A92D-C91001DDE858</a:ObjectID>
<a:Name>TM_WF_ProcessRunningReport(流程运行状况分析报表)</a:Name>
<a:Code>TM_WF_ProcessRunningReport</a:Code>
<a:CreationDate>1411542182</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:Comment>流程运行状况分析报表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o209">
<a:ObjectID>15C3E25E-750D-4111-9D69-0320F71CFDE7</a:ObjectID>
<a:Name>模版ID</a:Name>
<a:Code>FTMPID</a:Code>
<a:CreationDate>1411612940</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411613098</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o210">
<a:ObjectID>EE6A29E5-75AD-40E1-8696-3D515615EFE2</a:ObjectID>
<a:Name>组织ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1411612940</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411613098</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o211">
<a:ObjectID>29974B6C-18E8-435D-BC0B-B9AC91DEA444</a:ObjectID>
<a:Name>组织名称</a:Name>
<a:Code>FORG_NAME</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o212">
<a:ObjectID>8ACC137F-3565-445D-98AA-5D835FF941E4</a:ObjectID>
<a:Name>流程模板名称</a:Name>
<a:Code>FTEMPLATE_NAME</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o213">
<a:ObjectID>6BD2D5BF-9007-4BF4-8654-B9E309A7C960</a:ObjectID>
<a:Name>当期新增流程实例数量</a:Name>
<a:Code>FPROCINST_COUNT</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o214">
<a:ObjectID>DA74C662-CB64-432B-8806-1FA6660492BA</a:ObjectID>
<a:Name>运行中的流程数量</a:Name>
<a:Code>FPROCINST_RUNNING</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o215">
<a:ObjectID>AA6068D7-4987-4E2A-9D44-6DB4256B56DB</a:ObjectID>
<a:Name>运行中占比</a:Name>
<a:Code>FRUNNING_PERCENT</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o216">
<a:ObjectID>A403C98B-3D9A-4D65-96BF-ECF14576F26F</a:ObjectID>
<a:Name>暂停的流程数量</a:Name>
<a:Code>FPROCINST_PAUSE</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o217">
<a:ObjectID>AF5E5AEB-E882-44B4-AA80-FB6392DA83A8</a:ObjectID>
<a:Name>暂停占比</a:Name>
<a:Code>FPAUSE_PERCENT</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o218">
<a:ObjectID>1EAF7916-01F8-4259-B6DF-D799424614C9</a:ObjectID>
<a:Name>挂起的流程数量</a:Name>
<a:Code>FPROCINST_SLEEP</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o219">
<a:ObjectID>7DAA79CE-5B21-4969-823A-A80AF1CADB8D</a:ObjectID>
<a:Name>挂起占比</a:Name>
<a:Code>FSLEEP_PERCENT</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o220">
<a:ObjectID>FA65EB76-2110-4F58-9FD5-4531849533D2</a:ObjectID>
<a:Name>终止的流程数量</a:Name>
<a:Code>FPROCINST_ABORT</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o221">
<a:ObjectID>4BF34287-05C1-4E0E-B16B-C6F4FE66BF66</a:ObjectID>
<a:Name>终止占比</a:Name>
<a:Code>FABORT_PERCENT</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o222">
<a:ObjectID>FC170857-CDEB-4FE7-92C5-A8260BD99320</a:ObjectID>
<a:Name>完成的流程数量</a:Name>
<a:Code>FPROCINST_COMPLETE</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o223">
<a:ObjectID>12FB416A-152C-4ED2-BC6E-9FF41CB536A0</a:ObjectID>
<a:Name>完成占比</a:Name>
<a:Code>FCOMPLETE_PERCENT</a:Code>
<a:CreationDate>1411542203</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o27">
<a:ObjectID>8F131E51-E518-48EE-A7A2-BB6624B4BCA0</a:ObjectID>
<a:Name>TM_WF_COSTINGTIMEREPORT(流程时间耗费分析报表)</a:Name>
<a:Code>TM_WF_COSTINGTIMEREPORT</a:Code>
<a:CreationDate>1411541849</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1460014740</a:ModificationDate>
<a:Modifier>RD_weixy</a:Modifier>
<a:Comment>流程时间耗费分析报表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o224">
<a:ObjectID>5B827071-A363-408C-A7A9-0060832A6F0B</a:ObjectID>
<a:Name>模版id</a:Name>
<a:Code>ftmpid</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o225">
<a:ObjectID>2DAE8176-1C19-4DA9-ADCA-6245E23ACDF6</a:ObjectID>
<a:Name>组织id</a:Name>
<a:Code>forgid</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o226">
<a:ObjectID>53C871AC-C3D7-4BB3-A8AC-997291866BB6</a:ObjectID>
<a:Name>流程ID</a:Name>
<a:Code>FPROCDEFID</a:Code>
<a:CreationDate>1411719934</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411721431</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o227">
<a:ObjectID>93F0631D-5D18-45F7-BA28-3139D396E8BC</a:ObjectID>
<a:Name>节点ID</a:Name>
<a:Code>FACTIVITYID</a:Code>
<a:CreationDate>1411719934</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411720093</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o228">
<a:ObjectID>FF6D6ED3-DEB1-43C2-9F60-208CBA685A25</a:ObjectID>
<a:Name>组织名称</a:Name>
<a:Code>forg_name</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o229">
<a:ObjectID>1DD5B7E9-BD17-4371-82E9-71D91C39FF86</a:ObjectID>
<a:Name>模版名称</a:Name>
<a:Code>ftemplate_name</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o230">
<a:ObjectID>F3C9DAB7-AE4B-457E-96AF-B1DEE3770E68</a:ObjectID>
<a:Name>流程名称</a:Name>
<a:Code>FPRO_NAME</a:Code>
<a:CreationDate>1411719934</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411720093</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o231">
<a:ObjectID>C52BBE1C-65E2-468D-ABE4-D38658024A38</a:ObjectID>
<a:Name>节点名称</a:Name>
<a:Code>FACT_NAME</a:Code>
<a:CreationDate>1411719934</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411720093</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o232">
<a:ObjectID>E24B4F7A-EE40-4E49-AA55-BEF58A9D7EA5</a:ObjectID>
<a:Name>平均处理时长</a:Name>
<a:Code>fworking_hours</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411884764</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o233">
<a:ObjectID>9A3ECE5F-F53A-40C8-BDF0-B9D1C6FEEA6B</a:ObjectID>
<a:Name>最长处理时长</a:Name>
<a:Code>fworking_max</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411884764</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o234">
<a:ObjectID>93F78DDC-B11E-46C7-8E2A-69D2CE040B9C</a:ObjectID>
<a:Name>最长处理时长实例</a:Name>
<a:Code>fprocinst_max</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o235">
<a:ObjectID>CD9BDB3B-8576-4F17-BCDA-726AAF5E3198</a:ObjectID>
<a:Name>最短处理时长</a:Name>
<a:Code>fworking_min</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411884764</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o236">
<a:ObjectID>ED7247B1-79CF-4881-AB29-2D7F891FBAC5</a:ObjectID>
<a:Name>最短处理时长实例</a:Name>
<a:Code>fprocinst_min</a:Code>
<a:CreationDate>1411541854</a:CreationDate>
<a:Creator>RD_Xin_X_Deng</a:Creator>
<a:ModificationDate>1411542515</a:ModificationDate>
<a:Modifier>RD_Xin_X_Deng</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o237">
<a:ObjectID>6EBE1666-180E-418E-82ED-078E70E9E96A</a:ObjectID>
<a:Name>流程实例数</a:Name>
<a:Code>FPROCINST_COUNT</a:Code>
<a:CreationDate>1460014711</a:CreationDate>
<a:Creator>RD_weixy</a:Creator>
<a:ModificationDate>1460014740</a:ModificationDate>
<a:Modifier>RD_weixy</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o31">
<a:ObjectID>18FCD2F5-6576-4467-BAB0-CE69617A8FB7</a:ObjectID>
<a:Name>TM_BF_INSTALL (业务流程实例表)</a:Name>
<a:Code>TM_BF_INSTALL</a:Code>
<a:CreationDate>1411885554</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:Comment>取业务流程实例正式表、历史表、归档表，合并数据输出到本表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o238">
<a:ObjectID>FE8690CF-8A48-46DE-AC93-393E68FEE42D</a:ObjectID>
<a:Name>流程实例内码</a:Name>
<a:Code>FInstanceId</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o239">
<a:ObjectID>52F9334C-6C22-4D1C-8919-6EE650DE6BEB</a:ObjectID>
<a:Name>流程内码</a:Name>
<a:Code>FFlowId</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o240">
<a:ObjectID>AA970DD3-10EB-4862-BB83-C9C90C62E04D</a:ObjectID>
<a:Name>来源实例内码</a:Name>
<a:Code>FSourceId</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o241">
<a:ObjectID>A3CA96C9-E519-4668-BBA3-5CAECC294180</a:ObjectID>
<a:Name>全程实例内码</a:Name>
<a:Code>FMasterId</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o242">
<a:ObjectID>C99B418A-1193-45F6-BE10-0431C1449F1C</a:ObjectID>
<a:Name>实例状态</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o243">
<a:ObjectID>CF589F2B-2875-4E79-9C94-E29955FA0124</a:ObjectID>
<a:Name>发起单据</a:Name>
<a:Code>FFirstFormId</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o244">
<a:ObjectID>9B9BC61A-FF2D-4ED0-96B2-03B6B62BF34D</a:ObjectID>
<a:Name>发起单据内码</a:Name>
<a:Code>FFirstBillId</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o245">
<a:ObjectID>BFEF8DD0-7B26-46F7-8BE4-0F67826139B8</a:ObjectID>
<a:Name>发起单据编号</a:Name>
<a:Code>FFirstBillNo</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o246">
<a:ObjectID>4070C32A-30ED-4EB4-BB9E-F4A400B40037</a:ObjectID>
<a:Name>实例创建时间</a:Name>
<a:Code>FStartTime</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o247">
<a:ObjectID>F881BA43-3C27-4B02-917D-55570AA5432C</a:ObjectID>
<a:Name>IDX_TM_INSTALL_ID</a:Name>
<a:Code>IDX_TM_INSTALL_ID</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o248">
<a:ObjectID>827BC2E1-19F2-4F91-9600-D546C860052A</a:ObjectID>
<a:CreationDate>1411885966</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o238"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o249">
<a:ObjectID>5F663BB8-1F34-486B-8067-08B7ADE43ECA</a:ObjectID>
<a:Name>IDX_TM_INSTALL_MASTERID</a:Name>
<a:Code>IDX_TM_INSTALL_MASTERID</a:Code>
<a:CreationDate>1411885556</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o250">
<a:ObjectID>6EB69061-DA57-4ED8-82D8-02FEB9AC4EA9</a:ObjectID>
<a:CreationDate>1411885981</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o241"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o33">
<a:ObjectID>322CC2DC-DD9F-409A-B2BE-C9F461FD5FAA</a:ObjectID>
<a:Name>TM_BF_INSTENTRYALL (流程路线表)</a:Name>
<a:Code>TM_BF_INSTENTRYALL</a:Code>
<a:CreationDate>1411886021</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:Comment>合并流程路线正式表、历史表、归档表的数据到此临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o251">
<a:ObjectID>FCCBC536-E1D5-41D0-A9D6-577E530A94E2</a:ObjectID>
<a:Name>路线内码</a:Name>
<a:Code>FRouteId</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o252">
<a:ObjectID>3667C7F9-EE1B-4D0D-9260-4DB1DE399CCD</a:ObjectID>
<a:Name>流程实例内码</a:Name>
<a:Code>FInstanceId</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o253">
<a:ObjectID>F918DC50-687A-44E9-BCB3-7454D5BB3264</a:ObjectID>
<a:Name>流程图路线</a:Name>
<a:Code>FLineId</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o254">
<a:ObjectID>A73B3DF9-1F33-489E-8474-48D726BF8F9A</a:ObjectID>
<a:Name>来源单据表格Id</a:Name>
<a:Code>FSTableName</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o255">
<a:ObjectID>135D59FD-655E-4DDE-A35E-C208849366F0</a:ObjectID>
<a:Name>来源单据内码</a:Name>
<a:Code>FSId</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o256">
<a:ObjectID>461F4551-1D1B-4E1E-9D23-A95EFE32471F</a:ObjectID>
<a:Name>目标单据表格Id</a:Name>
<a:Code>FTTableName</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o257">
<a:ObjectID>BC46D93D-4FDA-4DCE-97B0-A17CCBDEBBEF</a:ObjectID>
<a:Name>目标单据内码</a:Name>
<a:Code>FTId</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o258">
<a:ObjectID>CF2C1A2C-835A-404A-A37B-EF62603B255C</a:ObjectID>
<a:Name>流程首节点</a:Name>
<a:Code>FFirstNode</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o259">
<a:ObjectID>B02816D5-0170-4662-AB81-F68A94AD41EA</a:ObjectID>
<a:Name>发生时间</a:Name>
<a:Code>FCreateTime</a:Code>
<a:CreationDate>1411886023</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o260">
<a:ObjectID>890C0CA7-B668-4BE9-974C-6F91D1AF599A</a:ObjectID>
<a:Name>IDX_TM_BF_INSTENTRYALL_ID</a:Name>
<a:Code>IDX_TM_BF_INSTENTRYALL_ID</a:Code>
<a:CreationDate>1411886246</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o261">
<a:ObjectID>E341DE81-5619-48C0-9B27-D4F228B6A927</a:ObjectID>
<a:CreationDate>1411886334</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o251"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o262">
<a:ObjectID>20485593-D4B6-410B-B196-3C161F9B6CC4</a:ObjectID>
<a:Name>IDX_TM_BF_INSTENTRYALL_INST</a:Name>
<a:Code>IDX_TM_BF_INSTENTRYALL_INST</a:Code>
<a:CreationDate>1411886246</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o263">
<a:ObjectID>32FB76A1-ED14-4027-86E3-23A347318843</a:ObjectID>
<a:CreationDate>1411886342</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o252"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o264">
<a:ObjectID>759C08DE-0414-48B9-8118-312DA1E7874B</a:ObjectID>
<a:Name>IDX_TM_BF_INSTENTRYALL_SID</a:Name>
<a:Code>IDX_TM_BF_INSTENTRYALL_SID</a:Code>
<a:CreationDate>1411886246</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o265">
<a:ObjectID>4E07D197-537B-4835-9EC4-67F2D9065F45</a:ObjectID>
<a:CreationDate>1411886349</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o255"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o266">
<a:ObjectID>E08412D7-D385-46C9-976C-19BB8BE3D3F5</a:ObjectID>
<a:Name>IDX_TM_BF_INSTENTRYALL_TID</a:Name>
<a:Code>IDX_TM_BF_INSTENTRYALL_TID</a:Code>
<a:CreationDate>1411886246</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o267">
<a:ObjectID>3FF45B83-665D-4BCA-9607-E9B89D1305ED</a:ObjectID>
<a:CreationDate>1411886356</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o257"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o17">
<a:ObjectID>FDD6FE73-01DA-4BF8-A2CA-DF1FD5F00AFE</a:ObjectID>
<a:Name>TM_BF_INSTANCEMASTERIDS (业务流程全程实例内码集合)</a:Name>
<a:Code>TM_BF_INSTANCEMASTERIDS</a:Code>
<a:CreationDate>1384741645</a:CreationDate>
<a:Creator>RD_yong_fu</a:Creator>
<a:ModificationDate>1384742067</a:ModificationDate>
<a:Modifier>RD_yong_fu</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o268">
<a:ObjectID>4744CD7C-1318-4FB0-9B5F-842A0EBFC548</a:ObjectID>
<a:Name>全程实例内码</a:Name>
<a:Code>FMASTERID</a:Code>
<a:CreationDate>1384741664</a:CreationDate>
<a:Creator>RD_yong_fu</a:Creator>
<a:ModificationDate>1384742004</a:ModificationDate>
<a:Modifier>RD_yong_fu</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o269">
<a:ObjectID>86154E41-B019-4BB5-BAB1-EA22E9350B96</a:ObjectID>
<a:Name>IDX_TM_BF_INSTANCEMASTERIDS</a:Name>
<a:Code>IDX_TM_BF_INSTANCEMASTERIDS</a:Code>
<a:CreationDate>1411891255</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411897930</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o270">
<a:ObjectID>06C94968-F2A6-4D2C-93DF-2FFDE2FD0C96</a:ObjectID>
<a:CreationDate>1411891341</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411897930</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o268"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o35">
<a:ObjectID>D371F09F-9455-4BBF-8186-58FC15B65D9B</a:ObjectID>
<a:Name>TM_BF_INSTAMOUNTALL (流程携带表)</a:Name>
<a:Code>TM_BF_INSTAMOUNTALL</a:Code>
<a:CreationDate>1411886391</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:Comment>合并业务流程携带正式表、历史表、归档表的数据到本表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o271">
<a:ObjectID>40ADE9AA-6055-462D-AD38-834964913E45</a:ObjectID>
<a:Name>数据内码</a:Name>
<a:Code>FDetailId</a:Code>
<a:CreationDate>1411886393</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o272">
<a:ObjectID>6BE9273F-BCAA-405D-ACA4-4EA8812ECC72</a:ObjectID>
<a:Name>路线内码</a:Name>
<a:Code>FRouteId</a:Code>
<a:CreationDate>1411886393</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o273">
<a:ObjectID>B597239C-FF25-45C6-A430-D140E179CB9B</a:ObjectID>
<a:Name>源单字段</a:Name>
<a:Code>FSourceField</a:Code>
<a:CreationDate>1411886393</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411891393</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o274">
<a:ObjectID>5F792F53-7103-448E-9095-4E29E2656726</a:ObjectID>
<a:Name>目标单字段</a:Name>
<a:Code>FTargetField</a:Code>
<a:CreationDate>1411886393</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411891393</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o275">
<a:ObjectID>B18CEC76-3A6B-4372-A2C4-3AC054207EAE</a:ObjectID>
<a:Name>携带数值</a:Name>
<a:Code>FAmount</a:Code>
<a:CreationDate>1411886393</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o276">
<a:ObjectID>E104C53F-7DA6-4723-BDD1-529504BA556C</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>FCreateTime</a:Code>
<a:CreationDate>1411886393</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o277">
<a:ObjectID>E31E8E7C-1D23-4269-96E0-B904E67224F4</a:ObjectID>
<a:Name>IDX_TM_BF_INSTAMOUNTALL_ID</a:Name>
<a:Code>IDX_TM_BF_INSTAMOUNTALL_ID</a:Code>
<a:CreationDate>1411886393</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o278">
<a:ObjectID>860F7A40-51E4-4BF7-BF1C-FF4C9C4FFAC7</a:ObjectID>
<a:CreationDate>1411886591</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o271"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o279">
<a:ObjectID>6CC9AA3A-D98B-41D6-B77D-A652CB217B18</a:ObjectID>
<a:Name>IDX_TM_BF_INSTAMOUNTALL_ROUTE</a:Name>
<a:Code>IDX_TM_BF_INSTAMOUNTALL_ROUTE</a:Code>
<a:CreationDate>1411886393</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o280">
<a:ObjectID>F0C9684D-9B4E-4B50-919A-9CAB829169A7</a:ObjectID>
<a:CreationDate>1411886598</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1411889577</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o272"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o37">
<a:ObjectID>E9255D82-F140-469C-84D6-871F2CD0B4D1</a:ObjectID>
<a:Name>TM_WF_PROCINSTIDS (工作流实例内码)</a:Name>
<a:Code>TM_WF_PROCINSTIDS</a:Code>
<a:CreationDate>1412823773</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1412828292</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:Comment>临时存储待处理的工作流实例内码集合。</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o281">
<a:ObjectID>FCDC7548-1F1D-420A-BA9B-B0D3369FBF10</a:ObjectID>
<a:Name>实例内码</a:Name>
<a:Code>FProcInstId</a:Code>
<a:CreationDate>1412823775</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1412828292</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o282">
<a:ObjectID>BA7ECCE4-9EEE-4212-8298-490C44DC47D3</a:ObjectID>
<a:Name>IDX_TM_WF_PROCINSTIDS</a:Name>
<a:Code>IDX_TM_WF_PROCINSTIDS</a:Code>
<a:CreationDate>1412823856</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1412828292</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o283">
<a:ObjectID>BC7C3626-52B5-4B1F-8594-CA6252A5DD72</a:ObjectID>
<a:CreationDate>1412823862</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1412828292</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<c:Column>
<o:Column Ref="o281"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o284">
<a:ObjectID>4955C055-F5F0-461F-A88D-99A3BE55A5CD</a:ObjectID>
<a:Name>TM_BAS_NUMBERVALID4INT（编码校验器INT）</a:Name>
<a:Code>TM_BAS_NUMBERVALID4INT</a:Code>
<a:CreationDate>1418109205</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1418109538</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o285">
<a:ObjectID>5D885E88-F8CF-476F-83CA-1C7A53786986</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1418109276</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1418109538</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o286">
<a:ObjectID>B22FED39-AF54-4C87-AA11-8CE148A0D37C</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1418109372</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1418109538</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>INT</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o287">
<a:ObjectID>19AD52C9-E549-4361-9AA9-7AA7B980FB2D</a:ObjectID>
<a:Name>TM_BAS_NUMBERVALID4STR（编码校验器String）</a:Name>
<a:Code>TM_BAS_NUMBERVALID4STR</a:Code>
<a:CreationDate>1418109417</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1418109538</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o288">
<a:ObjectID>1BDEA7CB-B0A0-4138-9C6D-39510C1BAD62</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1418109433</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1418109538</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o289">
<a:ObjectID>64D29660-4944-49FD-9981-2C3C0E43015F</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1418109454</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1418109538</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o43">
<a:ObjectID>4281A400-F52F-467D-B5C7-A3C1F2BFEEA2</a:ObjectID>
<a:Name>TM_BF_INSTAMOUNTBACKUP (流程携带)</a:Name>
<a:Code>TM_BF_INSTAMOUNTBACKUP</a:Code>
<a:CreationDate>1418870115</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:Comment>还原另存为文件的业务流程数据时，利用本表为中间表，临时存储还原的数据</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o290">
<a:ObjectID>5A6D4D15-A301-4F21-9C6C-3B7022E365B3</a:ObjectID>
<a:Name>数据内码</a:Name>
<a:Code>FDetailId</a:Code>
<a:CreationDate>1418870115</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o291">
<a:ObjectID>5CA4D5A8-CFC4-4C5A-829F-B5F9DA9E5252</a:ObjectID>
<a:Name>路线内码</a:Name>
<a:Code>FRouteId</a:Code>
<a:CreationDate>1418870115</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o292">
<a:ObjectID>18DC4956-5ADC-4FEE-A84B-635BA68D5035</a:ObjectID>
<a:Name>源单字段</a:Name>
<a:Code>FSourceField</a:Code>
<a:CreationDate>1418870115</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o293">
<a:ObjectID>A473B4E4-5527-4DDC-A81A-FC53577BE925</a:ObjectID>
<a:Name>目标单字段</a:Name>
<a:Code>FTargetField</a:Code>
<a:CreationDate>1418870115</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o294">
<a:ObjectID>02F029AD-F77C-474F-B518-8DEF29B7A661</a:ObjectID>
<a:Name>携带数值</a:Name>
<a:Code>FAmount</a:Code>
<a:CreationDate>1418870115</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o295">
<a:ObjectID>318878C2-8FCD-467E-A73F-3B39872F1F19</a:ObjectID>
<a:Name>创建时间</a:Name>
<a:Code>FCreateTime</a:Code>
<a:CreationDate>1418870115</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o39">
<a:ObjectID>F07B0F3D-80D2-4633-9726-ECDDF4FEFB3B</a:ObjectID>
<a:Name>TM_BF_INSTBACKUP (业务流程实例)</a:Name>
<a:Code>TM_BF_INSTBACKUP</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:Comment>还原另存文件的业务流程数据时，将此表作为中间表，临时存储还原的数据</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o296">
<a:ObjectID>FC78B03D-C968-453E-B02E-7DC2E9494549</a:ObjectID>
<a:Name>流程实例内码</a:Name>
<a:Code>FInstanceId</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o297">
<a:ObjectID>1C470E04-B3E2-4DB5-9D35-7EFD71734F8A</a:ObjectID>
<a:Name>流程内码</a:Name>
<a:Code>FFlowId</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o298">
<a:ObjectID>605B7456-678C-4264-B555-917A923A8C84</a:ObjectID>
<a:Name>来源实例内码</a:Name>
<a:Code>FSourceId</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o299">
<a:ObjectID>094722A1-2300-4447-A9B5-F64C00D61226</a:ObjectID>
<a:Name>全程实例内码</a:Name>
<a:Code>FMasterId</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o300">
<a:ObjectID>B7DBA664-1AFD-4065-8361-223F77B41FFC</a:ObjectID>
<a:Name>实例状态</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o301">
<a:ObjectID>3A8FE4F5-4C60-4609-B783-C68E1093C31F</a:ObjectID>
<a:Name>发起单据</a:Name>
<a:Code>FFirstFormId</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o302">
<a:ObjectID>C791C7AC-6431-428B-8CD1-2B60AB9BBC27</a:ObjectID>
<a:Name>发起单据内码</a:Name>
<a:Code>FFirstBillId</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o303">
<a:ObjectID>CDF8BBB9-0583-43DA-BA73-A3A635100138</a:ObjectID>
<a:Name>发起单据编号</a:Name>
<a:Code>FFirstBillNo</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o304">
<a:ObjectID>BA937747-4CAE-496E-A117-76886FFB6DEA</a:ObjectID>
<a:Name>实例创建时间</a:Name>
<a:Code>FStartTime</a:Code>
<a:CreationDate>1418868850</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o41">
<a:ObjectID>206B73CF-14B3-4B94-B7BD-E549E680639D</a:ObjectID>
<a:Name>TM_BF_INSTENTRYBACKUP (流程路线)</a:Name>
<a:Code>TM_BF_INSTENTRYBACKUP</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:Comment>还原另存文件的业务流程数据时，利用本表为中间表，临时存储还原的数据</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o305">
<a:ObjectID>954C047A-203B-4FFA-B88A-32603A2A17EA</a:ObjectID>
<a:Name>路线内码</a:Name>
<a:Code>FRouteId</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o306">
<a:ObjectID>0B0137CD-3846-4555-879C-867104DF411E</a:ObjectID>
<a:Name>流程实例内码</a:Name>
<a:Code>FInstanceId</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o307">
<a:ObjectID>D9EEAEB3-2D6C-4557-A7B0-4A57450445D0</a:ObjectID>
<a:Name>流程图路线</a:Name>
<a:Code>FLineId</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o308">
<a:ObjectID>2CB5D7A9-EA46-4E68-9DB2-5643DB973543</a:ObjectID>
<a:Name>来源单据表格</a:Name>
<a:Code>FSTableId</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o309">
<a:ObjectID>32B011BC-A740-4E68-A410-700907A5F4CF</a:ObjectID>
<a:Name>来源单据内码</a:Name>
<a:Code>FSId</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o310">
<a:ObjectID>E84A3158-7002-4516-81A9-E664020B38EB</a:ObjectID>
<a:Name>目标单据表格</a:Name>
<a:Code>FTTableId</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o311">
<a:ObjectID>6FBDE9A9-6EE8-45F5-9E70-9AFA6C400142</a:ObjectID>
<a:Name>目标单据内码</a:Name>
<a:Code>FTId</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o312">
<a:ObjectID>EAFC01B0-24E6-44C5-A77B-F7DD0A984670</a:ObjectID>
<a:Name>流程首节点</a:Name>
<a:Code>FFirstNode</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o313">
<a:ObjectID>4714DDC3-30AA-4AEC-81AE-8220726524F7</a:ObjectID>
<a:Name>发生时间</a:Name>
<a:Code>FCreateTime</a:Code>
<a:CreationDate>1418869854</a:CreationDate>
<a:Creator>rd_JohnnyDing</a:Creator>
<a:ModificationDate>1418871103</a:ModificationDate>
<a:Modifier>rd_JohnnyDing</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o314">
<a:ObjectID>1BC21CBB-B27B-40E9-AED0-AA25260E8E4D</a:ObjectID>
<a:Name>TM_BAS_BDMAP(基础资料映射)</a:Name>
<a:Code>TM_BAS_BDMAP</a:Code>
<a:CreationDate>1413774493</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1413799232</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o315">
<a:ObjectID>A09A1655-1B50-4136-9081-3C9EBB554DFA</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1413774660</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1419992222</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>VARCHAR(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o316">
<a:ObjectID>50A3616D-C1D3-44E6-8D63-7F3A440C83D5</a:ObjectID>
<a:Name>业务编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1413774852</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1413775269</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>NVARCHAR(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o317">
<a:ObjectID>24AA8E0D-6588-4A68-9286-A9D9853C7017</a:ObjectID>
<a:Name>业务名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1413774852</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1413775269</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>NVARCHAR(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o318">
<a:ObjectID>EF838DAE-240D-458C-8305-6113352AFD6C</a:ObjectID>
<a:Name>基础资料映射内码</a:Name>
<a:Code>FBDMapID</a:Code>
<a:CreationDate>1414054597</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1414054725</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o319">
<a:ObjectID>241A07C3-B9F0-4DAC-B4C9-E1FB8D9B9638</a:ObjectID>
<a:Name>基础资料映射明细内码</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1414054666</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1414054725</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o320">
<a:ObjectID>4F4E3863-871A-496A-BF87-4FF6E2FBE290</a:ObjectID>
<a:Name>多语言PKID</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1414135944</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1414135991</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o321">
<a:ObjectID>374E7E09-BC6F-4B3A-A642-35D99B06BDA8</a:ObjectID>
<a:Name>PK_TM_BAS_BDMAP</a:Name>
<a:Code>PK_TM_BAS_BDMAP</a:Code>
<a:CreationDate>1413799226</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1413799311</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o315"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o322">
<a:ObjectID>ECCFAED4-EEBE-464F-BC2B-02578F4CC57F</a:ObjectID>
<a:Name>IDX_TM_BAS_BDMAP_NB</a:Name>
<a:Code>IDX_TM_BAS_BDMAP_NB</a:Code>
<a:CreationDate>1413775015</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1413775269</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o323">
<a:ObjectID>CBE99B01-5B8F-49FC-A85C-B33CBE48B556</a:ObjectID>
<a:CreationDate>1413775106</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1413775269</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<c:Column>
<o:Column Ref="o316"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o324">
<a:ObjectID>49DCDB78-77E0-4677-A508-193C9D1D27EB</a:ObjectID>
<a:Name>IDX_TM_BAS_BDMAP_NM</a:Name>
<a:Code>IDX_TM_BAS_BDMAP_NM</a:Code>
<a:CreationDate>1413775140</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1413775269</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o325">
<a:ObjectID>7A2676EF-F005-4267-86D1-B853E13E5F37</a:ObjectID>
<a:CreationDate>1413775179</a:CreationDate>
<a:Creator>RD_qin_li</a:Creator>
<a:ModificationDate>1413775269</a:ModificationDate>
<a:Modifier>RD_qin_li</a:Modifier>
<c:Column>
<o:Column Ref="o317"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o321"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o321"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o47">
<a:ObjectID>37F9E993-52FE-4115-B8C4-C46603AAC496</a:ObjectID>
<a:Name>TM_SEC_FUNCPERMISSIONENTRY(功能权限明细表体)</a:Name>
<a:Code>TM_SEC_FUNCPERMISSIONENTRY</a:Code>
<a:CreationDate>1429843742</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429843826</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o326">
<a:ObjectID>E6B7BFC9-A4C9-4415-B367-EB40987A9594</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>fitemid</a:Code>
<a:CreationDate>1429843742</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429843826</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o327">
<a:ObjectID>361E71BC-FECD-4138-8A53-66FEDD2A3B35</a:ObjectID>
<a:Name>分录内码</a:Name>
<a:Code>fentryid</a:Code>
<a:CreationDate>1429843742</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429843826</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o328">
<a:ObjectID>55BBDA26-8243-4FE3-B8B0-DFB690F855D6</a:ObjectID>
<a:Name>权限项内码</a:Name>
<a:Code>fpermissionitemid</a:Code>
<a:CreationDate>1429843742</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429843826</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o329">
<a:ObjectID>2DDBCC84-170E-4E98-8B55-4A49F3CF4A8D</a:ObjectID>
<a:Name>权限状态</a:Name>
<a:Code>fpermissionstatus</a:Code>
<a:CreationDate>1429843742</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429843826</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o330">
<a:ObjectID>BB22AA11-22AC-437D-87D1-0D37EB3FF42F</a:ObjectID>
<a:Name>数据范围</a:Name>
<a:Code>fdatarule</a:Code>
<a:CreationDate>1429843742</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429843826</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o45">
<a:ObjectID>3D3F610A-2CDE-423F-951E-45685277DA44</a:ObjectID>
<a:Name>TM_SEC_FUNCPERMISSION(功能权限授权)</a:Name>
<a:Code>TM_SEC_FUNCPERMISSION</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:Comment>功能权限信息保存表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o331">
<a:ObjectID>6D1C8662-6C0F-4707-A839-C6193A5A419B</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FITEMID</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o332">
<a:ObjectID>D0EBE9F0-DD3F-44D5-A14C-820996BE825A</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>fforbidstatus</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429863449</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o333">
<a:ObjectID>F7EF52BD-FD98-4390-85CC-50829DD3B343</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>fnumber</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429863449</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
</o:Column>
<o:Column Id="o334">
<a:ObjectID>88CCADE3-63FA-426A-949C-BBB17DE34263</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>fdescription</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429863449</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o335">
<a:ObjectID>54D3F193-B47E-4D85-84D6-3BA91B2EFE94</a:ObjectID>
<a:Name>角色内码</a:Name>
<a:Code>FROLEID</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o336">
<a:ObjectID>8AD028CE-DDFD-4000-ADAB-E0ECCFEDEE44</a:ObjectID>
<a:Name>业务对象内码</a:Name>
<a:Code>fobjecttypeid</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o337">
<a:ObjectID>0BBD3BF6-8A1D-4FC4-9EAD-792CE4309336</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o338">
<a:ObjectID>F012A1A5-0027-42EC-9C64-04CD74C1E818</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o339">
<a:ObjectID>F04FEA63-6781-48D1-AFAA-9869D14D7532</a:ObjectID>
<a:Name>最后修改人</a:Name>
<a:Code>FMODIFIERID</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o340">
<a:ObjectID>E5E735E1-4E69-4544-B663-63B73C62E09D</a:ObjectID>
<a:Name>最后修改日期</a:Name>
<a:Code>FMODIFYDATE</a:Code>
<a:CreationDate>1429776384</a:CreationDate>
<a:Creator>rd_brunce_liu</a:Creator>
<a:ModificationDate>1429776527</a:ModificationDate>
<a:Modifier>rd_brunce_liu</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o49">
<a:ObjectID>89E270A6-A2EC-4ABE-B473-99087A031EDC</a:ObjectID>
<a:Name>TM_BAS_LOGBACKUPTODB(日志备份到日志库)</a:Name>
<a:Code>TM_BAS_LOGBACKUPTODB</a:Code>
<a:CreationDate>1527301798</a:CreationDate>
<a:Creator>rd_jia_xiong</a:Creator>
<a:ModificationDate>1527301798</a:ModificationDate>
<a:Modifier>rd_jia_xiong</a:Modifier>
<a:Comment>日志备份日志库临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o341">
<a:ObjectID>8F6C14FF-4A61-4C3E-9F4B-CC45F4BFB9DF</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1527301798</a:CreationDate>
<a:Creator>rd_jia_xiong</a:Creator>
<a:ModificationDate>1527301798</a:ModificationDate>
<a:Modifier>rd_jia_xiong</a:Modifier>
<a:Comment>上机日志内码</a:Comment>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o51">
<a:ObjectID>29F3504B-61A2-4067-BE8A-1E53F1120A12</a:ObjectID>
<a:Name>TM_WF_NodeCostingTimeReport(流程节点时间耗费分析表)</a:Name>
<a:Code>TM_WF_NodeCostingTimeReport</a:Code>
<a:CreationDate>1527647923</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1597200670</a:ModificationDate>
<a:Modifier>RD_huazhu_ni</a:Modifier>
<a:Comment>流程节点时间耗费</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o342">
<a:ObjectID>51BDA789-EE1E-4108-8E37-0D63D96B09FA</a:ObjectID>
<a:Name>模板ID</a:Name>
<a:Code>FTMPID</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o343">
<a:ObjectID>89D28F91-C2B8-461A-96DC-0F9083DE8B1A</a:ObjectID>
<a:Name>组织ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o344">
<a:ObjectID>BE616848-A4D0-400F-B8EB-A20FB994FD34</a:ObjectID>
<a:Name>流程ID</a:Name>
<a:Code>FPROCDEFID</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o345">
<a:ObjectID>352C010F-D52E-4896-94BC-B2BCC55F4826</a:ObjectID>
<a:Name>节点ID</a:Name>
<a:Code>FACTIVITYID</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o346">
<a:ObjectID>0B537663-6877-4F45-B1EA-3BC03AE142E0</a:ObjectID>
<a:Name>组织名称</a:Name>
<a:Code>FORG_NAME</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o347">
<a:ObjectID>D209D2F6-3722-47E3-8FB5-95F11C994588</a:ObjectID>
<a:Name>单据</a:Name>
<a:Code>FOBJECT_TYPE</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o348">
<a:ObjectID>4C36DF25-CDFD-4601-A662-45F06CB05027</a:ObjectID>
<a:Name>单据编号</a:Name>
<a:Code>FBILL_NO</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o349">
<a:ObjectID>B2E251ED-4937-4419-8D7A-5CA794B0FCF6</a:ObjectID>
<a:Name>流程模板名称</a:Name>
<a:Code>FTEMPLATE_NAME</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o350">
<a:ObjectID>E2B0774C-FD3D-40AC-845D-E4373CA15055</a:ObjectID>
<a:Name>流程名称</a:Name>
<a:Code>FPRO_NAME</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o351">
<a:ObjectID>6C017E4D-DED2-4E89-A322-072D217CB2D3</a:ObjectID>
<a:Name>流程实例</a:Name>
<a:Code>FPROC_INST</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o352">
<a:ObjectID>1DD9FB12-1057-4CE5-8DE4-0D0432EEDD58</a:ObjectID>
<a:Name>流程状态</a:Name>
<a:Code>FPRO_STATUS</a:Code>
<a:CreationDate>1527844747</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527844911</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o353">
<a:ObjectID>AA919635-0ED7-4B05-BF4E-7C90FDC699DE</a:ObjectID>
<a:Name>节点名称</a:Name>
<a:Code>FACT_INST</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o354">
<a:ObjectID>2FF183B5-E5AE-46F6-AFF1-8D864EB4A044</a:ObjectID>
<a:Name>节点创建时间</a:Name>
<a:Code>FCREATE_TIME</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o355">
<a:ObjectID>D4D460D9-4D21-4932-9629-5EC49E6DCCEE</a:ObjectID>
<a:Name>节点完成时间</a:Name>
<a:Code>FCOMPLETED_TIME</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o356">
<a:ObjectID>90F6556A-17D7-4D13-BCD0-2CE21B76038B</a:ObjectID>
<a:Name>处理人</a:Name>
<a:Code>FRECEIVER_NAME</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
</o:Column>
<o:Column Id="o357">
<a:ObjectID>AB54A748-A9D7-4D70-B7E0-E394FB77493E</a:ObjectID>
<a:Name>处理时长</a:Name>
<a:Code>FWORKING_HOURS</a:Code>
<a:CreationDate>1527647927</a:CreationDate>
<a:Creator>rd_shijiong_zhan</a:Creator>
<a:ModificationDate>1527648763</a:ModificationDate>
<a:Modifier>rd_shijiong_zhan</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o358">
<a:ObjectID>5F4B53D1-31E8-4201-9C39-6B98F303D995</a:ObjectID>
<a:Name>流程处理时长</a:Name>
<a:Code>FPROCWORKING_HOURS</a:Code>
<a:CreationDate>1597200630</a:CreationDate>
<a:Creator>RD_huazhu_ni</a:Creator>
<a:ModificationDate>1597200670</a:ModificationDate>
<a:Modifier>RD_huazhu_ni</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
</c:Columns>
</o:Table>
</c:Tables>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:DefaultGroups>
<o:Group Id="o359">
<a:ObjectID>39BFC30F-A7F5-4611-829D-47094C24D104</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1384163659</a:CreationDate>
<a:Creator>weixy</a:Creator>
<a:ModificationDate>1384163659</a:ModificationDate>
<a:Modifier>weixy</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o360">
<a:ObjectID>D0792F96-B5BE-4BD5-A7EB-6EC937CD1525</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1384163659</a:CreationDate>
<a:Creator>weixy</a:Creator>
<a:ModificationDate>1384163659</a:ModificationDate>
<a:Modifier>weixy</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/sqlsv2k5.xdb</a:TargetModelURL>
<a:TargetModelID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>