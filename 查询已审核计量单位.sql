-- 查询所有已审核的计量单位
-- =====================================================

SELECT 
    u.FUNITID AS 单位内码,
    u.FNUMBER AS 单位编码,
    ul.FNAME AS 单位名称,
    ul.FDESCRIPTION AS 单位描述,
    u.FUNITGROUPID AS 单位组内码,
    CASE u.FISBASEUNIT 
        WHEN '1' THEN '是' 
        ELSE '否' 
    END AS 是否基准单位,
    u.FPRECISION AS 精度,
    u.FROUNDTYPE AS 舍入类型,
    CASE u.FISSYSTEMSET 
        WHEN '1' THEN '是' 
        ELSE '否' 
    END AS 是否系统预设,
    u.FCREATEDATE AS 创建日期,
    u.FMODIFYDATE AS 修改日期,
    u.FAPPROVEDATE AS 审核日期,
    CASE u.FDOCUMENTSTATUS 
        WHEN 'C' THEN '已审核'
        WHEN 'A' THEN '已提交'
        WHEN 'Z' THEN '暂存'
        ELSE '未知状态'
    END AS 单据状态,
    CASE u.FFORBIDSTATUS 
        WHEN 'A' THEN '正常'
        WHEN 'B' THEN '已禁用'
        ELSE '未知状态'
    END AS 禁用状态
FROM t_BD_Unit u
    LEFT JOIN t_BD_Unit_L ul ON u.FUNITID = ul.FUNITID
WHERE u.FDOCUMENTSTATUS = 'C'          -- 已审核状态
    AND ul.FLOCALEID = 2052            -- 中文语言
    AND u.FFORBIDSTATUS = 'A'          -- 正常状态（未禁用）
ORDER BY u.FNUMBER;

-- =====================================================
-- 🔧 查询说明
-- =====================================================
/*
1. 查询条件说明：
   - FDOCUMENTSTATUS = 'C'：已审核状态
   - FLOCALEID = 2052：中文语言标识
   - FFORBIDSTATUS = 'A'：正常状态（未禁用）

2. 状态码对应关系：
   单据状态 (FDOCUMENTSTATUS)：
   - 'C'：已审核
   - 'A'：已提交
   - 'Z'：暂存
   
   禁用状态 (FFORBIDSTATUS)：
   - 'A'：正常
   - 'B'：已禁用

3. 如需查询包含禁用的单位，可以注释掉：
   -- AND u.FFORBIDSTATUS = 'A'
*/

-- =====================================================
-- 📊 其他常用查询变体
-- =====================================================

-- 查询基准计量单位（去掉注释使用）
/*
SELECT 
    u.FUNITID AS 单位内码,
    u.FNUMBER AS 单位编码,
    ul.FNAME AS 单位名称,
    u.FUNITGROUPID AS 单位组内码
FROM t_BD_Unit u
    LEFT JOIN t_BD_Unit_L ul ON u.FUNITID = ul.FUNITID
WHERE u.FDOCUMENTSTATUS = 'C'
    AND ul.FLOCALEID = 2052
    AND u.FFORBIDSTATUS = 'A'
    AND u.FISBASEUNIT = '1'            -- 基准计量单位
ORDER BY u.FNUMBER;
*/

-- 按单位组分组统计（去掉注释使用）
/*
SELECT 
    u.FUNITGROUPID AS 单位组内码,
    COUNT(*) AS 单位数量,
    SUM(CASE WHEN u.FISBASEUNIT = '1' THEN 1 ELSE 0 END) AS 基准单位数量
FROM t_BD_Unit u
WHERE u.FDOCUMENTSTATUS = 'C'
    AND u.FFORBIDSTATUS = 'A'
GROUP BY u.FUNITGROUPID
ORDER BY u.FUNITGROUPID;
*/ 