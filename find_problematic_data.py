#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找导致数据转换错误的具体行数据
"""

import pyodbc
import pandas as pd
import re

# 数据库连接配置
DB_CONFIG = {
    'server': '192.168.1.250',
    'database': 'HLDB',
    'username': 'hldbuser',
    'password': 'Hldbuser@241031',
    'driver': 'ODBC Driver 17 for SQL Server'
}

def connect_database():
    """连接数据库"""
    try:
        connection_string = (
            f"DRIVER={{{DB_CONFIG['driver']}}};"
            f"SERVER={DB_CONFIG['server']};"
            f"DATABASE={DB_CONFIG['database']};"
            f"UID={DB_CONFIG['username']};"
            f"PWD={DB_CONFIG['password']};"
            "TrustServerCertificate=yes;"
        )
        
        connection = pyodbc.connect(connection_string)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def is_valid_number(value):
    """检查值是否为有效数字"""
    if value is None or value == '':
        return True, "空值"
    
    value_str = str(value).strip()
    if value_str == '':
        return True, "空字符串"
    
    # 检查长度
    if len(value_str) > 15:
        return False, f"长度过长({len(value_str)}位)"
    
    # 检查是否包含多个小数点
    if value_str.count('.') > 1:
        return False, "多个小数点"
    
    # 检查是否包含科学计数法
    if 'e' in value_str.lower():
        return False, "科学计数法"
    
    # 检查是否只包含数字、小数点、负号
    if not re.match(r'^-?[0-9]*\.?[0-9]*$', value_str):
        return False, "包含非数字字符"
    
    # 尝试转换为float
    try:
        float(value_str)
        return True, "正常"
    except ValueError:
        return False, "无法转换为数字"

def find_problematic_data():
    """查找有问题的数据"""
    connection = connect_database()
    if not connection:
        return
    
    print("🔍 开始查找有问题的数据...")
    
    # 查询所有数据
    query = """
    SELECT TOP 1000
        年月, 厂区, 部门, 工序, 工号, 姓名,
        [1], [2], [3], [4], [5], [6], [7], [8], [9], [10],
        [11], [12], [13], [14], [15], [16], [17], [18], [19], [20],
        [21], [22], [23], [24], [25], [26], [27], [28], [29], [30], [31]
    FROM 测试
    """
    
    try:
        df = pd.read_sql(query, connection)
        print(f"✅ 成功读取 {len(df)} 条记录")
        
        # 检查每一行的每个数字列
        day_columns = [f"{i}" for i in range(1, 32)]
        problem_rows = []
        
        for index, row in df.iterrows():
            row_problems = []
            
            for col in day_columns:
                value = row[col]
                is_valid, reason = is_valid_number(value)
                
                if not is_valid:
                    row_problems.append({
                        'column': col,
                        'value': value,
                        'reason': reason
                    })
            
            if row_problems:
                problem_rows.append({
                    'row_index': index,
                    'year_month': row['年月'],
                    'factory': row['厂区'],
                    'department': row['部门'],
                    'process': row['工序'],
                    'employee_id': row['工号'],
                    'name': row['姓名'],
                    'problems': row_problems
                })
        
        # 输出结果
        if problem_rows:
            print(f"\n❌ 发现 {len(problem_rows)} 行有问题的数据:")
            print("=" * 100)
            
            for i, problem_row in enumerate(problem_rows[:20]):  # 只显示前20行
                print(f"\n第 {problem_row['row_index'] + 1} 行问题:")
                print(f"  年月: {problem_row['year_month']}")
                print(f"  厂区: {problem_row['factory']}")
                print(f"  部门: {problem_row['department']}")
                print(f"  工序: {problem_row['process']}")
                print(f"  工号: {problem_row['employee_id']}")
                print(f"  姓名: {problem_row['name']}")
                print(f"  问题列:")
                
                for problem in problem_row['problems']:
                    print(f"    列[{problem['column']}]: '{problem['value']}' - {problem['reason']}")
                
                if i >= 19:  # 只显示前20行
                    remaining = len(problem_rows) - 20
                    if remaining > 0:
                        print(f"\n... 还有 {remaining} 行有问题的数据未显示")
                    break
            
            # 生成清理SQL
            print(f"\n" + "=" * 100)
            print("🔧 建议的数据清理SQL:")
            print("=" * 100)
            
            # 统计问题类型
            problem_types = {}
            for problem_row in problem_rows:
                for problem in problem_row['problems']:
                    reason = problem['reason']
                    if reason not in problem_types:
                        problem_types[reason] = []
                    problem_types[reason].append(problem)
            
            print("\n问题统计:")
            for reason, problems in problem_types.items():
                print(f"  {reason}: {len(problems)} 个")
            
            # 生成UPDATE语句示例
            print(f"\n清理SQL示例 (将有问题的值设为NULL):")
            for problem_row in problem_rows[:5]:  # 只生成前5行的示例
                for problem in problem_row['problems']:
                    print(f"UPDATE 测试 SET [{problem['column']}] = NULL WHERE 工号 = '{problem_row['employee_id']}' AND 年月 = '{problem_row['year_month']}' AND [{problem['column']}] = '{problem['value']}';")
        
        else:
            print("✅ 没有发现有问题的数据，所有数字列都正常")
        
        # 生成修复后的查询建议
        print(f"\n" + "=" * 100)
        print("💡 修复建议:")
        print("=" * 100)
        print("1. 先运行上面的清理SQL，将有问题的数据设为NULL")
        print("2. 或者在查询中使用更严格的数据验证")
        print("3. 建议的安全查询模式:")
        print("""
CASE WHEN 
    ISNULL([列名],'') <> '' 
    AND ISNUMERIC([列名]) = 1 
    AND LEN([列名]) <= 10 
    AND [列名] NOT LIKE '%[^0-9.-]%'
    AND [列名] NOT LIKE '%.%.%'
THEN CAST([列名] AS FLOAT) 
ELSE 0 
END
        """)
        
    except Exception as e:
        print(f"❌ 查询执行失败: {e}")
    finally:
        connection.close()
        print("🔒 数据库连接已关闭")

def generate_safe_query():
    """生成安全的查询SQL"""
    print(f"\n" + "=" * 100)
    print("🔧 生成最安全的查询SQL:")
    print("=" * 100)
    
    safe_conversion = """
CASE WHEN 
    ISNULL(a.[{col}],'') <> '' 
    AND ISNUMERIC(a.[{col}]) = 1 
    AND LEN(a.[{col}]) <= 10 
    AND a.[{col}] NOT LIKE '%[^0-9.-]%'
    AND a.[{col}] NOT LIKE '%.%.%'
    AND a.[{col}] NOT LIKE '%e%'
    AND a.[{col}] NOT LIKE '%E%'
THEN CAST(a.[{col}] AS FLOAT) 
ELSE 0 
END"""
    
    # 生成所有列的安全转换
    safe_conversions = []
    for i in range(1, 32):
        safe_conversions.append(f"({safe_conversion.format(col=i)})")
    
    safe_sum = " +\n        ".join(safe_conversions) + " AS 产能总和"
    
    print("复制以下SQL替换你的产能总和计算部分:")
    print("-" * 50)
    print(safe_sum)

def main():
    """主函数"""
    print("=" * 100)
    print("🔍 数据问题诊断工具")
    print("=" * 100)
    
    # 查找问题数据
    find_problematic_data()
    
    # 生成安全查询
    generate_safe_query()

if __name__ == "__main__":
    main()
