<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1511851397" ExtractionBranch="1" ExtractionDate="1511851397" ExtractionId="1191621" ExtractionVersion="2" ID="{C2817B50-1EF8-4CC7-AFF0-6F0DC4EE3F0D}" Label="" LastModificationDate="1611716301" Name="Mobile移动应用" Objects="451" RepositoryId="{5C34CC33-BB05-4053-9571-E184DEFF2D78}" Symbols="1" Target="Microsoft SQL Server 2008" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>C2817B50-1EF8-4CC7-AFF0-6F0DC4EE3F0D</a:ObjectID>
<a:Name>Mobile移动应用</a:Name>
<a:Code>MOBILE</a:Code>
<a:CreationDate>1511503361</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1512980457</a:ModificationDate>
<a:Modifier>k3cloudbuild</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=Yes
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;\ExtendedSubObject &lt;&lt;AggregateParameter&gt;&gt;]
Create=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;ResourcePool&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;WorkloadGroup&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=0
RefrUpdateConstraint=0
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=

[ModelOptions\Default Opts\FRMEOBJ&lt;&lt;ResourcePool&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMEOBJ&lt;&lt;WorkloadGroup&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMESOB&lt;&lt;ResourcePool&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMESOB&lt;&lt;WorkloadGroup&gt;&gt;]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {5C34CC33-BB05-4053-9571-E184DEFF2D78}
MODEL_ID 1191621
MODEL_VRSN 2
BRANCH_ID 1
EXT_DATE 1511851397
EXT_END 1511851397
OBJECTS 
BEG_BLOCK 
 1191621 {C2817B50-1EF8-4CC7-AFF0-6F0DC4EE3F0D}
 1191622 {FA025B12-CE84-46B0-87DB-D1B8794997A5}
 1191623 {AC69D988-E8E1-4AC5-AE89-CFAEDAC929A7}
 1191624 {4211D4CA-E81E-4039-B7B1-63C3EBDC8790}
 1191625 {0563AC97-9196-46C0-A6AE-CE83E587A227}
 1191626 {D711E69F-C4B4-4339-9E2F-2F68B5A07A40}
 1191627 {C1E425ED-4DBA-4D50-A78D-898D361838BF}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>FA025B12-CE84-46B0-87DB-D1B8794997A5</a:ObjectID>
<a:Name>Microsoft SQL Server 2008</a:Name>
<a:Code>MSSQLSRV2008</a:Code>
<a:CreationDate>1511503361</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1511503361</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F5C20738-B05A-4F70-BC90-9B5EB9437766</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>AC69D988-E8E1-4AC5-AE89-CFAEDAC929A7</a:ObjectID>
<a:Name>Mobile移动应用_ALL</a:Name>
<a:Code>Mobile_ALL</a:Code>
<a:CreationDate>1511503361</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1511503640</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:PackageSymbol Id="o5">
<a:CreationDate>1511503415</a:CreationDate>
<a:ModificationDate>1511503435</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-3038,-1798), (3035,1801))</a:Rect>
<a:LineColor>11711154</a:LineColor>
<a:FillColor>12648447</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Package Ref="o6"/>
</c:Object>
</o:PackageSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o6">
<a:ObjectID>4211D4CA-E81E-4039-B7B1-63C3EBDC8790</a:ObjectID>
<a:Name>Mobile移动应用</a:Name>
<a:Code>Mobile</a:Code>
<a:CreationDate>1511503415</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;\ExtendedSubObject &lt;&lt;AggregateParameter&gt;&gt;]
Create=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;ResourcePool&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;WorkloadGroup&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o7">
<a:ObjectID>C1E425ED-4DBA-4D50-A78D-898D361838BF</a:ObjectID>
<a:Name>Mobile移动应用</a:Name>
<a:Code>Mobile</a:Code>
<a:CreationDate>1511503415</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1511503445</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o7"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o8">
<a:ObjectID>BC8B1011-FF8C-4773-A81F-9AAC53812E8A</a:ObjectID>
<a:Name>T_GWB_CONFIG(星空工作台配置)</a:Name>
<a:Code>T_GWB_CONFIG</a:Code>
<a:CreationDate>1600823308</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>星空工作台配置</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o9">
<a:ObjectID>259FB49D-724A-45E8-AB2E-9B398AB2081C</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o10">
<a:ObjectID>01B9AB07-9DF7-4355-970C-288A7883969F</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o11">
<a:ObjectID>DAE8C0B9-57BA-492A-BF08-3039221B9182</a:ObjectID>
<a:Name>工作台ID</a:Name>
<a:Code>FWORKBENCHID</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o12">
<a:ObjectID>39B53748-2926-4156-8F1A-E6FA1FF927D0</a:ObjectID>
<a:Name>数据状态</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o13">
<a:ObjectID>B71F2B44-07DB-4D32-9781-1ED16FFEE7B2</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varhchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o14">
<a:ObjectID>8FA6A776-4B5A-48AB-89E7-09BED1E85D30</a:ObjectID>
<a:Name>修改人</a:Name>
<a:Code>FMODIFIERID</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o15">
<a:ObjectID>3DF3B18E-A668-408E-AD3F-F4C8792D40F1</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o16">
<a:ObjectID>D19984AC-C26B-4365-BFE1-886A3DEDD0FA</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o17">
<a:ObjectID>7F3FCB83-1123-4376-AB50-B91B596AA272</a:ObjectID>
<a:Name>修改日期</a:Name>
<a:Code>FMODIFYDATE</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o18">
<a:ObjectID>C627808B-D5F3-473A-AAB7-46A88CC34CDB</a:ObjectID>
<a:Name>审核人</a:Name>
<a:Code>FAPPROVERID</a:Code>
<a:CreationDate>1600840218</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o19">
<a:ObjectID>357757B4-A870-4881-A32C-337F54B57C46</a:ObjectID>
<a:Name>审核日期</a:Name>
<a:Code>FAPPROVEDATE</a:Code>
<a:CreationDate>1600840218</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o20">
<a:ObjectID>708CA0A8-5EC8-4823-835A-C00F774442E8</a:ObjectID>
<a:Name>禁用人</a:Name>
<a:Code>FFORBIDERID</a:Code>
<a:CreationDate>1600840218</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o21">
<a:ObjectID>973E80CD-E6F5-4087-B0DE-432939DCD061</a:ObjectID>
<a:Name>禁用日期</a:Name>
<a:Code>FFORBIDDATE</a:Code>
<a:CreationDate>1600840218</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o22">
<a:ObjectID>0DAF4A37-EF76-42A7-9DE3-90ED576BA154</a:ObjectID>
<a:Name>PK_GWB_CONFIG</a:Name>
<a:Code>PK_GWB_CONFIG</a:Code>
<a:CreationDate>1600824219</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_CONFIG</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o9"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o23">
<a:ObjectID>505DD796-3B3B-4E17-8B66-BB417C4D8472</a:ObjectID>
<a:Name>IDX_GWB_CONFIG_WORKBENCHID</a:Name>
<a:Code>IDX_GWB_CONFIG_WORKBENCHID</a:Code>
<a:CreationDate>1600840218</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o24">
<a:ObjectID>C5A14F32-64A4-4A92-94A0-239DF2B078D8</a:ObjectID>
<a:CreationDate>1600841581</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o11"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o22"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o25">
<a:ObjectID>CD87A119-B0C7-4B98-9A99-740E975EBB11</a:ObjectID>
<a:Name>T_GWB_CONFIG_L(工作台配置多语言表)</a:Name>
<a:Code>T_GWB_CONFIG_L</a:Code>
<a:CreationDate>1600827235</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>工作台配置多语言表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o26">
<a:ObjectID>CFFDA819-3C7D-4C8C-987E-0AD5CBB2589A</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1600827413</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o27">
<a:ObjectID>7494F802-53D5-49BC-97F1-14BBDDD63A42</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1600827413</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o28">
<a:ObjectID>7BB0CEC9-F523-49E0-B9D2-48CFCEC0AB2A</a:ObjectID>
<a:Name>语言内码</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>1600827413</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o29">
<a:ObjectID>565510B0-4EF2-45C0-B009-9C30E3D7786F</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1600827413</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o30">
<a:ObjectID>F029F179-AD1D-4393-8E91-06195DEDD8A9</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1600827413</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o31">
<a:ObjectID>88EF6E64-FF68-483E-804B-CB3C498CDC61</a:ObjectID>
<a:Name>PK_GWB_CONFIG_L</a:Name>
<a:Code>PK_GWB_CONFIG_L</a:Code>
<a:CreationDate>1600831398</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_CONFIG_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o26"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o32">
<a:ObjectID>AA99139B-A7AD-4CBC-AC83-D0875F0622C9</a:ObjectID>
<a:Name>IDX_GWB_CONFIG_L_ID</a:Name>
<a:Code>IDX_GWB_CONFIG_L_ID</a:Code>
<a:CreationDate>1600841914</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o33">
<a:ObjectID>39CA5173-E4AB-4DB4-9AED-DEBA9DD0F8E2</a:ObjectID>
<a:CreationDate>1600841982</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o27"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o34">
<a:ObjectID>A7F83732-0CF0-4B44-9DBA-11B55907DDFF</a:ObjectID>
<a:CreationDate>1600842035</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o28"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o31"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o31"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o35">
<a:ObjectID>1435C082-856F-4746-A6B0-926F695EE113</a:ObjectID>
<a:Name>T_GWB_CONFIGAPPLISTENTRY(工作台应用列表分录表)</a:Name>
<a:Code>T_GWB_CONFIGAPPLISTENTRY</a:Code>
<a:CreationDate>1600828029</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>工作台应用列表分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o36">
<a:ObjectID>1EF40928-F738-4C6C-8A53-FE9DA91E892F</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1600828116</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o37">
<a:ObjectID>02A80540-7FDE-4E3C-9809-D038DD4008DE</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1600828116</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o38">
<a:ObjectID>E56197BA-8C52-4CF1-A8F4-1646A37B20D8</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1600828443</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o39">
<a:ObjectID>700881AE-22CE-4E83-B399-C79076498C36</a:ObjectID>
<a:Name>应用名称</a:Name>
<a:Code>FAPPNAME</a:Code>
<a:CreationDate>1600828616</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o40">
<a:ObjectID>429DBDD8-E9CD-4795-B3AB-257E10A5BC9D</a:ObjectID>
<a:Name>应用ID</a:Name>
<a:Code>FAPPID</a:Code>
<a:CreationDate>1600828616</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o41">
<a:ObjectID>392A56E2-D1B6-4BC0-B937-27266B123EFA</a:ObjectID>
<a:Name>应用类型</a:Name>
<a:Code>FAPPTYPE</a:Code>
<a:CreationDate>1600828616</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o42">
<a:ObjectID>8D1BAA0E-3CB9-4AC0-BE02-A97509A75B03</a:ObjectID>
<a:Name>应用FormID</a:Name>
<a:Code>FAPPFORMID</a:Code>
<a:CreationDate>1600828616</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o43">
<a:ObjectID>7F70E563-F54C-4B04-AE94-A93E501771E3</a:ObjectID>
<a:Name>应用FormType</a:Name>
<a:Code>FAPPFORMTYPE</a:Code>
<a:CreationDate>1600828616</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o44">
<a:ObjectID>BCFEA947-3A90-4846-98C2-002AC181ACF8</a:ObjectID>
<a:Name>额外参数</a:Name>
<a:Code>FOPTION</a:Code>
<a:CreationDate>1603368021</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(500)</a:DataType>
<a:Length>500</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o45">
<a:ObjectID>9BE747DA-9D07-493E-8114-33EB7E0F0A6A</a:ObjectID>
<a:Name>PK_GWB_CONFIGAPPLISTENTRY</a:Name>
<a:Code>PK_GWB_CONFIGAPPLISTENTRY</a:Code>
<a:CreationDate>1600828443</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_CONFIGAPPLISTENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o37"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o46">
<a:ObjectID>C86359BF-5AFC-4DF0-B037-F489D6AFED80</a:ObjectID>
<a:Name>IDX_GWB_CONFIGAPPENTRY_ID</a:Name>
<a:Code>IDX_GWB_CONFIGAPPENTRY_ID</a:Code>
<a:CreationDate>1600842117</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o47">
<a:ObjectID>88EFC20B-56BF-4D14-B734-B95F7E5ADB21</a:ObjectID>
<a:CreationDate>1600842426</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o36"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o48">
<a:ObjectID>EC3DF09A-63EA-4DEF-9282-46D77DE59783</a:ObjectID>
<a:Name>IDX_GWB_CONFIGAPPENTRY_SEQ</a:Name>
<a:Code>IDX_GWB_CONFIGAPPENTRY_SEQ</a:Code>
<a:CreationDate>1600842426</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o49">
<a:ObjectID>B07C2D14-BFAC-4812-9D47-370ED224B748</a:ObjectID>
<a:CreationDate>1600842465</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o38"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o45"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o45"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o50">
<a:ObjectID>8DBCF270-76AE-434A-B3F2-94BC9AA0A9B1</a:ObjectID>
<a:Name>T_GWB_CONFIGAPPLISTENTRY_L(工作台应用列表分录多语言表)</a:Name>
<a:Code>T_GWB_CONFIGAPPLISTENTRY_L</a:Code>
<a:CreationDate>1600846654</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>工作台应用列表分录多语言表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o51">
<a:ObjectID>21B488ED-6716-49C0-8CBC-DB6942C997C0</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1600846654</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o52">
<a:ObjectID>CB6130E7-DFB3-4E4B-BAA2-408F308750D3</a:ObjectID>
<a:Name>分录内码</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1600846855</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o53">
<a:ObjectID>580CAF96-5A3C-4917-BCD2-B79FC340BB7C</a:ObjectID>
<a:Name>多语言内码</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>1600846879</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o54">
<a:ObjectID>2191F7A8-BEE8-4783-A6AE-AE7D7F3428C0</a:ObjectID>
<a:Name>别名</a:Name>
<a:Code>FNICKNAME</a:Code>
<a:CreationDate>1600846879</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o55">
<a:ObjectID>22090812-A775-4A9B-BF4D-D9AD933730BA</a:ObjectID>
<a:Name>PK_GWB_CONFIGAPPLISTENTRY_L</a:Name>
<a:Code>PK_GWB_CONFIGAPPLISTENTRY_L</a:Code>
<a:CreationDate>1600846855</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_CONFIGAPPLISTENTRY_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o51"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o56">
<a:ObjectID>E542EF1C-81B6-44C3-A0AA-384901237E0B</a:ObjectID>
<a:Name>IDX_GWB_APPENTRY_L_ENTRYID</a:Name>
<a:Code>IDX_GWB_APPENTRY_L_ENTRYID</a:Code>
<a:CreationDate>1600847177</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o57">
<a:ObjectID>521615BF-1061-493F-BA8A-D446F44A1BB5</a:ObjectID>
<a:CreationDate>1600847326</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o53"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o58">
<a:ObjectID>F058AEC7-26C4-41E7-B450-47232466C319</a:ObjectID>
<a:CreationDate>1600847514</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o52"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o55"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o55"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o59">
<a:ObjectID>AFDA6658-1F2E-4DBC-AF67-8E494E779387</a:ObjectID>
<a:Name>T_GWB_LFBILLOPENTRY（轻表单单据操作分录表）</a:Name>
<a:Code>T_GWB_LFBILLOPENTRY</a:Code>
<a:CreationDate>1603446891</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单单据操作分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o60">
<a:ObjectID>EA6BEBF1-C169-4716-9D7D-87AAA8357DD2</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1603446989</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>A14D4A49-0DB3-492C-AAF5-D760B5CD34C6</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1603446989</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>A9E09602-0AC1-4A9C-B596-59FAA2A1F688</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1603446989</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>8E4D889A-2618-4AB2-92FE-B939635E09ED</a:ObjectID>
<a:Name>操作类型</a:Name>
<a:Code>FOPERATIONTYPE</a:Code>
<a:CreationDate>1603446989</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>7872CC9A-1407-4F2D-9800-C554742F2BC5</a:ObjectID>
<a:Name>操作代码</a:Name>
<a:Code>FOPERATIONCODE</a:Code>
<a:CreationDate>1603446989</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>A94DD8A4-68BD-4D74-83FB-1F3D390F1116</a:ObjectID>
<a:Name>操作ID</a:Name>
<a:Code>FOPERATIONID</a:Code>
<a:CreationDate>1603446989</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>176E0261-4837-4BAC-B7AB-D2B9BFF10932</a:ObjectID>
<a:Name>操作参数</a:Name>
<a:Code>FOPERATIONOPTION</a:Code>
<a:CreationDate>1605514586</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>text</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o67">
<a:ObjectID>1C462272-0412-439B-9A0B-1CA9AE11BB4A</a:ObjectID>
<a:Name>PK_GWB_LFBILLOPENTRY</a:Name>
<a:Code>PK_GWB_LFBILLOPENTRY</a:Code>
<a:CreationDate>1603446989</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LFBILLOPENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o61"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o68">
<a:ObjectID>94A6A1CD-7914-4613-AF9A-C7D8D75DAEFD</a:ObjectID>
<a:Name>IDX_GWB_LFBILLOPENTRY_ID</a:Name>
<a:Code>IDX_GWB_LFBILLOPENTRY_ID</a:Code>
<a:CreationDate>1603447223</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o69">
<a:ObjectID>029B6619-7A44-4585-9118-CC019D43378D</a:ObjectID>
<a:CreationDate>1603447374</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o60"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o70">
<a:ObjectID>E27FF3D0-ED4E-4475-B39C-CEF28F92534B</a:ObjectID>
<a:Name>IDX_GWB_LFBILLOPENTRY_SEQ</a:Name>
<a:Code>IDX_GWB_LFBILLOPENTRY_SEQ</a:Code>
<a:CreationDate>1603447374</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o71">
<a:ObjectID>C09B6964-6C02-494A-B090-A8DF7776F40C</a:ObjectID>
<a:CreationDate>1603447426</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o62"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o67"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o67"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o72">
<a:ObjectID>D822C79E-7B2D-4049-B4FF-5655D8101AFD</a:ObjectID>
<a:Name>T_GWB_LFBILLOPENTRY_L（轻表单单据操作分录多语言表）</a:Name>
<a:Code>T_GWB_LFBILLOPENTRY_L</a:Code>
<a:CreationDate>1603447449</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单单据操作分录多语言表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o73">
<a:ObjectID>F84B74AC-4452-4768-8744-11E5ADD657D1</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1603447449</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>0776DCD4-6701-44D1-9322-87FAA0AC1A78</a:ObjectID>
<a:Name>分录内码</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1603447449</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>8C0BC157-EF80-469F-84BA-C436A98BE2AC</a:ObjectID>
<a:Name>多语言标识</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>1603447449</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>CE4140BE-3389-4354-AA67-506B2A6F49D3</a:ObjectID>
<a:Name>操作名称</a:Name>
<a:Code>FOPERATIONNAME</a:Code>
<a:CreationDate>1603447449</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o77">
<a:ObjectID>0047A74E-0234-4769-BE0A-C42CB2316CCF</a:ObjectID>
<a:Name>PK_GWB_LFBILLOPENTRY_L</a:Name>
<a:Code>PK_GWB_LFBILLOPENTRY_L</a:Code>
<a:CreationDate>1603447449</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LFBILLOPENTRY_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o73"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o78">
<a:ObjectID>9DAB9410-0694-4994-9ECB-151AC4475979</a:ObjectID>
<a:Name>IDX_GWB_LFOPENTRY_L_ENTRYID</a:Name>
<a:Code>IDX_GWB_LFOPENTRY_L_ENTRYID</a:Code>
<a:CreationDate>1603447725</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o79">
<a:ObjectID>6CAB5DD7-9EAC-4F4C-82B7-E6E93477327B</a:ObjectID>
<a:CreationDate>1603448116</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o75"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o80">
<a:ObjectID>5FF72C0C-7EA0-41B1-A832-FBB0330DB055</a:ObjectID>
<a:CreationDate>1603677587</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o74"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o77"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o77"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o81">
<a:ObjectID>4862795B-BBDA-48B0-BDDD-7C80FFC86C38</a:ObjectID>
<a:Name>T_GWB_LFDETAILOPENTRY（轻表单详情操作分录表）</a:Name>
<a:Code>T_GWB_LFDETAILOPENTRY</a:Code>
<a:CreationDate>1603448595</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单详情操作分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o82">
<a:ObjectID>895D8A09-5CA5-425D-BAD9-BDF7125EF5F1</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1603448746</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>41CCB731-0A0A-47A7-B741-EFDF83B50176</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1603448746</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>EC23FC84-05D7-435A-9357-57B636638FEE</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1603448746</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>624C57F2-16F7-4936-BBFA-9269958C6090</a:ObjectID>
<a:Name>操作类型</a:Name>
<a:Code>FDETAILOPERATIONTYPE</a:Code>
<a:CreationDate>1603448746</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>713BF83D-9BC8-42D9-99B9-E23F8605E8B7</a:ObjectID>
<a:Name>操作代码</a:Name>
<a:Code>FDETAILOPERATIONCODE</a:Code>
<a:CreationDate>1603448746</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>D4E0AA51-AC1F-42DE-94F7-DA239859119B</a:ObjectID>
<a:Name>操作ID</a:Name>
<a:Code>FDETAILOPERATIONID</a:Code>
<a:CreationDate>1603448746</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>47F7C7EA-47BC-4775-8E4C-07BEFFB868D9</a:ObjectID>
<a:Name>操作参数</a:Name>
<a:Code>FDETAILOPERATIONOPTION</a:Code>
<a:CreationDate>1605513376</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>text</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o89">
<a:ObjectID>3F38A022-D9AC-49A5-8B5F-905F22B25D6A</a:ObjectID>
<a:Name>PK_GWB_LFDETAILOPENTRY</a:Name>
<a:Code>PK_GWB_LFDETAILOPENTRY</a:Code>
<a:CreationDate>1603675497</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LFDETAILOPENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o83"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o90">
<a:ObjectID>672764CF-F53E-42D6-B8D9-A34C08A53719</a:ObjectID>
<a:Name>IDX_GWB_LFDETAILOPENTRY_ID</a:Name>
<a:Code>IDX_GWB_LFDETAILOPENTRY_ID</a:Code>
<a:CreationDate>1603675622</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o91">
<a:ObjectID>0A13A758-4832-45EF-86B3-5FBD86307065</a:ObjectID>
<a:CreationDate>1603675729</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o82"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o92">
<a:ObjectID>D23638F0-09FB-489F-AA65-3005F38374EF</a:ObjectID>
<a:Name>IDX_GWB_LFDETAILOPENTRY_SEQ</a:Name>
<a:Code>IDX_GWB_LFDETAILOPENTRY_SEQ</a:Code>
<a:CreationDate>1603675729</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o93">
<a:ObjectID>D1F3E1EC-9BE7-46C8-93DB-F5B631722CEF</a:ObjectID>
<a:CreationDate>1603675800</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o84"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o89"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o89"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o94">
<a:ObjectID>6D91676C-CF45-4CC7-B0E7-412191E627CE</a:ObjectID>
<a:Name>T_GWB_LFDETAILOPENTRY_L（轻表单单据详情操作分录多语言表）</a:Name>
<a:Code>T_GWB_LFDETAILOPENTRY_L</a:Code>
<a:CreationDate>1603675905</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单单据详情操作分录多语言表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o95">
<a:ObjectID>1F50F2FD-6541-467E-B54F-256867C2AF74</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1603676097</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>84236209-A74E-41D8-99CE-25A971725732</a:ObjectID>
<a:Name>分录内码</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1603676097</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>DB465D5C-8D82-4A58-A579-28BF54CE1F2E</a:ObjectID>
<a:Name>多语言标识</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>1603676097</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>FAE15E4F-A021-4843-85D8-94510A203BB3</a:ObjectID>
<a:Name>操作名称</a:Name>
<a:Code>FDETAILOPERATIONNAME</a:Code>
<a:CreationDate>1603676097</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o99">
<a:ObjectID>BDCC7FAF-16F4-4F79-BAAA-0502EBBBB655</a:ObjectID>
<a:Name>PK_GWB_LFDETAILOPENTRY_L</a:Name>
<a:Code>PK_GWB_LFDETAILOPENTRY_L</a:Code>
<a:CreationDate>1603676097</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LFDETAILOPENTRY_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o95"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o100">
<a:ObjectID>4672E6B6-CE2B-4213-94BD-BAE1B977D0F7</a:ObjectID>
<a:Name>IDX_GWB_LFDETAILOP_ENTRYID</a:Name>
<a:Code>IDX_GWB_LFDETAILOP_ENTRYID</a:Code>
<a:CreationDate>1603677400</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o101">
<a:ObjectID>8E097FAE-53DB-4F3D-B3BB-6D48BA3FAEA8</a:ObjectID>
<a:CreationDate>1603677498</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o96"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o102">
<a:ObjectID>8CF495B3-8358-41CB-B9AB-39066CC9E8C0</a:ObjectID>
<a:CreationDate>1603677498</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o97"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o99"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o99"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o103">
<a:ObjectID>B4DFE85A-C87D-4CA6-8FAB-DB663BE1D5ED</a:ObjectID>
<a:Name>T_GWB_LFENTRYFIELDENTRY（轻表单单据体字段分录表）</a:Name>
<a:Code>T_GWB_LFENTRYFIELDENTRY</a:Code>
<a:CreationDate>1603440925</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单单据体字段分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o104">
<a:ObjectID>9C5E42D2-12B3-4551-B258-D5116DF0252F</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>0B3066C8-19F6-46A8-95B4-7DD986127F99</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>72075264-8FB8-4ED0-9210-35A66B962BB3</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>A5976AD8-01C2-410B-AA67-7CB86DF13B26</a:ObjectID>
<a:Name>字段名称</a:Name>
<a:Code>FENTRYFIELDNAME</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>E521875C-9895-4C3C-8A49-D08D7FB21DB8</a:ObjectID>
<a:Name>字段类型</a:Name>
<a:Code>FENTRYFIELDTYPE</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>52C8F53A-5404-41C3-A510-8D0F08CB86C4</a:ObjectID>
<a:Name>高亮显示</a:Name>
<a:Code>FENTRYFIELDHIGHLIGHT</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>EC5CDFDB-BD80-4CC2-93A9-D7D9E3881DB6</a:ObjectID>
<a:Name>明细预览显示</a:Name>
<a:Code>FDETAILPREVIEWSHOW</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>E801146C-CF54-410A-9266-2D647227460D</a:ObjectID>
<a:Name>明细预览关键字段</a:Name>
<a:Code>FDETAILPREVIEWKEYWORD</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>8A3056C5-A11F-4071-9807-2B86FC830BF5</a:ObjectID>
<a:Name>字段Key</a:Name>
<a:Code>FENTRYFIELDKEY</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o113">
<a:ObjectID>0D0FCFFA-440A-4333-9288-3D05B5E9D94D</a:ObjectID>
<a:Name>PK_GWB_LFENTRYFIELDENTRY</a:Name>
<a:Code>PK_GWB_LFENTRYFIELDENTRY</a:Code>
<a:CreationDate>1603442155</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LFENTRYFIELDENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o105"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o114">
<a:ObjectID>EB317A44-0CE0-499B-AFA2-859D8F1BE610</a:ObjectID>
<a:Name>IDX_GWB_LFENTRYFIELDENTRY_ID</a:Name>
<a:Code>IDX_GWB_LFENTRYFIELDENTRY_ID</a:Code>
<a:CreationDate>1603444971</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o115">
<a:ObjectID>3280D840-36B4-42EB-A3AF-86E3B6B12632</a:ObjectID>
<a:CreationDate>1603445068</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o104"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o116">
<a:ObjectID>D3FE94F2-2D2D-40FB-A267-B4688844A16A</a:ObjectID>
<a:Name>IDX_GWB_LFENTRYFIELDENTRY_SEQ</a:Name>
<a:Code>IDX_GWB_LFENTRYFIELDENTRY_SEQ</a:Code>
<a:CreationDate>1603445068</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o117">
<a:ObjectID>1AF5A5F1-FF87-45D4-8A66-707CBD5C9383</a:ObjectID>
<a:CreationDate>1603445174</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o106"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o113"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o113"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o118">
<a:ObjectID>CD3E9A48-4BFD-45CC-8959-23FDDF94E74F</a:ObjectID>
<a:Name>T_GWB_LFHEADFIELDENTRY（轻表单单据头字段分录表）</a:Name>
<a:Code>T_GWB_LFHEADFIELDENTRY</a:Code>
<a:CreationDate>1603438072</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单单据头字段分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o119">
<a:ObjectID>22093793-CFEE-470E-BDFE-9086464D9560</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>8F09EA69-3515-498F-BE4C-C16AEC0D636D</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>3BC9EE4C-F87B-4DE8-9CF7-3D948D2AD4C7</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>B8041C4E-0558-4F5D-8726-9494333B3E4D</a:ObjectID>
<a:Name>字段名称</a:Name>
<a:Code>FHEADFIELDNAME</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>305059D3-662D-4F6E-9DF3-84B7E639F699</a:ObjectID>
<a:Name>字段类型</a:Name>
<a:Code>FHEADFIELDTYPE</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>C0B0D458-CEBB-4583-8879-901BA8E443E6</a:ObjectID>
<a:Name>高亮显示</a:Name>
<a:Code>FHEADFIELDHIGHLIGHT</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>F455F582-715B-40A7-A3F4-E6D583B90140</a:ObjectID>
<a:Name>固定显示</a:Name>
<a:Code>FHEADFIELDFIXEDVISIBLE</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>F734D639-FD9C-4065-9AC4-C261FBE4D909</a:ObjectID>
<a:Name>字段Key</a:Name>
<a:Code>FHEADFIELDKEY</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o127">
<a:ObjectID>752616BB-8FC1-42B6-9DDE-4399EA996168</a:ObjectID>
<a:Name>PK_GWB_LFHEADFIELDENTRY</a:Name>
<a:Code>PK_GWB_LFHEADFIELDENTRY</a:Code>
<a:CreationDate>1603440005</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LFHEADFIELDENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o120"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o128">
<a:ObjectID>BDACCA36-A3EE-476A-AF75-E695F86D1F00</a:ObjectID>
<a:Name>IDX_GWB_LFHEADFIELDENTRY_ID</a:Name>
<a:Code>IDX_GWB_LFHEADFIELDENTRY_ID</a:Code>
<a:CreationDate>1603440707</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o129">
<a:ObjectID>F3DBDE97-B7ED-4956-B0D9-7F794952C93C</a:ObjectID>
<a:CreationDate>1603440745</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o119"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o130">
<a:ObjectID>D242080E-A9EB-45F9-A422-19DCB3891E5C</a:ObjectID>
<a:Name>IDX_GWB_LFHEADFIELDENTRY_SEQ</a:Name>
<a:Code>IDX_GWB_LFHEADFIELDENTRY_SEQ</a:Code>
<a:CreationDate>1603440877</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o131">
<a:ObjectID>76183082-1954-4CE0-8238-01CB16850A89</a:ObjectID>
<a:CreationDate>1603440897</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o121"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o127"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o127"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o132">
<a:ObjectID>10A38560-7CE7-4BA4-9E4F-4873863DA123</a:ObjectID>
<a:Name>T_GWB_LFLISTFIELDENTRY（轻表单列表显示字段分录表）</a:Name>
<a:Code>T_GWB_LFLISTFIELDENTRY</a:Code>
<a:CreationDate>1603445358</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单列表显示字段分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o133">
<a:ObjectID>4FD3026B-9E8A-4274-ABCA-C2B51E1A74D0</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>F3F830F8-C66B-416C-BD1E-539BD84BFDDA</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>45178723-CAD0-496F-B299-7CD64DA41592</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>786F2E8A-3144-405E-91AF-C2DBB02BE742</a:ObjectID>
<a:Name>字段名称</a:Name>
<a:Code>FLISTFIELDNAME</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o137">
<a:ObjectID>8809EFFA-90FA-4EDE-B74C-7F392F2D02E4</a:ObjectID>
<a:Name>字段类型</a:Name>
<a:Code>FLISTFIELDTYPE</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>259C7076-D2A4-4144-9C80-A047F6E5E80F</a:ObjectID>
<a:Name>高亮显示</a:Name>
<a:Code>FLISTFIELDHIGHLIGHT</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o139">
<a:ObjectID>61FCAEA2-5F8D-4C92-99F1-B603F6D46DDE</a:ObjectID>
<a:Name>列表关键字段</a:Name>
<a:Code>FISLISTKEYWORD</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o140">
<a:ObjectID>AE9C2F7F-13A1-4B48-A2FD-3F5EF2944B89</a:ObjectID>
<a:Name>字段Key</a:Name>
<a:Code>FLISTFIELDKEY</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o141">
<a:ObjectID>EE17CE92-9FE7-407C-813D-D4F2FB33A5FB</a:ObjectID>
<a:Name>PK_GWB_LFLISTFIELDENTRY</a:Name>
<a:Code>PK_GWB_LFLISTFIELDENTRY</a:Code>
<a:CreationDate>1603445410</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LFLISTFIELDENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o134"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o142">
<a:ObjectID>9C8D7ECF-D789-4E91-9F46-F31EE053A790</a:ObjectID>
<a:Name>IDX_GWB_LFLISTFIELDENTRY_ID</a:Name>
<a:Code>IDX_GWB_LFLISTFIELDENTRY_ID</a:Code>
<a:CreationDate>1603445864</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o143">
<a:ObjectID>91D0CF0E-3BEF-4540-BAE6-71F7D9E6FC15</a:ObjectID>
<a:CreationDate>1603446064</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o133"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o144">
<a:ObjectID>118783F7-BB38-4AA4-89AE-DC5FEB9DADD7</a:ObjectID>
<a:Name>IDX_GWB_LFLISTFIELDENTRY_SEQ</a:Name>
<a:Code>IDX_GWB_LFLISTFIELDENTRY_SEQ</a:Code>
<a:CreationDate>1603445864</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o145">
<a:ObjectID>E677406F-1D83-4F0A-B9B8-21B249453A30</a:ObjectID>
<a:CreationDate>1603446055</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o135"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o141"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o141"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o146">
<a:ObjectID>77BB2ECC-4850-4C15-A17F-4C0E81CF4F51</a:ObjectID>
<a:Name>T_GWB_LIGHTFORM（轻表单）</a:Name>
<a:Code>T_GWB_LIGHTFORM</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o147">
<a:ObjectID>681E7B04-A04D-4681-A72E-E20B1727D7E3</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>E0454616-C8C1-4E39-BE9A-67F1747D6ACA</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o149">
<a:ObjectID>712C2A7F-22B2-4DAC-A114-AD4DD6B1ADF9</a:ObjectID>
<a:Name>数据状态</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o150">
<a:ObjectID>B0EDD13F-FCD8-485C-A275-6681FB9432BA</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;A&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>2BE744B5-E1C4-4C2F-BB7B-60B09AE9C048</a:ObjectID>
<a:Name>修改人</a:Name>
<a:Code>FMODIFIERID</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o152">
<a:ObjectID>6E3C4B30-CEF1-4002-A4CF-168DB94F93DD</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>073CEF9A-74E3-464B-88B7-FF831ECC500C</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o154">
<a:ObjectID>B0EEA57A-D9F6-4CD8-A08A-D13AE2AFCC88</a:ObjectID>
<a:Name>修改日期</a:Name>
<a:Code>FMODIFYDATE</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>7D0911E4-7340-4752-99CB-7C4CADE247C5</a:ObjectID>
<a:Name>源单据</a:Name>
<a:Code>FSOURCEBILL</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>3B00D72B-4979-455E-BD33-F662417CB611</a:ObjectID>
<a:Name>主单据体</a:Name>
<a:Code>FMAINENTRY</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o157">
<a:ObjectID>E2CC6349-AFA7-42B6-A796-389CE355AB7C</a:ObjectID>
<a:Name>发布列表菜单</a:Name>
<a:Code>FPUBLISHLIST</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>9F058EF9-F744-4725-A193-530D928E27A0</a:ObjectID>
<a:Name>发布新增菜单</a:Name>
<a:Code>FPUBLISHNEW</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>BDBCAF2D-5062-48A4-953F-5F8F20360519</a:ObjectID>
<a:Name>启用附件上传</a:Name>
<a:Code>FENABLEATTACHMENT</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>47270BF3-C3E6-44C9-BC51-0CFA76376366</a:ObjectID>
<a:Name>审核人</a:Name>
<a:Code>FAPPROVERID</a:Code>
<a:CreationDate>1603803074</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>8A3A1847-51D7-4CF0-9E4D-F4C48300DE2D</a:ObjectID>
<a:Name>禁用人</a:Name>
<a:Code>FFORBIDERID</a:Code>
<a:CreationDate>1603803074</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>3AF9538E-705F-4463-B1AB-E3421B2C6C42</a:ObjectID>
<a:Name>审核日期</a:Name>
<a:Code>FAPPROVEDATE</a:Code>
<a:CreationDate>1603803074</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>B54EE901-4561-4D33-8E02-D380D73ABC55</a:ObjectID>
<a:Name>禁用日期</a:Name>
<a:Code>FFORBIDDATE</a:Code>
<a:CreationDate>1603803074</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o164">
<a:ObjectID>DAED88B8-46BD-4550-8733-2D3E377CEAFB</a:ObjectID>
<a:Name>PK_GWB_LIGHTFORM</a:Name>
<a:Code>PK_GWB_LIGHTFORM</a:Code>
<a:CreationDate>1603431315</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LIGHTFORM</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o147"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o165">
<a:ObjectID>7EA37FE7-A18F-44AC-A8E1-D9CF4B67FDCA</a:ObjectID>
<a:Name>IDX_GWB_LIGHTFORM_SOURCEBILL</a:Name>
<a:Code>IDX_GWB_LIGHTFORM_SOURCEBILL</a:Code>
<a:CreationDate>1603434876</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o166">
<a:ObjectID>B2E9C444-F50B-4A69-8011-EBFFF1C8FAEF</a:ObjectID>
<a:CreationDate>1603435688</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o155"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o164"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o164"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o167">
<a:ObjectID>0013868D-C934-424D-8305-B17F18681C27</a:ObjectID>
<a:Name>T_GWB_LIGHTFORM_L（轻表单多语言表）</a:Name>
<a:Code>T_GWB_LIGHTFORM_L</a:Code>
<a:CreationDate>1603436720</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:Comment>轻表单多语言表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o168">
<a:ObjectID>24A073B0-4B2E-456C-8BE5-7D632D2C7EBC</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1603436720</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>EF09934B-DBFC-40AF-8143-E64BF574A5D7</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1603436720</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>A4753803-5858-4E9E-A5EB-F811C9EC30B4</a:ObjectID>
<a:Name>多语言标识</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>1603436720</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o171">
<a:ObjectID>179E4B19-359A-4AF3-BD43-26D8C144F071</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1603436720</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o172">
<a:ObjectID>35F3663E-B414-4E85-9D24-80EA01637105</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1603436720</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o173">
<a:ObjectID>FE46D452-CD49-4C11-8F15-CC759F078AE9</a:ObjectID>
<a:Name>PK_GWB_LIGHTFORM_L</a:Name>
<a:Code>PK_GWB_LIGHTFORM_L</a:Code>
<a:CreationDate>1603436720</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<a:ConstraintName>PK_GWB_LIGHTFORM_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o168"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o174">
<a:ObjectID>7783B68D-05EF-49FD-8D85-4B49490FB6F1</a:ObjectID>
<a:Name>IDX_GWB_LIGHTFORM_L_ID</a:Name>
<a:Code>IDX_GWB_LIGHTFORM_L_ID</a:Code>
<a:CreationDate>1603437962</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o175">
<a:ObjectID>481671EA-7A2E-4915-8B72-054B445595DE</a:ObjectID>
<a:CreationDate>1603438000</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o169"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o176">
<a:ObjectID>D1D1116B-B37F-4F18-A423-5A6CA4CF1D04</a:ObjectID>
<a:CreationDate>1603447809</a:CreationDate>
<a:Creator>rd_jian_tj</a:Creator>
<a:ModificationDate>1611716301</a:ModificationDate>
<a:Modifier>rd_jian_tj</a:Modifier>
<c:Column>
<o:Column Ref="o170"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o173"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o173"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o177">
<a:ObjectID>DEFF04E5-4369-41C1-9940-11BA0D20C2FE</a:ObjectID>
<a:Name>T_Mob_BusinessLog(业务日志表)</a:Name>
<a:Code>T_Mob_BusinessLog</a:Code>
<a:CreationDate>1512700228</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1512701018</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:Comment>临时单据保存、生成销售订单等各个操作需要记录日志，特别是未生成销售订单时，需要记录未生成成功的原因。这个表就是记录这些业务日志</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o178">
<a:ObjectID>9FF2622A-8458-4F63-945C-8519B737B4A5</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1512700228</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1512700283</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>newid()</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>9D41CCDA-8F1D-4755-A625-5DA3CB7BA8AF</a:ObjectID>
<a:Name>FTitle</a:Name>
<a:Code>FTitle</a:Code>
<a:CreationDate>1512700286</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1515723282</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>C583E396-A5A8-4362-B14E-79FFAC45AD06</a:ObjectID>
<a:Name>FType</a:Name>
<a:Code>FType</a:Code>
<a:CreationDate>1512700286</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1512970152</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>101B97E5-45C2-4E33-972A-A4A004349C34</a:ObjectID>
<a:Name>FStatus</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1512700286</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1512970152</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>07FB42F0-65CB-4FD5-843C-076C6C7F9A23</a:ObjectID>
<a:Name>FDateTime</a:Name>
<a:Code>FDateTime</a:Code>
<a:CreationDate>1512700286</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1512700727</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>getdate()</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>9ED6E9D9-F2B0-4FA9-B634-CFD55F12D9F1</a:ObjectID>
<a:Name>FContent</a:Name>
<a:Code>FContent</a:Code>
<a:CreationDate>1512700286</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1515723282</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>6E328301-0798-4779-8DF3-C13D4F674C94</a:ObjectID>
<a:Name>FCallBackService</a:Name>
<a:Code>FCallBackService</a:Code>
<a:CreationDate>1512700286</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1515723282</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>29B4B8FE-E1BA-4BE3-854C-DCAA7875E566</a:ObjectID>
<a:Name>FCallBackData</a:Name>
<a:Code>FCallBackData</a:Code>
<a:CreationDate>1512700286</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1515723282</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>ntext</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>05435F3B-3478-4CFB-ABA0-944F85943F31</a:ObjectID>
<a:Name>FSource</a:Name>
<a:Code>FSource</a:Code>
<a:CreationDate>1512700286</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1515723282</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o187">
<a:ObjectID>50FA9B27-0F33-4DAE-A9CD-A86C90634255</a:ObjectID>
<a:Name>PK_Mob_BusinessLog</a:Name>
<a:Code>PK_Mob_BusinessLog</a:Code>
<a:CreationDate>1512700228</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1512713973</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:ConstraintName>PK_MOB_BUSINESSLOG</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o178"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o187"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o187"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o188">
<a:ObjectID>13DA1AE0-3078-4685-9F06-2744D6238EA7</a:ObjectID>
<a:Name>T_Mob_UserConfig(用户配置表)</a:Name>
<a:Code>T_Mob_UserConfig</a:Code>
<a:CreationDate>1513732547</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1513734294</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:Comment>用于存储移动轻应用用户相关的个性化配置</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o189">
<a:ObjectID>2A63A4D8-71FB-4C41-96E3-A98626C5C06F</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1513732547</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1513732651</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>newid()</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o190">
<a:ObjectID>38718A3C-F060-40BC-9A78-A2F5F1EB8B11</a:ObjectID>
<a:Name>FUSERID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1513732616</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1515723163</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o191">
<a:ObjectID>A4CB6F56-D316-4387-9C29-E9058D831393</a:ObjectID>
<a:Name>FKEY</a:Name>
<a:Code>FKEY</a:Code>
<a:CreationDate>1513734242</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1515723163</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o192">
<a:ObjectID>F34A6272-2146-4731-8BC0-A50B30BBE07F</a:ObjectID>
<a:Name>FVALUE</a:Name>
<a:Code>FVALUE</a:Code>
<a:CreationDate>1513734242</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1515723163</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>ntext</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o193">
<a:ObjectID>67E861D6-8A8C-4BEC-A7A0-0F5DA1E89606</a:ObjectID>
<a:Name>PK_Mob_UserConfig</a:Name>
<a:Code>PK_Mob_UserConfig</a:Code>
<a:CreationDate>1513732616</a:CreationDate>
<a:Creator>rd_qiupo_chen</a:Creator>
<a:ModificationDate>1513734327</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:ConstraintName>PK_MOB_USERCONFIG</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o189"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o193"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o193"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o194">
<a:ObjectID>59A7BE61-875F-4FF1-9C92-FE5C4BE8FB81</a:ObjectID>
<a:Name>T_LIVE_THEMEUNSCUSERENTRY(主题取消订阅用户表)</a:Name>
<a:Code>T_LIVE_THEMEUNSCUSERENTRY</a:Code>
<a:CreationDate>1585205438</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o195">
<a:ObjectID>76B29CB3-178A-43FD-8BE6-6D9B4B57984B</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1585205438</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>56E75BE2-07A5-44CD-9F66-AA4D5AAC1305</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1585205438</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>1C4DD783-8CDF-4A13-9F9F-F95656CD7EC8</a:ObjectID>
<a:Name>用户Id</a:Name>
<a:Code>FUNSUBSCRIBEUSERID</a:Code>
<a:CreationDate>1585205438</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>25F5C819-9A95-405B-8C9C-91B6244418D1</a:ObjectID>
<a:Name>退订日期</a:Name>
<a:Code>FUNSUBSCRIBEUSERDATE</a:Code>
<a:CreationDate>1586343189</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o199">
<a:ObjectID>584B712D-398B-4E02-8DBC-4A07E71E2644</a:ObjectID>
<a:Name>PK_LIVE_THEMEUNSCUSERENTRY</a:Name>
<a:Code>PK_LIVE_THEMEUNSCUSERENTRY</a:Code>
<a:CreationDate>1585205438</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_THEMEUNSCUSERENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o195"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o200">
<a:ObjectID>A4E9F30F-112D-4484-AAC9-90AA47DF29A5</a:ObjectID>
<a:Name>IDX_LIVE_UNSCUSERENTRY_ID</a:Name>
<a:Code>IDX_LIVE_UNSCUSERENTRY_ID</a:Code>
<a:CreationDate>1585205438</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o201">
<a:ObjectID>41A4641F-B025-42CE-8857-8F3229614DAC</a:ObjectID>
<a:CreationDate>1585205486</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o196"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o199"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o199"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o202">
<a:ObjectID>70BE9E0C-B5FF-4329-A618-22A5F52E208A</a:ObjectID>
<a:Name>T_LIVE_ANALYSISTHEME(主题表)</a:Name>
<a:Code>T_LIVE_ANALYSISTHEME</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>主题表</a:Comment>
<a:PhysicalOptions>ON [PRIMARY]</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o203">
<a:ObjectID>ACB8FD9D-A4D8-4F99-BA35-5CEC144100AE</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>7F473A05-185C-46EA-AFD1-AC423D02BCAC</a:ObjectID>
<a:Name>主题编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>nvarchar(72)</a:DataType>
<a:Length>72</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o205">
<a:ObjectID>325A406F-D152-473A-9CB2-BDF83206C2F0</a:ObjectID>
<a:Name>单据状态</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o206">
<a:ObjectID>F5D98D74-A240-44F4-B90A-DF44E900D853</a:ObjectID>
<a:Name>主题分组</a:Name>
<a:Code>FGROUP</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o207">
<a:ObjectID>D4A83EDE-0125-4011-87E1-0970A1C76F08</a:ObjectID>
<a:Name>公开主题</a:Name>
<a:Code>FPUBLIC</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;1&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o208">
<a:ObjectID>AC7FC782-84AB-45B4-B1EB-D5AE46EEB559</a:ObjectID>
<a:Name>系统预置</a:Name>
<a:Code>FISSYSPRESET</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o209">
<a:ObjectID>F76AC2DD-591E-4061-9212-EAA542A05831</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o210">
<a:ObjectID>E8BA89A6-6164-448D-BE3E-0B010E6A4D13</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o211">
<a:ObjectID>6DB0D71A-2094-4524-B214-6ADBADA0943A</a:ObjectID>
<a:Name>修改人</a:Name>
<a:Code>FMODIFIERID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o212">
<a:ObjectID>9E7BA1BD-C8F5-46EA-9883-DFAC98759ECD</a:ObjectID>
<a:Name>修改日期</a:Name>
<a:Code>FMODIFYDATE</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o213">
<a:ObjectID>083888EB-90FC-4B62-9C41-398CDCF61AF7</a:ObjectID>
<a:Name>审核人</a:Name>
<a:Code>FAPPROVERID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o214">
<a:ObjectID>3AC974B3-3762-4EE2-A5A6-3260930F1D62</a:ObjectID>
<a:Name>审核日期</a:Name>
<a:Code>FAPPROVEDATE</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o215">
<a:ObjectID>05449C08-AB7C-4279-AE08-4B9C311A522C</a:ObjectID>
<a:Name>禁用人</a:Name>
<a:Code>FFORBIDERID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o216">
<a:ObjectID>FD0AB5CC-39B6-4F57-9961-47576F61821D</a:ObjectID>
<a:Name>禁用日期</a:Name>
<a:Code>FFORBIDDATE</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o217">
<a:ObjectID>6C501D31-0100-4B16-845D-A55B2AA814F3</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;A&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o218">
<a:ObjectID>BCE64CA4-E0C7-4B14-9E35-EC0B1C28A9F5</a:ObjectID>
<a:Name>PK_LIVE_ANALYSISTHEME</a:Name>
<a:Code>PK_LIVE_ANALYSISTHEME</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:PhysicalOptions>WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]</a:PhysicalOptions>
<a:ConstraintName>PK_LIVE_ANALYSISTHEME</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o203"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o219">
<a:ObjectID>1E795DA7-A2A2-46F0-B34E-03AEC8FB41AD</a:ObjectID>
<a:Name>IDX_LIVE_ANALYSISTHEME_PUBLIC</a:Name>
<a:Code>IDX_LIVE_ANALYSISTHEME_PUBLIC</a:Code>
<a:CreationDate>1584510859</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o220">
<a:ObjectID>C535955F-64B5-4294-A0D0-95BCEAD88B1D</a:ObjectID>
<a:CreationDate>1584510893</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o207"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o221">
<a:ObjectID>323AAC9D-FC2D-49F2-8081-12058738D733</a:ObjectID>
<a:Name>IDX_LIVE_ANALYSISTHEME_GROUP</a:Name>
<a:Code>IDX_LIVE_ANALYSISTHEME_GROUP</a:Code>
<a:CreationDate>1584510893</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o222">
<a:ObjectID>2919F04A-F26F-448C-8E55-FED77F57D836</a:ObjectID>
<a:CreationDate>1584510926</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o206"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o218"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o218"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o223">
<a:ObjectID>A1A6C68F-ABFC-4766-8778-9D6E715F6BE7</a:ObjectID>
<a:Name>T_LIVE_ANALYSISTHEME_L(主题多语言表)</a:Name>
<a:Code>T_LIVE_ANALYSISTHEME_L</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>主题多语言表</a:Comment>
<a:PhysicalOptions>ON [PRIMARY]</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o224">
<a:ObjectID>698628BD-B091-4EB8-B725-3A54EE165C0D</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o225">
<a:ObjectID>B5250D6A-04B3-45DC-B06E-723BF4D56FAD</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o226">
<a:ObjectID>C8B0945D-6497-4F26-8101-918FE9D615A4</a:ObjectID>
<a:Name>语言内码</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o227">
<a:ObjectID>EE4B72A2-7AB2-42A5-8A75-78E308465DC5</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o228">
<a:ObjectID>36C63EE7-1313-46D7-BADD-1E8D1CFC6AF2</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o229">
<a:ObjectID>4DE661DD-27A0-4DA7-92A8-15B3952596E1</a:ObjectID>
<a:Name>PK_LIVE_ANALYSISTHEME_L</a:Name>
<a:Code>PK_LIVE_ANALYSISTHEME_L</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:PhysicalOptions>WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]</a:PhysicalOptions>
<a:ConstraintName>PK_LIVE_ANALYSISTHEME_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o224"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o230">
<a:ObjectID>E1FD7620-9DD3-48DB-A3CB-19ECBECCF708</a:ObjectID>
<a:Name>IDX_LIVE_ANALYSISTHEME_L_ID</a:Name>
<a:Code>IDX_LIVE_ANALYSISTHEME_L_ID</a:Code>
<a:CreationDate>1584511321</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o231">
<a:ObjectID>A668E7A1-57DE-4F98-B7CD-D1B1168A4CB4</a:ObjectID>
<a:CreationDate>1584512333</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o225"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o232">
<a:ObjectID>A7623129-9FDE-49FD-B9DA-70B30997DB63</a:ObjectID>
<a:CreationDate>1584512333</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o226"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o229"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o229"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o233">
<a:ObjectID>6EE3D087-1239-41BA-B01E-0AAE65046AAA</a:ObjectID>
<a:Name>T_LIVE_ANALYSISTHEMEENTRY(主题分录表)</a:Name>
<a:Code>T_LIVE_ANALYSISTHEMEENTRY</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>主题分录表</a:Comment>
<a:PhysicalOptions>ON [PRIMARY]</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o234">
<a:ObjectID>8206673A-87CB-419C-B24D-D198AFD7D698</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o235">
<a:ObjectID>4A15C52C-BEAE-4D1A-A461-DE26DA66F9FD</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o236">
<a:ObjectID>AA628FDD-C74E-46E5-8B48-17761A5DD88F</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o237">
<a:ObjectID>7E98D28C-ADD9-4095-AF30-DA00C50D0FE3</a:ObjectID>
<a:Name>卡片Id</a:Name>
<a:Code>FCARDID</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o238">
<a:ObjectID>A3147E33-D9D1-4030-98ED-2B8CFFE93E03</a:ObjectID>
<a:Name>PK_LIVE_ANALYSISTHEMEENTRY</a:Name>
<a:Code>PK_LIVE_ANALYSISTHEMEENTRY</a:Code>
<a:CreationDate>1584501706</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:PhysicalOptions>WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]</a:PhysicalOptions>
<a:ConstraintName>PK_LIVE_ANALYSISTHEMEENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o235"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o239">
<a:ObjectID>468BE276-B06F-43C8-890B-593AA3671CED</a:ObjectID>
<a:Name>IDX_LIVE_THEMEENTRY_ID</a:Name>
<a:Code>IDX_LIVE_THEMEENTRY_ID</a:Code>
<a:CreationDate>1584511565</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o240">
<a:ObjectID>3B12B03B-E88A-4BDA-95D4-F159C80B36B9</a:ObjectID>
<a:CreationDate>1584511595</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o234"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o241">
<a:ObjectID>C561BEF6-F41B-412D-8EBB-1091231ABEF2</a:ObjectID>
<a:Name>IDX_LIVE_THEMEENTRY_SEQ</a:Name>
<a:Code>IDX_LIVE_THEMEENTRY_SEQ</a:Code>
<a:CreationDate>1584511506</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o242">
<a:ObjectID>17D4F108-EA47-417C-BE85-BB8817AA2C38</a:ObjectID>
<a:CreationDate>1584511565</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o236"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o238"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o238"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o243">
<a:ObjectID>24B2D0DC-D398-46B7-B95B-1AC239831F2D</a:ObjectID>
<a:Name>T_LIVE_ANALYSISTHEMEGROUP(主题分组)</a:Name>
<a:Code>T_LIVE_ANALYSISTHEMEGROUP</a:Code>
<a:CreationDate>1321671235</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o244">
<a:ObjectID>3CC2322A-F6F1-4A2D-9246-489A9DDAFA02</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1321671363</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o245">
<a:ObjectID>EC9B45D6-C1D9-4492-A13B-7216CE99EBDF</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1321671363</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o246">
<a:ObjectID>7AE28E3B-42C5-4883-8FB5-7350FCDCCD94</a:ObjectID>
<a:Name>组内码</a:Name>
<a:Code>FGROUPID</a:Code>
<a:CreationDate>1321671363</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o247">
<a:ObjectID>48553E25-3EEB-47E2-B197-C5820DF6405F</a:ObjectID>
<a:Name>父项ID</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>1321671363</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o248">
<a:ObjectID>8544D64C-1E60-4143-B515-C4C5055E4993</a:ObjectID>
<a:Name>父项全称</a:Name>
<a:Code>FFULLPARENTID</a:Code>
<a:CreationDate>1321671363</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
</o:Column>
<o:Column Id="o249">
<a:ObjectID>05C79A48-431B-4B13-A1A5-49B601535FDB</a:ObjectID>
<a:Name>左距离</a:Name>
<a:Code>FLEFT</a:Code>
<a:CreationDate>1321671363</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o250">
<a:ObjectID>D9FB5882-A12D-4DFE-A276-ECAC4652A666</a:ObjectID>
<a:Name>右距离</a:Name>
<a:Code>FRIGHT</a:Code>
<a:CreationDate>1321671363</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o251">
<a:ObjectID>2DD27DEE-D460-436D-8180-786C9F2D78EE</a:ObjectID>
<a:Name>PK_LIVE_THEMEGROUP</a:Name>
<a:Code>PK_LIVE_THEMEGROUP</a:Code>
<a:CreationDate>1321671577</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_THEMEGROUP</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o244"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o252">
<a:ObjectID>9DA5200A-2842-4C60-B034-8144B5F87520</a:ObjectID>
<a:Name>IDX_LIVE_THEMEGROUP_NUM</a:Name>
<a:Code>IDX_LIVE_THEMEGROUP_NUM</a:Code>
<a:CreationDate>1351738259</a:CreationDate>
<a:Creator>zhendong_jiang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o253">
<a:ObjectID>0BD40109-F18B-4787-83FA-E9A761E08AAA</a:ObjectID>
<a:CreationDate>1351738281</a:CreationDate>
<a:Creator>zhendong_jiang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o245"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o251"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o251"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o254">
<a:ObjectID>6763465E-6EDA-42C3-BA71-2B8942E0D3E0</a:ObjectID>
<a:Name>T_LIVE_ANALYSISTHEMEGROUP_L(分组多语言表)</a:Name>
<a:Code>T_LIVE_ANALYSISTHEMEGROUP_L</a:Code>
<a:CreationDate>1321840468</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o255">
<a:ObjectID>A4BC4BBD-9A08-4446-B038-3163482682BF</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1321840468</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o256">
<a:ObjectID>DDFA1CF9-92D5-4578-9D0C-C3CFBA37E84C</a:ObjectID>
<a:Name>父表内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1321840468</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o257">
<a:ObjectID>A328E3DB-EA20-4B44-9D0A-EE42B4070750</a:ObjectID>
<a:Name>本地化标志</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1321840468</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o258">
<a:ObjectID>5E174CC0-EA9C-484C-A921-B41AD720A3F7</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1321840468</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o259">
<a:ObjectID>53D458F7-61AF-4360-86AA-A6D286B09063</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1321840468</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(510)</a:DataType>
<a:Length>510</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o260">
<a:ObjectID>41B30075-3BB6-473A-8B87-DF3B07AF5B3C</a:ObjectID>
<a:Name>PK_LIVE_THEMEGROUP_L</a:Name>
<a:Code>PK_LIVE_THEMEGROUP_L</a:Code>
<a:CreationDate>1321840468</a:CreationDate>
<a:Creator>wei_liao</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_THEMEGROUP_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o255"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o261">
<a:ObjectID>F753E620-5B4E-4378-A4B4-40AB488F7C3F</a:ObjectID>
<a:Name>IDX_LIVE_THEMEGROUP_ID_L</a:Name>
<a:Code>IDX_LIVE_THEMEGROUP_ID_L</a:Code>
<a:CreationDate>1351738355</a:CreationDate>
<a:Creator>zhendong_jiang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Unique>1</a:Unique>
<c:IndexColumns>
<o:IndexColumn Id="o262">
<a:ObjectID>C92116BE-578A-4DC5-BE82-04BE6E314610</a:ObjectID>
<a:CreationDate>1351738502</a:CreationDate>
<a:Creator>zhendong_jiang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o256"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o263">
<a:ObjectID>006CDFF0-1666-4C94-8844-43FBFBBC482C</a:ObjectID>
<a:CreationDate>1351738380</a:CreationDate>
<a:Creator>zhendong_jiang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o257"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o260"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o260"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o264">
<a:ObjectID>51EC4808-63F9-4D79-A698-75DA193452C1</a:ObjectID>
<a:Name>T_LIVE_CARDBILLLISTDETAIL(卡片单据列表数据源表)</a:Name>
<a:Code>T_LIVE_CARDBILLLISTDETAIL</a:Code>
<a:CreationDate>1586329055</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片单据列表数据源表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o265">
<a:ObjectID>97EC436E-3A7F-4CE2-AAD0-ACBD6DA74F40</a:ObjectID>
<a:Name>分录内码</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1586329055</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o266">
<a:ObjectID>E06ADA91-3900-42C8-99C5-FBC96B44A889</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FDETAILID</a:Code>
<a:CreationDate>1586329055</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o267">
<a:ObjectID>3DBD9B86-33AB-4DD4-98AC-206F5472FEE9</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1586329055</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o268">
<a:ObjectID>EB5D5636-FE2E-47DE-900C-156264609D3F</a:ObjectID>
<a:Name>列表显示字段</a:Name>
<a:Code>FLISTSHOWFIELD</a:Code>
<a:CreationDate>1586329055</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o269">
<a:ObjectID>006A7F9D-0678-4558-BA05-BE7A8107A8BD</a:ObjectID>
<a:Name>列表显示字段Key</a:Name>
<a:Code>FLISTSHOWFIELDKEY</a:Code>
<a:CreationDate>1586329055</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o270">
<a:ObjectID>5BF97C0D-0DF5-4753-AC56-83EAAB734CA4</a:ObjectID>
<a:Name>单据头字段</a:Name>
<a:Code>FLISTISHEADFIELD</a:Code>
<a:CreationDate>1586329055</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o271">
<a:ObjectID>5325A90C-6A73-489F-A146-F1FABF6AF523</a:ObjectID>
<a:Name>单据体字段汇总方式</a:Name>
<a:Code>FENTRYSUMTYPE</a:Code>
<a:CreationDate>1586329055</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o272">
<a:ObjectID>4ED30402-E79E-4E58-9B54-1079A5F49369</a:ObjectID>
<a:Name>PK_LIVE_CARDBILLLISTDETAIL</a:Name>
<a:Code>PK_LIVE_CARDBILLLISTDETAIL</a:Code>
<a:CreationDate>1586329238</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_CARDBILLLISTDETAIL</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o266"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o273">
<a:ObjectID>1F63DDF9-B3CB-4678-9A1B-018B88D6ECEC</a:ObjectID>
<a:Name>IDX_LIVE_CARDBILLLISTDETAIL_ID</a:Name>
<a:Code>IDX_LIVE_CARDBILLLISTDETAIL_ID</a:Code>
<a:CreationDate>1586329329</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o274">
<a:ObjectID>76219EEA-2F20-4594-A05E-0D2B3E64C0BC</a:ObjectID>
<a:CreationDate>1586329343</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o265"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o272"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o272"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o275">
<a:ObjectID>4BEB8D5E-D39C-4188-A225-C394FB3EFE40</a:ObjectID>
<a:Name>T_LIVE_CARDDRILLFIELDENTRY(卡片钻取字段分录表)</a:Name>
<a:Code>T_LIVE_CARDDRILLFIELDENTRY</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片钻取字段分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o276">
<a:ObjectID>C4FE5482-6CBE-4BDD-8C68-226BCD043177</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o277">
<a:ObjectID>635FF86D-0ED5-4B9E-A99A-E18885556517</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o278">
<a:ObjectID>4AA14FCF-72D1-49FF-9663-D54DB490A3C4</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o279">
<a:ObjectID>63BB57B8-8807-45E5-BD61-A47841DA8C09</a:ObjectID>
<a:Name>默认钻取字段</a:Name>
<a:Code>FISDEFAULTDRILLFIELD</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o280">
<a:ObjectID>7EDF4FB1-B23C-42BD-A967-13F384B08FF0</a:ObjectID>
<a:Name>[数据源1]对应字段</a:Name>
<a:Code>FSOURCEFIELD1</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o281">
<a:ObjectID>C6DF212C-8A09-4AD5-A893-43BDE640FA0D</a:ObjectID>
<a:Name>[数据源1]对应字段Key</a:Name>
<a:Code>FSOURCEFIELD1KEY</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o282">
<a:ObjectID>0415DDBA-8EBF-4951-A314-55B65BAFA0B9</a:ObjectID>
<a:Name>[数据源2]对应字段</a:Name>
<a:Code>FSOURCEFIELD2</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o283">
<a:ObjectID>87181E95-B030-4CFE-9584-575683DD456F</a:ObjectID>
<a:Name>[数据源2]对应字段Key</a:Name>
<a:Code>FSOURCEFIELD2KEY</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o284">
<a:ObjectID>CAA46498-5D41-4C9E-A225-A0129059712D</a:ObjectID>
<a:Name>[数据源3]对应字段</a:Name>
<a:Code>FSOURCEFIELD3</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o285">
<a:ObjectID>C20B4F50-F28D-46AA-9361-4E0ED41964B0</a:ObjectID>
<a:Name>[数据源3]对应字段Key</a:Name>
<a:Code>FSOURCEFIELD3KEY</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o286">
<a:ObjectID>38253905-1051-430D-9CF9-1F96933459E6</a:ObjectID>
<a:Name>[数据源4]对应字段</a:Name>
<a:Code>FSOURCEFIELD4</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o287">
<a:ObjectID>64FCA59A-C1E0-4CCC-8608-267DDA777630</a:ObjectID>
<a:Name>[数据源4]对应字段Key</a:Name>
<a:Code>FSOURCEFIELD4KEY</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o288">
<a:ObjectID>EA87E936-00D7-4028-98BB-7898DDCAB078</a:ObjectID>
<a:Name>[数据源5]对应字段</a:Name>
<a:Code>FSOURCEFIELD5</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o289">
<a:ObjectID>1AF0D11C-1063-4A06-A12D-082106AE1AAE</a:ObjectID>
<a:Name>[数据源5]对应字段Key</a:Name>
<a:Code>FSOURCEFIELD5KEY</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o290">
<a:ObjectID>729F443F-05F8-4FCC-8C4D-20C29AD07F75</a:ObjectID>
<a:Name>PK_LIVE_CARDDRILLFIELDENTRY</a:Name>
<a:Code>PK_LIVE_CARDDRILLFIELDENTRY</a:Code>
<a:CreationDate>1586328339</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_CARDDRILLFIELDENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o277"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o291">
<a:ObjectID>4E208552-AA48-4D32-BF2A-B03E5418A037</a:ObjectID>
<a:Name>IDX_LIVE_DRILLFIELDENTRY_ID</a:Name>
<a:Code>IDX_LIVE_DRILLFIELDENTRY_ID</a:Code>
<a:CreationDate>1586328682</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o292">
<a:ObjectID>FC687EAB-76DA-45C2-8DA9-30A6A7E9E726</a:ObjectID>
<a:CreationDate>1586328768</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o276"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o293">
<a:ObjectID>C95C2F06-E317-496B-9F00-FE87289934BE</a:ObjectID>
<a:Name>IDX_LIVE_DRILLFIELDENTRY_SEQ</a:Name>
<a:Code>IDX_LIVE_DRILLFIELDENTRY_SEQ</a:Code>
<a:CreationDate>1586328768</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o294">
<a:ObjectID>B234EBAA-21D6-4E77-A180-735B51A4B866</a:ObjectID>
<a:CreationDate>1586328808</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o278"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o290"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o290"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o295">
<a:ObjectID>E0583935-FF4F-41A4-8FD2-E7DD8A31D5D4</a:ObjectID>
<a:Name>T_LIVE_CARDDRILLFIELDENTRY_L(卡片钻取字段分录多语言表)</a:Name>
<a:Code>T_LIVE_CARDDRILLFIELDENTRY_L</a:Code>
<a:CreationDate>1586328867</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片钻取字段分录多语言表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o296">
<a:ObjectID>355B8B5F-965A-4E1F-A034-A16611D5D0B4</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1586328899</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o297">
<a:ObjectID>22E83873-DB5B-4218-BFF8-81FFFB07961A</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1586328899</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o298">
<a:ObjectID>5A11EC68-85B4-4F82-8055-A3DC82B49CA1</a:ObjectID>
<a:Name>语言内码</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>1586328899</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o299">
<a:ObjectID>643581ED-8BDF-494B-9177-B0E6DE2724DF</a:ObjectID>
<a:Name>钻取字段</a:Name>
<a:Code>FDRILLFIELD</a:Code>
<a:CreationDate>1586328899</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o300">
<a:ObjectID>E6D24D47-94B9-4C1D-BF97-9EC099B2C9D7</a:ObjectID>
<a:Name>PK_LIVE_CARDDRILLFIELDENTRY_L</a:Name>
<a:Code>PK_LIVE_CARDDRILLFIELDENTRY_L</a:Code>
<a:CreationDate>1586328899</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_CARDDRILLFIELDENTRY_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o296"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o301">
<a:ObjectID>17C4CF2A-F697-4FDD-806E-26659D7D8A47</a:ObjectID>
<a:Name>IDX_LIVE_DRILLFIELDENTRY_L_ID</a:Name>
<a:Code>IDX_LIVE_DRILLFIELDENTRY_L_ID</a:Code>
<a:CreationDate>1586328958</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o302">
<a:ObjectID>86FFD742-CC93-41B9-BAA9-64026B276A2B</a:ObjectID>
<a:CreationDate>1586328979</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o297"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o303">
<a:ObjectID>370775AC-B060-4875-931D-C5C767365974</a:ObjectID>
<a:CreationDate>1586328979</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o298"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o300"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o300"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o304">
<a:ObjectID>A2D9E8EB-D607-494B-9561-41EEA30FDDC7</a:ObjectID>
<a:Name>T_LIVE_REPORTCARD(卡片表)</a:Name>
<a:Code>T_LIVE_REPORTCARD</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o305">
<a:ObjectID>9E29E9D8-30E5-427C-8781-67E5A9546A33</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o306">
<a:ObjectID>CA8E41CD-7C5C-48D1-90EB-120D86C0995F</a:ObjectID>
<a:Name>卡片类型</a:Name>
<a:Code>FBILLTYPEID</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o307">
<a:ObjectID>59D59B22-88B5-4372-9271-916B8A2AADF8</a:ObjectID>
<a:Name>卡片编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(72)</a:DataType>
<a:Length>72</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o308">
<a:ObjectID>C6A7EB4D-DA98-4056-8385-BFE2DA796FF6</a:ObjectID>
<a:Name>单据状态</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o309">
<a:ObjectID>47D63EB0-5687-4C22-A91C-9CE6FB5DA7D0</a:ObjectID>
<a:Name>系统预置</a:Name>
<a:Code>FISSYSPRESET</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o310">
<a:ObjectID>34941389-485D-41DE-A22C-B8EC162B5767</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o311">
<a:ObjectID>AC82076C-BDAD-4690-B5B8-8CC09423F978</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o312">
<a:ObjectID>82B5F5FC-6BF1-4ADC-A508-94E2BBE00965</a:ObjectID>
<a:Name>修改人</a:Name>
<a:Code>FMODIFIERID</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o313">
<a:ObjectID>FAC4A093-6BF2-47C6-BC44-7DBB2E204F73</a:ObjectID>
<a:Name>修改日期</a:Name>
<a:Code>FMODIFYDATE</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o314">
<a:ObjectID>95DCFFE3-1E83-428D-9D05-E45117896C4C</a:ObjectID>
<a:Name>审核人</a:Name>
<a:Code>FAPPROVERID</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o315">
<a:ObjectID>DFC02634-D56C-4F65-B5E2-B581B7A0BAA1</a:ObjectID>
<a:Name>审核日期</a:Name>
<a:Code>FAPPROVEDATE</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o316">
<a:ObjectID>3DB49ADC-0921-490C-AAC5-716BDD54ABBC</a:ObjectID>
<a:Name>禁用人</a:Name>
<a:Code>FFORBIDERID</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o317">
<a:ObjectID>8FDEF996-99AA-41E6-A980-B24A9AC19E59</a:ObjectID>
<a:Name>禁用日期</a:Name>
<a:Code>FFORBIDDATE</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o318">
<a:ObjectID>2F375205-D2C2-46C0-ABF3-C3E65A4B158C</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1586325822</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;A&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o319">
<a:ObjectID>5D8EFBEB-EE29-4544-9413-81ED5B5D7D07</a:ObjectID>
<a:Name>PK_LIVE_REPORTCARD</a:Name>
<a:Code>PK_LIVE_REPORTCARD</a:Code>
<a:CreationDate>1586326053</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_REPORTCARD</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o305"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o320">
<a:ObjectID>8EC448F4-69D8-4E07-BCE8-C8A4726807E2</a:ObjectID>
<a:Name>IDX_LIVE_REPORTCARD_BILLTYPE</a:Name>
<a:Code>IDX_LIVE_REPORTCARD_BILLTYPE</a:Code>
<a:CreationDate>1586325931</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o321">
<a:ObjectID>42A863B1-BFBC-4465-966E-DB0D13BD29B8</a:ObjectID>
<a:CreationDate>1586325990</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o306"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o319"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o319"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o322">
<a:ObjectID>E9DF02A3-ABEF-4BDB-AA22-0DDABAAD7863</a:ObjectID>
<a:Name>T_LIVE_REPORTCARD_D(卡片钻取表)</a:Name>
<a:Code>T_LIVE_REPORTCARD_D</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片钻取表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o323">
<a:ObjectID>1E0589FD-B543-4AD1-AA6A-54953C5774AD</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o324">
<a:ObjectID>2000C0C2-3804-4EB3-AD8D-DB37AE2AF646</a:ObjectID>
<a:Name>下查钻取方式</a:Name>
<a:Code>FDRILLTYPE</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o325">
<a:ObjectID>081DBCA8-3D51-4B34-9C8B-208029646BF2</a:ObjectID>
<a:Name>默认排序方式</a:Name>
<a:Code>FDEFAULTSORTTYPE</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o326">
<a:ObjectID>4692EB8E-0E2E-4927-A8D4-F3A3A5AB0129</a:ObjectID>
<a:Name>钻取比较上期显示规则</a:Name>
<a:Code>FPREDISPLAYRULESWHENDRILL</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o327">
<a:ObjectID>BA4433E6-6E76-4C5C-B945-F85A148C6B5B</a:ObjectID>
<a:Name>末级联查单据列表</a:Name>
<a:Code>FCANSHOWBILLLIST</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o328">
<a:ObjectID>1B547B70-AE66-4D8E-B3D3-6990ED1FF0B0</a:ObjectID>
<a:Name>钻取时支持显示占比</a:Name>
<a:Code>FSHOWRATIOWHENDRILL</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o329">
<a:ObjectID>A27838DF-E0A6-4B60-AE36-2153A520AC7E</a:ObjectID>
<a:Name>钻取时支持显示比较上期</a:Name>
<a:Code>FSHOWPREPERIODWHENDRILL</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o330">
<a:ObjectID>C00DA8D6-DAA0-4D41-98B6-940FD79A3539</a:ObjectID>
<a:Name>PK_LIVE_REPORTCARD_D</a:Name>
<a:Code>PK_LIVE_REPORTCARD_D</a:Code>
<a:CreationDate>1586326307</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_REPORTCARD_D</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o323"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o330"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o330"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o331">
<a:ObjectID>D515F199-8D1F-4A38-8453-510CFB0321AA</a:ObjectID>
<a:Name>T_LIVE_REPORTCARD_E(卡片指标表)</a:Name>
<a:Code>T_LIVE_REPORTCARD_E</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1596096206</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片指标表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o332">
<a:ObjectID>8206F667-1C88-4866-A596-4C29C34B1E5D</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o333">
<a:ObjectID>417EAE94-5A22-4765-A228-2BD325C45DB9</a:ObjectID>
<a:Name>默认日期范围</a:Name>
<a:Code>FDATETYPE</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o334">
<a:ObjectID>BD1F5C57-B765-4D5D-8E6A-540AEE9C73D4</a:ObjectID>
<a:Name>开始日期</a:Name>
<a:Code>FCUSTOMSTARTDATE</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o335">
<a:ObjectID>F94E9435-E968-4C37-8BB4-C82B639CFD2F</a:ObjectID>
<a:Name>结束日期</a:Name>
<a:Code>FCUSTOMENDDATE</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o336">
<a:ObjectID>91202613-E8C9-43C6-A040-CEC328ED1550</a:ObjectID>
<a:Name>计算方式</a:Name>
<a:Code>FCALCGROUP</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o337">
<a:ObjectID>EECB0C40-16DC-412B-872F-7B6B35971494</a:ObjectID>
<a:Name>汇总方式</a:Name>
<a:Code>FSUMTYPE</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o338">
<a:ObjectID>7AE62DA9-5231-4972-B776-311B61C99C39</a:ObjectID>
<a:Name>汇总字段</a:Name>
<a:Code>FSUMFIELD</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o339">
<a:ObjectID>DAD049CE-8B8F-4928-B426-A969985E5B8F</a:ObjectID>
<a:Name>汇总字段Key</a:Name>
<a:Code>FSUMFIELDKEY</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o340">
<a:ObjectID>AA123A02-378B-4D47-8D3F-B14ED3408EE2</a:ObjectID>
<a:Name>表达式</a:Name>
<a:Code>FDISPLAYEXPRESSION</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o341">
<a:ObjectID>D0A2873C-E3CF-466E-AED6-106553AD5250</a:ObjectID>
<a:Name>可选日期范围</a:Name>
<a:Code>FVALIDDATESCOPE</a:Code>
<a:CreationDate>1591779954</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096206</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o342">
<a:ObjectID>70D7B8B9-0221-4227-BA46-1B58FC357309</a:ObjectID>
<a:Name>PK_LIVE_REPORTCARD_E</a:Name>
<a:Code>PK_LIVE_REPORTCARD_E</a:Code>
<a:CreationDate>1586326608</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_REPORTCARD_E</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o332"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o342"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o342"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o343">
<a:ObjectID>7E9F7AD0-C725-44E6-9E3E-3A68D5F1385A</a:ObjectID>
<a:Name>T_LIVE_REPORTCARD_L(卡片多语言表)</a:Name>
<a:Code>T_LIVE_REPORTCARD_L</a:Code>
<a:CreationDate>1586326114</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片多语言表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o344">
<a:ObjectID>73569702-B9BF-429A-8BE3-7291CC4EACBF</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1586326146</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o345">
<a:ObjectID>C0ED119F-C147-4D51-A8C1-17221900B1FE</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1586326146</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o346">
<a:ObjectID>017C8CFB-133E-4FC3-88B9-8329A955FADB</a:ObjectID>
<a:Name>语言内码</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>1586326146</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o347">
<a:ObjectID>30987693-73D2-4F55-AF5F-435FFB4B1E28</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1586326146</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o348">
<a:ObjectID>359B049F-ED17-4448-A10C-F6A5C85577AB</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1586326146</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o349">
<a:ObjectID>40B10C2A-D28C-486E-9AB5-7679643AD9BB</a:ObjectID>
<a:Name>PK_LIVE_REPORTCARD_L</a:Name>
<a:Code>PK_LIVE_REPORTCARD_L</a:Code>
<a:CreationDate>1586326146</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_REPORTCARD_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o344"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o350">
<a:ObjectID>56B566D1-A34B-43BE-9107-ED87B98A144F</a:ObjectID>
<a:Name>IDX_LIVE_REPORTCARD_L_ID</a:Name>
<a:Code>IDX_LIVE_REPORTCARD_L_ID</a:Code>
<a:CreationDate>1586326146</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o351">
<a:ObjectID>ECE9F8BF-FA8A-401E-81A6-A6F82588AF01</a:ObjectID>
<a:CreationDate>1586326174</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o345"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o352">
<a:ObjectID>DF054B90-7F99-4E85-8A16-F2E655950004</a:ObjectID>
<a:CreationDate>1586326174</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o346"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o349"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o349"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o353">
<a:ObjectID>F3C6BBF2-88EA-481C-8EB7-4E71530F2A7D</a:ObjectID>
<a:Name>T_LIVE_REPORTCARD_S(卡片显示表)</a:Name>
<a:Code>T_LIVE_REPORTCARD_S</a:Code>
<a:CreationDate>1586326911</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片显示表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o354">
<a:ObjectID>7FC4645D-EC72-4EFC-B259-D142D735E3EE</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o355">
<a:ObjectID>B9C2A4D4-1CB5-4786-AF75-A13EE57DA203</a:ObjectID>
<a:Name>显示单位</a:Name>
<a:Code>FDISPLAYUNIT</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o356">
<a:ObjectID>F3B57D94-411F-4656-A578-CE5A0B71AE64</a:ObjectID>
<a:Name>不显示尾零</a:Name>
<a:Code>FNOTSHOWLASTZERO</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o357">
<a:ObjectID>AE110DCB-452D-4B65-8B12-A05F9DCA46C8</a:ObjectID>
<a:Name>卡片比较上期显示规则</a:Name>
<a:Code>FPREDISPLAYRULESWHENCARD</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o358">
<a:ObjectID>83B5BDA7-094B-4BB4-BFEB-F3C4BF642C23</a:ObjectID>
<a:Name>卡片支持显示比较上期</a:Name>
<a:Code>FSHOWPREPERIODWHENCARD</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o359">
<a:ObjectID>DE7FD0D9-82F6-456D-AF51-AAA9585DA127</a:ObjectID>
<a:Name>分割符</a:Name>
<a:Code>FSEPARATOR</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o360">
<a:ObjectID>D6D4E276-8887-420F-8AD8-639973A88FEF</a:ObjectID>
<a:Name>仅显示钻取摘要</a:Name>
<a:Code>FONLYSHOWSUMMARY</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o361">
<a:ObjectID>2F7D5B7F-1BFB-4501-B0AE-72D9C0C030C9</a:ObjectID>
<a:Name>钻取摘要</a:Name>
<a:Code>FDRILLSUMMARYTYPE</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o362">
<a:ObjectID>1C2A04F9-6AE3-42D9-91BC-00C4E40C0736</a:ObjectID>
<a:Name>排名序号图标</a:Name>
<a:Code>FRANKSEQICON</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o363">
<a:ObjectID>A601DC9C-E5C0-4B4E-AC14-65109B43C469</a:ObjectID>
<a:Name>小数位数</a:Name>
<a:Code>FFIXNUMBER</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o364">
<a:ObjectID>AA3EF113-4843-4407-A83E-C1EAFDAEA395</a:ObjectID>
<a:Name>钻取摘要显示数量</a:Name>
<a:Code>FDRILLSUMMARYNUMBER</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o365">
<a:ObjectID>C5FA35FC-7F5D-45DE-B37B-F375034B9AF2</a:ObjectID>
<a:Name>PK_LIVE_REPORTCARD_S</a:Name>
<a:Code>PK_LIVE_REPORTCARD_S</a:Code>
<a:CreationDate>1586326939</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_REPORTCARD_S</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o354"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o365"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o365"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o366">
<a:ObjectID>E06965D6-E32B-4E61-B984-B3B0FE6ADD7F</a:ObjectID>
<a:Name>T_LIVE_REPORTCARDENTRY(卡片分录表)</a:Name>
<a:Code>T_LIVE_REPORTCARDENTRY</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>卡片分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o367">
<a:ObjectID>46B153E7-05B3-4C78-A850-41983E5578FC</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o368">
<a:ObjectID>FFA8A804-8503-427E-B5BE-12D4A00871D2</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o369">
<a:ObjectID>C8AD5C2C-6EBA-427E-AE41-E5E6C65C9773</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o370">
<a:ObjectID>EC3A010A-C5F3-43AE-A652-1CF254CD66D2</a:ObjectID>
<a:Name>数据源类型</a:Name>
<a:Code>FSOURCETYPE</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o371">
<a:ObjectID>1FA73DAC-E162-4E57-9A5B-DB2AB7FA20D2</a:ObjectID>
<a:Name>数据源</a:Name>
<a:Code>FSOURCEKEY</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o372">
<a:ObjectID>AA4E509C-F217-48D7-B961-9C817E63E5D0</a:ObjectID>
<a:Name>数据源名称</a:Name>
<a:Code>FSOURCENAME</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o373">
<a:ObjectID>CCEF2117-EF46-4D0C-B0E6-55D1B86AFD12</a:ObjectID>
<a:Name>单据体</a:Name>
<a:Code>FSOURCEENTRY</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o374">
<a:ObjectID>F4539682-E2FB-4567-BC40-AD1F3DB5D426</a:ObjectID>
<a:Name>单据体Key</a:Name>
<a:Code>FSOURCEENTRYKEY</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o375">
<a:ObjectID>C238FC73-20BC-43B3-8FAB-0DCB1FE6F768</a:ObjectID>
<a:Name>过滤方案</a:Name>
<a:Code>FFILTERSTRING</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o376">
<a:ObjectID>BD83BB96-6176-466B-8660-4992B1923976</a:ObjectID>
<a:Name>过滤方案Key</a:Name>
<a:Code>FFILTERSTRINGKEY</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o377">
<a:ObjectID>B9FE431D-9C9B-4FC8-B32D-CBB981FC4805</a:ObjectID>
<a:Name>自定义过滤条件</a:Name>
<a:Code>FEXTENDFILTERSTRING</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(510)</a:DataType>
<a:Length>510</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o378">
<a:ObjectID>1B2030F8-CD7A-4887-A956-FC9A70314D22</a:ObjectID>
<a:Name>日期字段</a:Name>
<a:Code>FDATEFIELD</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o379">
<a:ObjectID>95489226-11B0-4277-B72F-5AA2E4105280</a:ObjectID>
<a:Name>日期字段Key</a:Name>
<a:Code>FDATEFIELDKEY</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o380">
<a:ObjectID>1806FE00-2E44-4B02-95B2-33BD299D6B5D</a:ObjectID>
<a:Name>强调显示字段</a:Name>
<a:Code>FFOCUSSHOWFIELD</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o381">
<a:ObjectID>AA286707-10B5-4392-BF21-8FE84D53B8F7</a:ObjectID>
<a:Name>强调显示字段Key</a:Name>
<a:Code>FFOCUSSHOWFIELDKEY</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o382">
<a:ObjectID>37C4A17C-7B9E-4CAB-B6E8-B014F966A205</a:ObjectID>
<a:Name>PK_LIVE_REPORTCARDENTRY</a:Name>
<a:Code>PK_LIVE_REPORTCARDENTRY</a:Code>
<a:CreationDate>1586327293</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_REPORTCARDENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o368"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o383">
<a:ObjectID>D1D8CEA8-4308-442B-A744-B8F625716251</a:ObjectID>
<a:Name>IDX_LIVE_REPORTCARDENTRY_ID</a:Name>
<a:Code>IDX_LIVE_REPORTCARDENTRY_ID</a:Code>
<a:CreationDate>1586327497</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o384">
<a:ObjectID>FE32226F-40CB-4C12-B968-B49FEB44E932</a:ObjectID>
<a:CreationDate>1586327623</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o367"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o385">
<a:ObjectID>4BB01686-5054-4655-820E-4430A75C1073</a:ObjectID>
<a:Name>IDX_LIVE_REPORTCARDENTRY_SEQ</a:Name>
<a:Code>IDX_LIVE_REPORTCARDENTRY_SEQ</a:Code>
<a:CreationDate>1586327623</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o386">
<a:ObjectID>10903F7A-85D3-40C3-A5CF-1E977E916BFE</a:ObjectID>
<a:CreationDate>1586327647</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o369"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o382"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o382"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o387">
<a:ObjectID>9E8D5DE3-8EA9-495F-AC9F-9A0E65AB00A5</a:ObjectID>
<a:Name>T_LIVE_THEMEACROLEENTRY(主题分配角色表)</a:Name>
<a:Code>T_LIVE_THEMEACROLEENTRY</a:Code>
<a:CreationDate>1584586873</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>主题分配角色表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o388">
<a:ObjectID>5BE3B3C9-7462-411A-8E4D-1C4DFEB765BE</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1584586873</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o389">
<a:ObjectID>E04E4E85-315B-496B-A006-60D113616D01</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1584586873</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o390">
<a:ObjectID>F62BFD76-DE05-4D08-B1B5-FEB50D3A00C6</a:ObjectID>
<a:Name>角色Id</a:Name>
<a:Code>FALLOCATEROLEID</a:Code>
<a:CreationDate>1584586873</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o391">
<a:ObjectID>86D2DA49-ECF1-4639-BFD1-9A02D7D96192</a:ObjectID>
<a:Name>分配日期</a:Name>
<a:Code>FALLOCATEROLEDATE</a:Code>
<a:CreationDate>1586343012</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o392">
<a:ObjectID>310235D9-C845-4757-8117-FAB2559C9907</a:ObjectID>
<a:Name>PK_LIVE_THEMEACROLEENTRY</a:Name>
<a:Code>PK_LIVE_THEMEACROLEENTRY</a:Code>
<a:CreationDate>1584586873</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_THEMEACROLEENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o388"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o393">
<a:ObjectID>3DE687CE-00E7-4C3C-9E1B-EAA65733D50D</a:ObjectID>
<a:Name>IDX_LIVE_THEMEACROLEENTRY_ID</a:Name>
<a:Code>IDX_LIVE_THEMEACROLEENTRY_ID</a:Code>
<a:CreationDate>1584587005</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o394">
<a:ObjectID>C6CBFF4B-F1FB-4BEC-B7B4-B875BB50B572</a:ObjectID>
<a:CreationDate>1584587034</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o389"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o392"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o392"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o395">
<a:ObjectID>EA66127A-5E85-458C-BA18-633A8238BC2B</a:ObjectID>
<a:Name>T_LIVE_THEMEACUSERENTRY(主题分配用户表)</a:Name>
<a:Code>T_LIVE_THEMEACUSERENTRY</a:Code>
<a:CreationDate>1584587098</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>主题分配用户表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o396">
<a:ObjectID>02DAB453-A1D1-4556-B2B6-819BF45B4224</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1584587098</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o397">
<a:ObjectID>99C080AB-5E0D-498B-B063-82B53C7CE19B</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1584587098</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o398">
<a:ObjectID>89B182F3-DF3F-4D7A-9702-2DA6965B8750</a:ObjectID>
<a:Name>用户Id</a:Name>
<a:Code>FALLOCATEUSERID</a:Code>
<a:CreationDate>1584587098</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o399">
<a:ObjectID>197AC642-FAD7-4E3E-BB7B-27659A585063</a:ObjectID>
<a:Name>分配日期</a:Name>
<a:Code>FALLOCATEUSERDATE</a:Code>
<a:CreationDate>1586343065</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o400">
<a:ObjectID>549C8C2D-0A0C-4F94-A364-CCA4D4EE1BFF</a:ObjectID>
<a:Name>PK_LIVE_THEMEACUSERENTRY</a:Name>
<a:Code>PK_LIVE_THEMEACUSERENTRY</a:Code>
<a:CreationDate>1584587148</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_THEMEACUSERENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o396"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o401">
<a:ObjectID>212354C6-DBBC-4967-A690-5E26CA5AA16F</a:ObjectID>
<a:Name>IDX_LIVE_THEMEACUSERENTRY_ID</a:Name>
<a:Code>IDX_LIVE_THEMEACUSERENTRY_ID</a:Code>
<a:CreationDate>1584587098</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o402">
<a:ObjectID>143A137D-3C29-41A8-96F1-50808D43DB85</a:ObjectID>
<a:CreationDate>1584587194</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o397"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o400"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o400"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o403">
<a:ObjectID>D94C7932-72F8-487C-900F-92481A1FAD7C</a:ObjectID>
<a:Name>T_LIVE_THEMESCUSERENTRY(主题订阅用户表)</a:Name>
<a:Code>T_LIVE_THEMESCUSERENTRY</a:Code>
<a:CreationDate>1585205320</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o404">
<a:ObjectID>EC4CDFC5-30A5-4A06-BAC7-E39BCED171C2</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1585205320</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o405">
<a:ObjectID>7691754F-09E9-4C57-93AB-56FF7A2917D3</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1585205320</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o406">
<a:ObjectID>3A7A48B0-9549-4F6F-BAEA-0CA75325034C</a:ObjectID>
<a:Name>用户Id</a:Name>
<a:Code>FSUBSCRIBEUSERID</a:Code>
<a:CreationDate>1585205320</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o407">
<a:ObjectID>86F516E3-E28B-42C7-A099-5B7603610FAB</a:ObjectID>
<a:Name>订阅日期</a:Name>
<a:Code>FSUBSCRIBEUSERDATE</a:Code>
<a:CreationDate>1586343165</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o408">
<a:ObjectID>C5E46E66-5A7E-4CC7-986C-A588BB95041F</a:ObjectID>
<a:Name>PK_LIVE_THEMESCUSERENTRY</a:Name>
<a:Code>PK_LIVE_THEMESCUSERENTRY</a:Code>
<a:CreationDate>1585205400</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:ConstraintName>PK_LIVE_THEMESCUSERENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o404"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o409">
<a:ObjectID>D29CAD14-097F-465A-992E-73BFFAC41857</a:ObjectID>
<a:Name>IDX_LIVE_THEMESCUSERENTRY</a:Name>
<a:Code>IDX_LIVE_THEMESCUSERENTRY</a:Code>
<a:CreationDate>1585205320</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o410">
<a:ObjectID>FC5E786F-CB95-4255-858B-A370E691D494</a:ObjectID>
<a:CreationDate>1585205383</a:CreationDate>
<a:Creator>rd_chengyu_tang</a:Creator>
<a:ModificationDate>1591786204</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o405"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o408"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o408"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o411">
<a:ObjectID>1C387779-0F77-4059-9F2D-2D2628F9A642</a:ObjectID>
<a:Name>T_LIVE_XTROBOTMSG_L（云之家消息机器人多语言表）</a:Name>
<a:Code>T_LIVE_XTROBOTMSG_L</a:Code>
<a:CreationDate>1591844106</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o412">
<a:ObjectID>E1333C84-065B-480A-B7DD-2F146A45F806</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1591844106</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o413">
<a:ObjectID>85BD39C9-B726-4830-8066-089EE6DC3D60</a:ObjectID>
<a:Name>父表内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1591844106</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o414">
<a:ObjectID>B7190542-C17B-45DF-957D-3AB15EFE87CF</a:ObjectID>
<a:Name>本地化标志</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1591844106</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o415">
<a:ObjectID>E986D936-22AB-4B92-BBFC-8637C11ACC8C</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1591844106</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>nvarchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o416">
<a:ObjectID>812B7EEB-0F27-4AC4-80F1-9969172F7685</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1591844106</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>nvarchar(510)</a:DataType>
<a:Length>510</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o417">
<a:ObjectID>AE1268EF-FCFD-4FC1-87F5-133C45F532E4</a:ObjectID>
<a:Name>PK_LIVE_XTROBOTMSG_L</a:Name>
<a:Code>PK_LIVE_XTROBOTMSG_L</a:Code>
<a:CreationDate>1591844106</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o412"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o418">
<a:ObjectID>5B7B1347-7640-4528-8CE8-0A922671F81E</a:ObjectID>
<a:Name>IDX_LIVE_XTROBOTMSG_L</a:Name>
<a:Code>IDX_LIVE_XTROBOTMSG_L</a:Code>
<a:CreationDate>1591844399</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o419">
<a:ObjectID>1938AA75-31CA-4233-A9E4-1B953983A02A</a:ObjectID>
<a:CreationDate>1591844486</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o413"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o420">
<a:ObjectID>B563FE76-745B-4439-A7A6-5DE58F23BE9B</a:ObjectID>
<a:CreationDate>1591844486</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095087</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o414"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o417"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o417"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o421">
<a:ObjectID>21D04BCA-F143-4C30-9BB7-0E7AD3FB335B</a:ObjectID>
<a:Name>T_LIVE_XTROBOTMSG(云之家消息机器人定时任务列表)</a:Name>
<a:Code>T_LIVE_XTROBOTMSG</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>云之家消息机器人定时任务列表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o422">
<a:ObjectID>1D968C4E-DF05-4569-BEAC-FE52F6233714</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o423">
<a:ObjectID>5D6B7074-ED49-4549-8A9E-18A7F260A314</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o424">
<a:ObjectID>90A257C6-BD4F-490B-A19F-7B59E22A80A9</a:ObjectID>
<a:Name>单据状态</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o425">
<a:ObjectID>79E4952D-4870-4DC1-B934-92A5185FD7E3</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o426">
<a:ObjectID>F6E80EC6-18F6-41FC-8A89-827AC6AD41C4</a:ObjectID>
<a:Name>修改人</a:Name>
<a:Code>FMODIFIERID</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o427">
<a:ObjectID>6D48D60E-9333-42F6-9C38-D16A07BBF37F</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o428">
<a:ObjectID>C1A48452-6AA2-41A4-B10E-60B5751CD943</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>getdate()</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o429">
<a:ObjectID>A673501B-6785-46EF-B7D3-ABF5596523B5</a:ObjectID>
<a:Name>修改日期</a:Name>
<a:Code>FMODIFYDATE</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>getdate()</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o430">
<a:ObjectID>4C4316A0-E407-4DDA-827E-680469A1C98F</a:ObjectID>
<a:Name>webhook</a:Name>
<a:Code>FWEBHOOK</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(600)</a:DataType>
<a:Length>600</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o431">
<a:ObjectID>632EE25B-E9BC-47DC-B54F-7CA4F8D079BD</a:ObjectID>
<a:Name>开始执行日期</a:Name>
<a:Code>FSTARTTIME</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>getdate()</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o432">
<a:ObjectID>187910F5-7885-4C3C-811B-125B42CBEE38</a:ObjectID>
<a:Name>日期间隔类型</a:Name>
<a:Code>FTIMESPANTYPE</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o433">
<a:ObjectID>37BA590C-7604-4CE4-9B44-88EEA7AAA79B</a:ObjectID>
<a:Name>日期间隔</a:Name>
<a:Code>FTIMESPAN</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o434">
<a:ObjectID>CE890744-4614-48A8-A283-001E3FF7331B</a:ObjectID>
<a:Name>卡片Id</a:Name>
<a:Code>FCARDID</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o435">
<a:ObjectID>443E545D-4FBD-416B-AE72-399E1C374199</a:ObjectID>
<a:Name>消息类型</a:Name>
<a:Code>FMSGTYPE</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o436">
<a:ObjectID>C6FA3508-9025-4DF0-9212-BBAE46756CF8</a:ObjectID>
<a:Name>权限用户</a:Name>
<a:Code>FRIGHTUSERID</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o437">
<a:ObjectID>6A17DCE1-FDDA-44FC-BDB3-54B54E1B3CDD</a:ObjectID>
<a:Name>执行结果</a:Name>
<a:Code>FRESULT</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o438">
<a:ObjectID>BCA5D6E6-CE18-4D0C-B007-7C0418484CDF</a:ObjectID>
<a:Name>最后一次的执行时间</a:Name>
<a:Code>FLASTRUNTIME</a:Code>
<a:CreationDate>1591777335</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o439">
<a:ObjectID>F44B87EA-FE9B-4305-935B-FE01F0D8B6C4</a:ObjectID>
<a:Name>审核人</a:Name>
<a:Code>FAPPROVERID</a:Code>
<a:CreationDate>1592206417</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o440">
<a:ObjectID>B3488C6D-A9EA-40F3-BF6C-60126CEFA9F8</a:ObjectID>
<a:Name>审核日期</a:Name>
<a:Code>FAPPROVEDATE</a:Code>
<a:CreationDate>1592206417</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o441">
<a:ObjectID>46178A02-F17D-47A5-A032-2D0FA00C0876</a:ObjectID>
<a:Name>禁用人</a:Name>
<a:Code>FFORBIDERID</a:Code>
<a:CreationDate>1592206417</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o442">
<a:ObjectID>BC55348E-B2D0-4873-929D-586DA0194D02</a:ObjectID>
<a:Name>禁用日期</a:Name>
<a:Code>FFORBIDDATE</a:Code>
<a:CreationDate>1592206417</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o443">
<a:ObjectID>79E97B3F-4174-434A-AB40-82329CCB5B53</a:ObjectID>
<a:Name>PK_LIVE_XTROBOTMSG</a:Name>
<a:Code>PK_LIVE_XTROBOTMSG</a:Code>
<a:CreationDate>1591777973</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596095091</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o422"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o443"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o443"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o444">
<a:ObjectID>0FC45EC1-2C00-43B8-911A-CBCCB0EC3EF1</a:ObjectID>
<a:Name>T_LIVE_CARDALIASENTRY(小K别名分录表)</a:Name>
<a:Code>T_LIVE_CARDALIASENTRY</a:Code>
<a:CreationDate>1591775864</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096270</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:Comment>别名分录表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o445">
<a:ObjectID>081BBDD2-91D5-4CE5-AF69-306C0FF93C42</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1591775864</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096270</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o446">
<a:ObjectID>E59D1E00-5CA0-4B42-811C-6648E3EDB57F</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1591775864</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096270</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o447">
<a:ObjectID>A3DADB90-57CD-4729-A3D6-D2CB41DF1ED7</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1591775864</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096270</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o448">
<a:ObjectID>596818F6-1E9C-4BE4-99DA-D53CD3CE040B</a:ObjectID>
<a:Name>别名名称</a:Name>
<a:Code>FALIASNAME</a:Code>
<a:CreationDate>1591775864</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096270</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o449">
<a:ObjectID>9652A889-C81A-407D-A8B4-DD0E77AF20E6</a:ObjectID>
<a:Name>PK_LIVE_CARDALIASENTRY</a:Name>
<a:Code>PK_LIVE_CARDALIASENTRY</a:Code>
<a:CreationDate>1591776405</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096270</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o446"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o450">
<a:ObjectID>2304860E-62FA-4A9C-9ABA-640AA6D59199</a:ObjectID>
<a:Name>IDX_LIVE_CARDALIASENTRY</a:Name>
<a:Code>IDX_LIVE_CARDALIASENTRY</a:Code>
<a:CreationDate>1591776992</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096270</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o451">
<a:ObjectID>5F0371EC-1834-43A5-AEF7-C4A36EF01EC7</a:ObjectID>
<a:CreationDate>1591777281</a:CreationDate>
<a:Creator>rd_changjian_huang</a:Creator>
<a:ModificationDate>1596096270</a:ModificationDate>
<a:Modifier>rd_changjian_huang</a:Modifier>
<c:Column>
<o:Column Ref="o445"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o449"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o449"/>
</c:ClusterObject>
</o:Table>
</c:Tables>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:DefaultGroups>
<o:Group Id="o452">
<a:ObjectID>0563AC97-9196-46C0-A6AE-CE83E587A227</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1511503361</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1511503361</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o453">
<a:ObjectID>D711E69F-C4B4-4339-9E2F-2F68B5A07A40</a:ObjectID>
<a:Name>Microsoft SQL Server 2008</a:Name>
<a:Code>MSSQLSRV2008</a:Code>
<a:CreationDate>1511503361</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1515722782</a:ModificationDate>
<a:Modifier>rd_qiupo_chen</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/sqlsv2k8.xdb</a:TargetModelURL>
<a:TargetModelID>F5C20738-B05A-4F70-BC90-9B5EB9437766</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>