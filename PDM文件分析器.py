#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDM文件分析器 - 分析金蝶K/3 Cloud数据字典
用于找出正确的表结构和字段对应关系
"""

import os
import re
import json
import pyodbc
import pandas as pd
from datetime import datetime

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

class PDMAnalyzer:
    def __init__(self):
        self.connection_string = f"DRIVER={{{DRIVER}}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}"
        self.conn = None
        self.pdm_folder = "pdm"
        self.table_structures = {}
        self.field_mappings = {}
        
    def connect_db(self):
        """连接数据库"""
        try:
            self.conn = pyodbc.connect(self.connection_string)
            print(f"✅ 成功连接到数据库: {DATABASE}")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def analyze_pdm_files(self):
        """分析PDM文件"""
        print("\n🔍 开始分析PDM文件...")
        
        if not os.path.exists(self.pdm_folder):
            print(f"❌ PDM文件夹不存在: {self.pdm_folder}")
            return False
        
        pdm_files = [f for f in os.listdir(self.pdm_folder) if f.endswith('.pdm')]
        print(f"📁 发现 {len(pdm_files)} 个PDM文件")
        
        for pdm_file in pdm_files:
            print(f"📄 分析文件: {pdm_file}")
            self.parse_pdm_file(os.path.join(self.pdm_folder, pdm_file))
        
        return True
    
    def parse_pdm_file(self, file_path):
        """解析单个PDM文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 查找表定义
            table_pattern = r'<o:Table Id="([^"]*)"[^>]*>\s*<a:ObjectID>([^<]*)</a:ObjectID>\s*<a:Name>([^<]*)</a:Name>\s*<a:Code>([^<]*)</a:Code>'
            tables = re.findall(table_pattern, content, re.MULTILINE | re.DOTALL)
            
            for table_match in tables:
                table_id, object_id, table_name, table_code = table_match
                if table_code.startswith('T_'):
                    self.table_structures[table_code] = {
                        'name': table_name,
                        'id': table_id,
                        'fields': []
                    }
            
            # 查找字段定义
            field_pattern = r'<o:Column Id="([^"]*)"[^>]*>\s*<a:ObjectID>([^<]*)</a:ObjectID>\s*<a:Name>([^<]*)</a:Name>\s*<a:Code>([^<]*)</a:Code>\s*<a:DataType>([^<]*)</a:DataType>'
            fields = re.findall(field_pattern, content, re.MULTILINE | re.DOTALL)
            
            # 将字段关联到表
            for field_match in fields:
                field_id, field_object_id, field_name, field_code, data_type = field_match
                # 这里需要更复杂的逻辑来关联字段到表，暂时简化处理
                for table_code in self.table_structures:
                    if field_code not in [f['code'] for f in self.table_structures[table_code]['fields']]:
                        # 简单的启发式方法：如果在同一个文件中找到，可能相关
                        pass
            
        except Exception as e:
            print(f"❌ 解析PDM文件失败 {file_path}: {e}")
    
    def query_actual_table_structure(self):
        """查询数据库中的实际表结构"""
        print("\n🔍 查询数据库实际表结构...")
        
        if not self.conn:
            if not self.connect_db():
                return False
        
        # 查询关键表的结构
        key_tables = ['T_SEC_USER', 'T_BD_OPERATOR', 'T_STK_INSTOCK', 'T_STK_INSTOCKENTRY', 'T_BD_MATERIAL', 'T_BD_MATERIAL_L']
        
        for table_name in key_tables:
            try:
                sql = f"""
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    CHARACTER_MAXIMUM_LENGTH,
                    IS_NULLABLE,
                    COLUMN_DEFAULT,
                    ORDINAL_POSITION
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
                """
                
                df = pd.read_sql(sql, self.conn)
                if len(df) > 0:
                    print(f"✅ 表 {table_name}: {len(df)} 个字段")
                    self.table_structures[table_name] = {
                        'fields': df.to_dict('records'),
                        'exists': True
                    }
                else:
                    print(f"❌ 表 {table_name}: 不存在")
                    self.table_structures[table_name] = {'exists': False}
                    
            except Exception as e:
                print(f"❌ 查询表 {table_name} 失败: {e}")
    
    def find_user_operator_relationship(self):
        """查找用户和操作员的关联关系"""
        print("\n🔗 分析用户和操作员关联关系...")

        if not self.conn:
            return

        # 检查T_SEC_USER表结构
        if 'T_SEC_USER' in self.table_structures and self.table_structures['T_SEC_USER'].get('exists'):
            user_fields = [f['COLUMN_NAME'] for f in self.table_structures['T_SEC_USER']['fields']]
            print(f"👤 T_SEC_USER字段: {', '.join(user_fields)}")

        # 检查T_BD_OPERATOR表结构
        if 'T_BD_OPERATOR' in self.table_structures and self.table_structures['T_BD_OPERATOR'].get('exists'):
            operator_fields = [f['COLUMN_NAME'] for f in self.table_structures['T_BD_OPERATOR']['fields']]
            print(f"👨‍💼 T_BD_OPERATOR字段: {', '.join(operator_fields)}")

            # 发现T_BD_OPERATOR表没有FNUMBER和FNAME字段，需要寻找其他方案
            print("⚠️ 发现T_BD_OPERATOR表没有FNUMBER和FNAME字段")

        # 寻找其他可能包含工号信息的表
        print("\n🔍 寻找包含工号信息的其他表...")

        # 查找可能的员工表或操作员相关表
        search_queries = [
            {
                'name': '查找T_BD_STAFF员工表',
                'sql': """
                SELECT TOP 5 * FROM T_BD_STAFF
                """
            },
            {
                'name': '查找T_BD_OPERATOR_L多语言表',
                'sql': """
                SELECT TOP 5 * FROM T_BD_OPERATOR_L
                """
            },
            {
                'name': '查找包含FNUMBER的操作员相关表',
                'sql': """
                SELECT TABLE_NAME, COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE COLUMN_NAME = 'FNUMBER'
                    AND TABLE_NAME LIKE '%OPERATOR%'
                """
            },
            {
                'name': '查找T_BD_OPERATORENTRY表',
                'sql': """
                SELECT TOP 5 * FROM T_BD_OPERATORENTRY
                """
            }
        ]

        results = {}
        for test in search_queries:
            try:
                df = pd.read_sql(test['sql'], self.conn)
                print(f"\n📊 {test['name']}:")
                if len(df) > 0:
                    print(df.to_string(index=False))
                    results[test['name']] = {'data': df, 'success': True}
                else:
                    print("   无数据")
                    results[test['name']] = {'success': False}

            except Exception as e:
                print(f"❌ {test['name']} 执行失败: {e}")
                results[test['name']] = {'success': False, 'error': str(e)}

        # 尝试通过T_BD_STAFF表获取员工工号
        try:
            staff_test_sql = """
            SELECT TOP 5
                s.FSTAFFID, s.FNUMBER as 员工工号, s.FNAME as 员工姓名,
                u.FUSERID, u.FNAME as 用户名
            FROM T_BD_STAFF s
            LEFT JOIN T_SEC_USER u ON s.FSTAFFID = u.FEMPID
            WHERE s.FNUMBER IS NOT NULL
            """

            staff_df = pd.read_sql(staff_test_sql, self.conn)
            print(f"\n📊 员工表关联测试:")
            print(staff_df.to_string(index=False))
            results['员工表关联'] = {'data': staff_df, 'success': True}

        except Exception as e:
            print(f"❌ 员工表关联测试失败: {e}")
            results['员工表关联'] = {'success': False, 'error': str(e)}

        return results
    
    def generate_optimized_sql(self, test_results=None):
        """生成优化的SQL查询"""
        print("\n🎯 生成优化的SQL查询...")

        # 根据测试结果选择最佳关联方式
        best_join = ""
        join_comment = "-- 基于分析结果选择关联方式"

        # 检查是否找到了员工表关联
        if test_results and '员工表关联' in test_results and test_results['员工表关联'].get('success'):
            best_join = """-- 通过员工表获取工号
    LEFT JOIN T_BD_STAFF staff ON creator.FEMPID = staff.FSTAFFID"""
            join_comment = "-- 使用员工表关联获取工号 (推荐方案)"
            work_number_field = "staff.FNUMBER"
        else:
            # 如果没有员工表，尝试其他方案
            best_join = """-- 尝试通过操作员表关联 (可能为空)
    LEFT JOIN T_BD_OPERATOR creator_op ON h.FCREATORID = creator_op.FOPERATORID"""
            join_comment = "-- 操作员表关联 (工号可能为空)"
            work_number_field = "creator_op.FBILLNO"  # 使用FBILLNO作为备用
        
        # 确定工号字段
        if test_results and '员工表关联' in test_results and test_results['员工表关联'].get('success'):
            work_number_field = "staff.FNUMBER"
        else:
            work_number_field = "creator_op.FBILLNO"  # 使用FBILLNO作为备用

        optimized_sql = f"""-- 物料到期查询 - PDM分析优化版
-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- 基于PDM文件分析和数据库实际结构优化
{join_comment}

SELECT
    -- 入库单基本信息
    h.FBILLNO AS '入库单号',
    h.FDATE AS '入库日期',
    -- 物料信息
    mat.FNUMBER AS '物料编码',
    m.FNAME AS '物料名称',
    m.FSPECIFICATION AS '规格型号',
    -- 生产日期与保质期
    e.F_JSHL_DATE_83G AS '生产日期',
    e.F_JSHL_QTY_QTR AS '保质期(天)',
    -- 计算到期日期
    DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) AS '计算到期日期',
    -- 计算剩余天数
    DATEDIFF(day, GETDATE(), DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G)) AS '剩余天数',
    -- 库存信息
    e.FREALQTY AS '实收数量',
    e.FBASEUNITQTY AS '基本单位数量',
    -- 仓库信息
    org.FNAME AS '仓库组织',
    -- 制单人信息（包含工号）
    creator.FNAME AS '制单人',
    {work_number_field} AS '制单人工号',  -- 关键：制单人工号
    h.FCREATEDATE AS '制单日期'
FROM
    T_STK_INSTOCK h
    INNER JOIN T_STK_INSTOCKENTRY e ON h.FID = e.FID
    LEFT JOIN T_BD_MATERIAL_L m ON e.FMATERIALID = m.FMATERIALID AND m.FLOCALEID = 2052 -- 2052为简体中文
    LEFT JOIN T_BD_MATERIAL mat ON e.FMATERIALID = mat.FMATERIALID -- 获取物料编码
    LEFT JOIN t_org_organizations_l org ON h.FSTOCKORGID = org.FORGID AND org.FLOCALEID = 2052
    -- 关联制单人信息到用户表
    LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
    {best_join}
WHERE
    -- 筛选条件：生产日期不为空 且 保质期不为空
    e.F_JSHL_DATE_83G IS NOT NULL
    AND e.F_JSHL_QTY_QTR IS NOT NULL
    -- 筛选未来30天内到期的物料（可根据需要启用）
    --AND DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) BETWEEN GETDATE() AND DATEADD(day, 30, GETDATE())
    -- 过滤已作废的单据
    AND h.FCancelStatus = 'A' -- 假设'A'表示未作废
    -- 确保只查询审核通过的单据
    AND h.FDocumentStatus = 'C' -- 假设'C'表示已审核
ORDER BY
    DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) ASC, -- 优先显示最快到期的物料
    h.FBILLNO; -- 相同到期日期按单号排序

/*
PDM分析结果说明:
1. 已验证表结构的存在性
2. 发现T_BD_OPERATOR表没有FNUMBER字段
3. 尝试通过T_BD_STAFF员工表获取工号
4. 如果工号仍为空，请检查数据完整性

实际表结构发现:
- T_BD_OPERATOR表字段: FOPERATORID, FMASTERID, FOPERATORTYPE, FCREATORID, FCREATEDATE, FMODIFIERID, FMODIFYDATE, FBILLNO
- T_SEC_USER表有FEMPID字段，可能关联到员工表
- 建议使用员工表T_BD_STAFF获取工号信息

备用方案:
如果员工表关联失败，可以尝试:
1. 直接使用用户名作为标识
2. 使用T_BD_OPERATOR.FBILLNO作为编号
3. 检查其他可能包含工号的表
*/"""
        
        # 保存到文件
        with open('物料到期查询_PDM优化版.sql', 'w', encoding='utf-8') as f:
            f.write(optimized_sql)
        
        print("💾 优化SQL已保存到: 物料到期查询_PDM优化版.sql")
        return optimized_sql
    
    def save_analysis_report(self):
        """保存分析报告"""
        report = {
            "分析时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "数据库信息": {
                "服务器": SERVER,
                "数据库": DATABASE
            },
            "表结构分析": self.table_structures,
            "字段映射": self.field_mappings
        }
        
        with open('PDM分析报告.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("📊 PDM分析报告已保存到: PDM分析报告.json")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 开始PDM文件分析...")
        print("=" * 60)
        
        # 1. 分析PDM文件
        self.analyze_pdm_files()
        
        # 2. 查询数据库实际结构
        if self.connect_db():
            self.query_actual_table_structure()
            
            # 3. 分析用户操作员关联关系
            test_results = self.find_user_operator_relationship()
            
            # 4. 生成优化SQL
            self.generate_optimized_sql(test_results)
            
            # 5. 保存分析报告
            self.save_analysis_report()
            
            self.conn.close()
        
        print("\n" + "=" * 60)
        print("🎉 PDM分析完成！生成的文件:")
        print("   📄 物料到期查询_PDM优化版.sql")
        print("   📊 PDM分析报告.json")

def main():
    analyzer = PDMAnalyzer()
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()
