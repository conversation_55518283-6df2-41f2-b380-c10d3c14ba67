-- 分析PDM文件中的表结构和字段信息
-- 用于总结金蝶K/3 Cloud数据库结构

-- 1. 查询所有表的基本信息
SELECT 
    TABLE_CATALOG AS '数据库名',
    TABLE_SCHEMA AS '架构',
    TABLE_NAME AS '表名',
    TABLE_TYPE AS '表类型'
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_TYPE = 'BASE TABLE'
    AND TABLE_NAME LIKE 'T_%'  -- 金蝶表通常以T_开头
ORDER BY TABLE_NAME;

-- 2. 查询所有字段的详细信息
SELECT 
    t.TABLE_NAME AS '表名',
    c.COLUMN_NAME AS '字段名',
    c.DATA_TYPE AS '数据类型',
    c.CHARACTER_MAXIMUM_LENGTH AS '最大长度',
    c.NUMERIC_PRECISION AS '数值精度',
    c.NUMERIC_SCALE AS '小数位数',
    c.IS_NULLABLE AS '是否可空',
    c.COLUMN_DEFAULT AS '默认值',
    CASE 
        WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PK'
        WHEN fk.COLUMN_NAME IS NOT NULL THEN 'FK'
        ELSE ''
    END AS '键类型'
FROM INFORMATION_SCHEMA.TABLES t
INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
LEFT JOIN (
    SELECT ku.TABLE_NAME, ku.COLUMN_NAME
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
    INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
    WHERE tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
) fk ON c.TABLE_NAME = fk.TABLE_NAME AND c.COLUMN_NAME = fk.COLUMN_NAME
WHERE t.TABLE_TYPE = 'BASE TABLE'
    AND t.TABLE_NAME LIKE 'T_%'
ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION;

-- 3. 专门查询用户和操作员相关表
SELECT 
    t.TABLE_NAME AS '表名',
    c.COLUMN_NAME AS '字段名',
    c.DATA_TYPE AS '数据类型',
    c.CHARACTER_MAXIMUM_LENGTH AS '最大长度',
    c.IS_NULLABLE AS '是否可空'
FROM INFORMATION_SCHEMA.TABLES t
INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
WHERE t.TABLE_TYPE = 'BASE TABLE'
    AND (t.TABLE_NAME LIKE '%USER%' 
         OR t.TABLE_NAME LIKE '%OPERATOR%'
         OR t.TABLE_NAME LIKE '%BD_OPERATOR%'
         OR t.TABLE_NAME LIKE '%SEC_USER%')
ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION;

-- 4. 查询包含FNUMBER字段的所有表（工号相关）
SELECT 
    TABLE_NAME AS '表名',
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型',
    CHARACTER_MAXIMUM_LENGTH AS '最大长度'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE COLUMN_NAME = 'FNUMBER'
    AND TABLE_NAME LIKE 'T_%'
ORDER BY TABLE_NAME;

-- 5. 查询包含FUSERID字段的所有表（用户ID关联）
SELECT 
    TABLE_NAME AS '表名',
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE COLUMN_NAME LIKE '%USERID%'
    AND TABLE_NAME LIKE 'T_%'
ORDER BY TABLE_NAME;

-- 6. 查询包含FOPERATORID字段的所有表（操作员ID关联）
SELECT 
    TABLE_NAME AS '表名',
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE COLUMN_NAME LIKE '%OPERATOR%'
    AND TABLE_NAME LIKE 'T_%'
ORDER BY TABLE_NAME;

-- 7. 查询外键关系
SELECT 
    fk.TABLE_NAME AS '子表',
    fk.COLUMN_NAME AS '外键字段',
    pk.TABLE_NAME AS '父表',
    pk.COLUMN_NAME AS '主键字段',
    rc.CONSTRAINT_NAME AS '约束名'
FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE fk ON rc.CONSTRAINT_NAME = fk.CONSTRAINT_NAME
INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE pk ON rc.UNIQUE_CONSTRAINT_NAME = pk.CONSTRAINT_NAME
WHERE fk.TABLE_NAME LIKE 'T_%'
ORDER BY fk.TABLE_NAME, fk.COLUMN_NAME;

-- 8. 查询入库相关表的结构
SELECT 
    t.TABLE_NAME AS '表名',
    c.COLUMN_NAME AS '字段名',
    c.DATA_TYPE AS '数据类型',
    c.CHARACTER_MAXIMUM_LENGTH AS '最大长度'
FROM INFORMATION_SCHEMA.TABLES t
INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
WHERE t.TABLE_TYPE = 'BASE TABLE'
    AND (t.TABLE_NAME LIKE '%INSTOCK%' 
         OR t.TABLE_NAME LIKE '%STK_%')
ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION;
