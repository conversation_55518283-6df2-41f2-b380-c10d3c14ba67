-- 客户欠款数据插入SQL
-- 插入2025-04-30的客户欠款数据

INSERT INTO T_CUSTOMER_DEBT (F日期, F客户名称, F当前欠款) VALUES
('2025-04-30', '杭州诚营农业发展有限公司', -200.00),
('2025-04-30', '杭州方帅农业开发有限公司', -12296.00),
('2025-04-30', '杭州农副产品物流中心良渚蔬菜批发市场荣叶蔬菜商行', 316.00),
('2025-04-30', '杭州萧山中正菌业批发部', 35276.00),
('2025-04-30', '杭州远润农产品有限公司', 0.00),
('2025-04-30', '杭州琦丰农业发展有限公司', 30420.00),
('2025-04-30', '嘉兴市闻川旅行社有限公司', -4310.00),
('2025-04-30', '宁波市鄞州中河锦强食用农产品店', 0.00),
('2025-04-30', '平湖市吴树叶蔬菜批发部', -8446.00),
('2025-04-30', '平湖市轶轶食用菌专业合作社', 8415.00),
('2025-04-30', '衢州市益美农产品有限公司', 4.00),
('2025-04-30', '泉州市振祥农业科技有限公司', 0.00),
('2025-04-30', '台州市椒江鑫润农产品有限公司', 0.00),
('2025-04-30', '上海德添农产品有限公司', 0.00),
('2025-04-30', '上海永大菌业有限公司', 0.00),
('2025-04-30', '温州市古逸农业科技有限公司', 49395.00),
('2025-04-30', '义乌市农业开发有限公司', 0.00),
('2025-04-30', '义乌市旭珍蔬菜商行', -10088.00),
('2025-04-30', '浙江庆元尚亿农业有限公司', -39575.00),
('2025-04-30', '海盐县元通街道益群副食品店', 0.00),
('2025-04-30', '重庆多人行供应链有限公司', 0.00),
('2025-04-30', '福建邹氏农业发展有限公司', 0.00),
('2025-04-30', '漳州市振鑫楼果蔬有限公司', 0.00),
('2025-04-30', '江西郭先生蔬菜有限公司', 0.00),
('2025-04-30', '青云谱区昌泰农副产品营业部(个体工商户)', 0.00),
('2025-04-30', '赣州国晨农业发展有限公司', 0.00),
('2025-04-30', '江苏新祺欣农业科技有限公司', 0.00),
('2025-04-30', '江苏口口溜食品有限公司', 0.00);

-- 验证插入结果
SELECT
    F日期,
    F客户名称,
    FORMAT(F当前欠款, 'C', 'zh-CN') AS '格式化欠款金额',
    F当前欠款
FROM T_CUSTOMER_DEBT
WHERE F日期 = '2025-04-30'
ORDER BY F当前欠款 DESC;

-- 统计信息
SELECT
    COUNT(*) AS '总客户数',
    SUM(CASE WHEN F当前欠款 > 0 THEN 1 ELSE 0 END) AS '有欠款客户数',
    SUM(CASE WHEN F当前欠款 < 0 THEN 1 ELSE 0 END) AS '预付款客户数',
    SUM(CASE WHEN F当前欠款 = 0 THEN 1 ELSE 0 END) AS '无欠款客户数',
    SUM(F当前欠款) AS '总欠款金额'
FROM T_CUSTOMER_DEBT
WHERE F日期 = '2025-04-30';
