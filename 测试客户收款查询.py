import pyodbc
import pandas as pd

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

def connect_to_database():
    """连接到SQL Server数据库"""
    connection_string = f"""
    DRIVER={{{DRIVER}}};
    SERVER={SERVER};
    DATABASE={DATABASE};
    UID={USERNAME};
    PWD={PASSWORD};
    TrustServerCertificate=yes;
    """
    try:
        conn = pyodbc.connect(connection_string)
        print("数据库连接成功！")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def main():
    conn = connect_to_database()
    if not conn:
        return
    
    try:
        # 执行最终的客户收款单汇总查询
        print("=== 客户收款单汇总查询结果 ===")
        final_query = """
        SELECT 
            c_l.FNAME AS '客户名称',
            org_l.FNAME AS '收款组织',
            CONVERT(varchar(10), r.<PERSON>, 120) AS '收款日期',
            SUM(DISTINCT re.FSETTLERECAMOUNT) AS '收款金额'
        FROM 
            T_AR_RECEIVEBILL r                                    -- 收款单主表
            INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID -- 收款单分录表
            LEFT JOIN T_AR_RECEIVEBILLSRCENTRY src ON r.FID = src.FID  -- 收款单源单据表
            LEFT JOIN T_AR_RECEIVABLE ar ON src.FSRCBILLNO = ar.FBILLNO  -- 通过源单据号关联应收单
            LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID      -- 关联客户主表
            LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052  -- 客户多语言表(中文)
            LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID  -- 关联组织表
            LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052  -- 组织多语言表(中文)
        WHERE 
            r.FDOCUMENTSTATUS = 'C'                              -- 已审核单据
            AND r.FCANCELSTATUS = 'A'                            -- 未作废
            AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'  -- 2025年5月1日之后
            AND c_l.FNAME IS NOT NULL                            -- 确保有客户名称
            AND org_l.FNAME IS NOT NULL                          -- 确保有组织名称
        GROUP BY 
            c_l.FNAME,
            org_l.FNAME, 
            CONVERT(varchar(10), r.FDATE, 120),
            r.FID,
            re.FENTRYID
        ORDER BY 
            CONVERT(varchar(10), r.FDATE, 120) DESC,             -- 按日期倒序
            c_l.FNAME,                                           -- 按客户名称排序
            org_l.FNAME;                                         -- 按组织名称排序
        """
        
        final_df = pd.read_sql(final_query, conn)
        print(f"查询结果总数: {len(final_df)} 条记录")
        print("\n前30条记录:")
        print(final_df.head(30))
        
        # 统计信息
        print(f"\n=== 统计信息 ===")
        print(f"涉及客户数量: {final_df['客户名称'].nunique()}")
        print(f"涉及组织数量: {final_df['收款组织'].nunique()}")
        print(f"日期范围: {final_df['收款日期'].min()} 到 {final_df['收款日期'].max()}")
        print(f"总收款金额: {final_df['收款金额'].sum():,.2f}")
        
        print(f"\n各客户收款汇总:")
        customer_summary = final_df.groupby('客户名称')['收款金额'].sum().sort_values(ascending=False)
        print(customer_summary)
        
        print(f"\n各组织收款汇总:")
        org_summary = final_df.groupby('收款组织')['收款金额'].sum().sort_values(ascending=False)
        print(org_summary)
        
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
