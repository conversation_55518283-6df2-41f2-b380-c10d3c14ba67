2025-05-28 16:52:21,381 - INFO - 开始运行完整的物料到期分析...
2025-05-28 16:52:21,444 - INFO - 数据库连接成功
2025-05-28 16:52:21,444 - INFO - 开始分析制单人工号映射问题...
2025-05-28 16:52:21,457 - ERROR - 分析查询执行失败: Execution failed on sql '
        SELECT DISTINCT
            h.FCREATOR<PERSON>,
            creator.FNAME AS '制单人姓名',
            creator.FUSERACCOUNT AS '用户账号',
            op.FOPERATORID,
            op.FNAME AS '操作员姓名',
            COUNT(*) AS '入库单数量'
        FROM T_STK_INSTOCK h
        LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
        LEFT JOIN T_BD_OPERATOR op ON h.FCREATORID = op.FOPERATORID
        WHERE h.FCancelStatus = 'A' AND h.FDocumentStatus = 'C'
        GROUP BY h.<PERSON><PERSON><PERSON><PERSON><PERSON>, creator.<PERSON><PERSON><PERSON>, creator.FUSERACCOUNT, op.FOPERATORID, op.FNAME
        ORDER BY COUNT(*) DESC
        ': ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207)")
2025-05-28 16:52:21,464 - INFO - 查找替代的员工工号映射方案...
2025-05-28 16:52:21,473 - ERROR - 替代方案查询失败: Execution failed on sql '
        SELECT DISTINCT
            u.FUSERID,
            u.FNAME AS '用户姓名',
            u.FUSERACCOUNT AS '用户账号',
            s.FNUMBER AS '员工工号',
            s.FNAME AS '员工姓名'
        FROM T_SEC_USER u
        LEFT JOIN T_BD_STAFF s ON u.FUSERACCOUNT = s.FNUMBER
            OR u.FNAME = s.FNAME
        WHERE u.FUSERID IN (
            SELECT DISTINCT h.FCREATORID 
            FROM T_STK_INSTOCK h 
            WHERE h.FCancelStatus = 'A' AND h.FDocumentStatus = 'C'
        )
        ORDER BY u.FNAME
        ': ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207)")
2025-05-28 16:52:21,474 - INFO - 执行物料到期查询...
2025-05-28 16:52:21,474 - INFO - 生成优化查询，策略: multi_fallback
2025-05-28 16:52:21,486 - ERROR - 查询执行失败: Execution failed on sql '
        -- 物料到期查询 - 优化版（解决制单人工号问题）
        SELECT
            -- 入库单基本信息
            h.FBILLNO AS '入库单号',
            h.FDATE AS '入库日期',
            -- 物料信息
            mat.FNUMBER AS '物料编码',
            m.FNAME AS '物料名称',
            m.FSPECIFICATION AS '规格型号',
            -- 生产日期与保质期
            e.F_JSHL_DATE_83G AS '生产日期',
            e.F_JSHL_QTY_QTR AS '保质期(天)',
            -- 计算到期日期
            DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) AS '计算到期日期',
            -- 计算剩余天数
            DATEDIFF(day, GETDATE(), DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G)) AS '剩余天数',
            -- 库存信息
            e.FREALQTY AS '实收数量',
            e.FBASEUNITQTY AS '基本单位数量',
            -- 仓库信息
            org.FNAME AS '仓库组织',
            -- 制单人信息
            creator.FNAME AS '制单人',
            -- 制单人工号（优化的多重备用方案）
            
            COALESCE(
                staff_from_op.FNUMBER,           -- 方案1：操作员分录关联员工工号
                staff_direct.FNUMBER,            -- 方案2：用户账号直接匹配员工工号
                staff_name.FNUMBER,              -- 方案3：用户姓名匹配员工姓名
                person_direct.FNUMBER,           -- 方案4：用户账号匹配人员编号
                person_name.FNUMBER,             -- 方案5：用户姓名匹配人员姓名
                creator.FUSERACCOUNT,            -- 方案6：用户账号作为备用
                CAST(creator.FUSERID AS NVARCHAR(50))  -- 方案7：用户ID作为最后备用
            ) AS '制单人工号',
            h.FCREATEDATE AS '制单日期'
        FROM
            T_STK_INSTOCK h
            INNER JOIN T_STK_INSTOCKENTRY e ON h.FID = e.FID
            LEFT JOIN T_BD_MATERIAL_L m ON e.FMATERIALID = m.FMATERIALID AND m.FLOCALEID = 2052
            LEFT JOIN T_BD_MATERIAL mat ON e.FMATERIALID = mat.FMATERIALID
            LEFT JOIN t_org_organizations_l org ON h.FSTOCKORGID = org.FORGID AND org.FLOCALEID = 2052
            
            -- 制单人用户信息
            LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
            -- 方案1：通过操作员分录关联员工
            LEFT JOIN T_BD_OPERATORENTRY op_entry ON h.FCREATORID = op_entry.FOPERATORID
                AND op_entry.FISUSE = 1
            LEFT JOIN T_BD_STAFF staff_from_op ON op_entry.FSTAFFID = staff_from_op.FSTAFFID
            -- 方案2：用户账号直接匹配员工工号
            LEFT JOIN T_BD_STAFF staff_direct ON creator.FUSERACCOUNT = staff_direct.FNUMBER
            -- 方案3：用户姓名匹配员工姓名
            LEFT JOIN T_BD_STAFF staff_name ON creator.FNAME = staff_name.FNAME
            -- 方案4：用户账号匹配人员编号
            LEFT JOIN T_BD_PERSON person_direct ON creator.FUSERACCOUNT = person_direct.FNUMBER
            -- 方案5：用户姓名匹配人员姓名
            LEFT JOIN T_BD_PERSON person_name ON creator.FNAME = person_name.FNAME
        WHERE
            -- 筛选条件：生产日期不为空 且 保质期不为空
            e.F_JSHL_DATE_83G IS NOT NULL
            AND e.F_JSHL_QTY_QTR IS NOT NULL
            -- 过滤已作废的单据
            AND h.FCancelStatus = 'A'
            -- 确保只查询审核通过的单据
            AND h.FDocumentStatus = 'C'
        ORDER BY
            DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) ASC,
            h.FBILLNO;
        ': ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207)")
2025-05-28 16:52:21,494 - INFO - 数据库连接已关闭
2025-05-28 16:52:31,729 - INFO - 开始运行完整的物料到期分析...
2025-05-28 16:52:31,802 - INFO - 数据库连接成功
2025-05-28 16:52:31,802 - INFO - 开始分析制单人工号映射问题...
2025-05-28 16:52:31,818 - ERROR - 分析查询执行失败: Execution failed on sql '
        SELECT DISTINCT
            h.FCREATORID,
            creator.FNAME AS '制单人姓名',
            creator.FUSERACCOUNT AS '用户账号',
            op.FOPERATORID,
            op.FNAME AS '操作员姓名',
            COUNT(*) AS '入库单数量'
        FROM T_STK_INSTOCK h
        LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
        LEFT JOIN T_BD_OPERATOR op ON h.FCREATORID = op.FOPERATORID
        WHERE h.FCancelStatus = 'A' AND h.FDocumentStatus = 'C'
        GROUP BY h.FCREATORID, creator.FNAME, creator.FUSERACCOUNT, op.FOPERATORID, op.FNAME
        ORDER BY COUNT(*) DESC
        ': ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207)")
2025-05-28 16:52:31,819 - INFO - 查找替代的员工工号映射方案...
2025-05-28 16:52:31,831 - ERROR - 替代方案查询失败: Execution failed on sql '
        SELECT DISTINCT
            u.FUSERID,
            u.FNAME AS '用户姓名',
            u.FUSERACCOUNT AS '用户账号',
            s.FNUMBER AS '员工工号',
            s.FNAME AS '员工姓名'
        FROM T_SEC_USER u
        LEFT JOIN T_BD_STAFF s ON u.FUSERACCOUNT = s.FNUMBER
            OR u.FNAME = s.FNAME
        WHERE u.FUSERID IN (
            SELECT DISTINCT h.FCREATORID 
            FROM T_STK_INSTOCK h 
            WHERE h.FCancelStatus = 'A' AND h.FDocumentStatus = 'C'
        )
        ORDER BY u.FNAME
        ': ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207)")
2025-05-28 16:52:31,831 - INFO - 执行物料到期查询...
2025-05-28 16:52:31,832 - INFO - 生成优化查询，策略: multi_fallback
2025-05-28 16:52:31,843 - ERROR - 查询执行失败: Execution failed on sql '
        -- 物料到期查询 - 优化版（解决制单人工号问题）
        SELECT
            -- 入库单基本信息
            h.FBILLNO AS '入库单号',
            h.FDATE AS '入库日期',
            -- 物料信息
            mat.FNUMBER AS '物料编码',
            m.FNAME AS '物料名称',
            m.FSPECIFICATION AS '规格型号',
            -- 生产日期与保质期
            e.F_JSHL_DATE_83G AS '生产日期',
            e.F_JSHL_QTY_QTR AS '保质期(天)',
            -- 计算到期日期
            DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) AS '计算到期日期',
            -- 计算剩余天数
            DATEDIFF(day, GETDATE(), DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G)) AS '剩余天数',
            -- 库存信息
            e.FREALQTY AS '实收数量',
            e.FBASEUNITQTY AS '基本单位数量',
            -- 仓库信息
            org.FNAME AS '仓库组织',
            -- 制单人信息
            creator.FNAME AS '制单人',
            -- 制单人工号（优化的多重备用方案）
            
            COALESCE(
                staff_from_op.FNUMBER,           -- 方案1：操作员分录关联员工工号
                staff_direct.FNUMBER,            -- 方案2：用户账号直接匹配员工工号
                staff_name.FNUMBER,              -- 方案3：用户姓名匹配员工姓名
                person_direct.FNUMBER,           -- 方案4：用户账号匹配人员编号
                person_name.FNUMBER,             -- 方案5：用户姓名匹配人员姓名
                creator.FUSERACCOUNT,            -- 方案6：用户账号作为备用
                CAST(creator.FUSERID AS NVARCHAR(50))  -- 方案7：用户ID作为最后备用
            ) AS '制单人工号',
            h.FCREATEDATE AS '制单日期'
        FROM
            T_STK_INSTOCK h
            INNER JOIN T_STK_INSTOCKENTRY e ON h.FID = e.FID
            LEFT JOIN T_BD_MATERIAL_L m ON e.FMATERIALID = m.FMATERIALID AND m.FLOCALEID = 2052
            LEFT JOIN T_BD_MATERIAL mat ON e.FMATERIALID = mat.FMATERIALID
            LEFT JOIN t_org_organizations_l org ON h.FSTOCKORGID = org.FORGID AND org.FLOCALEID = 2052
            
            -- 制单人用户信息
            LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
            -- 方案1：通过操作员分录关联员工
            LEFT JOIN T_BD_OPERATORENTRY op_entry ON h.FCREATORID = op_entry.FOPERATORID
                AND op_entry.FISUSE = 1
            LEFT JOIN T_BD_STAFF staff_from_op ON op_entry.FSTAFFID = staff_from_op.FSTAFFID
            -- 方案2：用户账号直接匹配员工工号
            LEFT JOIN T_BD_STAFF staff_direct ON creator.FUSERACCOUNT = staff_direct.FNUMBER
            -- 方案3：用户姓名匹配员工姓名
            LEFT JOIN T_BD_STAFF staff_name ON creator.FNAME = staff_name.FNAME
            -- 方案4：用户账号匹配人员编号
            LEFT JOIN T_BD_PERSON person_direct ON creator.FUSERACCOUNT = person_direct.FNUMBER
            -- 方案5：用户姓名匹配人员姓名
            LEFT JOIN T_BD_PERSON person_name ON creator.FNAME = person_name.FNAME
        WHERE
            -- 筛选条件：生产日期不为空 且 保质期不为空
            e.F_JSHL_DATE_83G IS NOT NULL
            AND e.F_JSHL_QTY_QTR IS NOT NULL
            -- 过滤已作废的单据
            AND h.FCancelStatus = 'A'
            -- 确保只查询审核通过的单据
            AND h.FDocumentStatus = 'C'
        ORDER BY
            DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) ASC,
            h.FBILLNO;
        ': ('42S22', "[42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]列名 'FNAME' 无效。 (207)")
2025-05-28 16:52:31,848 - INFO - 数据库连接已关闭
2025-05-28 16:57:54,717 - INFO - 开始运行完整的物料到期分析...
2025-05-28 16:57:54,770 - INFO - 数据库连接成功
2025-05-28 16:57:54,771 - INFO - 开始分析制单人工号映射问题...
2025-05-28 16:57:54,890 - INFO - 制单人与操作员关联分析:
2025-05-28 16:57:54,981 - INFO - 操作员与员工关联分析:
2025-05-28 16:57:55,003 - INFO - 查找替代的员工工号映射方案...
2025-05-28 16:57:55,156 - INFO - 执行物料到期查询...
2025-05-28 16:57:55,156 - INFO - 生成优化查询，策略: multi_fallback
2025-05-28 16:57:55,361 - INFO - 查询完成，共获取 50 条记录
2025-05-28 16:57:55,557 - INFO - 结果已导出到: 物料到期查询结果_20250528_165755.xlsx
2025-05-28 16:57:55,566 - INFO - 数据库连接已关闭
