-- 客户欠款查询表创建SQL
-- 简化版本：只包含日期、客户名称、当前欠款三个字段

-- =====================================================
-- 客户欠款表 (T_CUSTOMER_DEBT)
-- =====================================================
CREATE TABLE T_CUSTOMER_DEBT (
    FID BIGINT IDENTITY(1,1) PRIMARY KEY,               -- 主键ID (自增长)
    F日期 DATE NOT NULL,                                 -- 日期
    F客户名称 NVARCHAR(255) NOT NULL,                     -- 客户名称
    F业务员 NVARCHAR(100) NULL,                          -- 业务员
    F当前欠款 DECIMAL(18,2) NOT NULL DEFAULT 0           -- 当前欠款金额
);

-- =====================================================
-- 创建索引以提高查询性能
-- =====================================================
CREATE INDEX IX_T_CUSTOMER_DEBT_DATE ON T_CUSTOMER_DEBT(F日期);
CREATE INDEX IX_T_CUSTOMER_DEBT_CUSTOMER ON T_CUSTOMER_DEBT(F客户名称);
CREATE INDEX IX_T_CUSTOMER_DEBT_SALESMAN ON T_CUSTOMER_DEBT(F业务员);
CREATE INDEX IX_T_CUSTOMER_DEBT_AMOUNT ON T_CUSTOMER_DEBT(F当前欠款);

