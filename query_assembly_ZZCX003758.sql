-- 查询组装拆卸单号 ZZCX003758 的完整信息
-- 包含主表、成品表、子件表的详细信息

-- 1. 主表信息
SELECT 
    '主表信息' AS 表类型,
    a.FID AS 内码,
    a.FBillNo AS 单据编号,
    a.FBillTypeID AS 单据类型,
    a.FDate AS 日期,
    a.FAffairType AS 事务类型,
    a.Fee AS 费用,
    a.FStockOrgId AS 库存组织,
    a.FDeptID AS 部门,
    a.FSTOCKERGROUPID AS 库存组,
    a.FSTOCKERID AS 仓管员,
    a.FDocumentStatus AS 状态,
    a.FCreatorID AS 创建人,
    a.FCreateDate AS 创建日期,
    a.FModifierID AS 最后修改人,
    a.FModifyDate AS 最后修改日期,
    a.FAPPROVERID AS 审核人,
    a.FAPPROVEDATE AS 审核日期,
    a.FCancelStatus AS 作废状态,
    a.FNote AS 备注,
    a.FOwnerTypeId AS 成品货主类型,
    a.FOwnerId AS 成品货主,
    a.FSUBPROOWNTYPEIDH AS 子件货主类型,
    a.FSUBPROOWNERIDH AS 子件货主,
    NULL AS 分录内码,
    NULL AS 行号,
    NULL AS 行类型,
    NULL AS 物料ID,
    NULL AS 数量,
    NULL AS 单位ID,
    NULL AS 仓库ID,
    NULL AS 金额,
    NULL AS 父项分录内码
FROM T_STK_ASSEMBLY a
WHERE a.FBillNo = 'ZZCX003758'

UNION ALL

-- 2. 成品表信息
SELECT 
    '成品表信息' AS 表类型,
    p.FID AS 内码,
    a.FBillNo AS 单据编号,
    a.FBillTypeID AS 单据类型,
    a.FDate AS 日期,
    a.FAffairType AS 事务类型,
    p.Fee AS 费用,
    a.FStockOrgId AS 库存组织,
    a.FDeptID AS 部门,
    a.FSTOCKERGROUPID AS 库存组,
    a.FSTOCKERID AS 仓管员,
    a.FDocumentStatus AS 状态,
    a.FCreatorID AS 创建人,
    a.FCreateDate AS 创建日期,
    a.FModifierID AS 最后修改人,
    a.FModifyDate AS 最后修改日期,
    a.FAPPROVERID AS 审核人,
    a.FAPPROVEDATE AS 审核日期,
    a.FCancelStatus AS 作废状态,
    COALESCE(p.FDescription, a.FNote) AS 备注,
    p.FOwnerTypeID AS 成品货主类型,
    p.FOwnerID AS 成品货主,
    a.FSUBPROOWNTYPEIDH AS 子件货主类型,
    a.FSUBPROOWNERIDH AS 子件货主,
    p.FEntryID AS 分录内码,
    p.FSeq AS 行号,
    p.FRowType AS 行类型,
    p.FMaterialID AS 物料ID,
    p.FQty AS 数量,
    p.FUnitID AS 单位ID,
    p.FStockID AS 仓库ID,
    p.FAMOUNT AS 金额,
    NULL AS 父项分录内码
FROM T_STK_ASSEMBLY a
INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
WHERE a.FBillNo = 'ZZCX003758'

UNION ALL

-- 3. 子件表信息
SELECT 
    '子件表信息' AS 表类型,
    s.FDetailID AS 内码,
    a.FBillNo AS 单据编号,
    a.FBillTypeID AS 单据类型,
    a.FDate AS 日期,
    a.FAffairType AS 事务类型,
    a.Fee AS 费用,
    a.FStockOrgId AS 库存组织,
    a.FDeptID AS 部门,
    a.FSTOCKERGROUPID AS 库存组,
    a.FSTOCKERID AS 仓管员,
    a.FDocumentStatus AS 状态,
    a.FCreatorID AS 创建人,
    a.FCreateDate AS 创建日期,
    a.FModifierID AS 最后修改人,
    a.FModifyDate AS 最后修改日期,
    a.FAPPROVERID AS 审核人,
    a.FAPPROVEDATE AS 审核日期,
    a.FCancelStatus AS 作废状态,
    COALESCE(s.FDescription, a.FNote) AS 备注,
    s.FOwnerTypeID AS 成品货主类型,
    s.FOwnerID AS 成品货主,
    a.FSUBPROOWNTYPEIDH AS 子件货主类型,
    a.FSUBPROOWNERIDH AS 子件货主,
    s.FDetailID AS 分录内码,
    s.FSeq AS 行号,
    s.FRowType AS 行类型,
    s.FMaterialID AS 物料ID,
    s.FQty AS 数量,
    s.FUnitID AS 单位ID,
    s.FStockID AS 仓库ID,
    s.FAMOUNT AS 金额,
    s.FEntryID AS 父项分录内码
FROM T_STK_ASSEMBLY a
INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
INNER JOIN T_STK_ASSEMBLYSUBITEM s ON p.FEntryID = s.FEntryID
WHERE a.FBillNo = 'ZZCX003758'

ORDER BY 表类型, 分录内码, 行号;


-- 如果你只需要简化的查询，可以使用以下SQL：

/*
-- 简化查询 - 只查询主要信息
SELECT 
    a.FBillNo AS 单据编号,
    a.FDate AS 日期,
    a.FAffairType AS 事务类型,
    a.FDocumentStatus AS 状态,
    a.FNote AS 备注,
    p.FSeq AS 成品行号,
    p.FMaterialID AS 成品物料ID,
    p.FQty AS 成品数量,
    s.FSeq AS 子件行号,
    s.FMaterialID AS 子件物料ID,
    s.FQty AS 子件数量
FROM T_STK_ASSEMBLY a
LEFT JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
LEFT JOIN T_STK_ASSEMBLYSUBITEM s ON p.FEntryID = s.FEntryID
WHERE a.FBillNo = 'ZZCX003758'
ORDER BY p.FSeq, s.FSeq;
*/
