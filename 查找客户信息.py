import pyodbc
import pandas as pd

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

def connect_to_database():
    """连接到SQL Server数据库"""
    connection_string = f"""
    DRIVER={{{DRIVER}}};
    SERVER={SERVER};
    DATABASE={DATABASE};
    UID={USERNAME};
    PWD={PASSWORD};
    TrustServerCertificate=yes;
    """
    try:
        conn = pyodbc.connect(connection_string)
        print("数据库连接成功！")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def main():
    conn = connect_to_database()
    if not conn:
        return
    
    try:
        # 1. 查看收款单主表的完整字段，寻找可能的客户字段
        print("=== 1. 收款单主表完整字段列表 ===")
        main_fields_query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILL'
        ORDER BY ORDINAL_POSITION
        """
        main_fields = pd.read_sql(main_fields_query, conn)
        print(main_fields)
        
        # 2. 查看收款单主表的实际数据，看看有没有客户相关字段
        print("\n=== 2. 收款单主表示例数据（查看所有可能的ID字段）===")
        sample_query = """
        SELECT TOP 5 
            FID, FBILLNO, FDATE, FPAYORGID, FSALEORGID, FSALEERID, 
            FSALEDEPTID, FSALEGROUPID, FSETTLEORGID, FCURRENCYID
        FROM T_AR_RECEIVEBILL
        WHERE FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), FDATE, 120) >= '2025-05-01'
        ORDER BY FDATE DESC
        """
        sample_df = pd.read_sql(sample_query, conn)
        print(sample_df)
        
        # 3. 查看是否有收款单和客户的直接关联表
        print("\n=== 3. 查找收款单相关的所有表 ===")
        receivebill_tables_query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME LIKE '%RECEIVEBILL%'
        ORDER BY TABLE_NAME
        """
        receivebill_tables = pd.read_sql(receivebill_tables_query, conn)
        print("收款单相关表:")
        print(receivebill_tables)
        
        # 4. 检查是否有收款单客户关联表
        if 'T_AR_RECEIVEBILLCUSTOMER' in receivebill_tables['TABLE_NAME'].values:
            print("\n=== 4. 收款单客户关联表结构 ===")
            customer_table_query = """
            SELECT COLUMN_NAME, DATA_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'T_AR_RECEIVEBILLCUSTOMER'
            ORDER BY ORDINAL_POSITION
            """
            customer_table = pd.read_sql(customer_table_query, conn)
            print(customer_table)
        
        # 5. 尝试通过销售员关联客户
        print("\n=== 5. 尝试通过销售员关联客户 ===")
        try:
            saler_query = """
            SELECT TOP 10
                r.FID,
                r.FBILLNO,
                r.FDATE,
                r.FSALEERID,
                emp_l.FNAME as 销售员,
                org_l.FNAME as 收款组织,
                re.FSETTLERECAMOUNT
            FROM T_AR_RECEIVEBILL r
            INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
            LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID
            LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052
            LEFT JOIN T_BD_STAFF emp ON r.FSALEERID = emp.FSTAFFID
            LEFT JOIN T_BD_STAFF_L emp_l ON emp.FSTAFFID = emp_l.FSTAFFID AND emp_l.FLocaleId = 2052
            WHERE r.FDOCUMENTSTATUS = 'C'
            AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
            ORDER BY r.FDATE DESC
            """
            saler_df = pd.read_sql(saler_query, conn)
            print(saler_df)
        except Exception as e:
            print(f"销售员关联失败: {e}")
        
        # 6. 查看收款单分录表是否有客户信息
        print("\n=== 6. 检查收款单分录表是否有客户相关字段 ===")
        entry_customer_query = """
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILLENTRY'
        AND (COLUMN_NAME LIKE '%CUST%' OR COLUMN_NAME LIKE '%客户%' 
             OR COLUMN_NAME LIKE '%CONTACT%' OR COLUMN_NAME LIKE '%PARTNER%')
        """
        try:
            entry_customer = pd.read_sql(entry_customer_query, conn)
            print("收款单分录表客户相关字段:")
            print(entry_customer)
        except:
            print("收款单分录表没有客户相关字段")
        
        # 7. 查看是否有其他收款相关的客户表
        print("\n=== 7. 查找所有可能包含客户信息的收款相关表 ===")
        all_customer_tables_query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE (TABLE_NAME LIKE '%RECEIV%' AND TABLE_NAME LIKE '%CUST%')
        OR (TABLE_NAME LIKE '%收款%' AND TABLE_NAME LIKE '%客户%')
        ORDER BY TABLE_NAME
        """
        all_customer_tables = pd.read_sql(all_customer_tables_query, conn)
        print("收款客户相关表:")
        print(all_customer_tables)
        
        # 8. 尝试查看应收核销表，看是否能找到客户关联
        print("\n=== 8. 查看应收核销相关表 ===")
        writeoff_tables_query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME LIKE '%WRITEOFF%' OR TABLE_NAME LIKE '%核销%'
        ORDER BY TABLE_NAME
        """
        try:
            writeoff_tables = pd.read_sql(writeoff_tables_query, conn)
            print("核销相关表:")
            print(writeoff_tables)
        except:
            print("没有找到核销相关表")
            
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
