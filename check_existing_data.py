#!/usr/bin/env python3
"""
检查数据库中的现有数据和主键定义
找出导致主键冲突的具体原因
"""

import pyodbc
import re

def check_database():
    """检查数据库状态"""
    
    # 数据库连接字符串（根据您的实际情况调整）
    # 这里使用常见的SQL Server连接方式
    connection_strings = [
        # 使用Windows认证
        'DRIVER={ODBC Driver 18 for SQL Server};SERVER=localhost;DATABASE=master;Trusted_Connection=yes;TrustServerCertificate=yes;',
        # 使用本地SQL Server Express
        'DRIVER={ODBC Driver 18 for SQL Server};SERVER=.\\SQLEXPRESS;DATABASE=master;Trusted_Connection=yes;TrustServerCertificate=yes;',
        # 使用默认实例
        'DRIVER={ODBC Driver 18 for SQL Server};SERVER=.;DATABASE=master;Trusted_Connection=yes;TrustServerCertificate=yes;',
    ]
    
    conn = None
    for conn_str in connection_strings:
        try:
            print(f"尝试连接: {conn_str.split(';')[1]}")
            conn = pyodbc.connect(conn_str)
            print("连接成功！")
            break
        except Exception as e:
            print(f"连接失败: {e}")
            continue
    
    if not conn:
        print("无法连接到数据库。请检查：")
        print("1. SQL Server是否正在运行")
        print("2. ODBC Driver 18 for SQL Server是否已安装") 
        print("3. 连接字符串是否正确")
        return
    
    try:
        cursor = conn.cursor()
        
        # 1. 检查是否存在"测试"表
        print("\n1. 检查表是否存在...")
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = '测试'
        """)
        
        table_exists = cursor.fetchone()
        if not table_exists:
            print("表'测试'不存在")
            return
        
        print("表'测试'存在")
        
        # 2. 查看表结构
        print("\n2. 查看表结构...")
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '测试'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        print("表字段：")
        for col in columns:
            nullable = "NULL" if col[2] == "YES" else "NOT NULL"
            length = f"({col[3]})" if col[3] else ""
            print(f"  {col[0]}: {col[1]}{length} {nullable}")
        
        # 3. 查看主键定义
        print("\n3. 查看主键定义...")
        cursor.execute("""
            SELECT 
                tc.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
            JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu 
                ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            WHERE tc.TABLE_NAME = '测试' 
                AND tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
            ORDER BY kcu.ORDINAL_POSITION
        """)
        
        pk_columns = cursor.fetchall()
        if pk_columns:
            print(f"主键约束: {pk_columns[0][0]}")
            print("主键字段:")
            for pk_col in pk_columns:
                print(f"  {pk_col[2]}. {pk_col[1]}")
        else:
            print("未找到主键定义")
        
        # 4. 检查表中现有数据的数量
        print("\n4. 检查现有数据...")
        cursor.execute("SELECT COUNT(*) FROM [测试]")
        count = cursor.fetchone()[0]
        print(f"表中现有记录数: {count}")
        
        if count > 0:
            print("\n表中已有数据！这可能是导致主键冲突的原因。")
            
            # 显示前几条记录
            print("\n前5条现有记录:")
            cursor.execute("SELECT TOP 5 * FROM [测试]")
            rows = cursor.fetchall()
            
            # 获取列名
            column_names = [column[0] for column in cursor.description]
            
            for i, row in enumerate(rows, 1):
                print(f"\n记录 {i}:")
                for j, value in enumerate(row):
                    print(f"  {column_names[j]}: {value}")
        
        # 5. 如果有主键，检查是否有重复
        if pk_columns and count > 0:
            print("\n5. 检查主键字段的样本值...")
            pk_field_names = [col[1] for col in pk_columns]
            pk_fields_str = ", ".join(f"[{field}]" for field in pk_field_names)
            
            query = f"""
                SELECT TOP 10 {pk_fields_str}, COUNT(*) as cnt
                FROM [测试]
                GROUP BY {pk_fields_str}
                ORDER BY cnt DESC
            """
            
            cursor.execute(query)
            sample_data = cursor.fetchall()
            
            print("主键字段样本值:")
            for row in sample_data:
                key_values = " | ".join(str(val) for val in row[:-1])
                count = row[-1]
                print(f"  {key_values} (出现{count}次)")
                if count > 1:
                    print("  ⚠️ 发现重复主键!")
    
    except Exception as e:
        print(f"查询出错: {e}")
    
    finally:
        conn.close()

def suggest_solutions():
    """提供解决方案建议"""
    print("\n" + "="*60)
    print("解决方案建议：")
    print("\n1. 如果表中已有数据:")
    print("   - 清空表: TRUNCATE TABLE [测试]")
    print("   - 或删除冲突记录")
    print("   - 或修改INSERT语句为UPDATE语句")
    
    print("\n2. 如果主键定义不明确:")
    print("   - 查看完整的CREATE TABLE语句")
    print("   - 确认主键包含哪些字段")
    
    print("\n3. 如果需要插入重复数据:")
    print("   - 考虑使用INSERT IGNORE或MERGE语句")
    print("   - 修改表结构，移除主键约束")
    print("   - 添加自增ID作为主键")

if __name__ == "__main__":
    print("正在检查数据库状态...")
    check_database()
    suggest_solutions() 