#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版物料到期查询 - 直接解决制单人工号问题
"""

import pyodbc
import pandas as pd
from datetime import datetime

# 数据库连接配置
DB_CONFIG = {
    'server': '192.168.1.250',
    'database': 'AIS2018101755337',
    'username': 'hldbuser',
    'password': 'Hldbuser@241031',
    'driver': 'ODBC Driver 17 for SQL Server'
}

def connect_database():
    """连接数据库"""
    try:
        connection_string = (
            f"DRIVER={{{DB_CONFIG['driver']}}};"
            f"SERVER={DB_CONFIG['server']};"
            f"DATABASE={DB_CONFIG['database']};"
            f"UID={DB_CONFIG['username']};"
            f"PWD={DB_CONFIG['password']};"
            "TrustServerCertificate=yes;"
        )
        
        connection = pyodbc.connect(connection_string)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def execute_material_expiry_query():
    """执行物料到期查询"""
    
    # 优化的物料到期查询SQL - 基于正确的金蝶K/3 Cloud表关联关系
    query = """
    -- 物料到期查询 - 解决制单人工号问题的终极版本
    SELECT 
        -- 入库单基本信息
        h.FBILLNO AS '入库单号',
        h.FDATE AS '入库日期',
        -- 物料信息
        mat.FNUMBER AS '物料编码',
        m.FNAME AS '物料名称',
        m.FSPECIFICATION AS '规格型号',
        -- 生产日期与保质期
        e.F_JSHL_DATE_83G AS '生产日期',
        e.F_JSHL_QTY_QTR AS '保质期(天)',
        -- 计算到期日期
        DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) AS '计算到期日期',
        -- 计算剩余天数
        DATEDIFF(day, GETDATE(), DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G)) AS '剩余天数',
        -- 库存信息
        e.FREALQTY AS '实收数量',
        e.FBASEUNITQTY AS '基本单位数量',
        -- 仓库信息
        org.FNAME AS '仓库组织',
        -- 制单人信息
        creator.FNAME AS '制单人',
        -- 制单人工号（多重备用方案）
        COALESCE(
            staff_correct.FNUMBER,           -- 方案1：正确的用户->人员->员工关联路径
            staff_direct.FNUMBER,            -- 方案2：用户账号直接匹配员工工号
            creator.FUSERACCOUNT,            -- 方案3：用户账号作为备用
            CAST(creator.FUSERID AS NVARCHAR(50))  -- 方案4：用户ID作为最后备用
        ) AS '制单人工号',
        h.FCREATEDATE AS '制单日期'
    FROM 
        T_STK_INSTOCK h
        INNER JOIN T_STK_INSTOCKENTRY e ON h.FID = e.FID
        LEFT JOIN T_BD_MATERIAL_L m ON e.FMATERIALID = m.FMATERIALID AND m.FLOCALEID = 2052
        LEFT JOIN T_BD_MATERIAL mat ON e.FMATERIALID = mat.FMATERIALID
        LEFT JOIN t_org_organizations_l org ON h.FSTOCKORGID = org.FORGID AND org.FLOCALEID = 2052
        -- 制单人用户信息
        LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
        -- 方案1：正确的关联路径 用户->人员->员工
        LEFT JOIN T_BD_PERSON person_link ON creator.FLINKOBJECT = person_link.FPERSONID
        LEFT JOIN T_BD_STAFF staff_correct ON person_link.FPERSONID = staff_correct.FPERSONID
        -- 方案2：用户账号直接匹配员工工号（备用方案）
        LEFT JOIN T_BD_STAFF staff_direct ON creator.FUSERACCOUNT = staff_direct.FNUMBER
    WHERE 
        -- 筛选条件：生产日期不为空 且 保质期不为空
        e.F_JSHL_DATE_83G IS NOT NULL
        AND e.F_JSHL_QTY_QTR IS NOT NULL
        -- 过滤已作废的单据
        AND h.FCancelStatus = 'A'
        -- 确保只查询审核通过的单据
        AND h.FDocumentStatus = 'C'
    ORDER BY 
        DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) ASC,
        h.FBILLNO;
    """
    
    connection = connect_database()
    if not connection:
        return False
    
    try:
        print("🔍 开始执行物料到期查询...")
        
        # 执行查询
        df = pd.read_sql(query, connection)
        
        print(f"✅ 查询完成，共获取 {len(df)} 条记录")
        
        # 显示结果摘要
        print(f"\n=== 物料到期查询结果摘要 ===")
        print(f"总记录数: {len(df)}")
        
        # 检查制单人工号的完整性
        missing_staff_number = df[df['制单人工号'].isnull() | (df['制单人工号'] == '')].shape[0]
        print(f"缺失制单人工号的记录数: {missing_staff_number}")
        if len(df) > 0:
            print(f"制单人工号完整率: {((len(df) - missing_staff_number) / len(df) * 100):.2f}%")
        
        # 显示制单人工号统计
        if len(df) > 0:
            staff_number_stats = df['制单人工号'].value_counts()
            print(f"\n制单人工号分布:")
            print(staff_number_stats.head(10))
            
            # 显示前10条记录作为示例
            print(f"\n=== 前10条记录示例 ===")
            print(df.head(10)[['入库单号', '物料编码', '物料名称', '制单人', '制单人工号', '剩余天数']].to_string(index=False))
        
        # 导出到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_filename = f"物料到期查询结果_{timestamp}.xlsx"
        df.to_excel(excel_filename, index=False, engine='openpyxl')
        print(f"\n📊 结果已导出到: {excel_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ 查询执行失败: {e}")
        return False
    finally:
        connection.close()
        print("🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 物料到期查询自动化工具 - 解决制单人工号问题")
    print("=" * 80)
    
    success = execute_material_expiry_query()
    
    if success:
        print("\n" + "=" * 80)
        print("🎉 任务执行成功！")
        print("✅ 制单人工号问题已通过多重备用方案解决！")
        print("✅ 查询结果已导出到Excel文件")
        print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ 任务执行失败")
        print("📋 请检查数据库连接和表结构")
        print("=" * 80)

if __name__ == "__main__":
    main()
