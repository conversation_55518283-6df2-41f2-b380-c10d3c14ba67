#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复INSERT语句中的引号不一致问题
主要问题：工号字段有些有引号，有些没有引号，导致可能的主键冲突
"""

import re
import sys

def fix_quote_consistency(input_file, output_file):
    """修复INSERT语句中的引号不一致问题"""
    
    print(f"正在处理文件: {input_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计原始数据
    insert_pattern = r"INSERT INTO \[测试\] \([^)]+\) VALUES \(([^)]+)\);"
    matches = re.findall(insert_pattern, content)
    print(f"找到 {len(matches)} 个INSERT语句")
    
    # 分析引号使用情况
    quoted_employee_ids = 0
    unquoted_employee_ids = 0
    
    for values_str in matches:
        # 简单的检查：查找工号字段（第5个字段，从0开始计数）
        values = parse_values_simple(values_str)
        if len(values) >= 5:
            employee_id = values[4].strip()
            if employee_id.startswith("'") and employee_id.endswith("'"):
                quoted_employee_ids += 1
            else:
                unquoted_employee_ids += 1
    
    print(f"有引号的工号: {quoted_employee_ids}")
    print(f"无引号的工号: {unquoted_employee_ids}")
    
    # 修复INSERT语句：确保所有字符串字段都有引号，数值字段保持原样
    fixed_content = fix_insert_statements(content)
    
    # 保存修复后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"修复后的文件已保存为: {output_file}")
    
    return True

def parse_values_simple(values_str):
    """简单解析VALUES字段"""
    # 使用正则表达式分割，但保持简单
    parts = []
    current = ""
    in_quotes = False
    quote_char = None
    
    for char in values_str:
        if not in_quotes:
            if char in ("'", '"'):
                in_quotes = True
                quote_char = char
                current += char
            elif char == ',':
                parts.append(current.strip())
                current = ""
            else:
                current += char
        else:
            current += char
            if char == quote_char:
                in_quotes = False
                quote_char = None
    
    if current.strip():
        parts.append(current.strip())
    
    return parts

def fix_insert_statements(content):
    """修复INSERT语句格式"""
    
    # 正则表达式匹配INSERT语句
    insert_pattern = r"(INSERT INTO \[测试\] \([^)]+\) VALUES \()([^)]+)(\);)"
    
    def fix_single_insert(match):
        prefix = match.group(1)
        values_str = match.group(2)
        suffix = match.group(3)
        
        # 解析并修复values
        fixed_values = fix_values_formatting(values_str)
        
        return prefix + fixed_values + suffix
    
    # 替换所有INSERT语句
    fixed_content = re.sub(insert_pattern, fix_single_insert, content)
    
    return fixed_content

def fix_values_formatting(values_str):
    """修复VALUES子句的格式"""
    
    values = parse_values_advanced(values_str)
    fixed_values = []
    
    for i, value in enumerate(values):
        value = value.strip()
        
        # 处理NULL
        if value.upper() == 'NULL':
            fixed_values.append('NULL')
            continue
        
        # 字符串字段（根据位置判断）
        if i in [0, 1, 2, 3, 4, 5]:  # 年月, 厂区, 部门, 工序, 工号, 姓名
            if not (value.startswith("'") and value.endswith("'")):
                # 如果是纯数字，也需要加引号（工号字段）
                if value.isdigit() or is_numeric_employee_id(value):
                    fixed_values.append(f"'{value}'")
                else:
                    # 移除现有引号再重新添加
                    clean_value = value.strip('\'"')
                    fixed_values.append(f"'{clean_value}'")
            else:
                fixed_values.append(value)
        
        # 数值字段或特殊文本字段
        else:
            # 检查是否是特殊文本值（如'停产', '事假'等）
            if is_special_text_value(value):
                if not (value.startswith("'") and value.endswith("'")):
                    fixed_values.append(f"'{value}'")
                else:
                    fixed_values.append(value)
            # 纯数值
            elif is_numeric_value(value):
                # 移除引号
                clean_value = value.strip('\'"')
                fixed_values.append(clean_value)
            # 其他情况，保持原样
            else:
                fixed_values.append(value)
    
    return ', '.join(fixed_values)

def parse_values_advanced(values_str):
    """高级解析VALUES字段，正确处理嵌套引号"""
    values = []
    current_value = ""
    in_quotes = False
    quote_char = None
    i = 0
    
    while i < len(values_str):
        char = values_str[i]
        
        if not in_quotes:
            if char in ("'", '"'):
                in_quotes = True
                quote_char = char
                current_value += char
            elif char == ',':
                values.append(current_value.strip())
                current_value = ""
            else:
                current_value += char
        else:
            current_value += char
            if char == quote_char:
                # 检查是否是转义的引号
                if i + 1 < len(values_str) and values_str[i + 1] == quote_char:
                    current_value += quote_char
                    i += 1  # 跳过下一个引号
                else:
                    in_quotes = False
                    quote_char = None
        
        i += 1
    
    # 添加最后一个值
    if current_value.strip():
        values.append(current_value.strip())
    
    return values

def is_numeric_employee_id(value):
    """检查是否是数字工号"""
    try:
        int(value)
        return True
    except ValueError:
        return False

def is_special_text_value(value):
    """检查是否是特殊文本值"""
    special_values = [
        '停产', '事假', '年假', '病假', '丧假', '休', '计件'
    ]
    clean_value = value.strip('\'"')
    return clean_value in special_values

def is_numeric_value(value):
    """检查是否是数值"""
    clean_value = value.strip('\'"')
    if clean_value.upper() == 'NULL' or clean_value == '':
        return False
    
    try:
        float(clean_value)
        return True
    except ValueError:
        return False

def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("使用方法: python fix_quote_consistency.py <输入文件> <输出文件>")
        print("示例: python fix_quote_consistency.py insert_statements.sql insert_statements_fixed.sql")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        success = fix_quote_consistency(input_file, output_file)
        if success:
            print("✅ 引号一致性修复完成")
        else:
            print("❌ 修复失败")
            sys.exit(1)
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 