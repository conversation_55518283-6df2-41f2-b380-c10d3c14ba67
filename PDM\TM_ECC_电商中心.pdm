<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1429518523" ExtractionBranch="1" ExtractionDate="1429518523" ExtractionId="2378" ExtractionVersion="13" ID="{05C1590A-C408-46C1-A96B-5A574DAAA070}" Label="" LastModificationDate="1429518496" Name="TM_ECC_电商中心" Objects="108" RepositoryId="{5488ED8C-130C-4B03-8F8C-843C1654E4B2}" Symbols="6" Target="Microsoft SQL Server 2005" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>05C1590A-C408-46C1-A96B-5A574DAAA070</a:ObjectID>
<a:Name>TM_ECC_电商中心</a:Name>
<a:Code>TM_ECC</a:Code>
<a:CreationDate>1402623787</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1402624466</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Event&gt;&gt;]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=0
RefrUpdateConstraint=0
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=30
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=128
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=128
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {5488ED8C-130C-4B03-8F8C-843C1654E4B2}
MODEL_ID 2378
MODEL_VRSN 13
BRANCH_ID 1
EXT_DATE 1429518523
EXT_END 1429518523
OBJECTS 
BEG_BLOCK 
 2378 {05C1590A-C408-46C1-A96B-5A574DAAA070}
 2379 {A057AD2C-E1F7-4049-8E2F-706207D4CBAF}
 2380 {005F27D3-C7B5-4210-9EE1-CA7C49359B45}
 2381 {C4ABD8AE-7EDB-448E-B098-B563C2AD545F}
 2382 {25430520-6E5E-4649-BE5B-8EFDA84B5EDE}
 2383 {7B2C8ED6-5708-4171-9006-C70B08E1E3EF}
 2384 {0C25CE86-955D-4A0A-BBF1-7B38EB0C6257}
 12571 {E39DE80E-5AD2-4EA7-8686-41F553B29900}
 12572 {F9657A47-B187-4A48-9ECF-A488341C58B4}
 12573 {AB04FBB4-235C-49E0-8D31-917210D682D3}
 12574 {38966E9E-30BE-4249-9F94-940224A5A534}
 12575 {DAC92A94-2350-41DE-BD6A-839644F56095}
 12576 {7A812686-44EA-4B21-9D09-F35F7C96EB9A}
 12577 {9FABA529-E505-4A16-9DF8-D24150A269F7}
 12578 {D64AE399-9B91-43F9-8021-92401EE7AD45}
 12579 {ED7AAFC2-F749-4EE9-A6F3-E7CDFAC58408}
 12580 {DFB84A8D-AC16-48EF-9763-3F8A5FEB5C45}
 12581 {B879A6E8-A98E-455F-B968-0F36424198ED}
 12582 {ADC9B83B-A901-4FBA-AB4D-9584F7EEB690}
 12583 {66EFE73A-55DA-4466-AACB-0E8523FF92E0}
 12584 {8154BA39-93F6-41CD-BE91-75DC6CA158AE}
 12585 {4E4E9D3F-03BF-4EC1-B480-506ABBB831DF}
 12586 {5FD28CAB-151C-42BD-B1B1-767E9EB74507}
 12587 {1984DF01-3ACC-4E85-8BD3-7CD151C2F89A}
 12588 {ACCB4A07-602A-4758-A010-3E95C7C17FB9}
 12589 {541ACF99-9AEE-4277-96ED-7E8DCFB63CE8}
 12590 {95C0406D-755B-4517-A7DC-DD6DE23667EE}
 12591 {D6CCA240-089B-4DA9-A839-B8891FD7F5C2}
 12592 {A6AD3D98-30DF-4723-BB36-F2064824C9A9}
 12593 {6E6C7D5E-B6F5-41C8-8BE0-A2C6F989C943}
 12594 {279D187C-A9C2-4096-924C-D0666FBA95BB}
 12595 {5365E261-FD9B-4C5F-A1BF-01C075B3996C}
 12596 {35F9FA23-4EAF-4987-904A-4FB80453E6A9}
 12597 {A535329A-D31E-43E9-A898-679761A76A35}
 12598 {273F0B9E-B877-4199-8811-A24E924C6182}
 12599 {C87C0EB2-1557-4171-9222-E5108FE72AE5}
 12600 {0D51EECB-4477-4D0D-B1E9-06052F075D55}
 12601 {F159199C-68D2-4428-B394-821E5A5768C4}
 12602 {4D792FEB-D763-4642-A13E-C6938252DA4E}
 12603 {B5DEF0A0-4F79-40D7-8B83-3D9274563D70}
 12604 {6F403BB7-E1E3-42B2-8558-ECA2E1D040CE}
 12605 {36F49E6B-43C1-4921-9DDB-9B44B5692698}
 12606 {8197325D-3067-4EF7-B0EE-94239DD09C03}
 12607 {72E9D758-92EB-44BB-B34F-B6361CB78CF0}
 12608 {93FA4946-DF40-40E5-89FC-7E9A06C20A2D}
 12609 {AE2F6D69-7232-44F6-B3B9-161F0FAFF64C}
 12610 {082B6F52-892D-425A-A6CC-3C0E53F0F8E5}
 12611 {A14ADE65-6E43-477A-8BC0-1170AE5F84A1}
 12612 {F5CA0477-088A-47CF-B15C-2A9141F4DFF1}
 12613 {9BD5BED0-9300-4E24-B641-100E3CF1D33B}
 12614 {41C1853A-76EF-4E51-9396-05696F254544}
 12615 {9E77C0BD-61C2-414F-AB77-BE0060484CC6}
 12616 {02B141CD-4F37-46A1-91DC-472D65047705}
 12617 {FCC24F29-99D1-4B80-BC7A-1098F695E01E}
 12618 {9DCF3437-C0FA-4CD0-9269-A24D80194D48}
 12671 {9FC36B29-E00A-45CB-87DF-8D64F69C2980}
 12672 {AE9262D6-EFA1-4DD6-99C7-522CB1D0319E}
 12673 {A289B25D-C252-4925-8B56-32C9F7C76E84}
 12674 {72BB4DD9-EB3E-43B0-9557-8AD56E5C70C2}
 12675 {1C688BA0-EB15-465D-8562-268A335F8037}
 12676 {8D9F2C5A-41F4-4035-9A72-F497DBA00C99}
 12677 {5D5C90BD-C95E-4AF7-94E9-D421E8E334B5}
 12678 {FF17EBB8-2D2B-4489-8D17-85FD17919730}
 12679 {3192DF9F-C6FD-44DC-A3E9-93F7D42BBF3E}
 12771 {067F0960-7C13-4AC5-BFF0-7DBF875B2D3D}
 12772 {6F99A50E-AC13-4F70-93B8-2165B1FAABCA}
 12921 {29720881-23C8-4B73-98FB-0E4CED5128E2}
 13021 {01790E77-6822-454D-B7FC-4A301291D689}
 13022 {E3E781C7-FBAB-438E-BE51-C492757D7BA7}
 13221 {D4520528-8290-4EC9-A45B-5734971EA4F2}
 13222 {7A176B92-9CC7-4505-8E33-92CCF2328CA9}
 13223 {EFC0F4E5-F76F-4C83-9972-693FD3BAFC1A}
 13224 {53838F7B-AD8D-49CD-9BE4-2F7EBCB529B1}
 13225 {E99F6ED5-A2EB-4F2E-90BC-EB98403D2654}
 13274 {2F466F03-40F8-4381-9AB5-0AB32CC0E546}
 14093 {E4F0CA31-727B-4DE4-B655-DB7F6F0188C3}
 14094 {E6FE803A-D901-440E-A7A2-F1AE720C1E2A}
 14095 {E2431C69-4227-4825-90A5-6D8237E70A3A}
 14096 {3C9574FB-700D-4602-A03B-9D55FCE51CA3}
 14097 {7C5D2814-D811-49AF-A2AC-BB67816CB1A6}
 14098 {67E00FB4-D228-4FA3-A592-39853A0A5425}
 14099 {CC4C4E21-D709-42D7-B779-3E1AB4D5A621}
 14100 {7690C8CF-25AC-49DA-ABD3-8240D6CFE00E}
 14101 {521285F9-BF1B-4C86-9C8E-C36C363B9F8F}
 14102 {044570C9-DACE-481B-BEAE-2796717D6623}
 14103 {F6B1E96B-E20E-4166-A0E9-48E7FC6E026F}
 14728 {2B812B58-5AA7-4466-AA1A-D163454E8885}
 14729 {2230B526-0443-4A35-AADC-84A4DF13EC75}
 14730 {98C894E0-3227-4057-A244-0F2304E839BF}
 14731 {23964280-C559-4667-A773-418F3FDDD17E}
 14732 {94EB6200-1865-4DD5-9821-EEE71251E8E7}
 14733 {BECFDAA8-8AEF-43BA-A1F1-E2D6F0260264}
 14734 {F7E9F5F8-0F90-4B93-A56A-AF1B7F7DBE37}
 14735 {82A8568D-298C-43A2-8856-72767D83055F}
 14736 {3F4D3CCF-94A9-4B8F-B360-41EEAA5A5BA9}
 14737 {5EDFCC5C-0E2E-485C-BBA9-FF3BCC036DD9}
 14738 {621A8701-7A68-470C-82EC-957426F9FC1A}
 14739 {8DA2F1BE-EE00-4F4E-BD44-0B3D6195E746}
 14740 {4B078E64-2FC0-4709-B26F-2F09160349ED}
 14741 {B9B1CE3B-E899-4DE5-AE80-BA5D48AD59F5}
 14742 {F89655F8-CF2B-4CAF-A56B-D42FCFE9EAF2}
 14743 {2A3F434B-B942-4227-994C-592516318675}
 14744 {0BC0F43E-AA8C-4B6B-A573-CDD0C55B0761}
 14745 {3542EC65-EF42-4FCB-85B1-F76629E4CCBA}
 14746 {0D9006E3-12C3-43EA-91A1-109C68DE38B3}
 14747 {1F1C6ADE-E813-4783-9557-6DC1E2983689}
 14748 {AB3BAFFC-7905-473F-9493-428904C0EFB7}
 16077 {D042D119-EAD5-4283-86E4-BB2F4F36868B}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>A057AD2C-E1F7-4049-8E2F-706207D4CBAF</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1402624465</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1402624466</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>005F27D3-C7B5-4210-9EE1-CA7C49359B45</a:ObjectID>
<a:Name>TM_ECC_电商中心_all</a:Name>
<a:Code>TM_ECC_电商中心_all</a:Code>
<a:CreationDate>1402623787</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1402624506</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:PackageSymbol Id="o5">
<a:CreationDate>1402624506</a:CreationDate>
<a:ModificationDate>1402624506</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-19327,18372), (-11379,21971))</a:Rect>
<a:LineColor>11711154</a:LineColor>
<a:FillColor>12648447</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Package Ref="o6"/>
</c:Object>
</o:PackageSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o6">
<a:ObjectID>C4ABD8AE-7EDB-448E-B098-B563C2AD545F</a:ObjectID>
<a:Name>01 TM_ECC电商中心</a:Name>
<a:Code>01 TM_ECC电商中心</a:Code>
<a:CreationDate>1402624232</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1402624466</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o7">
<a:ObjectID>0C25CE86-955D-4A0A-BBF1-7B38EB0C6257</a:ObjectID>
<a:Name>01 TM_ECC_电商中心</a:Name>
<a:Code>01 TM_ECC_电商中心</a:Code>
<a:CreationDate>1402624232</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421051903</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=800
Graphic unit=2
Window color=255 255 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255 255 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=15000
Height=7500
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LCNMFont=新宋体,8,N
LCNMFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=15000
Height=7500
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=15000
Height=7500
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=15000
Height=7500
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0 0 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=15000
Height=7500
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=15000
Height=7500
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:TableSymbol Id="o8">
<a:CreationDate>1421051731</a:CreationDate>
<a:ModificationDate>1421053791</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-11662,-2549), (3335,4949))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o9"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o10">
<a:CreationDate>1421053417</a:CreationDate>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-32625,-2400), (-17626,5099))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o11"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o12">
<a:CreationDate>1421979531</a:CreationDate>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((5933,-3492), (24893,6492))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o13"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o14">
<a:CreationDate>1422515268</a:CreationDate>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((4133,9707), (23093,19691))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o15"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o16">
<a:CreationDate>1422515866</a:CreationDate>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-18508,9631), (452,19615))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o17"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o7"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o9">
<a:ObjectID>E39DE80E-5AD2-4EA7-8686-41F553B29900</a:ObjectID>
<a:Name>TM_ECC_LOCKSTK(锁库条件临时表)</a:Name>
<a:Code>TM_ECC_LOCKSTK</a:Code>
<a:CreationDate>1421051731</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o18">
<a:ObjectID>F9657A47-B187-4A48-9ECF-A488341C58B4</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o19">
<a:ObjectID>AB04FBB4-235C-49E0-8D31-917210D682D3</a:ObjectID>
<a:Name>预留关系内码</a:Name>
<a:Code>FLINKID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o20">
<a:ObjectID>38966E9E-30BE-4249-9F94-940224A5A534</a:ObjectID>
<a:Name>预留关系分录内码</a:Name>
<a:Code>FLINKENTRYID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o21">
<a:ObjectID>DAC92A94-2350-41DE-BD6A-839644F56095</a:ObjectID>
<a:Name>即时库存内码</a:Name>
<a:Code>FINVDETAILID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o22">
<a:ObjectID>7A812686-44EA-4B21-9D09-F35F7C96EB9A</a:ObjectID>
<a:Name>业务对象Form内码</a:Name>
<a:Code>FOBJECTID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o23">
<a:ObjectID>9FABA529-E505-4A16-9DF8-D24150A269F7</a:ObjectID>
<a:Name>单据类型内码</a:Name>
<a:Code>FBILLTYPEID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o24">
<a:ObjectID>D64AE399-9B91-43F9-8021-92401EE7AD45</a:ObjectID>
<a:Name>单据编号</a:Name>
<a:Code>FBILLNO</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o25">
<a:ObjectID>ED7AAFC2-F749-4EE9-A6F3-E7CDFAC58408</a:ObjectID>
<a:Name>单据内码</a:Name>
<a:Code>FBILLID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o26">
<a:ObjectID>DFB84A8D-AC16-48EF-9763-3F8A5FEB5C45</a:ObjectID>
<a:Name>实体Key</a:Name>
<a:Code>FENTIRYKEY</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o27">
<a:ObjectID>B879A6E8-A98E-455F-B968-0F36424198ED</a:ObjectID>
<a:Name>单据序号</a:Name>
<a:Code>FBILLSEQ</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o28">
<a:ObjectID>ADC9B83B-A901-4FBA-AB4D-9584F7EEB690</a:ObjectID>
<a:Name>单据分录内码</a:Name>
<a:Code>FBILLDETAILID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o29">
<a:ObjectID>66EFE73A-55DA-4466-AACB-0E8523FF92E0</a:ObjectID>
<a:Name>物料内码</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o30">
<a:ObjectID>8154BA39-93F6-41CD-BE91-75DC6CA158AE</a:ObjectID>
<a:Name>库存组织内码</a:Name>
<a:Code>FSTOCKORGID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o31">
<a:ObjectID>4E4E9D3F-03BF-4EC1-B480-506ABBB831DF</a:ObjectID>
<a:Name>BOM</a:Name>
<a:Code>FBOMID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o32">
<a:ObjectID>5FD28CAB-151C-42BD-B1B1-767E9EB74507</a:ObjectID>
<a:Name>计划跟踪号</a:Name>
<a:Code>FMTONO</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o33">
<a:ObjectID>1984DF01-3ACC-4E85-8BD3-7CD151C2F89A</a:ObjectID>
<a:Name>项目编号</a:Name>
<a:Code>FPROJECTNO</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o34">
<a:ObjectID>ACCB4A07-602A-4758-A010-3E95C7C17FB9</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FAUXPROPID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o35">
<a:ObjectID>541ACF99-9AEE-4277-96ED-7E8DCFB63CE8</a:ObjectID>
<a:Name>批号</a:Name>
<a:Code>FLOT</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o36">
<a:ObjectID>95C0406D-755B-4517-A7DC-DD6DE23667EE</a:ObjectID>
<a:Name>批号文本</a:Name>
<a:Code>FLOT_TEXT</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o37">
<a:ObjectID>D6CCA240-089B-4DA9-A839-B8891FD7F5C2</a:ObjectID>
<a:Name>生产日期</a:Name>
<a:Code>FPRODUCEDATE</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o38">
<a:ObjectID>A6AD3D98-30DF-4723-BB36-F2064824C9A9</a:ObjectID>
<a:Name>有效期至</a:Name>
<a:Code>FEXPIRYDATE</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o39">
<a:ObjectID>6E6C7D5E-B6F5-41C8-8BE0-A2C6F989C943</a:ObjectID>
<a:Name>仓库</a:Name>
<a:Code>FSTOCKID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o40">
<a:ObjectID>279D187C-A9C2-4096-924C-D0666FBA95BB</a:ObjectID>
<a:Name>仓位</a:Name>
<a:Code>FSTOCKLOCID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o41">
<a:ObjectID>5365E261-FD9B-4C5F-A1BF-01C075B3996C</a:ObjectID>
<a:Name>货主类型</a:Name>
<a:Code>FOWNERTYPEID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o42">
<a:ObjectID>35F9FA23-4EAF-4987-904A-4FB80453E6A9</a:ObjectID>
<a:Name>货主</a:Name>
<a:Code>FOWNERID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o43">
<a:ObjectID>A535329A-D31E-43E9-A898-679761A76A35</a:ObjectID>
<a:Name>保管者类型</a:Name>
<a:Code>FKEEPERTYPEID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o44">
<a:ObjectID>273F0B9E-B877-4199-8811-A24E924C6182</a:ObjectID>
<a:Name>保管者</a:Name>
<a:Code>FKEEPERID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o45">
<a:ObjectID>C87C0EB2-1557-4171-9222-E5108FE72AE5</a:ObjectID>
<a:Name>库存状态</a:Name>
<a:Code>FSTOCKSTATUSID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o46">
<a:ObjectID>0D51EECB-4477-4D0D-B1E9-06052F075D55</a:ObjectID>
<a:Name>单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o47">
<a:ObjectID>F159199C-68D2-4428-B394-821E5A5768C4</a:ObjectID>
<a:Name>锁库数量</a:Name>
<a:Code>FLOCKQTY</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o48">
<a:ObjectID>4D792FEB-D763-4642-A13E-C6938252DA4E</a:ObjectID>
<a:Name>基本单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o49">
<a:ObjectID>B5DEF0A0-4F79-40D7-8B83-3D9274563D70</a:ObjectID>
<a:Name>基本单位锁库数量</a:Name>
<a:Code>FBASELOCKQTY</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o50">
<a:ObjectID>6F403BB7-E1E3-42B2-8558-ECA2E1D040CE</a:ObjectID>
<a:Name>辅助单位</a:Name>
<a:Code>FSECUNITID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o51">
<a:ObjectID>36F49E6B-43C1-4921-9DDB-9B44B5692698</a:ObjectID>
<a:Name>辅助单位锁库数量</a:Name>
<a:Code>FSECLOCKQTY</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o52">
<a:ObjectID>8197325D-3067-4EF7-B0EE-94239DD09C03</a:ObjectID>
<a:Name>需求组织</a:Name>
<a:Code>FDEMANDORGID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o53">
<a:ObjectID>72E9D758-92EB-44BB-B34F-B6361CB78CF0</a:ObjectID>
<a:Name>需求物料内码</a:Name>
<a:Code>FDEMANDMATERIALID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o54">
<a:ObjectID>93FA4946-DF40-40E5-89FC-7E9A06C20A2D</a:ObjectID>
<a:Name>需求基本单位</a:Name>
<a:Code>FDEMANDBASEUNITID</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>AE2F6D69-7232-44F6-B3B9-161F0FAFF64C</a:ObjectID>
<a:Name>需求日期</a:Name>
<a:Code>FDEMANDDATETIME</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o56">
<a:ObjectID>082B6F52-892D-425A-A6CC-3C0E53F0F8E5</a:ObjectID>
<a:Name>需求优先级</a:Name>
<a:Code>FDEMANDPRIORITY</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o57">
<a:ObjectID>A14ADE65-6E43-477A-8BC0-1170AE5F84A1</a:ObjectID>
<a:Name>物料是否MTO</a:Name>
<a:Code>FISMTO</a:Code>
<a:CreationDate>1421052129</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o58">
<a:ObjectID>F5CA0477-088A-47CF-B15C-2A9141F4DFF1</a:ObjectID>
<a:Name>PK_TM_ECC_LOCKSTK</a:Name>
<a:Code>PK_TM_ECC_LOCKSTK</a:Code>
<a:CreationDate>1421052495</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o18"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o59">
<a:ObjectID>9BD5BED0-9300-4E24-B641-100E3CF1D33B</a:ObjectID>
<a:Name>IDX_ECC_LOCKSTK_MID</a:Name>
<a:Code>IDX_ECC_LOCKSTK_MID</a:Code>
<a:CreationDate>1421052205</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o60">
<a:ObjectID>41C1853A-76EF-4E51-9396-05696F254544</a:ObjectID>
<a:CreationDate>1421052260</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<c:Column>
<o:Column Ref="o29"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o61">
<a:ObjectID>9E77C0BD-61C2-414F-AB77-BE0060484CC6</a:ObjectID>
<a:Name>IDX_ECC_LOCKSTK_FBE</a:Name>
<a:Code>IDX_ECC_LOCKSTK_FBE</a:Code>
<a:CreationDate>1421052260</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o62">
<a:ObjectID>02B141CD-4F37-46A1-91DC-472D65047705</a:ObjectID>
<a:CreationDate>1421052323</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<c:Column>
<o:Column Ref="o28"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o63">
<a:ObjectID>FCC24F29-99D1-4B80-BC7A-1098F695E01E</a:ObjectID>
<a:CreationDate>1421052323</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<c:Column>
<o:Column Ref="o25"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o64">
<a:ObjectID>9DCF3437-C0FA-4CD0-9269-A24D80194D48</a:ObjectID>
<a:CreationDate>1421052323</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421052549</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<c:Column>
<o:Column Ref="o22"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o58"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o58"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o13">
<a:ObjectID>E4F0CA31-727B-4DE4-B655-DB7F6F0188C3</a:ObjectID>
<a:Name>TM_ECC_INVENTORY(电商库存综合查询临时表)</a:Name>
<a:Code>TM_ECC_INVENTORY</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:Comment>电商库存综合查询临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o65">
<a:ObjectID>E6FE803A-D901-440E-A7A2-F1AE720C1E2A</a:ObjectID>
<a:Name>库存组织</a:Name>
<a:Code>FSTOCKORGID</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>E2431C69-4227-4825-90A5-6D8237E70A3A</a:ObjectID>
<a:Name>仓库内码</a:Name>
<a:Code>FSTOCKID</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>3C9574FB-700D-4602-A03B-9D55FCE51CA3</a:ObjectID>
<a:Name>物料内码</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>7C5D2814-D811-49AF-A2AC-BB67816CB1A6</a:ObjectID>
<a:Name>物料MASTERID</a:Name>
<a:Code>FMATMASTERID</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>67E00FB4-D228-4FA3-A592-39853A0A5425</a:ObjectID>
<a:Name>辅助属性内码</a:Name>
<a:Code>FAUXPROPID</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>CC4C4E21-D709-42D7-B779-3E1AB4D5A621</a:ObjectID>
<a:Name>基本单位内码</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o71">
<a:ObjectID>7690C8CF-25AC-49DA-ABD3-8240D6CFE00E</a:ObjectID>
<a:Name>库存量(基本单位)</a:Name>
<a:Code>FBASEQTY</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o72">
<a:ObjectID>521285F9-BF1B-4C86-9C8E-C36C363B9F8F</a:ObjectID>
<a:Name>锁库量(基本单位)</a:Name>
<a:Code>FBASELOCKQTY</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o73">
<a:ObjectID>044570C9-DACE-481B-BEAE-2796717D6623</a:ObjectID>
<a:Name>可用量(基本单位)</a:Name>
<a:Code>FBASEAVBQTY</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>F6B1E96B-E20E-4166-A0E9-48E7FC6E026F</a:ObjectID>
<a:Name>在途量(基本单位)</a:Name>
<a:Code>FBASETRANSITQTY</a:Code>
<a:CreationDate>1421979531</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1421984340</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o11">
<a:ObjectID>9FC36B29-E00A-45CB-87DF-8D64F69C2980</a:ObjectID>
<a:Name>TM_STD_QUERYLOCKCOND(查询即时库的条件)</a:Name>
<a:Code>TM_STD_QUERYLOCKCOND</a:Code>
<a:CreationDate>1421053417</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1429518496</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o75">
<a:ObjectID>AE9262D6-EFA1-4DD6-99C7-522CB1D0319E</a:ObjectID>
<a:Name>库存组织</a:Name>
<a:Code>FSTOCKORGID</a:Code>
<a:CreationDate>1421053440</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>A289B25D-C252-4925-8B56-32C9F7C76E84</a:ObjectID>
<a:Name>仓库</a:Name>
<a:Code>FSTOCKID</a:Code>
<a:CreationDate>1421053440</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>2F466F03-40F8-4381-9AB5-0AB32CC0E546</a:ObjectID>
<a:Name>仓位</a:Name>
<a:Code>FSTOCKPLACEID</a:Code>
<a:CreationDate>1421385267</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421385484</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>D042D119-EAD5-4283-86E4-BB2F4F36868B</a:ObjectID>
<a:Name>物料分配内码</a:Name>
<a:Code>FMATMASTERID</a:Code>
<a:CreationDate>1429518445</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1429518496</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>72BB4DD9-EB3E-43B0-9557-8AD56E5C70C2</a:ObjectID>
<a:Name>物料ID</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1421053440</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o80">
<a:ObjectID>1C688BA0-EB15-465D-8562-268A335F8037</a:ObjectID>
<a:Name>计量单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1421053440</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o81">
<a:ObjectID>8D9F2C5A-41F4-4035-9A72-F497DBA00C99</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FAUXPROPID</a:Code>
<a:CreationDate>1421053440</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o82">
<a:ObjectID>5D5C90BD-C95E-4AF7-94E9-D421E8E334B5</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1421053440</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>FF17EBB8-2D2B-4489-8D17-85FD17919730</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>1421053440</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>3192DF9F-C6FD-44DC-A3E9-93F7D42BBF3E</a:ObjectID>
<a:Name>基本单位数量</a:Name>
<a:Code>FBASEQTY</a:Code>
<a:CreationDate>1421053440</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>067F0960-7C13-4AC5-BFF0-7DBF875B2D3D</a:ObjectID>
<a:Name>保管者ID</a:Name>
<a:Code>FKEEPERID</a:Code>
<a:CreationDate>1421053839</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053969</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>6F99A50E-AC13-4F70-93B8-2165B1FAABCA</a:ObjectID>
<a:Name>货主ID</a:Name>
<a:Code>FOWNERID</a:Code>
<a:CreationDate>1421053839</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421053969</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>29720881-23C8-4B73-98FB-0E4CED5128E2</a:ObjectID>
<a:Name>货主类型</a:Name>
<a:Code>FOWNERTYPEID</a:Code>
<a:CreationDate>1421055468</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421055503</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>01790E77-6822-454D-B7FC-4A301291D689</a:ObjectID>
<a:Name>保管理者类型</a:Name>
<a:Code>FKEEPERTYPEID</a:Code>
<a:CreationDate>1421286979</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1422605027</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>E3E781C7-FBAB-438E-BE51-C492757D7BA7</a:ObjectID>
<a:Name>批号</a:Name>
<a:Code>FLOT</a:Code>
<a:CreationDate>1421287274</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421287333</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>D4520528-8290-4EC9-A45B-5734971EA4F2</a:ObjectID>
<a:Name>BOM</a:Name>
<a:Code>FBOMID</a:Code>
<a:CreationDate>1421302386</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421302469</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>7A176B92-9CC7-4505-8E33-92CCF2328CA9</a:ObjectID>
<a:Name>计划跟踪号</a:Name>
<a:Code>FMTONO</a:Code>
<a:CreationDate>1421302386</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421302469</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>EFC0F4E5-F76F-4C83-9972-693FD3BAFC1A</a:ObjectID>
<a:Name>项目编号</a:Name>
<a:Code>FPROJECTNO</a:Code>
<a:CreationDate>1421302386</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421302469</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>53838F7B-AD8D-49CD-9BE4-2F7EBCB529B1</a:ObjectID>
<a:Name>生产日期</a:Name>
<a:Code>FPRODUCEDATE</a:Code>
<a:CreationDate>1421302386</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421302469</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o94">
<a:ObjectID>E99F6ED5-A2EB-4F2E-90BC-EB98403D2654</a:ObjectID>
<a:Name>有效期至</a:Name>
<a:Code>FEXPIRYDATE</a:Code>
<a:CreationDate>1421302386</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1421302469</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o15">
<a:ObjectID>2B812B58-5AA7-4466-AA1A-D163454E8885</a:ObjectID>
<a:Name>TM_ECC_UPLOADINVENOTY1(商品库存上传临时表1)</a:Name>
<a:Code>TM_ECC_UPLOADINVENOTY1</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:Comment>商品库存上传临时表1,用于存放商品上传库存数据</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o95">
<a:ObjectID>2230B526-0443-4A35-AADC-84A4DF13EC75</a:ObjectID>
<a:Name>商品主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>98C894E0-3227-4057-A244-0F2304E839BF</a:ObjectID>
<a:Name>商品分录主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>23964280-C559-4667-A773-418F3FDDD17E</a:ObjectID>
<a:Name>网店主键</a:Name>
<a:Code>FNETSHOPID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>94EB6200-1865-4DD5-9821-EEE71251E8E7</a:ObjectID>
<a:Name>商品数字ID</a:Name>
<a:Code>FNUMIID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>BECFDAA8-8AEF-43BA-A1F1-E2D6F0260264</a:ObjectID>
<a:Name>商品SKU数字ID</a:Name>
<a:Code>FGOODSKUID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>F7E9F5F8-0F90-4B93-A56A-AF1B7F7DBE37</a:ObjectID>
<a:Name>物料MASTER主键</a:Name>
<a:Code>FMATMASTERID</a:Code>
<a:CreationDate>1422615922</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>82A8568D-298C-43A2-8856-72767D83055F</a:ObjectID>
<a:Name>物料主键</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>3F4D3CCF-94A9-4B8F-B360-41EEAA5A5BA9</a:ObjectID>
<a:Name>辅助属性主键</a:Name>
<a:Code>FAUXPROPID</a:Code>
<a:CreationDate>1422515321</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>5EDFCC5C-0E2E-485C-BBA9-FF3BCC036DD9</a:ObjectID>
<a:Name>套件主键</a:Name>
<a:Code>FSUITEID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o104">
<a:ObjectID>621A8701-7A68-470C-82EC-957426F9FC1A</a:ObjectID>
<a:Name>计量单位主键</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>8DA2F1BE-EE00-4F4E-BD44-0B3D6195E746</a:ObjectID>
<a:Name>可用库存数量</a:Name>
<a:Code>FAVBQTY</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>4B078E64-2FC0-4709-B26F-2F09160349ED</a:ObjectID>
<a:Name>基本计量单位主键</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1422515268</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>B9B1CE3B-E899-4DE5-AE80-BA5D48AD59F5</a:ObjectID>
<a:Name>可用库存数量(可用库存数量)</a:Name>
<a:Code>FBASEAVBQTY</a:Code>
<a:CreationDate>1422515321</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o17">
<a:ObjectID>F89655F8-CF2B-4CAF-A56B-D42FCFE9EAF2</a:ObjectID>
<a:Name>TM_ECC_UPLOADINVENOTY2(商品库存上传临时表2)</a:Name>
<a:Code>TM_ECC_UPLOADINVENOTY2</a:Code>
<a:CreationDate>1422515866</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:Comment>商品库存上传临时表2,用于存放可用库存数据</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o108">
<a:ObjectID>2A3F434B-B942-4227-994C-592516318675</a:ObjectID>
<a:Name>物料MASTER主键</a:Name>
<a:Code>FMATMASTERID</a:Code>
<a:CreationDate>1422515866</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>0BC0F43E-AA8C-4B6B-A573-CDD0C55B0761</a:ObjectID>
<a:Name>辅助属性主键</a:Name>
<a:Code>FAUXPROPID</a:Code>
<a:CreationDate>1422515866</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>3542EC65-EF42-4FCB-85B1-F76629E4CCBA</a:ObjectID>
<a:Name>库存组织主键</a:Name>
<a:Code>FSTOCKORGID</a:Code>
<a:CreationDate>1422515866</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>0D9006E3-12C3-43EA-91A1-109C68DE38B3</a:ObjectID>
<a:Name>仓库主键</a:Name>
<a:Code>FSTOCKID</a:Code>
<a:CreationDate>1422515866</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>1F1C6ADE-E813-4783-9557-6DC1E2983689</a:ObjectID>
<a:Name>基本计量单位主键</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1422515866</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>AB3BAFFC-7905-473F-9493-428904C0EFB7</a:ObjectID>
<a:Name>可用库存数量(可用库存数量)</a:Name>
<a:Code>FBASEAVBQTY</a:Code>
<a:CreationDate>1422515866</a:CreationDate>
<a:Creator>RD_sonic_chen</a:Creator>
<a:ModificationDate>1423123890</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
</c:Tables>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:DefaultGroups>
<o:Group Id="o114">
<a:ObjectID>25430520-6E5E-4649-BE5B-8EFDA84B5EDE</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1402623786</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1402623786</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o115">
<a:ObjectID>7B2C8ED6-5708-4171-9006-C70B08E1E3EF</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1402624465</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1402624466</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/sqlsv2k5.xdb</a:TargetModelURL>
<a:TargetModelID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>