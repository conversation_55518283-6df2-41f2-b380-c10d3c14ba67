@echo off
chcp 65001
echo 🚀 开始运行全自动数据库结构分析...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.x
    pause
    exit /b 1
)

REM 检查并安装必要的包
echo 📦 检查Python依赖包...
python -c "import pyodbc, pandas" >nul 2>&1
if errorlevel 1 (
    echo 📥 安装必要的Python包...
    pip install pyodbc pandas
    if errorlevel 1 (
        echo ❌ 包安装失败，请手动安装: pip install pyodbc pandas
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查完成
echo.

REM 运行分析脚本
echo 🔍 开始数据库分析...
python "全自动数据库结构分析.py"

echo.
echo 📄 分析完成！请查看生成的文件：
echo    - 物料到期查询_自动优化版.sql
echo    - 数据库结构分析报告.json  
echo    - 客户欠款插入语句.sql
echo.

pause
