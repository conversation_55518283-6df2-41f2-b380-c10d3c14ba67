# 物料到期查询自动化工具

## 📋 功能说明

这个工具专门解决金蝶K/3 Cloud系统中物料到期查询时**制单人工号带不出**的问题。

### 🎯 主要功能

1. **自动分析制单人工号映射问题**
   - 检查制单人ID与操作员表的关联情况
   - 分析操作员与员工表的关联情况
   - 查找替代的员工工号映射方案

2. **多重备用方案解决工号问题**
   - 方案1：操作员分录关联员工工号
   - 方案2：用户账号直接匹配员工工号
   - 方案3：用户姓名匹配员工姓名
   - 方案4：用户账号匹配人员编号
   - 方案5：用户姓名匹配人员姓名
   - 方案6：用户账号作为备用
   - 方案7：用户ID作为最后备用

3. **完整的物料到期查询**
   - 包含入库单信息、物料信息、生产日期、保质期
   - 自动计算到期日期和剩余天数
   - 显示制单人和制单人工号
   - 结果导出到Excel文件

## 🚀 快速开始

### 第一步：安装依赖包

```bash
python install_requirements.py
```

### 第二步：运行查询工具

```bash
python run_material_expiry_query.py
```

## 📊 输出结果

工具会生成以下输出：

1. **控制台输出**
   - 制单人与操作员关联分析
   - 操作员与员工关联分析
   - 替代映射方案分析
   - 查询结果摘要和统计

2. **Excel文件**
   - 文件名格式：`物料到期查询结果_YYYYMMDD_HHMMSS.xlsx`
   - 包含完整的查询结果

3. **日志文件**
   - 文件名：`material_expiry_query.log`
   - 记录详细的执行过程和错误信息

## 🔧 数据库配置

工具使用以下数据库连接配置：

```python
DB_CONFIG = {
    'server': '192.168.1.250',
    'database': 'AIS2018101755337',
    'username': 'hldbuser',
    'password': 'Hldbuser@241031',
    'driver': 'ODBC Driver 17 for SQL Server'
}
```

## 📋 查询字段说明

| 字段名 | 说明 |
|--------|------|
| 入库单号 | 入库单据编号 |
| 入库日期 | 入库日期 |
| 物料编码 | 物料的编码 |
| 物料名称 | 物料名称 |
| 规格型号 | 物料规格 |
| 生产日期 | 物料生产日期 |
| 保质期(天) | 保质期天数 |
| 计算到期日期 | 自动计算的到期日期 |
| 剩余天数 | 距离到期的剩余天数 |
| 实收数量 | 实际收货数量 |
| 基本单位数量 | 基本单位数量 |
| 仓库组织 | 仓库组织名称 |
| 制单人 | 制单人姓名 |
| **制单人工号** | **制单人工号（重点解决字段）** |
| 制单日期 | 制单日期 |

## ⚠️ 注意事项

1. **数据库连接**
   - 确保数据库服务器可访问
   - 确认用户名密码正确
   - 检查ODBC驱动是否安装

2. **权限要求**
   - 需要对相关表的SELECT权限
   - 主要涉及表：T_STK_INSTOCK、T_BD_STAFF、T_SEC_USER等

3. **数据质量**
   - 工具会自动处理制单人工号缺失的情况
   - 使用多重备用方案确保数据完整性

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   解决方案：
   - 检查网络连接
   - 确认数据库服务器地址和端口
   - 验证用户名密码
   ```

2. **制单人工号仍然为空**
   ```
   解决方案：
   - 查看日志文件了解具体原因
   - 检查用户表和员工表的数据质量
   - 可能需要手动维护用户与员工的关联关系
   ```

3. **Excel导出失败**
   ```
   解决方案：
   - 确保安装了openpyxl包
   - 检查文件写入权限
   - 关闭同名的Excel文件
   ```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件 `material_expiry_query.log`
2. 检查控制台输出的错误信息
3. 确认数据库表结构是否与预期一致

---

**开发者**: Augment Agent  
**版本**: 1.0  
**日期**: 2025-01-27
