#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Kingdee数据库连接的不同方式
"""

import pyodbc

def test_kingdee_connections():
    """测试不同的Kingdee数据库连接方式"""
    
    # 连接配置列表
    configs = [
        {
            'name': '方式1: hldbuser用户',
            'server': '192.168.1.250',
            'database': 'AIS2018101755337',
            'username': 'hldbuser',
            'password': 'Hldbuser@241031',
            'driver': 'ODBC Driver 17 for SQL Server'
        },
        {
            'name': '方式2: Windows认证',
            'server': '192.168.1.250',
            'database': 'AIS2018101755337',
            'username': None,
            'password': None,
            'driver': 'ODBC Driver 17 for SQL Server'
        },
        {
            'name': '方式3: sa用户（如果可用）',
            'server': '192.168.1.250',
            'database': 'AIS2018101755337',
            'username': 'sa',
            'password': 'Hldbuser@241031',
            'driver': 'ODBC Driver 17 for SQL Server'
        }
    ]
    
    for config in configs:
        print(f"\n{'='*60}")
        print(f"测试 {config['name']}")
        print('='*60)
        
        try:
            if config['username'] is None:
                # Windows认证
                connection_string = (
                    f"DRIVER={{{config['driver']}}};"
                    f"SERVER={config['server']};"
                    f"DATABASE={config['database']};"
                    "Trusted_Connection=yes;"
                    "TrustServerCertificate=yes;"
                )
            else:
                # SQL Server认证
                connection_string = (
                    f"DRIVER={{{config['driver']}}};"
                    f"SERVER={config['server']};"
                    f"DATABASE={config['database']};"
                    f"UID={config['username']};"
                    f"PWD={config['password']};"
                    "TrustServerCertificate=yes;"
                )
            
            print(f"连接字符串: {connection_string}")
            connection = pyodbc.connect(connection_string)
            print("✅ 连接成功！")
            
            # 测试查询组装拆卸表
            cursor = connection.cursor()
            cursor.execute("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME LIKE '%ASSEMBLY%' OR TABLE_NAME LIKE '%组装%'
            """)
            tables = cursor.fetchall()
            
            if tables:
                print("✅ 找到组装拆卸相关表:")
                for table in tables:
                    print(f"  - {table[0]}")
                    
                # 测试查询组装拆卸单
                cursor.execute("SELECT COUNT(*) FROM T_STK_ASSEMBLY")
                count = cursor.fetchone()[0]
                print(f"✅ T_STK_ASSEMBLY表中有 {count} 条记录")
                
                # 查询最近的几条记录
                cursor.execute("""
                    SELECT TOP 5 FBillNo, FDate, FAffairType 
                    FROM T_STK_ASSEMBLY 
                    ORDER BY FCreateDate DESC
                """)
                recent_records = cursor.fetchall()
                print("最近的组装拆卸单:")
                for record in recent_records:
                    print(f"  - {record[0]} | {record[1]} | {record[2]}")
                    
                # 查询是否有ZZCX003758
                cursor.execute("SELECT COUNT(*) FROM T_STK_ASSEMBLY WHERE FBillNo = 'ZZCX003758'")
                target_count = cursor.fetchone()[0]
                if target_count > 0:
                    print(f"✅ 找到目标单据 ZZCX003758！")
                else:
                    print(f"❌ 未找到目标单据 ZZCX003758")
                    
            else:
                print("❌ 未找到组装拆卸相关表")
            
            connection.close()
            print("🔒 连接已关闭")
            
            # 如果这个连接成功了，就不需要测试其他的了
            if tables:
                print(f"\n🎯 推荐使用: {config['name']}")
                return config
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
    
    return None

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 Kingdee数据库连接测试工具")
    print("=" * 80)
    
    successful_config = test_kingdee_connections()
    
    if successful_config:
        print(f"\n🎉 成功找到可用的连接方式: {successful_config['name']}")
    else:
        print("\n❌ 所有连接方式都失败了")

if __name__ == "__main__":
    main()
