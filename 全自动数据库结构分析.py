#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全自动数据库结构分析工具
用于分析金蝶K/3 Cloud数据库结构并解决制单人工号查询问题
"""

import pyodbc
import pandas as pd
import json
from datetime import datetime
import os

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

class DatabaseAnalyzer:
    def __init__(self):
        self.connection_string = f"DRIVER={{{DRIVER}}};SERVER={SERVER};DATABASE={DATABASE};UID={USERNAME};PWD={PASSWORD}"
        self.conn = None
        self.results = {}
        
    def connect(self):
        """连接数据库"""
        try:
            self.conn = pyodbc.connect(self.connection_string)
            print(f"✅ 成功连接到数据库: {DATABASE}")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def execute_query(self, sql, description=""):
        """执行SQL查询"""
        try:
            df = pd.read_sql(sql, self.conn)
            print(f"✅ {description}: 查询成功，返回 {len(df)} 条记录")
            return df
        except Exception as e:
            print(f"❌ {description}: 查询失败 - {e}")
            return None
    
    def analyze_table_structure(self):
        """分析表结构"""
        print("\n🔍 开始分析表结构...")
        
        # 1. 查询所有金蝶表
        sql_tables = """
        SELECT 
            TABLE_NAME AS '表名',
            CASE 
                WHEN TABLE_NAME LIKE 'T_BD_%' THEN '基础资料'
                WHEN TABLE_NAME LIKE 'T_STK_%' THEN '库存管理'
                WHEN TABLE_NAME LIKE 'T_AR_%' THEN '应收管理'
                WHEN TABLE_NAME LIKE 'T_AP_%' THEN '应付管理'
                WHEN TABLE_NAME LIKE 'T_SEC_%' THEN '安全管理'
                WHEN TABLE_NAME LIKE 'T_ORG_%' THEN '组织架构'
                ELSE '其他'
            END AS '模块分类'
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
            AND TABLE_NAME LIKE 'T_%'
        ORDER BY 模块分类, TABLE_NAME
        """
        
        tables_df = self.execute_query(sql_tables, "查询所有表")
        if tables_df is not None:
            self.results['tables'] = tables_df
            print(f"📊 发现 {len(tables_df)} 个金蝶表")
            
            # 按模块统计
            module_stats = tables_df['模块分类'].value_counts()
            print("📈 模块分布:")
            for module, count in module_stats.items():
                print(f"   {module}: {count} 个表")
    
    def analyze_user_operator_relation(self):
        """分析用户和操作员关联关系"""
        print("\n👥 分析用户和操作员关联关系...")
        
        # 检查关键表是否存在
        check_tables = ['T_SEC_USER', 'T_BD_OPERATOR', 'T_STK_INSTOCK']
        existing_tables = []
        
        for table in check_tables:
            sql_check = f"""
            SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = '{table}'
            """
            result = self.execute_query(sql_check, f"检查表 {table}")
            if result is not None and result.iloc[0]['count'] > 0:
                existing_tables.append(table)
                print(f"✅ 表 {table} 存在")
            else:
                print(f"❌ 表 {table} 不存在")
        
        self.results['existing_tables'] = existing_tables
        
        # 如果关键表存在，分析字段结构
        if 'T_SEC_USER' in existing_tables:
            sql_user_fields = """
            SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'T_SEC_USER'
            ORDER BY ORDINAL_POSITION
            """
            user_fields = self.execute_query(sql_user_fields, "T_SEC_USER表结构")
            if user_fields is not None:
                self.results['user_fields'] = user_fields
        
        if 'T_BD_OPERATOR' in existing_tables:
            sql_operator_fields = """
            SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'T_BD_OPERATOR'
            ORDER BY ORDINAL_POSITION
            """
            operator_fields = self.execute_query(sql_operator_fields, "T_BD_OPERATOR表结构")
            if operator_fields is not None:
                self.results['operator_fields'] = operator_fields
    
    def test_user_operator_join(self):
        """测试用户和操作员的关联方式"""
        print("\n🔗 测试用户和操作员关联方式...")
        
        if 'T_SEC_USER' not in self.results.get('existing_tables', []) or \
           'T_BD_OPERATOR' not in self.results.get('existing_tables', []):
            print("❌ 缺少必要的表，无法测试关联")
            return
        
        # 测试方案1：通过FUSERID关联
        sql_test1 = """
        SELECT TOP 5
            u.FUSERID, u.FNAME as 用户名, u.FNUMBER as 用户编号,
            op.FOperatorId, op.FNAME as 操作员名, op.FNUMBER as 操作员工号
        FROM T_SEC_USER u
        LEFT JOIN T_BD_OPERATOR op ON u.FUSERID = op.FUSERID
        WHERE u.FUSERID IS NOT NULL
        """
        
        test1_result = self.execute_query(sql_test1, "测试方案1: FUSERID关联")
        if test1_result is not None:
            success_rate1 = (test1_result['操作员工号'].notna().sum() / len(test1_result)) * 100
            print(f"📊 方案1成功率: {success_rate1:.1f}%")
            self.results['test1_result'] = test1_result
            self.results['test1_success_rate'] = success_rate1
        
        # 测试方案2：通过FNUMBER关联
        sql_test2 = """
        SELECT TOP 5
            u.FUSERID, u.FNAME as 用户名, u.FNUMBER as 用户编号,
            op.FOperatorId, op.FNAME as 操作员名, op.FNUMBER as 操作员工号
        FROM T_SEC_USER u
        LEFT JOIN T_BD_OPERATOR op ON u.FNUMBER = op.FNUMBER
        WHERE u.FNUMBER IS NOT NULL
        """
        
        test2_result = self.execute_query(sql_test2, "测试方案2: FNUMBER关联")
        if test2_result is not None:
            success_rate2 = (test2_result['操作员工号'].notna().sum() / len(test2_result)) * 100
            print(f"📊 方案2成功率: {success_rate2:.1f}%")
            self.results['test2_result'] = test2_result
            self.results['test2_success_rate'] = success_rate2
    
    def generate_optimized_query(self):
        """生成优化的物料到期查询"""
        print("\n🎯 生成优化的查询语句...")
        
        # 根据测试结果选择最佳关联方式
        best_join = ""
        if 'test1_success_rate' in self.results and 'test2_success_rate' in self.results:
            if self.results['test1_success_rate'] > self.results['test2_success_rate']:
                best_join = "LEFT JOIN T_BD_OPERATOR creator_op ON creator.FUSERID = creator_op.FUSERID"
                print("🏆 选择方案1: 通过FUSERID关联")
            else:
                best_join = "LEFT JOIN T_BD_OPERATOR creator_op ON creator.FNUMBER = creator_op.FNUMBER"
                print("🏆 选择方案2: 通过FNUMBER关联")
        else:
            best_join = "LEFT JOIN T_BD_OPERATOR creator_op ON creator.FUSERID = creator_op.FUSERID"
            print("⚠️ 使用默认方案: 通过FUSERID关联")
        
        optimized_query = f"""
-- 物料到期查询 - 自动优化版
-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
SELECT 
    h.FBILLNO AS '入库单号',
    h.FDATE AS '入库日期',
    mat.FNUMBER AS '物料编码',
    m.FNAME AS '物料名称',
    m.FSPECIFICATION AS '规格型号',
    e.F_JSHL_DATE_83G AS '生产日期',
    e.F_JSHL_QTY_QTR AS '保质期(天)',
    DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) AS '计算到期日期',
    DATEDIFF(day, GETDATE(), DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G)) AS '剩余天数',
    e.FREALQTY AS '实收数量',
    e.FBASEUNITQTY AS '基本单位数量',
    org.FNAME AS '仓库组织',
    creator.FNAME AS '制单人',
    creator_op.FNUMBER AS '制单人工号',
    h.FCREATEDATE AS '制单日期'
FROM 
    T_STK_INSTOCK h
    INNER JOIN T_STK_INSTOCKENTRY e ON h.FID = e.FID
    LEFT JOIN T_BD_MATERIAL_L m ON e.FMATERIALID = m.FMATERIALID AND m.FLOCALEID = 2052
    LEFT JOIN T_BD_MATERIAL mat ON e.FMATERIALID = mat.FMATERIALID
    LEFT JOIN t_org_organizations_l org ON h.FSTOCKORGID = org.FORGID AND org.FLOCALEID = 2052
    LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
    {best_join}
WHERE 
    e.F_JSHL_DATE_83G IS NOT NULL
    AND e.F_JSHL_QTY_QTR IS NOT NULL
    AND h.FCancelStatus = 'A'
    AND h.FDocumentStatus = 'C'
ORDER BY 
    DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) ASC,
    h.FBILLNO;
"""
        
        self.results['optimized_query'] = optimized_query
        
        # 保存到文件
        with open('物料到期查询_自动优化版.sql', 'w', encoding='utf-8') as f:
            f.write(optimized_query)
        print("💾 优化查询已保存到: 物料到期查询_自动优化版.sql")
    
    def save_analysis_report(self):
        """保存分析报告"""
        print("\n📄 生成分析报告...")
        
        report = {
            "分析时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "数据库信息": {
                "服务器": SERVER,
                "数据库": DATABASE,
                "用户": USERNAME
            },
            "表统计": {},
            "关联测试结果": {},
            "建议": []
        }
        
        # 添加表统计信息
        if 'tables' in self.results:
            module_stats = self.results['tables']['模块分类'].value_counts().to_dict()
            report["表统计"] = module_stats
        
        # 添加关联测试结果
        if 'test1_success_rate' in self.results:
            report["关联测试结果"]["方案1_FUSERID关联"] = f"{self.results['test1_success_rate']:.1f}%"
        if 'test2_success_rate' in self.results:
            report["关联测试结果"]["方案2_FNUMBER关联"] = f"{self.results['test2_success_rate']:.1f}%"
        
        # 添加建议
        if 'test1_success_rate' in self.results and 'test2_success_rate' in self.results:
            if self.results['test1_success_rate'] > 50:
                report["建议"].append("推荐使用FUSERID关联获取制单人工号")
            elif self.results['test2_success_rate'] > 50:
                report["建议"].append("推荐使用FNUMBER关联获取制单人工号")
            else:
                report["建议"].append("两种关联方式效果都不理想，建议检查数据完整性")
        
        # 保存报告
        with open('数据库结构分析报告.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("📊 分析报告已保存到: 数据库结构分析报告.json")
        
        # 打印摘要
        print("\n📋 分析摘要:")
        print(f"   发现表数量: {len(self.results.get('tables', []))}")
        print(f"   关键表状态: {len(self.results.get('existing_tables', []))}/3 存在")
        if 'test1_success_rate' in self.results:
            print(f"   最佳关联方案: {'FUSERID' if self.results['test1_success_rate'] > self.results.get('test2_success_rate', 0) else 'FNUMBER'}")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🚀 开始全自动数据库结构分析...")
        print("=" * 50)
        
        if not self.connect():
            return False
        
        try:
            # 执行各项分析
            self.analyze_table_structure()
            self.analyze_user_operator_relation()
            self.test_user_operator_join()
            self.generate_optimized_query()
            self.save_analysis_report()
            
            print("\n" + "=" * 50)
            print("🎉 分析完成！生成的文件:")
            print("   📄 物料到期查询_自动优化版.sql")
            print("   📊 数据库结构分析报告.json")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析过程中出现错误: {e}")
            return False
        
        finally:
            if self.conn:
                self.conn.close()
                print("🔌 数据库连接已关闭")

    def generate_customer_debt_inserts(self):
        """生成客户欠款数据插入语句"""
        print("\n💰 生成客户欠款插入语句...")

        # 客户欠款数据
        debt_data = """2025/4/30	杨少军	北京鸿展兴都农副产品有限公司	-55802.6902
2025/4/30	杨少军	沈阳鸿展旺哲农产品有限公司	0
2025/4/30	杨泽平	稀土高新区周明哲蔬菜批发部	171.61
2025/4/30	杨少军	黑龙江鸿展旺都农副产品有限公司	0
2025/4/30	范庆元	高碑店市瑞鲜商贸有限公司	0
2025/4/30	范庆元	鹿泉区学军蔬菜批发部	45
2025/4/30	范庆元	保定冀天鲜商贸有限公司	-13770
2025/4/30	范庆元	定州静卓蔬菜经营部	0
2025/4/30	范庆元	天津鸿凯发商贸有限公司	0
2025/4/30	范庆元	天津市正华航宇商贸有限公司	0"""

        lines = debt_data.strip().split('\n')
        insert_statements = []

        insert_statements.append("-- 客户欠款数据插入语句")
        insert_statements.append(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        insert_statements.append("-- 表结构: T_CUSTOMER_DEBT (FID, F日期, F客户名称, F业务员, F当前欠款)")
        insert_statements.append("")

        for line in lines[:10]:  # 只处理前10条作为示例
            parts = line.split('\t')
            if len(parts) >= 4:
                date = parts[0]
                salesperson = parts[1]
                customer = parts[2]
                amount = parts[3]

                insert_sql = f"INSERT INTO T_CUSTOMER_DEBT (F日期, F客户名称, F业务员, F当前欠款) VALUES ('{date}', '{customer}', '{salesperson}', {amount});"
                insert_statements.append(insert_sql)

        insert_content = '\n'.join(insert_statements)

        # 保存到文件
        with open('客户欠款插入语句.sql', 'w', encoding='utf-8') as f:
            f.write(insert_content)

        print("💾 客户欠款插入语句已保存到: 客户欠款插入语句.sql")
        self.results['customer_debt_inserts'] = insert_content

def main():
    """主函数"""
    analyzer = DatabaseAnalyzer()
    success = analyzer.run_full_analysis()

    # 额外生成客户欠款插入语句
    if analyzer.conn or analyzer.connect():
        analyzer.generate_customer_debt_inserts()

    if success:
        print("\n✅ 全自动分析成功完成！")
        print("💡 生成的文件:")
        print("   📄 物料到期查询_自动优化版.sql")
        print("   📊 数据库结构分析报告.json")
        print("   💰 客户欠款插入语句.sql")
    else:
        print("\n❌ 分析失败，请检查数据库连接和权限")

if __name__ == "__main__":
    main()
