-- 简化诊断查询 - 逐步排查工号关联问题

-- 第一步：检查用户表结构和数据
SELECT TOP 5
    '用户表基本信息' AS 类型,
    FUSERID AS 用户ID,
    FNAME AS 用户姓名,
    FUSERACCOUNT AS 用户账号,
    FEMPID AS 员工ID字段
FROM T_SEC_USER
WHERE FNAME IS NOT NULL
ORDER BY FUSERID;

-- 第二步：检查操作员分录表结构和数据
SELECT TOP 5
    '操作员分录表信息' AS 类型,
    FOPERATORID AS 操作员ID,
    FNUMBER AS 工号,
    FSTAFFID AS 员工ID,
    FISUSE AS 是否启用
FROM T_BD_OPERATORENTRY
WHERE FNUMBER IS NOT NULL
ORDER BY FOPERATORID;

-- 第三步：检查员工表结构和数据
SELECT TOP 5
    '员工表信息' AS 类型,
    FSTAFFID AS 员工ID,
    FNUMBER AS 员工工号,
    FNAME AS 员工姓名
FROM T_BD_STAFF
WHERE FNUMBER IS NOT NULL
ORDER BY FSTAFFID;

-- 第四步：检查入库单的制单人ID
SELECT TOP 5
    '入库单制单人' AS 类型,
    FBILLNO AS 单据号,
    FCREATORID AS 制单人ID,
    FCREATEDATE AS 制单日期
FROM T_STK_INSTOCK
WHERE FCREATORID IS NOT NULL
ORDER BY FCREATEDATE DESC;
