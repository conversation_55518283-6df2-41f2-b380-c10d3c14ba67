-- 最终合并查询，包含SP入库、STK入库、调拨入库，并扣除退库数量
SELECT
    COALESCE(SP.FSTOCKORGID, STK.FSTOCKORGID, TRANSFER.FSTOCKORGID) AS '入库组织',
    COALESCE(SP.物料编码, STK.物料编码, TRANSFER.物料编码) AS '物料编码',
    COALESCE(SP.FSTOCKID, STK.FSTOCKID, TRANSFER.FSTOCKID) AS '仓库',
    COALESCE(SP.仓库名称, STK.仓库名称, TRANSFER.仓库名称) AS '仓库名称',
    -- 合计数量，包含调拨入库，扣除退库数量
    COALESCE(SP.实收数量, 0) + COALESCE(STK.基本单位数量, 0) + COALESCE(TRANSFER.调拨入库数量, 0) - COALESCE(RET.退库基本单位数量, 0) AS '实收数量',
    COALESCE(SP.辅助单位, 0) + COALESCE(STK.辅助单位数量, 0) + COALESCE(TRANSFER.调拨入库辅助数量, 0) - COALESCE(RET.退库辅助单位数量, 0) AS '辅助单位',
    COALESCE(SP.物料名称, STK.物料名称, TRANSFER.物料名称) AS '物料名称'
FROM
(
    -- SP入库数据
    SELECT
        a.FSTOCKORGID,
        e.FNUMBER AS '物料编码',
        d.FNAME AS '物料名称',
        b.FSTOCKID,
        c.FNAME AS '仓库名称',
        SUM(b.FREALQTY) AS '实收数量',
        SUM(b.FSECREALQTY) AS '辅助单位'
    FROM
        T_SP_INSTOCK a
    LEFT JOIN
        T_SP_INSTOCKENTRY b ON a.FID = b.FID
    LEFT JOIN
        t_BD_Stock_L c ON c.FSTOCKID = b.FSTOCKID
    LEFT JOIN
        T_BD_MATERIAL e ON e.FMATERIALID = b.FMATERIALID
    LEFT JOIN
        T_BD_MATERIAL_L d ON b.FMATERIALID = d.FMATERIALID
    WHERE
        1=1
        AND a.FDOCUMENTSTATUS = 'C' AND a.FCANCELSTATUS= 'A'
        AND c.FNAME LIKE '%成品%' AND c.FNAME NOT LIKE '%半%'
        --AND b.FSTOCKID in ('120519')
${if(len(仓库)==0,"","and b.FSTOCKID in ('"+ 仓库 +"')")}
${if(len(截止日期)==0,"","AND a.FDATE <= '"+ 截止日期 +"'")}
    GROUP BY
        a.FSTOCKORGID,
        e.FNUMBER,
        d.FNAME,
        b.FSTOCKID,
        c.FNAME
) SP
FULL OUTER JOIN
(
    -- STK入库数据
    SELECT
        a.FSTOCKORGID,
        e.FNUMBER AS '物料编码',
        d.FNAME AS '物料名称',
        b.FSTOCKID,
        c.FNAME AS '仓库名称',
        SUM(b.FBASEUNITQTY) AS '基本单位数量',
        SUM(b.FAUXUNITQTY) AS '辅助单位数量'
    FROM
        T_STK_INSTOCK a
    LEFT JOIN
        T_STK_INSTOCKENTRY b ON a.FID = b.FID
    LEFT JOIN
        t_BD_Stock_L c ON c.FSTOCKID = b.FSTOCKID AND c.FLOCALEID = 2052
    LEFT JOIN
        T_BD_MATERIAL e ON e.FMATERIALID = b.FMATERIALID
    LEFT JOIN
        T_BD_MATERIAL_L d ON b.FMATERIALID = d.FMATERIALID AND d.FLOCALEID = 2052
    WHERE
        1=1
        AND a.FDocumentStatus = 'C'
        AND a.FCancelStatus = 'A'
        AND c.FNAME LIKE '%成品%' AND c.FNAME NOT LIKE '%半%'
        --AND b.FSTOCKID in ('120519')
${if(len(仓库)==0,"","and b.FSTOCKID in ('"+ 仓库 +"')")}
${if(len(截止日期)==0,"","AND a.FDATE <= '"+ 截止日期 +"'")}
    GROUP BY
        a.FSTOCKORGID,
        e.FNUMBER,
        d.FNAME,
        b.FSTOCKID,
        c.FNAME
) STK
ON SP.FSTOCKORGID = STK.FSTOCKORGID
AND SP.物料编码 = STK.物料编码
AND SP.FSTOCKID = STK.FSTOCKID
FULL OUTER JOIN
(
    -- 调拨入库数据
    SELECT
        h.FSTOCKORGID,
        mat_base.FNUMBER AS '物料编码',
        mat_l.FNAME AS '物料名称',
        e.FDestStockID AS FSTOCKID,
        stock_l.FNAME AS '仓库名称',
        SUM(e.FQty) AS '调拨入库数量',
        SUM(e.FSecQty) AS '调拨入库辅助数量'
    FROM T_STK_STKTRANSFERIN h
    INNER JOIN T_STK_STKTRANSFERINENTRY e ON h.FID = e.FID
    LEFT JOIN T_BD_MATERIAL mat_base ON e.FMaterialID = mat_base.FMATERIALID
    LEFT JOIN T_BD_MATERIAL_L mat_l ON e.FMaterialID = mat_l.FMATERIALID
        AND mat_l.FLOCALEID = 2052
    LEFT JOIN t_BD_Stock_L stock_l ON e.FDestStockID = stock_l.FStockId
        AND stock_l.FLocaleId = 2052
    WHERE 1=1
        AND h.FDOCUMENTSTATUS = 'C'  -- 只查询已审核的单据
        AND stock_l.FNAME LIKE '%成品%' AND stock_l.FNAME NOT LIKE '%半%'
        --AND e.FDestStockID in ('120519')
${if(len(仓库)==0,"","and e.FDestStockID in ('"+ 仓库 +"')")}
${if(len(截止日期)==0,"","AND h.FDATE <= '"+ 截止日期 +"'")}
    GROUP BY
        h.FSTOCKORGID,
        mat_base.FNUMBER,
        mat_l.FNAME,
        e.FDestStockID,
        stock_l.FNAME
) TRANSFER
ON COALESCE(SP.FSTOCKORGID, STK.FSTOCKORGID) = TRANSFER.FSTOCKORGID
AND COALESCE(SP.物料编码, STK.物料编码) = TRANSFER.物料编码
AND COALESCE(SP.FSTOCKID, STK.FSTOCKID) = TRANSFER.FSTOCKID
LEFT JOIN
(
    -- 合并退库单数据
    SELECT
        FSTOCKORGID,
        物料编码,
        FSTOCKID,
        SUM(基本单位退库数量) AS 退库基本单位数量,
        SUM(辅助单位退库数量) AS 退库辅助单位数量
    FROM (
        -- 生产退库单
        SELECT
            a.FSTOCKORGID,
            e.FNUMBER AS '物料编码',
            b.FSTOCKID,
            SUM(b.FBASEPRDREALQTY) AS 基本单位退库数量,
            SUM(b.FSECREALQTY) AS 辅助单位退库数量
        FROM
            T_PRD_RESTOCK a
        LEFT JOIN
            T_PRD_RESTOCKENTRY b ON a.FID = b.FID
        LEFT JOIN
            T_BD_MATERIAL e ON e.FMATERIALID = b.FMATERIALID
        WHERE
            a.FDOCUMENTSTATUS = 'C'
            --AND b.FSTOCKID in ('120519')
             ${if(len(仓库)==0,"","and b.FSTOCKID in ('"+ 仓库 +"')")}
            ${if(len(截止日期)==0,"","AND a.FDATE <= '"+ 截止日期 +"'")}
        GROUP BY
            a.FSTOCKORGID,
            e.FNUMBER,
            b.FSTOCKID

        UNION ALL

        -- 简单生产退库单
        SELECT
            a.FSTOCKORGID,
            e.FNUMBER AS '物料编码',
            b.FSTOCKID,
            SUM(b.FBASEOUTQTY) AS 基本单位退库数量,
            SUM(b.FSECOUTQTY) AS 辅助单位退库数量
        FROM
            T_SP_OUTSTOCK a
        LEFT JOIN
            T_SP_OUTSTOCKENTRY b ON a.FID = b.FID
        LEFT JOIN
            T_BD_MATERIAL e ON e.FMATERIALID = b.FMATERIALID
        WHERE
            a.FDOCUMENTSTATUS = 'C'
            --AND b.FSTOCKID in ('120519')
            ${if(len(仓库)==0,"","and b.FSTOCKID in ('"+ 仓库 +"')")}
            ${if(len(截止日期)==0,"","AND a.FDATE <= '"+ 截止日期 +"'")}
        GROUP BY
            a.FSTOCKORGID,
            e.FNUMBER,
            b.FSTOCKID
    ) AS 合并退库
    GROUP BY
        FSTOCKORGID,
        物料编码,
        FSTOCKID
) RET
ON COALESCE(SP.FSTOCKORGID, STK.FSTOCKORGID, TRANSFER.FSTOCKORGID) = RET.FSTOCKORGID
AND COALESCE(SP.物料编码, STK.物料编码, TRANSFER.物料编码) = RET.物料编码
AND COALESCE(SP.FSTOCKID, STK.FSTOCKID, TRANSFER.FSTOCKID) = RET.FSTOCKID
ORDER BY
    COALESCE(SP.物料编码, STK.物料编码, TRANSFER.物料编码),
    COALESCE(SP.FSTOCKID, STK.FSTOCKID, TRANSFER.FSTOCKID)
