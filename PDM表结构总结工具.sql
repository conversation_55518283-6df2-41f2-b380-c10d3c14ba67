-- PDM表结构总结工具
-- 用于分析金蝶K/3 Cloud数据库的完整结构

-- 1. 生成所有表的字段清单
SELECT 
    t.TABLE_NAME AS '表名',
    COUNT(c.COLUMN_NAME) AS '字段数量',
    STRING_AGG(c.COLUMN_NAME, ', ') AS '所有字段'
FROM INFORMATION_SCHEMA.TABLES t
INNER JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
WHERE t.TABLE_TYPE = 'BASE TABLE'
    AND t.TABLE_NAME LIKE 'T_%'
GROUP BY t.TABLE_NAME
ORDER BY t.TABLE_NAME;

-- 2. 查找包含特定关键字的字段
-- 用户相关字段
SELECT 
    '用户相关' AS '分类',
    TABLE_NAME AS '表名',
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME LIKE 'T_%'
    AND (COLUMN_NAME LIKE '%USER%' 
         OR COLUMN_NAME LIKE '%CREATOR%'
         OR COLUMN_NAME LIKE '%MODIFIER%'
         OR COLUMN_NAME LIKE '%AUDITOR%')

UNION ALL

-- 操作员相关字段
SELECT 
    '操作员相关',
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME LIKE 'T_%'
    AND (COLUMN_NAME LIKE '%OPERATOR%'
         OR COLUMN_NAME LIKE '%STOCKER%')

UNION ALL

-- 编号相关字段
SELECT 
    '编号相关',
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME LIKE 'T_%'
    AND COLUMN_NAME = 'FNUMBER'

UNION ALL

-- 组织相关字段
SELECT 
    '组织相关',
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME LIKE 'T_%'
    AND (COLUMN_NAME LIKE '%ORG%'
         OR COLUMN_NAME LIKE '%DEPT%')

ORDER BY 分类, 表名, 字段名;

-- 3. 分析主要业务表的关联关系
-- 入库相关表
SELECT 
    '入库业务' AS '业务类型',
    TABLE_NAME AS '表名',
    CASE 
        WHEN TABLE_NAME LIKE '%INSTOCK%' AND TABLE_NAME NOT LIKE '%ENTRY%' THEN '入库主表'
        WHEN TABLE_NAME LIKE '%INSTOCKENTRY%' THEN '入库明细表'
        WHEN TABLE_NAME LIKE '%STK_%' THEN '库存相关表'
        ELSE '其他'
    END AS '表类型'
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_TYPE = 'BASE TABLE'
    AND (TABLE_NAME LIKE '%INSTOCK%' 
         OR TABLE_NAME LIKE '%STK_%')

UNION ALL

-- 基础资料表
SELECT 
    '基础资料',
    TABLE_NAME,
    CASE 
        WHEN TABLE_NAME LIKE '%MATERIAL%' THEN '物料表'
        WHEN TABLE_NAME LIKE '%CUSTOMER%' THEN '客户表'
        WHEN TABLE_NAME LIKE '%SUPPLIER%' THEN '供应商表'
        WHEN TABLE_NAME LIKE '%OPERATOR%' THEN '操作员表'
        WHEN TABLE_NAME LIKE '%USER%' THEN '用户表'
        WHEN TABLE_NAME LIKE '%ORG%' THEN '组织表'
        WHEN TABLE_NAME LIKE '%DEPT%' THEN '部门表'
        ELSE '其他基础资料'
    END
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_TYPE = 'BASE TABLE'
    AND TABLE_NAME LIKE 'T_BD_%'

ORDER BY 业务类型, 表类型, 表名;

-- 4. 生成常用关联关系的模板SQL
SELECT 
'-- 用户和操作员关联模板
LEFT JOIN T_SEC_USER u ON 主表.FCREATORID = u.FUSERID
LEFT JOIN T_BD_OPERATOR op ON u.FUSERID = op.FUSERID  -- 或其他关联方式

-- 物料信息关联模板  
LEFT JOIN T_BD_MATERIAL mat ON 明细表.FMATERIALID = mat.FMATERIALID
LEFT JOIN T_BD_MATERIAL_L mat_l ON mat.FMATERIALID = mat_l.FMATERIALID AND mat_l.FLOCALEID = 2052

-- 组织信息关联模板
LEFT JOIN T_ORG_ORGANIZATIONS org ON 主表.FSTOCKORGID = org.FORGID
LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLOCALEID = 2052

-- 客户信息关联模板
LEFT JOIN T_BD_CUSTOMER cust ON 主表.FCUSTOMERID = cust.FCUSTID
LEFT JOIN T_BD_CUSTOMER_L cust_l ON cust.FCUSTID = cust_l.FCUSTID AND cust_l.FLOCALEID = 2052' AS 'SQL模板';

-- 5. 检查表是否存在的验证SQL
SELECT 
    '验证表存在性' AS '检查项',
    CASE WHEN EXISTS(SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'T_SEC_USER') 
         THEN 'T_SEC_USER 存在' ELSE 'T_SEC_USER 不存在' END AS '用户表',
    CASE WHEN EXISTS(SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'T_BD_OPERATOR') 
         THEN 'T_BD_OPERATOR 存在' ELSE 'T_BD_OPERATOR 不存在' END AS '操作员表',
    CASE WHEN EXISTS(SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'T_STK_INSTOCK') 
         THEN 'T_STK_INSTOCK 存在' ELSE 'T_STK_INSTOCK 不存在' END AS '入库主表',
    CASE WHEN EXISTS(SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'T_BD_MATERIAL') 
         THEN 'T_BD_MATERIAL 存在' ELSE 'T_BD_MATERIAL 不存在' END AS '物料表';
