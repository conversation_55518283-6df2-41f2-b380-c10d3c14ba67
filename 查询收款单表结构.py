import pyodbc
import pandas as pd

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

def connect_to_database():
    """连接到SQL Server数据库"""
    connection_string = f"""
    DRIVER={{{DRIVER}}};
    SERVER={SERVER};
    DATABASE={DATABASE};
    UID={USERNAME};
    PWD={PASSWORD};
    TrustServerCertificate=yes;
    """
    try:
        conn = pyodbc.connect(connection_string)
        print("数据库连接成功！")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def find_receivebill_tables(conn):
    """查找收款单相关的表"""
    query = """
    SELECT TABLE_NAME
    FROM INFORMATION_SCHEMA.TABLES
    WHERE TABLE_NAME LIKE '%RECEIV%'
       OR TABLE_NAME LIKE '%收款%'
       OR TABLE_NAME LIKE '%COLLECTION%'
       OR TABLE_NAME LIKE '%RECEIPT%'
       OR TABLE_NAME LIKE 'T_AR_%'
    ORDER BY TABLE_NAME;
    """
    try:
        df = pd.read_sql(query, conn)
        print("找到的相关表:")
        print(df)
        return df
    except Exception as e:
        print(f"查询表名失败: {e}")
        return None

def get_table_structure(conn, table_name):
    """获取表结构"""
    query = f"""
    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = '{table_name}'
    ORDER BY ORDINAL_POSITION;
    """
    try:
        df = pd.read_sql(query, conn)
        print(f"\n表 {table_name} 的结构:")
        print(df)
        return df
    except Exception as e:
        print(f"查询表结构失败: {e}")
        return None

def get_sample_data(conn, table_name, limit=5):
    """获取表的示例数据"""
    query = f"SELECT TOP {limit} * FROM {table_name}"
    try:
        df = pd.read_sql(query, conn)
        print(f"\n表 {table_name} 的示例数据:")
        print(df)
        return df
    except Exception as e:
        print(f"查询示例数据失败: {e}")
        return None

def main():
    # 连接数据库
    conn = connect_to_database()
    if not conn:
        return

    try:
        # 查看收款单主表的完整字段列表
        print("=== 收款单主表 T_AR_RECEIVEBILL 完整字段 ===")
        main_structure = get_table_structure(conn, 'T_AR_RECEIVEBILL')

        # 查看收款单分录表的完整字段列表
        print("\n=== 收款单分录表 T_AR_RECEIVEBILLENTRY 完整字段 ===")
        entry_structure = get_table_structure(conn, 'T_AR_RECEIVEBILLENTRY')

        # 查看收款单主表示例数据的前几个字段
        print("\n=== 收款单主表示例数据（前10个字段）===")
        main_sample_query = """
        SELECT TOP 3 FID, FBILLNO, FDOCUMENTSTATUS, FDATE, FCUSTOMERID, FCURRENCYID, FEXCHANGERATE
        FROM T_AR_RECEIVEBILL
        """
        try:
            main_sample = pd.read_sql(main_sample_query, conn)
            print(main_sample)
        except Exception as e:
            print(f"查询主表示例数据失败: {e}")
            # 尝试其他可能的字段名
            alt_query = """
            SELECT TOP 3 FID, FBILLNO, FDOCUMENTSTATUS, FDATE
            FROM T_AR_RECEIVEBILL
            """
            try:
                alt_sample = pd.read_sql(alt_query, conn)
                print("使用替代字段查询:")
                print(alt_sample)
            except Exception as e2:
                print(f"替代查询也失败: {e2}")

        # 查看收款单分录表示例数据的关键字段
        print("\n=== 收款单分录表示例数据（关键字段）===")
        entry_sample_query = """
        SELECT TOP 3 FENTRYID, FID, FSETTLERECAMOUNTFOR, FSETTLERECAMOUNT
        FROM T_AR_RECEIVEBILLENTRY
        """
        try:
            entry_sample = pd.read_sql(entry_sample_query, conn)
            print(entry_sample)
        except Exception as e:
            print(f"查询分录表示例数据失败: {e}")

        # 查找收款单表中包含客户的字段
        print("\n=== 收款单表中的客户相关字段 ===")
        receivebill_customer_query = """
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME IN ('T_AR_RECEIVEBILL', 'T_AR_RECEIVEBILLENTRY')
        AND (COLUMN_NAME LIKE '%CUST%' OR COLUMN_NAME LIKE '%客户%')
        ORDER BY TABLE_NAME, COLUMN_NAME;
        """
        receivebill_customer = pd.read_sql(receivebill_customer_query, conn)
        print(receivebill_customer)

        # 查找收款单表中的金额字段
        print("\n=== 收款单表中的金额字段 ===")
        receivebill_amount_query = """
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME IN ('T_AR_RECEIVEBILL', 'T_AR_RECEIVEBILLENTRY')
        AND (COLUMN_NAME LIKE '%AMOUNT%' OR COLUMN_NAME LIKE '%金额%' OR COLUMN_NAME LIKE '%AMT%')
        ORDER BY TABLE_NAME, COLUMN_NAME;
        """
        receivebill_amount = pd.read_sql(receivebill_amount_query, conn)
        print(receivebill_amount)

        # 查找可能的客户关联表
        print("\n=== 查找可能的客户关联方式 ===")
        # 查看收款单主表的所有字段，寻找可能的关联字段
        all_fields_query = """
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILL'
        AND (COLUMN_NAME LIKE '%ID' OR COLUMN_NAME LIKE '%REF%' OR COLUMN_NAME LIKE '%LINK%')
        ORDER BY COLUMN_NAME;
        """
        all_fields = pd.read_sql(all_fields_query, conn)
        print("收款单主表中可能的关联字段:")
        print(all_fields)

        # 尝试查找应收单表，收款单可能关联应收单，应收单再关联客户
        print("\n=== 查找应收单相关表 ===")
        receivable_tables_query = """
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME LIKE '%RECEIVABLE%' OR TABLE_NAME LIKE '%应收%'
        ORDER BY TABLE_NAME;
        """
        receivable_tables = pd.read_sql(receivable_tables_query, conn)
        print("应收相关表:")
        print(receivable_tables)

        # 如果找到应收表，查看其结构
        if not receivable_tables.empty:
            for table in ['T_AR_RECEIVABLE', 'T_AR_RECEIVABLEENTRY']:
                try:
                    print(f"\n=== {table} 表结构（客户相关字段）===")
                    receivable_structure_query = f"""
                    SELECT COLUMN_NAME, DATA_TYPE
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = '{table}'
                    AND (COLUMN_NAME LIKE '%CUST%' OR COLUMN_NAME LIKE '%客户%' OR COLUMN_NAME LIKE '%ID')
                    ORDER BY COLUMN_NAME;
                    """
                    receivable_structure = pd.read_sql(receivable_structure_query, conn)
                    print(receivable_structure)
                except Exception as e:
                    print(f"查询 {table} 失败: {e}")

        # 尝试直接查询收款单和客户的关联
        print("\n=== 尝试通过应收单关联客户 ===")
        try:
            test_join_query = """
            SELECT TOP 3
                r.FID as 收款单ID,
                r.FBILLNO as 收款单号,
                r.FDATE as 收款日期,
                c_l.FNAME as 客户名称
            FROM T_AR_RECEIVEBILL r
            LEFT JOIN T_AR_RECEIVABLE ar ON r.FSRCBILLNO = ar.FBILLNO
            LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID
            LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
            WHERE r.FDOCUMENTSTATUS = 'C'
            """
            test_join = pd.read_sql(test_join_query, conn)
            print("通过应收单关联客户测试:")
            print(test_join)
        except Exception as e:
            print(f"关联查询失败: {e}")

            # 尝试其他关联方式
            try:
                print("\n=== 尝试其他关联方式 ===")
                alt_join_query = """
                SELECT TOP 3
                    r.FID,
                    r.FBILLNO,
                    r.FDATE,
                    re.FSETTLERECAMOUNT
                FROM T_AR_RECEIVEBILL r
                INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
                WHERE r.FDOCUMENTSTATUS = 'C'
                """
                alt_join = pd.read_sql(alt_join_query, conn)
                print("收款单基本信息:")
                print(alt_join)
            except Exception as e2:
                print(f"基本查询也失败: {e2}")

    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
