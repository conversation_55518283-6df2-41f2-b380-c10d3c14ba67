<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1546859654" ExtractionBranch="1" ExtractionDate="1546859654" ExtractionId="1291952" ExtractionVersion="6" ID="{537BA89F-C859-448C-9EBB-A5C2EE7D8377}" Label="" LastModificationDate="1547014660" Name="RETAIL零售" Objects="474" RepositoryId="{5C34CC33-BB05-4053-9571-E184DEFF2D78}" Symbols="1" Target="Microsoft SQL Server 2008" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>537BA89F-C859-448C-9EBB-A5C2EE7D8377</a:ObjectID>
<a:Name>RETAIL零售</a:Name>
<a:Code>RETAIL</a:Code>
<a:CreationDate>1543800223</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1547014660</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;\ExtendedSubObject &lt;&lt;AggregateParameter&gt;&gt;]
Create=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;ResourcePool&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;WorkloadGroup&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=0
RefrUpdateConstraint=0
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=

[ModelOptions\Default Opts\FRMEOBJ&lt;&lt;ResourcePool&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMEOBJ&lt;&lt;WorkloadGroup&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMESOB&lt;&lt;ResourcePool&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMESOB&lt;&lt;WorkloadGroup&gt;&gt;]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {5C34CC33-BB05-4053-9571-E184DEFF2D78}
MODEL_ID 1291952
MODEL_VRSN 6
BRANCH_ID 1
EXT_DATE 1546859654
EXT_END 1546859654
OBJECTS 
BEG_BLOCK 
 1291952 {537BA89F-C859-448C-9EBB-A5C2EE7D8377}
 1291953 {572462E6-176B-44D6-8262-6913E0B91157}
 1291954 {E1F65ED2-0E4B-4DF9-B8BD-B1A21107BEF3}
 1291955 {D22F8E5B-F834-4CB4-8F57-438D2314C428}
 1291956 {58860157-E0FA-443C-B8F9-D17F5F31949E}
 1291957 {B831D7CE-A316-4CAF-91EE-6C69FF3C6761}
 1291958 {904A6DA6-4656-4458-B2A8-38974169CB87}
 1295408 {557C2E31-B024-4168-B7DE-FC96E2AB6761}
 1295409 {A1D9989A-58F6-4776-B12C-B56E54A6F379}
 1295410 {FC4B51AF-BF2E-4109-802B-B75B0D7701E9}
 1295411 {27FFD9A3-2BDC-4F4B-BE41-C5F420417971}
 1295412 {89430523-9D70-4713-AC57-5EA1C13AF46C}
 1295413 {0F342F36-4F75-4A86-AFC6-97BA2A5E737B}
 1295414 {0B446908-9BA2-46B6-8CB4-653B02D21886}
 1295415 {60280E60-63E2-4F0E-974E-0AA4A38840EC}
 1295416 {29C9A2B8-0B02-46C1-8891-9653D4D0F52A}
 1295417 {B385E47F-55EA-4D20-BFB9-5DC5C564A9EB}
 1295418 {A83F8B85-CB42-4C70-BF7E-B522F2EF6BA6}
 1295419 {3E1A6CB5-DDF1-4F81-AFC4-9249169B38A4}
 1295420 {F7612EBF-4319-4648-9949-78858B761B5D}
 1295421 {3D53D538-55B7-4548-8E62-253ED2D90CF5}
 1295422 {CD9CE290-1DC7-4F81-8198-E8F412CEE606}
 1295423 {25DED71A-C5AC-4F13-94D5-9128D98D4A2D}
 1295424 {CE7A6557-5907-48EC-A20A-9AC516DDFCFD}
 1295425 {2FBB4576-3234-4578-A17B-7DC7A03108E3}
 1295426 {9C40B805-A6CB-49F9-A39A-CC1A8D36ACA6}
 1295427 {992FAF18-D7DE-49CD-B02B-2D6E9EDA74A6}
 1295428 {CE2485D5-28CA-4DD9-BAD4-26C3A2E53A98}
 1295429 {5434A7D7-3CD5-4E40-9EC0-6E66C5CB91F1}
 1295430 {72FEB46C-7F32-4747-BCAB-6E098FE93A0C}
 1295431 {C6A1580E-E580-4145-A1D6-B03E4448B92D}
 1295432 {329242B0-619C-4876-9699-3AD8868F5DE3}
 1295433 {EB3E728D-BFFE-4A35-9A95-0F18809ED62A}
 1295434 {DA794514-854C-4265-8FAD-181FBB16C310}
 1295435 {6AC9D0B9-3BED-4CB3-825B-A0F40F576855}
 1295436 {495CACCC-15DB-4792-90E5-7411B5065884}
 1295437 {68945B51-FAC3-4A44-B68F-C69F42F4F999}
 1295438 {9D22EF66-5A21-4678-83AF-908EBA7F00D1}
 1295439 {5928EA3D-187D-4068-801B-9CA7925F6453}
 1295440 {C04EA78C-F9D5-4784-B566-71A367C73A1B}
 1295441 {A685270E-F084-4F11-BD80-101E374940AD}
 1295914 {2B723B96-E5C1-4D9E-95BB-8C69EF92B303}
 1295915 {209CC352-320D-42D7-91EB-04F69B5CFA49}
 1295916 {B5308E88-2276-43E5-827C-EFDAD26EF196}
 1295917 {CB3C4AFF-490D-45EA-9C93-18F38EDDCFBF}
 1295918 {7BD51642-5547-4664-8D5B-FF9A10E81347}
 1295919 {F4BE1DBD-3D0E-4698-B03A-B59CD0072CB0}
 1295920 {CAEAEE10-9A49-429E-8B1C-04DD3AFD86C1}
 1295921 {840E271C-875C-4DBE-8165-FD57E0F7B997}
 1295922 {973F642B-F951-497B-85A1-EE13FC09FBE0}
 1295923 {D1846369-4B5E-40EF-90DF-846A9DF2927C}
 1295924 {16E27459-C676-428D-AAE6-4BA2236B6904}
 1295925 {2BC498AA-9134-48DB-AC27-F0D099E2C6E0}
 1295926 {AFB504AF-9403-480E-9402-192191EF4660}
 1295927 {685B677E-DC13-4329-BF6A-8CCD394C6756}
 1295928 {2E84DC84-29AC-4AE3-8348-BE40DD686DD9}
 1295929 {B748DD13-5512-44FE-AA47-21B9A8F0681A}
 1295930 {46AF9A96-E7E5-489D-8246-20A6B7542096}
 1295931 {324DA5CD-CFED-40ED-97DE-32F7D80E689D}
 1295932 {1E0D6354-4791-4602-B7CA-14B7388763EE}
 1295933 {876B82E5-FC73-42FB-99F9-540D01C5D29D}
 1295934 {07FA3619-7048-4E0C-AB3B-69E930AAAEEF}
 1295935 {32A822C5-5D22-4EFB-99A2-FC846876F143}
 1295936 {65881006-1705-4267-8782-6F2F74B6FB0F}
 1295937 {00A56710-89C1-460B-BAD9-50B1AA7B731E}
 1295938 {021FFC4A-C3F9-45B7-B31E-7A9D477345F6}
 1295939 {4786C9B8-97EC-40A0-A969-0034269F4F3C}
 1295940 {9B3AF592-4F23-48DD-B840-79FC5175CD2E}
 1295941 {792F98B5-3FB7-4E05-8BE9-79170A8A0A8D}
 1295942 {A4D2AB4D-F7C7-4968-97DB-3FA79DB2CD82}
 1295943 {F647932B-0C87-4BD5-B10E-FC6178276872}
 1295944 {0B875A66-197F-4364-A0B3-249A86314EFC}
 1295945 {972C7934-A157-4A10-8B4C-E9DE480B2E03}
 1295946 {F2EA9BF8-03A0-4A4D-9317-6A868B5164A5}
 1295947 {C2559A79-34E3-4BDD-A4C5-1F733CFE830D}
 1295948 {94CD2638-815D-45DB-8622-38FF5BA39746}
 1295949 {63E8B9CD-605D-4807-9001-CE8C445CE5E5}
 1295950 {BD7765A0-6C3B-464B-A125-C98AE151B4E4}
 1295951 {9000C8AB-174E-40E5-8EE4-25B82CF3C70C}
 1295952 {12B9E944-4EE4-4D0F-97EE-766E42CAFBD9}
 1295953 {E5C8DD4C-79DC-418E-956D-B8868A91362A}
 1295954 {D12A9A43-446D-4159-98D7-864D9E2CD94C}
 1295955 {B39CD0A0-52E7-4D33-88DA-1E887A0FDB70}
 1295956 {6DBA8A66-7233-4937-8DF3-680ED4F5FEAF}
 1295957 {C2858064-AB81-4235-97D1-034DA7FF5A1A}
 1295958 {564F8640-FFFA-47C6-9572-26D7D5638CA8}
 1295959 {DB3466AA-3E3C-4657-B7F3-A88F3FD51037}
 1295960 {631112CD-EAB0-456E-AD3E-44113908C22C}
 1295961 {3B33694F-F35D-4937-A88E-35E284EE9880}
 1295962 {84D74334-8EC0-434F-9BEB-8707D39BBCF5}
 1295963 {D28D499D-B79F-481A-990C-547DBD0BC717}
 1295964 {77160FF7-4575-4EDA-B4C4-93A1AEA227CB}
 1295965 {629131A2-7F16-4A01-A034-CFEC2D8F5E09}
 1295966 {E75F9B3E-94E3-4222-AAE5-6369E5EE29E1}
 1295967 {0C84546F-A426-4B17-A757-84E88399B064}
 1295968 {476E2293-D48C-4D90-8389-ACA9C5E88D12}
 1295969 {DF561405-02ED-4E2B-A6DF-A29704AC997E}
 1295970 {195B5E1D-8683-463E-936B-901AAD48CF1E}
 1295971 {DC54037B-1E76-461B-AFBB-2577EF1A6D25}
 1295972 {1996945B-52D9-4634-914D-83D63E9673FB}
 1295973 {9D573958-DD1E-4B31-98DF-4709DF021695}
 1295974 {084C738A-93EB-4F40-8482-DFF45C70FAB7}
 1295975 {185E5E06-F85C-4583-B5E4-47E351C3EEB0}
 1295976 {BD67AC9F-EEDD-469C-84BB-A2CA4F50F757}
 1295977 {8617AD23-0672-4133-AD36-94938A387A97}
 1295978 {8283BA60-D380-4128-8182-2CB379334A56}
 1295979 {B05BF4B6-062A-4A0C-9080-4DB8754B8FE0}
 1295980 {26DAF350-BB9C-4DD3-8396-F1FFA6259D59}
 1295981 {DC2AFEE2-4E1F-4102-9B5C-4E0B4E7523F3}
 1295982 {86810CF1-8A0E-4A78-9FB4-19ACFC2E21C8}
 1295983 {296234A4-F2B1-4E3B-93D2-F42AB3936353}
 1295984 {AC71FC0D-69E3-424D-8F17-269AB7963821}
 1295985 {2D54BAFE-BA06-47B8-ADC8-FCF3D91FC59B}
 1295986 {445CD74F-BDA1-473B-BC4C-454422B4ECA8}
 1295987 {A619AB50-B6B7-4E31-BE2F-C6B7476C0831}
 1295988 {EBAC2F73-F96D-4345-A37E-883E0A7230D8}
 1295989 {39D37EBE-811D-403E-BF88-DF54C0C88432}
 1295990 {97943897-ED3C-448B-A453-CF5A53A94770}
 1295991 {D071C1EC-5154-4C07-B426-CA318D46DDBE}
 1295992 {E8F1D335-D85B-4BAB-9601-61A2FD6E3451}
 1295993 {E5048482-0FBC-4D34-AC12-5BA839B9C00C}
 1295994 {901731A0-E915-4179-8389-04081AF12102}
 1295995 {F3D97650-F6EB-4D57-94BC-5F387E7FE00F}
 1295996 {02DEE548-186D-4BA0-9B61-6C8431B8A5A0}
 1295997 {B69A003E-BB56-4E05-A855-92D8F4417FD5}
 1295998 {3A4A0343-680A-4BE0-BDC9-0A086C389628}
 1295999 {053000CC-BE89-4C57-BC4F-65BCA1B7E758}
 1296000 {E6E2958F-B5F3-4F2C-BD5A-858F3548E652}
 1296001 {8519B8AE-8A98-418B-9CBD-A641B835301D}
 1296002 {78598DD1-E048-438D-9906-D3FEE2ABC458}
 1296003 {BF605240-FCF0-4167-8A75-1ED17BC3020A}
 1296004 {1D5BF74B-4ED3-4F04-8261-0C4B9AC51C35}
 1296005 {0F42DBA2-A1DB-47A1-B41D-A33C10C4FCEF}
 1296006 {AAA4153A-466B-4ED2-809A-CD9E8EF2C645}
 1296007 {43192404-7E73-4566-9E10-355CC41F6E25}
 1296008 {1443C0D5-4F19-4ABA-8E6B-BB64473AEC05}
 1296009 {AB5259C9-0EC2-4FBA-8984-97BE8CA4F267}
 1296010 {50607687-82D6-4F97-B20D-31F39C1E78DA}
 1296011 {3D55EA22-EE6A-41BF-A85D-4F5C77A864E1}
 1296012 {7B706EC3-D6B2-451C-8AB8-758136CC3587}
 1296013 {D6E1C99C-3E00-4E48-88AD-C64A623D1E1A}
 1296014 {9A99741D-60C6-4387-A8F9-8E2F93A4DE6E}
 1296015 {66247400-6D30-4A4C-A44A-A5AC0C68E8FA}
 1296424 {106B0E2C-6536-4E91-A135-037E6F3ADAD4}
 1296425 {CE8C3366-E0F0-4B7A-8C86-1DF3519A0F56}
 1296426 {646AF875-046C-4FC3-BEEE-0DF2493F28BE}
 1296427 {5485AC1E-126B-4646-9E80-57394C2E84D9}
 1296428 {4172CF22-0B79-4677-9910-D3A004D3DAEB}
 1296429 {A8172868-B44E-4044-A27D-3A9182854592}
 1296430 {E3D427EF-D4DE-4FF3-8B57-F2A9CD543FEF}
 1296431 {D4A20B56-6F8D-406D-93B2-BB724783AD61}
 1296432 {54CD94E8-697E-4DCD-881A-04ABC4C61CF7}
 1296433 {492A76AD-0160-48E3-8C13-C078D5F13D29}
 1296434 {63BEC753-9044-43B5-A871-9B224A781734}
 1296435 {63475110-D961-4272-881B-9341E65BA3D3}
 1296436 {24DDFD5C-C629-43DE-8EE8-C30AD4E8CF12}
 1296437 {561E332C-7A30-43D2-816D-6C6889A9C570}
 1296438 {D03DA005-4C27-447A-836E-E5D541F4662D}
 1296439 {CBE3015C-36F3-47DF-B167-FD4176272BB7}
 1296440 {5CA12CC5-56DD-4082-B7BA-1198B00F5ACE}
 1296441 {4AB7454A-EE2B-4F79-899A-4F03BC38BB22}
 1296442 {77EC32CC-4380-4F24-86D8-6D6167442468}
 1296443 {18EB1246-ACEA-4CD6-B50D-2894111815CF}
 1296444 {638E1726-D056-4333-A4E4-2B0AD40DEEA4}
 1296445 {B1FBEF67-1E4B-4A6D-B4C8-1D63F3E6A58F}
 1296446 {6C029F0C-24A2-417B-839F-7024CDF8B302}
 1296447 {B3C92BEB-6FD7-4DA4-B973-26A4BB8C5C68}
 1296448 {D8B7DEA5-4368-4494-A274-30CD9B87E1A9}
 1296449 {009C1654-7E26-47F3-9DC7-62FFAAB33759}
 1296450 {05CC81D2-DEA0-4D12-9D2F-4E5661B4D523}
 1296451 {CD3E2592-1012-4EFA-9B70-3D1EE76B8F49}
 1296452 {D3E374D7-235A-4942-B0FD-CA5D9CD2C88D}
 1296453 {A7B792EB-86D0-4ED5-9A7E-0B35E565CDF8}
 1296454 {24B6E159-67EE-420C-A566-0F492F3CF5CF}
 1296455 {68F96FC2-D9D3-4E9F-B569-644BFA560D9F}
 1296456 {D0779353-CACD-42F7-9386-28A028A442D0}
 1296457 {258840BA-7017-4ED9-AD46-13AEEE22BC27}
 1296458 {5C402D8B-4965-48E2-AF95-4CCF7C7C77B6}
 1296459 {FD3700D0-5783-4988-B1EA-94AE137B88A9}
 1296460 {1C54303A-6FA5-4193-A82A-34B07E2CBF2D}
 1296461 {5C9D2D20-746D-47DA-A8DB-7EA479304206}
 1296462 {FAB5A63D-89E5-4BB5-A47F-F5AB5CCF54D4}
 1296463 {63F831CD-074D-48FB-95FC-D571D003E040}
 1296464 {F437C6F6-AEFC-452E-9D51-8CDF31FC52A0}
 1296465 {9CD9BA3F-5358-46BB-A60E-5C9E8ED81C3D}
 1296466 {3C9442AA-AB24-4A1D-958B-A8939C5AA109}
 1296467 {19BE93EF-62F2-471E-898B-93C52370527A}
 1296468 {B0625A32-9979-4632-AB84-E102DA764B35}
 1296469 {C5FC5804-DDB8-4575-B9CE-2188F10CC8D7}
 1296470 {CFDEB359-2F38-4727-B212-F80B274D4F02}
 1296471 {70C6C22D-704D-457C-9E22-B17BCF4359CE}
 1296472 {1B0034B2-1592-4FFB-B4FF-B132117370C8}
 1296473 {76A90064-3639-4F96-9FB8-6BDA60319C1A}
 1296474 {877A3A8D-D82A-41FB-ABE0-41965F7B8B0D}
 1296475 {D1A401D1-4B8E-43F6-AE2F-795D6964B28B}
 1296476 {81ABE09E-728A-43FC-8B05-F3F1BAB057C6}
 1296477 {29557402-369E-4072-8C4D-B95814883E16}
 1296478 {020D6A6C-491C-4A90-AE34-B6EEF5C83617}
 1296479 {01FC0C7F-760B-4E85-B87D-650FCE7C2A00}
 1296480 {A092A0D5-CAF4-4BE3-9951-427C690CCD2C}
 1296481 {B9134AF8-F5E3-4143-88F7-376BE9AFE371}
 1296482 {29EAC461-AC20-4FC0-87F4-154438EA2A46}
 1296483 {CD9CC85B-F4CA-4917-832C-71A8F9E44582}
 1296484 {07FDDDC0-BF57-441E-8DF4-632D57B9DCB3}
 1296485 {89BC4E8A-5233-4EAD-8DFC-3A8A3A18162F}
 1296486 {02F3A5C3-D5F8-4D1D-AE75-D58E1D11FE18}
 1296487 {66AA91C7-1CF3-4F95-BE92-9B8D4634B18F}
 1296488 {5D9F9ED7-5AC7-40DF-B1A5-BDEEF1F881C4}
 1296489 {E2641774-E476-468A-948A-8D1205C55956}
 1296490 {7124DD01-C3BE-4E7A-9DE3-71EF19366C99}
 1296491 {4E09E550-0772-419B-BF03-8C8D06D9F494}
 1296492 {0B7059AE-17C9-437E-90A1-28CBC7C52D9B}
 1296493 {1AAC58B3-8448-4829-9A8C-6AB0FC4E8CE9}
 1296494 {EA1602FC-8C82-493A-9D09-FADDF74AD1A0}
 1296495 {B62DB92A-B747-4F1D-BC07-A30464C4584F}
 1296496 {DA4AEAF9-277F-40EF-9555-D5465445ABC5}
 1296497 {12A0CF25-56BA-4B62-AD37-7081ABCC119E}
 1296498 {18F45827-6FC1-4BD6-9924-921A9BC518EF}
 1296499 {C4487B83-450C-4425-B5C2-D396FA681A61}
 1296500 {192A0727-7FE4-4124-AC3C-F42D1513EB8A}
 1296501 {CB1AB749-A200-4A6F-AB31-235944F9D6FE}
 1296502 {1383ABA4-A8C1-4BC3-87C7-FAC705EDDF53}
 1296503 {BA18F7E3-A4EE-468F-8156-E46CD7D34074}
 1296504 {9A69DD85-2558-491D-9D31-B5108ABC6A4E}
 1296505 {2CAC8CE0-017A-464D-A908-7E3822D1D566}
 1296506 {BD7E4587-A871-4823-84A6-CECB905BAB3D}
 1296507 {D34CBD5C-C43E-4756-BA6E-B08141004C6E}
 1296508 {218C9774-DF57-45BD-B114-00C88EB096E3}
 1296509 {ED5082F2-8269-4342-833E-5DC38B91B995}
 1296510 {2320B88A-67A8-4C9C-9A59-FFF133D00665}
 1296511 {3BC31186-4A47-4D3F-A54F-D731C5DA3623}
 1296512 {CC93FFCD-DE9F-4686-86CE-15AAD7FA8ADE}
 1296513 {3FF3DD0F-9883-450A-AE6E-8A092FEBD0DF}
 1296514 {E802ABB8-D6BF-4BB1-920D-4515D631521B}
 1296515 {E3497A52-4282-4173-84B6-985EBFEC959F}
 1296516 {9D5B1DBB-37B4-4C2A-82EB-7EDF9C7C1639}
 1296517 {E5AC4D5E-6E7F-46ED-8D71-E628146833EA}
 1296518 {F234BAD2-1B5E-4E85-B015-DA84F1DC63DF}
 1296519 {F104207F-2891-49A7-808B-52AA177CC80B}
 1296520 {71CE7C7E-E6DE-4C9A-88F0-5A82849E4454}
 1296521 {2F578C7C-85BC-4886-847A-6B3798B2BA7D}
 1296522 {587DFC54-381A-4DFD-B99A-76EE15357869}
 1296523 {6895862E-68E7-4866-9B2F-7CFDD63DB47A}
 1296524 {D09669C0-6D07-45C5-AECB-BB03CB98478E}
 1296662 {A092AE59-B6D5-4D48-94E6-6C7BE6AA0F3C}
 1296663 {5FF89691-241E-40AF-90B2-DB86044C2D51}
 1296664 {9B5881AD-A9A7-4FDB-B5E7-9D76E30630F5}
 1296665 {9ACE3D81-2D12-49C6-8475-53999EE15ACA}
 1296666 {45004E6D-CE66-4A5C-85FC-6D4125A2422D}
 1296667 {197E1E2B-DE27-468C-9251-A94763348F34}
 1296668 {8DF62198-8475-4657-B5F0-DAFD2D810F79}
 1296669 {9BA7C708-7945-42DB-85BC-6C51E28BB1C8}
 1296670 {96BF54AA-15E8-489B-9F57-AB3DE859EAF3}
 1296671 {F6C90413-CF09-450D-A11C-86F02E10F1D0}
 1296672 {C84ABCE7-A7D2-4EF7-8B9D-1F15AAF3070D}
 1296673 {D035FD9D-47C4-450B-B621-90C8807DE7EC}
 1296674 {2A9A6882-8C98-4043-AE7F-3E516E0514F1}
 1296675 {31203723-4E18-462E-A418-EF25973EBC25}
 1296676 {F3A614DB-CD6A-4003-ADC2-41992116375A}
 1296677 {F4D920FA-3560-4EEA-B373-6062213FB373}
 1296678 {EEB611AA-EF07-462E-8C76-D1378AD889A8}
 1296679 {992FC1BA-47CF-4D28-B049-F7F3163B41A9}
 1296680 {D2D77872-56DC-40C4-AECA-55B601E038F4}
 1296681 {FA0D01F9-4919-4D78-BEA1-E27CE64A3E3A}
 1296682 {AFECB4CB-13B7-484F-AD81-E33978489ACB}
 1296683 {C6C9EC21-991F-4339-A8F6-E6F5CCEA517B}
 1296684 {A40664F2-15E6-438B-96F0-B6ECA206BEF6}
 1296685 {C36EF989-7408-409E-878D-19ABF79F8A0D}
 1296686 {25B5333D-7209-4035-B826-B8D0B7B284AD}
 1296687 {2356BC2A-A9D9-4F00-AB58-AB192658B110}
 1296688 {2693259D-53F5-47F7-A555-F8437E2AF779}
 1296689 {E89E0ACB-B160-4AB5-BF07-0389E2F556C0}
 1296690 {BA2F3F73-ECFD-4CA7-88FB-86022EDE468B}
 1296691 {FAC780C8-7FB8-4E3A-857B-F9DA3EE78222}
 1296692 {3AFB2C5E-6F99-42A5-89B8-C7B3FB898F19}
 1296693 {C1E3176D-BBE9-49C0-966B-B9FEFB34DB48}
 1296694 {A15B9CC1-C071-410E-A783-38CD5D070EEA}
 1296695 {ED330D73-4339-48BE-8C52-2B0758E638FE}
 1296696 {B8421C14-2F68-446F-9531-7F264819D84F}
 1296697 {7D576A1A-E669-478B-84F9-23A48F92FBE2}
 1296698 {AB7BCD4A-66AF-4090-B59B-12B4002780BE}
 1296699 {06206F31-B078-4A6C-A3DC-A11B2430AE87}
 1296700 {87087DEA-46C5-4D1A-8963-85D419A36BE5}
 1296701 {09F7DA8E-7EAE-4A06-AE50-5D6CB9463651}
 1296702 {9B4BEA40-4602-4AE9-A44E-0FB013528408}
 1296703 {C7731321-4222-4BD9-B921-A81D588F4F9D}
 1296704 {9F71FE68-8D65-4E8C-84B9-81383ADE45CD}
 1296705 {7822606E-F80D-4542-A266-32AF1658F74F}
 1296706 {A0CD55B4-2806-4317-911A-71578EA05E7A}
 1296707 {3A3A9F5A-ED59-441D-BDC0-73B46A0201F5}
 1296708 {7B240EB1-0BE1-435A-902E-783C8480E564}
 1296709 {54952BC9-B96F-403C-8AE6-830B09EDBC98}
 1296710 {8F8352C6-71D6-4932-B117-FBC5EEBCBABB}
 1296711 {B8894E33-4D12-42FC-B4E0-A6D247BBE42C}
 1296712 {929517BB-D8BC-4B68-9923-79E0C5D9EE07}
 1296713 {02B5D415-FF69-480A-9A8B-979DC04B6A35}
 1296714 {22D2E893-D325-4551-8A34-21D83F8AD7B2}
 1296715 {A7900624-C0AF-4F34-B3E9-FAD7DA92164B}
 1296716 {E2A32F3B-708C-4A5E-B917-F98881429A21}
 1296717 {F3E8D65A-3391-4BDA-ACFF-07596BA98A14}
 1296718 {E2F208D6-3C08-410C-B1B6-B32D0294D0B9}
 1296719 {C4C49038-E047-4D17-9C05-26F67DB5D609}
 1296720 {A53AC547-578B-43D4-A0BA-6A607AC44018}
 1296721 {FC8F8417-1BC2-4289-BA3C-FEFDCCE7FEAD}
 1296722 {93D619B2-6504-41EB-ABD2-9D9D96D05CA1}
 1296723 {80FD5399-7F82-4E18-97D9-9BF0A6FE5CA5}
 1296724 {1FC47DC1-0599-49BB-BB87-68236DD40B06}
 1296725 {D9F62060-BC9C-4128-A584-856DD2A03CE0}
 1296726 {058DACD3-BB65-4357-834D-28468AEE8EF1}
 1296727 {87030656-D01D-44ED-B794-B7515145B974}
 1296728 {B4F3512D-A637-4D4E-A856-541139FA4969}
 1296729 {48002AC6-A414-4683-831A-DCAB64AD95D7}
 1296730 {E83C9A40-D96D-4E31-81F2-EBBF77B1DFA9}
 1296731 {32F9C9D4-CB8B-4E56-8734-C5367C4FAE26}
 1296732 {293EAAAB-CA34-4AE7-8E89-B3AE95AEB526}
 1296733 {F0D641BF-49A4-4AE4-A156-27B4A86B0CA2}
 1296734 {59B8F6D2-108B-4109-9EC0-87CB44E0FF98}
 1296735 {635DEFA4-286D-47A9-878C-F1FB926E8931}
 1296736 {E346CA03-9258-4276-9B1A-07815D1FBEC7}
 1296737 {9C14C43D-F374-4B01-968A-E3EA0DC6C164}
 1296738 {FACB71D7-EFF1-464E-8E4C-A60D1119DD77}
 1296739 {333155FA-7648-4126-907D-C21169850AD8}
 1296740 {1235A5C1-9723-4790-A8C2-AF59B852F125}
 1296741 {552BDAA8-F352-4A4A-8638-F16C5C344712}
 1296742 {BD42B6F3-5837-41C1-80B9-1E547E1D3BD6}
 1296743 {2DDD6EB6-DB5D-4E33-B10A-1D4A7759CB34}
 1296744 {005C5DA2-DE12-4FD7-BE66-5B44DFEF1181}
 1296745 {49192F94-4EAD-4058-B617-FDC0DFBE5AD9}
 1296746 {DE309940-5C90-41F8-8669-A9BFC9CB53FA}
 1296747 {4F8C3E10-D629-4A75-9474-B5006D24E215}
 1296748 {CFCB92C9-8E84-40B4-90E8-BB8C42EC78AA}
 1296749 {F6DB09FA-43D7-4FD9-BE98-B0231CF4204A}
 1296750 {A85FECC2-FDFC-4AAF-B751-7C45E7C94E2F}
 1296751 {B2531130-9088-4454-A26B-5B55887448E8}
 1296752 {71A3BE9F-D9BA-4A73-8326-221F446918C8}
 1296753 {BA0EA34F-852A-4834-90BF-1543F24C3DB5}
 1296754 {384C1BAC-D0F1-46B6-892C-8EF5B5DF3EFC}
 1296755 {0C9E4E33-50BF-4545-8084-BD5D88616B69}
 1296756 {D6771585-D4A5-4FC9-B110-AF563225CAB4}
 1296757 {104D6B6D-0C52-49E1-875F-1C5910722BC4}
 1296758 {28E2A5C9-6204-42F0-8EB0-C35BA28CEBAF}
 1296759 {D6D4CD59-E169-4698-9DD5-1C45E31FA4A2}
 1296760 {D35D0C8C-51F7-4240-ACCA-2EADF9AAA96C}
 1296761 {2A918004-88C2-482D-BB5A-FB85758FD472}
 1296762 {55472C56-125A-4051-9C65-7A3F9CE04806}
 1296763 {81524F84-5A3B-4A9C-842F-345898995478}
 1296764 {B4592912-EE74-4B7C-A0BB-FA0AC8EDAD85}
 1296765 {4433B150-4A42-495E-84D7-E0BC99F38A3D}
 1296766 {84029647-CB7A-4346-9E22-EACA582259F3}
 1296767 {A2C05F00-24D5-474D-B243-404B54DD0BAD}
 1296768 {59DE020C-2128-45A1-AF08-F3F8F473CCF5}
 1296769 {98C7B58E-B050-401C-8260-07FAA3355909}
 1296770 {0C9D46C7-4EDB-4B95-908A-CAEE1FF6C65C}
 1296821 {5773FBA1-EC5A-4D14-BF03-7B73D58B9508}
 1296822 {B8BE180B-1436-49C4-9F1C-CAA3010CCFE1}
 1296823 {43182919-D9D2-4FF2-AC83-03BB5D4DA41F}
 1296824 {3AAF6A34-A520-4CA5-878E-43A816BA56C5}
 1296825 {CFF31C54-908C-4BBD-8512-3CCB896131DB}
 1296826 {AD2CA826-DD57-47A2-B349-B23F780BCC19}
 1296827 {C8DF03ED-5C44-4F2B-B5FD-D1854A308A7C}
 1296828 {D4D9E699-C3EC-46E5-AC1A-5B96D95AC592}
 1296829 {DD6EEE55-6FBB-4DDE-8239-90B39797BA7E}
 1296830 {085F0B37-DC3E-42C1-A0B9-DF7934A334A2}
 1296831 {B5E20C06-6A23-4067-B70F-91A51CF53745}
 1296832 {F140B8DC-DCEA-4F67-AEFF-C46778DB5D3F}
 1296833 {D189F6F1-6E0D-42D5-B7FF-8652F68203D7}
 1296834 {7EB62E73-8811-4208-9484-B32F945B98A0}
 1296835 {4D443FDA-4368-4E91-9D6D-84AF39E7605F}
 1296836 {2EFA2229-9083-4361-98E5-9A575156214A}
 1296837 {A24A5D33-C805-4EA7-A2F8-879A192F1078}
 1296838 {AE99AB92-5A07-43B8-8643-000D8F1DFECE}
 1296839 {D5AB6BBE-E8D1-400C-9B30-147313B4F512}
 1296840 {80665AC7-0A7A-4CAC-B3B3-1131385AAE8E}
 1296841 {29FB42B4-5E5D-41F5-B5EF-DF7DF0D5B9A3}
 1296842 {06C5FC1D-DD3D-419B-9987-785E5DB83398}
 1296843 {44BCEBA7-5B54-4F50-A9FD-B1E9EA2B6150}
 1296844 {04F0343D-95D0-4496-97E4-FAD20723841D}
 1296845 {BB45EF6D-68B4-405F-B48F-F4F56813DA8E}
 1296846 {9C8E6F47-CA9F-424E-9981-03FB33C6342B}
 1296847 {87122A71-6CC4-48FB-9850-B85C6B976576}
 1296848 {7DF5A6CE-A892-4F9E-8A8B-1E3E2F59B223}
 1296849 {746851CE-0B40-444F-ABFB-2063BE985C9F}
 1296850 {CDFCC1AE-D690-4F1A-A187-03A712BA2F1E}
 1296851 {45A03180-1BB0-4ED0-8D8C-DB3C7CF19243}
 1296852 {DD7C6DAF-A63F-410D-90F4-1546C7B04DA2}
 1296853 {DA8BC953-F629-4690-B11F-3006A89E2805}
 1296854 {ED0C9C5D-8FB0-42E8-9615-A69724EC87C7}
 1296855 {C2A3F347-4560-49D0-8DE8-0F3A97BF665E}
 1296856 {3E0DE87D-4D6F-4F27-B099-2C08AC04CAB0}
 1296857 {F7F3D31F-FFDD-4684-B992-FCAEC597FA2A}
 1296858 {484CC8C3-1C99-45D5-BA7E-6BDCE5A76447}
 1296859 {71D29783-1363-4234-A966-D00EEF13268E}
 1296860 {22FE7AEC-7A05-4655-97C2-767C6F87E3A3}
 1296861 {958EF86A-5271-46A2-BD83-AE7BCB78E89E}
 1296862 {98DA31E2-6868-4FFF-8631-361D7315FDBF}
 1296863 {564F24B5-5AD8-431C-A942-B61E65657D99}
 1296864 {8CAB4126-68B4-416E-8FDC-9628A2ACC00C}
 1296865 {C71DF8F9-B45F-4FDD-BFC7-C5F720062664}
 1296866 {9A6AB328-DAF5-4DF3-9290-1937ABA9FE18}
 1296867 {FB0A3420-A54E-4C2B-991A-9CD77DA9899C}
 1296868 {52B0A510-966A-4839-AEF5-5F5080F4BE41}
 1296869 {5443A12E-2C41-4445-A6FF-87896C77F79E}
 1296870 {C1B214AF-F2F6-44B5-ABA0-B8F1C3B4E8A8}
 1296871 {0EC8A244-A24E-4727-80FB-1E0610C53655}
 1296872 {D3B08A21-2DE3-421A-BD9E-AA853AEA0BCF}
 1296873 {2ACDF740-3003-405D-896A-9905CFFE0A60}
 1296874 {2A36F2D8-D19A-43FE-BB2D-A57EA96DB63E}
 1296875 {11418A00-34E3-48CC-8372-29E00B1E37E2}
 1296876 {AF4C675D-598A-42A3-B1CE-B1FA16A5BDD0}
 1296877 {1D96906D-F702-43FA-AC47-D9C5A4965C22}
 1296878 {74A8B05F-301E-4008-95C5-33463510BB26}
 1296879 {C9F42738-A52F-4A98-AF2B-461B4705566B}
 1296880 {802599DF-C417-4231-B257-DCEE267A089D}
 1296881 {519622D4-DD7C-44CD-83CB-597461101037}
 1296882 {DC9A74D5-3B39-424C-BFEB-BF5EC436DA81}
 1296883 {D4FA3426-B843-4448-80F8-C2634B24F63E}
 1296884 {4AE18646-B191-4E95-B80C-51BE6DFAA501}
 1296885 {17E8840C-3106-43B9-96BD-F4AE21434AD3}
 1296886 {3F2A78EE-8B7D-48EE-BA07-C9650F1B1699}
 1296887 {2F074FCA-D7A7-4721-8871-54B42165A459}
 1296888 {EC000BFA-40BC-4A90-8E79-45BE023DFA19}
 1296889 {27022222-F7DE-4EEA-92E1-A998B971CC67}
 1296890 {CB1112AF-CEA7-46D4-8640-C915D1DA7527}
 1296891 {5FA77724-283A-432C-B224-7F7782375D01}
 1296892 {27DCE627-76F1-45A3-B23C-15F21BFF0B94}
 1296893 {0C1C7EFD-1622-4DEA-A031-59A2CE080A9A}
 1296894 {7CAC14AB-FB32-40CD-A5DB-8B65E8774522}
 1296895 {BE42288E-2D85-4AD2-960A-DEC2DA6B03E5}
 1296896 {B232E49F-73A4-4B05-97AA-63EE9F126840}
 1296897 {673388FB-7B91-499F-BE75-0F41CB0C3D94}
 1296898 {3D933B31-C9A3-473E-8123-4FB3C2BBDF9C}
 1296899 {84B40FEF-1BFC-4D90-B00D-BDD04C76E5D6}
 1296900 {CAE8A3C0-C7BF-42CB-91F9-1977C0D59EFF}
 1296901 {6466E11D-CA01-4A25-9952-D720583F4C06}
 1296902 {608B7822-DB51-46FD-8D15-4EF379B23FB1}
 1296903 {86C214FB-45C8-4071-B5C6-C5C654171C1E}
 1296904 {CF7090D7-98BC-4DE0-9C29-82B0D0799085}
 1296905 {62D2BEB9-860B-4E1A-9724-027C0F0FAE6D}
 1296906 {BB3DBF06-1916-42C5-9A78-5B172220C69E}
 1296907 {56621B1B-9659-49BB-B555-D5BE466D5E7B}
 1296908 {0B280E9B-E255-456E-9E3C-B50E570EAA41}
 1296909 {D5B535DF-75A2-492F-8805-8915A622EC14}
 1296910 {969DE5D4-E911-48A9-AE90-8AC4CD357CB2}
 1296911 {202C10B7-A01A-4486-9389-1F466208383B}
 1296912 {D94F19F7-1B29-4EB2-9C92-88B65ECEA4E6}
 1296913 {99F0E522-444A-4AAE-BFEE-26ADC4B1A665}
 1296914 {61BCC76F-67A8-4D3A-928D-C63EED638BCF}
 1296915 {A7EF857F-7BF7-4DB8-845E-BD1FD89CB5BC}
 1296916 {5FFB80E2-BEA4-472C-AC13-F47FF9D1FA4C}
 1296917 {2FC707A1-EEBE-4F09-8A40-D9CF476D87A9}
 1296918 {2518F30F-7BC7-4A94-8537-87457FF49477}
 1296919 {823D068D-8E78-4B82-AC67-4495BC2C04B3}
 1296920 {5A110B86-8E25-4C8D-AFCB-8CE9B46E3802}
 1296921 {092D985C-C395-43FB-BFBE-91793B59F82C}
 1296922 {823D5DDA-E82B-4B61-AB48-72809F47A619}
 1296923 {FBAC418A-3EA0-46A8-915B-1852F3D4345B}
 1296924 {61D51316-8C50-4441-A48B-4FB33769840F}
 1296925 {92238A99-8A22-469C-ACBD-BC4A7F39A513}
 1296926 {D2308610-AC1A-49DA-B24D-E4ACED47D3A1}
 1296927 {D10E371B-6FF7-4B3C-B20E-5F8537B964EF}
 1296928 {0A79C8F3-A856-4030-8A3B-8880F086480B}
 1296929 {83AD5C04-5953-4604-B652-B93C2922DDE0}
 1296930 {5994DCCC-7138-4DFB-B65F-BEA57AFE8011}
 1296931 {091D6BBD-C954-4C9F-A20E-1EB43DD04682}
 1296932 {05E63B81-8541-4BA5-B5DD-1237E0616129}
 1296933 {BD013545-218C-4837-9DC7-BA28180C3BC4}
 1296934 {6223A872-7AF4-49F8-9CC2-0074EFA1D832}
 1296935 {0388BC27-8EBB-4D41-9AF1-569FDEBDE7AD}
 1296936 {100777A9-CE9C-45B8-A191-B4E88BAF28C1}
 1296937 {C45EF693-6280-4049-9FE7-49B186BCDC4E}
 1296938 {8971B47B-419E-4F69-BD63-E5E067818119}
 1296939 {1718AC43-3F8E-4F2C-A148-9A20DD5844DF}
 1296940 {17B9774E-7007-4C7A-A3D3-E63A7F1A3B48}
 1296941 {2D968756-21A8-4333-93F1-9F0FCBAE3C78}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>572462E6-176B-44D6-8262-6913E0B91157</a:ObjectID>
<a:Name>Microsoft SQL Server 2008</a:Name>
<a:Code>MSSQLSRV2008</a:Code>
<a:CreationDate>1543800223</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1543800223</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F5C20738-B05A-4F70-BC90-9B5EB9437766</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>E1F65ED2-0E4B-4DF9-B8BD-B1A21107BEF3</a:ObjectID>
<a:Name>RETAIL零售_ALL</a:Name>
<a:Code>RETAIL零售_ALL</a:Code>
<a:CreationDate>1543800223</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1543800317</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:PackageSymbol Id="o5">
<a:CreationDate>1543800302</a:CreationDate>
<a:ModificationDate>1543800317</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-2401,-1798), (2398,1801))</a:Rect>
<a:LineColor>11711154</a:LineColor>
<a:FillColor>12648447</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Package Ref="o6"/>
</c:Object>
</o:PackageSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o6">
<a:ObjectID>D22F8E5B-F834-4CB4-8F57-438D2314C428</a:ObjectID>
<a:Name>RETAIL零售</a:Name>
<a:Code>RETAIL零售</a:Code>
<a:CreationDate>1543800302</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1547014648</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o7">
<a:ObjectID>904A6DA6-4656-4458-B2A8-38974169CB87</a:ObjectID>
<a:Name>RETAIL零售</a:Name>
<a:Code>RETAIL零售</a:Code>
<a:CreationDate>1543800302</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1543800323</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o7"/>
</c:DefaultDiagram>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:DefaultGroups>
<o:Group Id="o8">
<a:ObjectID>58860157-E0FA-443C-B8F9-D17F5F31949E</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1543800223</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1543800223</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o9">
<a:ObjectID>B831D7CE-A316-4CAF-91EE-6C69FF3C6761</a:ObjectID>
<a:Name>Microsoft SQL Server 2008</a:Name>
<a:Code>MSSQLSRV2008</a:Code>
<a:CreationDate>1543800223</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1547014634</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/sqlsv2k8.xdb</a:TargetModelURL>
<a:TargetModelID>F5C20738-B05A-4F70-BC90-9B5EB9437766</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>