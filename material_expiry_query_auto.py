#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物料到期查询自动化脚本 - 解决制单人工号问题
作者: Augment Agent
日期: 2025-01-27
"""

import pyodbc
import pandas as pd
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('material_expiry_query.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 数据库连接配置
DB_CONFIG = {
    'server': '192.168.1.250',
    'database': 'AIS2018101755337',
    'username': 'hldbuser',
    'password': 'Hldbuser@241031',
    'driver': 'ODBC Driver 17 for SQL Server'
}

class MaterialExpiryQueryTool:
    def __init__(self):
        self.connection = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            connection_string = (
                f"DRIVER={{{DB_CONFIG['driver']}}};"
                f"SERVER={DB_CONFIG['server']};"
                f"DATABASE={DB_CONFIG['database']};"
                f"UID={DB_CONFIG['username']};"
                f"PWD={DB_CONFIG['password']};"
                "TrustServerCertificate=yes;"
            )
            
            self.connection = pyodbc.connect(connection_string)
            logging.info("数据库连接成功")
            return True
            
        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            return False
    
    def analyze_creator_mapping_issue(self):
        """分析制单人工号映射问题"""
        logging.info("开始分析制单人工号映射问题...")

        # 查询1：检查制单人ID与用户表的关联情况
        query1 = """
        SELECT DISTINCT
            h.FCREATORID,
            u.FNAME AS '用户名',
            u.FUSERACCOUNT AS '用户账号',
            COUNT(*) AS '入库单数量'
        FROM T_STK_INSTOCK h
        LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
        WHERE h.FCancelStatus = 'A' AND h.FDocumentStatus = 'C'
        GROUP BY h.FCREATORID, u.FNAME, u.FUSERACCOUNT
        ORDER BY COUNT(*) DESC
        """

        # 查询2：检查用户与员工的完整关联路径
        query2 = """
        SELECT DISTINCT
            u.FUSERID AS '用户内码',
            u.FNAME AS '用户名',
            u.FUSERACCOUNT AS '用户账号',
            p.FPERSONID AS '人员内码',
            s.FSTAFFID AS '员工内码',
            s.FNUMBER AS '员工工号'
        FROM T_SEC_USER u
        LEFT JOIN T_BD_PERSON p ON u.FLINKOBJECT = p.FPERSONID
        LEFT JOIN T_BD_STAFF s ON p.FPERSONID = s.FPERSONID
        WHERE u.FUSERID IN (
            SELECT DISTINCT h.FCREATORID
            FROM T_STK_INSTOCK h
            WHERE h.FCancelStatus = 'A' AND h.FDocumentStatus = 'C'
        )
        ORDER BY u.FNAME
        """
        
        try:
            # 执行查询1
            df1 = pd.read_sql(query1, self.connection)
            logging.info("制单人与操作员关联分析:")
            print("\n=== 制单人与操作员关联分析 ===")
            print(df1.to_string(index=False))
            
            # 执行查询2
            df2 = pd.read_sql(query2, self.connection)
            logging.info("操作员与员工关联分析:")
            print("\n=== 操作员与员工关联分析 ===")
            print(df2.to_string(index=False))
            
            return df1, df2
            
        except Exception as e:
            logging.error(f"分析查询执行失败: {e}")
            return None, None
    
    def get_alternative_staff_mapping(self):
        """获取替代的员工工号映射方案"""
        logging.info("查找替代的员工工号映射方案...")

        # 方案1：通过用户账号直接匹配员工工号
        query_alt1 = """
        SELECT DISTINCT
            u.FUSERID,
            u.FNAME AS '用户名',
            u.FUSERACCOUNT AS '用户账号',
            s.FNUMBER AS '员工工号'
        FROM T_SEC_USER u
        LEFT JOIN T_BD_STAFF s ON u.FUSERACCOUNT = s.FNUMBER
        WHERE u.FUSERID IN (
            SELECT DISTINCT h.FCREATORID
            FROM T_STK_INSTOCK h
            WHERE h.FCancelStatus = 'A' AND h.FDocumentStatus = 'C'
        )
        AND s.FNUMBER IS NOT NULL
        ORDER BY u.FNAME
        """

        # 方案2：检查数据质量 - 哪些用户没有关联到员工工号
        query_alt2 = """
        SELECT DISTINCT
            u.FUSERID,
            u.FNAME AS '用户名',
            u.FUSERACCOUNT AS '用户账号',
            CASE
                WHEN p.FPERSONID IS NULL THEN '无人员关联'
                WHEN s.FSTAFFID IS NULL THEN '无员工关联'
                WHEN s.FNUMBER IS NULL THEN '员工工号为空'
                ELSE '关联正常'
            END AS '关联状态'
        FROM T_SEC_USER u
        LEFT JOIN T_BD_PERSON p ON u.FLINKOBJECT = p.FPERSONID
        LEFT JOIN T_BD_STAFF s ON p.FPERSONID = s.FPERSONID
        WHERE u.FUSERID IN (
            SELECT DISTINCT h.FCREATORID
            FROM T_STK_INSTOCK h
            WHERE h.FCancelStatus = 'A' AND h.FDocumentStatus = 'C'
        )
        ORDER BY u.FNAME
        """
        
        try:
            df_alt1 = pd.read_sql(query_alt1, self.connection)
            print("\n=== 方案1：用户直接关联员工 ===")
            print(df_alt1.to_string(index=False))
            
            df_alt2 = pd.read_sql(query_alt2, self.connection)
            print("\n=== 方案2：用户关联人员表 ===")
            print(df_alt2.to_string(index=False))
            
            return df_alt1, df_alt2
            
        except Exception as e:
            logging.error(f"替代方案查询失败: {e}")
            return None, None

    def generate_optimized_query(self, mapping_strategy='multi_fallback'):
        """生成优化的物料到期查询SQL"""
        logging.info(f"生成优化查询，策略: {mapping_strategy}")

        if mapping_strategy == 'multi_fallback':
            # 多重备用方案 - 基于金蝶K/3 Cloud正确的表关联关系
            staff_number_logic = """
            COALESCE(
                staff_correct.FNUMBER,           -- 方案1：正确的用户->人员->员工关联路径
                staff_direct.FNUMBER,            -- 方案2：用户账号直接匹配员工工号
                creator.FUSERACCOUNT,            -- 方案3：用户账号作为备用
                CAST(creator.FUSERID AS NVARCHAR(50))  -- 方案4：用户ID作为最后备用
            ) AS '制单人工号'"""

            joins = """
            -- 制单人用户信息
            LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
            -- 方案1：正确的关联路径 用户->人员->员工
            LEFT JOIN T_BD_PERSON person_link ON creator.FLINKOBJECT = person_link.FPERSONID
            LEFT JOIN T_BD_STAFF staff_correct ON person_link.FPERSONID = staff_correct.FPERSONID
            -- 方案2：用户账号直接匹配员工工号（备用方案）
            LEFT JOIN T_BD_STAFF staff_direct ON creator.FUSERACCOUNT = staff_direct.FNUMBER"""

        elif mapping_strategy == 'direct_match':
            # 直接匹配方案
            staff_number_logic = """
            COALESCE(
                staff_direct.FNUMBER,
                staff_name.FNUMBER,
                creator.FUSERACCOUNT
            ) AS '制单人工号'"""

            joins = """
            LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
            LEFT JOIN T_BD_STAFF staff_direct ON creator.FUSERACCOUNT = staff_direct.FNUMBER
            LEFT JOIN T_BD_STAFF staff_name ON creator.FNAME = staff_name.FNAME"""

        query = f"""
        -- 物料到期查询 - 优化版（解决制单人工号问题）
        SELECT
            -- 入库单基本信息
            h.FBILLNO AS '入库单号',
            h.FDATE AS '入库日期',
            -- 物料信息
            mat.FNUMBER AS '物料编码',
            m.FNAME AS '物料名称',
            m.FSPECIFICATION AS '规格型号',
            -- 生产日期与保质期
            e.F_JSHL_DATE_83G AS '生产日期',
            e.F_JSHL_QTY_QTR AS '保质期(天)',
            -- 计算到期日期
            DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) AS '计算到期日期',
            -- 计算剩余天数
            DATEDIFF(day, GETDATE(), DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G)) AS '剩余天数',
            -- 库存信息
            e.FREALQTY AS '实收数量',
            e.FBASEUNITQTY AS '基本单位数量',
            -- 仓库信息
            org.FNAME AS '仓库组织',
            -- 制单人信息
            creator.FNAME AS '制单人',
            -- 制单人工号（优化的多重备用方案）
            {staff_number_logic},
            h.FCREATEDATE AS '制单日期'
        FROM
            T_STK_INSTOCK h
            INNER JOIN T_STK_INSTOCKENTRY e ON h.FID = e.FID
            LEFT JOIN T_BD_MATERIAL_L m ON e.FMATERIALID = m.FMATERIALID AND m.FLOCALEID = 2052
            LEFT JOIN T_BD_MATERIAL mat ON e.FMATERIALID = mat.FMATERIALID
            LEFT JOIN t_org_organizations_l org ON h.FSTOCKORGID = org.FORGID AND org.FLOCALEID = 2052
            {joins}
        WHERE
            -- 筛选条件：生产日期不为空 且 保质期不为空
            e.F_JSHL_DATE_83G IS NOT NULL
            AND e.F_JSHL_QTY_QTR IS NOT NULL
            -- 过滤已作废的单据
            AND h.FCancelStatus = 'A'
            -- 确保只查询审核通过的单据
            AND h.FDocumentStatus = 'C'
        ORDER BY
            DATEADD(day, e.F_JSHL_QTY_QTR, e.F_JSHL_DATE_83G) ASC,
            h.FBILLNO;
        """

        return query

    def execute_material_expiry_query(self, strategy='multi_fallback', export_excel=True):
        """执行物料到期查询"""
        logging.info("执行物料到期查询...")

        try:
            query = self.generate_optimized_query(strategy)

            # 执行查询
            df = pd.read_sql(query, self.connection)

            logging.info(f"查询完成，共获取 {len(df)} 条记录")

            # 显示结果摘要
            print(f"\n=== 物料到期查询结果摘要 ===")
            print(f"总记录数: {len(df)}")

            # 检查制单人工号的完整性
            missing_staff_number = df[df['制单人工号'].isnull() | (df['制单人工号'] == '')].shape[0]
            print(f"缺失制单人工号的记录数: {missing_staff_number}")
            print(f"制单人工号完整率: {((len(df) - missing_staff_number) / len(df) * 100):.2f}%")

            # 显示制单人工号统计
            staff_number_stats = df['制单人工号'].value_counts()
            print(f"\n制单人工号分布:")
            print(staff_number_stats.head(10))

            if export_excel:
                # 导出到Excel
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                excel_filename = f"物料到期查询结果_{timestamp}.xlsx"
                df.to_excel(excel_filename, index=False, engine='openpyxl')
                logging.info(f"结果已导出到: {excel_filename}")
                print(f"\n结果已导出到: {excel_filename}")

            return df

        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            return None

    def run_complete_analysis(self):
        """运行完整的分析和查询"""
        logging.info("开始运行完整的物料到期分析...")

        if not self.connect_database():
            return False

        try:
            # 1. 分析制单人工号映射问题
            print("=" * 60)
            print("第一步：分析制单人工号映射问题")
            print("=" * 60)
            df1, df2 = self.analyze_creator_mapping_issue()

            # 2. 查找替代映射方案
            print("\n" + "=" * 60)
            print("第二步：查找替代映射方案")
            print("=" * 60)
            df_alt1, df_alt2 = self.get_alternative_staff_mapping()

            # 3. 执行优化的物料到期查询
            print("\n" + "=" * 60)
            print("第三步：执行优化的物料到期查询")
            print("=" * 60)
            result_df = self.execute_material_expiry_query()

            if result_df is not None:
                print("\n✅ 物料到期查询执行成功！")
                print("✅ 制单人工号问题已通过多重备用方案解决！")
                return True
            else:
                print("\n❌ 查询执行失败")
                return False

        except Exception as e:
            logging.error(f"完整分析执行失败: {e}")
            return False
        finally:
            if self.connection:
                self.connection.close()
                logging.info("数据库连接已关闭")

def main():
    """主函数"""
    print("=" * 80)
    print("物料到期查询自动化工具 - 解决制单人工号问题")
    print("=" * 80)

    tool = MaterialExpiryQueryTool()
    success = tool.run_complete_analysis()

    if success:
        print("\n🎉 任务完成！所有问题已解决！")
    else:
        print("\n❌ 任务执行失败，请检查日志文件")

if __name__ == "__main__":
    main()
