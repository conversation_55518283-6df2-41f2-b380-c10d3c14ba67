-- 查询组装拆卸单号 ZZCX003758 的详细信息
-- 分为成品（入库）和子件（出库）两部分

-- ========================================
-- 1. 验证单据是否存在
-- ========================================
SELECT 
    FBillNo AS 单据编号,
    FDate AS 单据日期,
    FAffairType AS 事务类型,
    FDocumentStatus AS 单据状态,
    FCancelStatus AS 作废状态,
    FNote AS 备注
FROM T_STK_ASSEMBLY 
WHERE FBillNo = 'ZZCX003758';

-- ========================================
-- 2. 成品信息查询（入库）
-- ========================================
SELECT
    '成品入库' AS 业务类型,
    a.FBillNo AS 单据编号,
    a.FDate AS 单据日期,
    p.FSeq AS 行号,
    p.FRowType AS 行类型,

    -- 物料信息
    m.FNUMBER AS 物料代码,
    ISNULL(m.FNAME_L2, m.FNUMBER) AS 物料名称,
    ISNULL(m.FSPECIFICATION_L2, '') AS 规格型号,

    -- 数量信息
    p.FQty AS 数量,
    ISNULL(u.FNAME_L2, u.FNUMBER) AS 单位名称,
    p.FBaseQty AS 基本数量,
    ISNULL(bu.FNAME_L2, bu.FNUMBER) AS 基本单位名称,

    -- 仓库信息
    s.FNUMBER AS 仓库代码,
    ISNULL(s.FNAME_L2, s.FNUMBER) AS 仓库名称,

    -- 批次信息
    p.FLOT_TEXT AS 批次号,
    CONVERT(VARCHAR(10), p.FProduceDate, 120) AS 生产日期,
    CONVERT(VARCHAR(10), p.FExpiryDate, 120) AS 到期日期,

    -- 金额信息
    p.FPRICE AS 单价,
    p.FAMOUNT AS 金额,

    -- 其他信息
    p.FDescription AS 描述,
    CASE WHEN p.FStockFlag = '1' THEN '是' ELSE '否' END AS 已更新库存

FROM T_STK_ASSEMBLY a
INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
LEFT JOIN T_BD_MATERIAL m ON p.FMaterialID = m.FMATERIALID
LEFT JOIN T_BD_UNIT u ON p.FUnitID = u.FUNITID
LEFT JOIN T_BD_UNIT bu ON p.FBaseUnitID = bu.FUNITID
LEFT JOIN T_BD_STOCK s ON p.FStockID = s.FSTOCKID
WHERE a.FBillNo = 'ZZCX003758'
ORDER BY p.FSeq;

-- ========================================
-- 3. 子件信息查询（出库）
-- ========================================
SELECT 
    '子件出库' AS 业务类型,
    a.FBillNo AS 单据编号,
    a.FDate AS 单据日期,
    p.FSeq AS 成品行号,
    s.FSeq AS 子件行号,
    s.FRowType AS 行类型,
    
    -- 物料信息
    m.FNUMBER AS 物料代码,
    ISNULL(m.FNAME_L2, m.FNUMBER) AS 物料名称,
    ISNULL(m.FSPECIFICATION_L2, '') AS 规格型号,

    -- 数量信息
    s.FQty AS 数量,
    ISNULL(u.FNAME_L2, u.FNUMBER) AS 单位名称,
    s.FBaseQty AS 基本数量,
    ISNULL(bu.FNAME_L2, bu.FNUMBER) AS 基本单位名称,
    s.FCostProportion AS 成本分摊比例,

    -- 仓库信息
    st.FNUMBER AS 仓库代码,
    ISNULL(st.FNAME_L2, st.FNUMBER) AS 仓库名称,
    
    -- 批次信息
    s.FLOT_TEXT AS 批次号,
    CONVERT(VARCHAR(10), s.FProduceDate, 120) AS 生产日期,
    CONVERT(VARCHAR(10), s.FExpiryDate, 120) AS 到期日期,
    
    -- 金额信息
    s.FPRICE AS 单价,
    s.FAMOUNT AS 金额,
    
    -- 关联信息
    s.FEntryID AS 关联成品分录ID,
    s.FDescription AS 描述,
    CASE WHEN s.FStockFlag = '1' THEN '是' ELSE '否' END AS 已更新库存
    
FROM T_STK_ASSEMBLY a
INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
INNER JOIN T_STK_ASSEMBLYSUBITEM s ON p.FEntryID = s.FEntryID
LEFT JOIN T_BD_MATERIAL m ON s.FMaterialID = m.FMATERIALID
LEFT JOIN T_BD_UNIT u ON s.FUnitID = u.FUNITID
LEFT JOIN T_BD_UNIT bu ON s.FBaseUnitID = bu.FUNITID
LEFT JOIN T_BD_STOCK st ON s.FStockID = st.FSTOCKID
WHERE a.FBillNo = 'ZZCX003758'
ORDER BY p.FSeq, s.FSeq;

-- ========================================
-- 4. 汇总查询 - 成品与子件对应关系
-- ========================================
SELECT 
    a.FBillNo AS 单据编号,
    a.FDate AS 单据日期,
    a.FAffairType AS 事务类型,
    
    -- 成品信息
    pm.FNUMBER AS 成品代码,
    pm.FNAME AS 成品名称,
    p.FQty AS 成品数量,
    pu.FNAME AS 成品单位,
    ps.FNAME AS 成品仓库,
    
    -- 子件信息
    sm.FNUMBER AS 子件代码,
    sm.FNAME AS 子件名称,
    s.FQty AS 子件数量,
    su.FNAME AS 子件单位,
    ss.FNAME AS 子件仓库,
    s.FCostProportion AS 成本分摊比例,
    
    -- 金额信息
    p.FAMOUNT AS 成品金额,
    s.FAMOUNT AS 子件金额
    
FROM T_STK_ASSEMBLY a
INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
INNER JOIN T_STK_ASSEMBLYSUBITEM s ON p.FEntryID = s.FEntryID
LEFT JOIN T_BD_MATERIAL pm ON p.FMaterialID = pm.FMATERIALID
LEFT JOIN T_BD_MATERIAL sm ON s.FMaterialID = sm.FMATERIALID
LEFT JOIN T_BD_UNIT pu ON p.FUnitID = pu.FUNITID
LEFT JOIN T_BD_UNIT su ON s.FUnitID = su.FUNITID
LEFT JOIN T_BD_STOCK ps ON p.FStockID = ps.FSTOCKID
LEFT JOIN T_BD_STOCK ss ON s.FStockID = ss.FSTOCKID
WHERE a.FBillNo = 'ZZCX003758'
ORDER BY p.FSeq, s.FSeq;

-- ========================================
-- 5. 如果单据不存在，查询类似单据
-- ========================================
-- 如果上面查询没有结果，可以运行这个查询找类似的单据
/*
SELECT TOP 10
    FBillNo AS 单据编号,
    FDate AS 单据日期,
    FAffairType AS 事务类型,
    FDocumentStatus AS 单据状态,
    FCreateDate AS 创建日期
FROM T_STK_ASSEMBLY 
WHERE FBillNo LIKE '%ZZCX%' OR FBillNo LIKE '%003758%'
ORDER BY FCreateDate DESC;
*/
