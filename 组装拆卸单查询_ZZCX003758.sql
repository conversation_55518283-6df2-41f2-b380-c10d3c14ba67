-- 查询组装拆卸单号 ZZCX003758 的详细信息
-- 分为成品（入库）和子件（出库）两部分

-- ========================================
-- 1. 验证单据是否存在并确定事务类型
-- ========================================
SELECT
    FBillNo AS 单据编号,
    FDate AS 单据日期,
    FAffairType AS 事务类型,
    CASE
        WHEN FAffairType = 'Assembly' THEN '子件出库 → 成品入库'
        WHEN FAffairType = 'Dassembly' THEN '成品出库 → 子件入库'
        ELSE '未知事务类型'
    END AS 业务流向,
    FDocumentStatus AS 单据状态,
    FCancelStatus AS 作废状态,
    FNote AS 备注
FROM T_STK_ASSEMBLY
WHERE FBillNo = 'ZZCX003758';

-- ========================================
-- 2. 成品信息汇总查询（根据事务类型判断入库/出库）
-- ========================================
SELECT
    -- 业务类型标识
    CASE
        WHEN a.FAffairType = 'Assembly' THEN '成品入库'
        WHEN a.FAffairType = 'Dassembly' THEN '成品出库'
        ELSE '成品-未知'
    END AS 业务类型,

    -- 仓库信息
    ISNULL(sl.FName, s.FNUMBER) AS 仓库,

    -- 物料信息
    m.FNUMBER AS 料号,

    -- 汇总数量信息
    SUM(p.FBaseQty) AS 基本数量,
    SUM(p.FEXTAUXUNITQTY) AS 辅助数量

FROM T_STK_ASSEMBLY a
INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
LEFT JOIN T_BD_MATERIAL m ON p.FMaterialID = m.FMATERIALID
LEFT JOIN T_BD_STOCK s ON p.FStockID = s.FSTOCKID
LEFT JOIN t_BD_Stock_L sl ON s.FSTOCKID = sl.FStockId AND sl.FLocaleId = 2052
WHERE a.FBillNo = 'ZZCX003758'
GROUP BY
    a.FAffairType,
    s.FNUMBER, sl.FName,
    m.FNUMBER
ORDER BY
    仓库, 料号;

-- ========================================
-- 3. 子件信息汇总查询（根据事务类型判断入库/出库）
-- ========================================
SELECT
    -- 业务类型标识
    CASE
        WHEN a.FAffairType = 'Assembly' THEN '子件出库'
        WHEN a.FAffairType = 'Dassembly' THEN '子件入库'
        ELSE '子件-未知'
    END AS 业务类型,

    -- 仓库信息
    ISNULL(stl.FName, st.FNUMBER) AS 仓库,

    -- 物料信息
    m.FNUMBER AS 料号,

    -- 汇总数量信息
    SUM(s.FBaseQty) AS 基本数量,
    SUM(s.FEXTAUXUNITQTY) AS 辅助数量

FROM T_STK_ASSEMBLY a
INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
INNER JOIN T_STK_ASSEMBLYSUBITEM s ON p.FEntryID = s.FEntryID
LEFT JOIN T_BD_MATERIAL m ON s.FMaterialID = m.FMATERIALID
LEFT JOIN T_BD_MATERIAL_L ml ON m.FMATERIALID = ml.FMATERIALID AND ml.FLOCALEID = 2052
LEFT JOIN T_BD_STOCK st ON s.FStockID = st.FSTOCKID
LEFT JOIN t_BD_Stock_L stl ON st.FSTOCKID = stl.FStockId AND stl.FLocaleId = 2052
WHERE a.FBillNo = 'ZZCX003758'
GROUP BY
    a.FAffairType,
    st.FNUMBER, stl.FName,
    m.FNUMBER
ORDER BY
    仓库, 料号;

-- ========================================
-- 4. 出库入库合并查询
-- ========================================
SELECT
    业务类型,
    仓库,
    料号,
    SUM(CASE WHEN 业务类型 LIKE '%出库' THEN 基本数量 ELSE 0 END) AS 出库基本数量,
    SUM(CASE WHEN 业务类型 LIKE '%入库' THEN 基本数量 ELSE 0 END) AS 入库基本数量,
    SUM(CASE WHEN 业务类型 LIKE '%出库' THEN 辅助数量 ELSE 0 END) AS 出库辅助数量,
    SUM(CASE WHEN 业务类型 LIKE '%入库' THEN 辅助数量 ELSE 0 END) AS 入库辅助数量
FROM (
    -- 成品数据
    SELECT
        CASE
            WHEN a.FAffairType = 'Assembly' THEN '成品入库'
            WHEN a.FAffairType = 'Dassembly' THEN '成品出库'
            ELSE '成品-未知'
        END AS 业务类型,
        ISNULL(sl.FName, s.FNUMBER) AS 仓库,
        m.FNUMBER AS 料号,
        SUM(p.FBaseQty) AS 基本数量,
        SUM(p.FEXTAUXUNITQTY) AS 辅助数量
    FROM T_STK_ASSEMBLY a
    INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
    LEFT JOIN T_BD_MATERIAL m ON p.FMaterialID = m.FMATERIALID
    LEFT JOIN T_BD_STOCK s ON p.FStockID = s.FSTOCKID
    LEFT JOIN t_BD_Stock_L sl ON s.FSTOCKID = sl.FStockId AND sl.FLocaleId = 2052
    WHERE a.FBillNo = 'ZZCX003758'
    GROUP BY a.FAffairType, s.FNUMBER, sl.FName, m.FNUMBER

    UNION ALL

    -- 子件数据
    SELECT
        CASE
            WHEN a.FAffairType = 'Assembly' THEN '子件出库'
            WHEN a.FAffairType = 'Dassembly' THEN '子件入库'
            ELSE '子件-未知'
        END AS 业务类型,
        ISNULL(stl.FName, st.FNUMBER) AS 仓库,
        m.FNUMBER AS 料号,
        SUM(s.FBaseQty) AS 基本数量,
        SUM(s.FEXTAUXUNITQTY) AS 辅助数量
    FROM T_STK_ASSEMBLY a
    INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
    INNER JOIN T_STK_ASSEMBLYSUBITEM s ON p.FEntryID = s.FEntryID
    LEFT JOIN T_BD_MATERIAL m ON s.FMaterialID = m.FMATERIALID
    LEFT JOIN T_BD_STOCK st ON s.FStockID = st.FSTOCKID
    LEFT JOIN t_BD_Stock_L stl ON st.FSTOCKID = stl.FStockId AND stl.FLocaleId = 2052
    WHERE a.FBillNo = 'ZZCX003758'
    GROUP BY a.FAffairType, st.FNUMBER, stl.FName, m.FNUMBER
) AS 合并数据
GROUP BY 业务类型, 仓库, 料号
ORDER BY 仓库, 料号;

-- ========================================
-- 4. 汇总查询 - 成品与子件对应关系
-- ========================================
SELECT 
    a.FBillNo AS 单据编号,
    a.FDate AS 单据日期,
    a.FAffairType AS 事务类型,
    
    -- 成品信息
    pm.FNUMBER AS 成品代码,
    ISNULL(pml.FNAME, pm.FNUMBER) AS 成品名称,
    p.FQty AS 成品数量,
    ISNULL(pul.FNAME, pu.FNUMBER) AS 成品单位,
    p.FAuxQty AS 成品辅助数量,
    ISNULL(paul.FNAME, pau.FNUMBER) AS 成品辅助单位,
    ISNULL(psl.FName, ps.FNUMBER) AS 成品仓库,

    -- 子件信息
    sm.FNUMBER AS 子件代码,
    ISNULL(sml.FNAME, sm.FNUMBER) AS 子件名称,
    s.FQty AS 子件数量,
    ISNULL(sul.FNAME, su.FNUMBER) AS 子件单位,
    s.FAuxQty AS 子件辅助数量,
    ISNULL(saul2.FNAME, sau2.FNUMBER) AS 子件辅助单位,
    ISNULL(ssl.FName, ss.FNUMBER) AS 子件仓库,
    s.FCostProportion AS 成本分摊比例,
    
    -- 金额信息
    p.FAMOUNT AS 成品金额,
    s.FAMOUNT AS 子件金额
    
FROM T_STK_ASSEMBLY a
INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
INNER JOIN T_STK_ASSEMBLYSUBITEM s ON p.FEntryID = s.FEntryID
LEFT JOIN T_BD_MATERIAL pm ON p.FMaterialID = pm.FMATERIALID
LEFT JOIN T_BD_MATERIAL_L pml ON pm.FMATERIALID = pml.FMATERIALID AND pml.FLOCALEID = 2052
LEFT JOIN T_BD_MATERIAL sm ON s.FMaterialID = sm.FMATERIALID
LEFT JOIN T_BD_MATERIAL_L sml ON sm.FMATERIALID = sml.FMATERIALID AND sml.FLOCALEID = 2052
LEFT JOIN T_BD_UNIT pu ON p.FUnitID = pu.FUNITID
LEFT JOIN t_BD_Unit_L pul ON pu.FUNITID = pul.FUNITID AND pul.FLOCALEID = 2052
LEFT JOIN T_BD_UNIT su ON s.FUnitID = su.FUNITID
LEFT JOIN t_BD_Unit_L sul ON su.FUNITID = sul.FUNITID AND sul.FLOCALEID = 2052
LEFT JOIN T_BD_STOCK ps ON p.FStockID = ps.FSTOCKID
LEFT JOIN t_BD_Stock_L psl ON ps.FSTOCKID = psl.FStockId AND psl.FLocaleId = 2052
LEFT JOIN T_BD_STOCK ss ON s.FStockID = ss.FSTOCKID
LEFT JOIN t_BD_Stock_L ssl ON ss.FSTOCKID = ssl.FStockId AND ssl.FLocaleId = 2052
WHERE a.FBillNo = 'ZZCX003758'
ORDER BY p.FSeq, s.FSeq;

-- ========================================
-- 5. 如果单据不存在，查询类似单据
-- ========================================
-- 如果上面查询没有结果，可以运行这个查询找类似的单据
/*
SELECT TOP 10
    FBillNo AS 单据编号,
    FDate AS 单据日期,
    FAffairType AS 事务类型,
    FDocumentStatus AS 单据状态,
    FCreateDate AS 创建日期
FROM T_STK_ASSEMBLY 
WHERE FBillNo LIKE '%ZZCX%' OR FBillNo LIKE '%003758%'
ORDER BY FCreateDate DESC;
*/
