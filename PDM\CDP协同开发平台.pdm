<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1446957356" ExtractionBranch="1" ExtractionDate="1446957356" ExtractionId="418029" ExtractionVersion="6" ID="{CAB89AF0-D3BE-4C5F-B3DA-2214FC9E461A}" Label="" LastModificationDate="1362562155" Name="CDP协同开发平台" Objects="527" RepositoryId="{5C34CC33-BB05-4053-9571-E184DEFF2D78}" Symbols="71" Target="Microsoft SQL Server 2005" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>CAB89AF0-D3BE-4C5F-B3DA-2214FC9E461A</a:ObjectID>
<a:Name>CDP协同开发平台</a:Name>
<a:Code>CDP协同开发平台</a:Code>
<a:CreationDate>1331628230</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1357724615</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=0
RefrUpdateConstraint=0
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=E:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {5C34CC33-BB05-4053-9571-E184DEFF2D78}
MODEL_ID 418029
MODEL_VRSN 6
BRANCH_ID 1
EXT_DATE 1446957356
EXT_END 1446957356
OBJECTS 
BEG_BLOCK 
 418029 {CAB89AF0-D3BE-4C5F-B3DA-2214FC9E461A}
 418030 {53F75850-A881-4892-AC59-5E5A234AFA44}
 418031 {FFCA4FF9-7FD8-475A-BAEC-CB98B22A512E}
 418032 {3E597AF8-489C-471F-B2EE-7AEEB3F3DB57}
 418033 {C9B663F2-573C-49B4-98F1-63024EE703A9}
 418034 {5E094F0E-F084-463C-A97A-C0FB23719C8D}
 418035 {30B7D1C7-DCFB-4B0C-AC50-DE706D0331DA}
 418036 {2F86DD4B-F2F0-4B9C-A6C8-C2E4307C0A16}
 418037 {D90E10F2-745C-4E2E-B49B-6169358B530D}
 418038 {693A5237-96CB-4DBF-996B-FBA68FC70F9A}
 418039 {FEE117FB-7B4A-4864-B15A-0F72E5C58AEC}
 418040 {909E6B5A-6F64-4E59-9223-6C8170A5AF44}
 418041 {81C3E670-1E7A-4DB4-A26F-2D355252209E}
 418042 {567588CA-C2C2-4F95-8303-5AB444F32030}
 418043 {7C14438B-C7FC-4E9A-BEB1-76F07D0A0E1E}
 418044 {BBED6F62-1727-48A5-A82D-AE0DC1674063}
 418045 {F095D3A2-8875-406B-A976-F3E76AC9346E}
 418046 {A8477336-927B-4893-80C7-08FC9B85E63B}
 418047 {6AF8B337-A79B-4E35-A064-CD22BB13C200}
 418048 {ED56EE28-D23E-40E2-A3F3-B4E7E1F937BA}
 418049 {04ADEB9F-A69E-49BC-B008-938658341A8D}
 418050 {EDDD63F1-E1C9-43B0-9760-0D8D77F92FBB}
 418051 {C27B78DF-21B5-41AD-953B-85AC262D6747}
 418053 {0F5B4DBD-4C5D-4D90-ADE0-64381F9EB2E7}
 418054 {72025001-3818-4065-8A95-B439E6C21680}
 418055 {F63F5A97-713B-4206-985E-946EE09CEAB7}
 418056 {AA212284-9D03-4F60-8330-F6AAC4D8B426}
 418057 {AC4ADA54-1436-45E9-865B-4C67C719822F}
 418058 {731CE24F-A1A9-41CA-ACDC-199EC98832B5}
 418059 {C854FB4A-F434-4160-B939-7DF505CC8C48}
 418060 {5B06B976-A12A-4CD0-9BA7-171134272AA0}
 418061 {A5D4200C-D174-4047-8356-9A813A6AD722}
 418062 {C27DC3D0-27A8-4EDE-9C6E-F5CC3CD86768}
 418063 {53E44287-3EB2-4EBA-959F-4BA00F18DD78}
 418064 {38438672-5078-4F60-AEF9-E8785AEDB5D5}
 418065 {36CFC585-BB66-492A-8477-0F0BF9F60850}
 418066 {86A06AA5-6657-422C-9F3D-AD69E9FF432A}
 418067 {50AC7B5D-B5E0-41F2-AD9B-81F5945D5682}
 418068 {48DAA61D-635E-4F42-BE24-7EC4B03D6479}
 418069 {697B2033-F4DB-46AC-A7A5-D2EE66BE3601}
 418070 {380741B0-51F9-4D31-A365-CDE5FEE570F6}
 418071 {9149BEB7-C3D3-4558-BBBB-3DD2205F9CF7}
 418072 {A01FFAC3-9863-4A4E-9BAF-E7ADE35A049E}
 418073 {7824D3E7-7EA9-41B3-8428-26B9172AF55B}
 418074 {149938F1-24A8-4F04-80D7-20C8154142E0}
 418075 {EFA26C85-9F28-467C-8852-F8C7FED210B3}
 418076 {EBF34315-C130-4128-9258-4A729647EF47}
 418077 {DC5727CF-D405-451E-B3FF-9C9359261DB8}
 418078 {3EE092CB-00E6-4852-8FC1-C44DF8610312}
 418079 {88D1A45F-CDCA-4535-A6BA-827EA8C31D59}
 418080 {AFCE58B1-4B43-40B8-BA30-5DA313C99B09}
 418081 {CC82A7BE-890E-4788-AD65-DEB3089375FC}
 418082 {9C7EE500-F88D-4419-B8A6-3A8E18C93B5C}
 418083 {71AA6D47-5775-4188-9E2B-BD4FBCC82A28}
 418084 {3EB8B0A7-0DE6-426B-B374-1C40706D67F8}
 418085 {87B3BBD8-374B-4DF2-AFDE-3BE551EEC851}
 418086 {D0ED184B-F982-49FC-9B0B-80D45DC4FF01}
 418087 {6B4DF3B9-0494-43A7-862F-F5178DE0A0A2}
 418088 {0787836E-1DF3-412D-B6F8-876D888AD63C}
 418089 {9D06409B-927B-4D7D-B5A3-0706321AA2B7}
 418090 {6BC5FA2F-5094-41C6-847E-89294DA03E98}
 418091 {96628690-46AA-43C2-86DD-EA7086FC195D}
 418092 {6E351729-8EBA-47B3-8F5E-1E28630335F7}
 418093 {4F6EEA1E-7F57-4494-A5D3-3CD20F2D7872}
 418094 {E9C26354-FA1E-4B0B-AD97-3925866CB921}
 418095 {92983808-923B-496D-9A58-26811BC76478}
 418096 {B9E9646A-9C70-49E6-AD9D-931A1C05D7BE}
 418097 {6110FEE1-55B0-4C2C-8127-84E1A727B94D}
 418098 {BCD86854-FC6E-464C-B4DC-5DBBB1FC3A6E}
 418099 {494E45B2-36B3-4290-B492-DAF95699B668}
 418100 {9BA8FBD7-AF88-4C79-BE4C-81BC45A4C3BD}
 418101 {42FF6A96-9CAD-4F6F-ABBB-21BE329E85D4}
 418102 {677C9415-7ABD-4997-8E8B-E40F83F7DD98}
 418103 {4D410166-3DE8-417F-B335-E3068DCB2F05}
 418104 {66C91791-D30C-4164-8173-784FC6A3BF53}
 418105 {DEA7C71F-9779-4828-8DDE-02BF102B5CCE}
 418106 {1BB63B8B-D87F-4D21-B8CE-8BFD8B503B97}
 418107 {297EE92C-D2F5-4500-B4C9-E83A5E3CFAEE}
 418108 {4ABEAD84-E128-443A-B2A1-B404F927A4FC}
 418109 {A0188C10-C653-4A6C-B4BD-CC32B4A7A4EA}
 418110 {72C43738-4021-4774-88CB-3FB6F5CD83C5}
 418111 {9225EAC5-24E3-459D-B342-77D35084CC10}
 418112 {2388E819-E406-4381-931F-8734B1DE4943}
 418113 {7F424704-179D-475B-A0D8-841DAFDC2B68}
 418114 {05D8C27A-3947-483C-9446-E7CCE3F6D6DE}
 418115 {2B2F4AF6-DFCA-49C8-960B-0AE993207EAD}
 418116 {661DDA36-E709-40A1-87C9-946660B9FDF1}
 418117 {CBA80B90-738B-49FE-B275-E10DE67134A5}
 418118 {18B8DCFD-1A75-4189-84FB-761338619D4A}
 418119 {FB4CDC4A-046C-44DC-8903-8BE15A8652FF}
 418120 {2B898DD7-C003-4853-A532-0041E752ABDE}
 418121 {3563A78A-3D1F-4343-85CC-C0E73D29FA95}
 418122 {00EC0A7A-91E5-4B0C-9413-CD7B1B7BCEB2}
 418123 {B200C25E-2FA8-4C82-9807-584D05B6BD6F}
 418124 {260E4866-C66E-469C-8E9F-C84F90B17C5C}
 418125 {0D20D21F-622E-4151-A9DB-5928579154BE}
 418126 {F35C93E7-3A51-4B03-A5E4-ACFEAD47D697}
 418127 {DD9D54DF-EFA6-4453-98D3-36937D539EE5}
 418128 {98BCC2A0-E990-4355-833A-92812C20C1C8}
 418129 {5AFEAECB-49D9-47E0-B636-443775CD6317}
 418130 {4BC2908B-F6CB-48D6-A4DB-4ED6B1AEA4DD}
 418131 {D3B039AA-1E66-4B65-B6DD-C9482AF5C3D4}
 418132 {3EC61085-7FDB-4724-BE5A-7094E26B02A0}
 418133 {E98F7991-687D-4CD5-9081-7693145C63A8}
 418134 {16A1A7EA-BCE4-4788-8FFB-10DF672B5DCF}
 418135 {F037B95D-558F-4AA3-94E6-6C46FB664E39}
 418136 {CEC67B14-C7EC-450A-82FB-41394A309778}
 418137 {1C153248-1359-4078-AE06-7EBE8B48CE6D}
 418138 {A73CFD41-E17B-4952-83B1-0A585F749387}
 418139 {B6260C37-0001-4999-899A-FAC8AE63D40E}
 418140 {04470161-80D3-470D-AC87-9E43CD6357E8}
 418141 {01E52DE5-0427-4FA6-B5EE-66E48B2343FB}
 418142 {FD0C3C09-8F47-4635-8D18-1904F7FC6A23}
 418143 {2EED0ADF-D73A-41D6-A8C2-1478D739B8B9}
 418144 {75F45058-167A-49CF-B0F4-BDBA8BBA49CF}
 418145 {C03F65D8-8177-459D-AB77-06536C3175A4}
 418146 {3220B564-0FDC-4518-BB50-319026CC55A3}
 418147 {A39576C3-F5F0-4A88-B323-59871FAA0EAC}
 418148 {F4FEBB29-EAA9-49A7-B4E3-D9C7535DE636}
 418149 {BE021D41-886E-470E-97BF-656D06E7FE77}
 418150 {1E91C9DE-58CD-4224-8D0B-6DFF0406996F}
 418151 {976F06C6-CC5A-4BF0-A330-E6CD93D17E12}
 418152 {68C086E3-A7D9-437C-8CF5-5A7E3B93FEDF}
 418153 {C98C7CFD-307C-4A85-8741-7A34A429E651}
 418154 {3D42B1F6-32CB-4344-9094-B90E252D77D4}
 418155 {50D5FDB7-B166-4B64-8D15-680335266D90}
 418156 {FF36918F-B621-43B4-965D-9C88CB352DB5}
 418157 {BB4E0A25-DB9E-4ABB-AEB2-1A9B1B52A014}
 418158 {87EB811F-3005-4D0B-92D8-9F476100D0F4}
 418159 {9A0BE983-2D10-4826-9F3E-9D457DCB703F}
 418160 {8CD95126-242F-4D8D-A04A-BA8AE04E5BC1}
 418161 {FD1C756C-B77D-4FBC-9B2A-B52DB0F2635A}
 418162 {9A28E6AE-637E-468B-9C0D-7131F7F27AD3}
 418170 {B2670A29-5C6E-43AD-95B4-46617B5D76B6}
 418171 {A1E856FB-E35E-4610-B265-94704F40FB02}
 418172 {F926DC2A-5213-4109-A23D-3C037D9D2781}
 418173 {DDC9EA98-B872-44F9-A53F-AE5828C1F508}
 418174 {95B233F5-6294-4780-B6AF-61586AC76A2B}
 418175 {6005D237-3FD3-4286-8DBE-CD5D9DADC9DD}
 418176 {424D17CE-D8A4-4B2A-91F6-8CA4F241BD47}
 418177 {AAF517C2-D8EC-4FC8-9A7E-C1FF1F197670}
 418178 {131931FF-AA30-434D-B97F-2B5BBB67466B}
 418179 {5B57EFF5-BD8C-44C5-A5C1-873F534D5E28}
 418180 {933625F1-6999-4F26-8EAF-9D0036165B07}
 418181 {7CFEB017-7402-4290-8F8C-3A35AD46254B}
 418182 {72B4481C-39FB-4142-83FE-5B443EA6724C}
 418183 {36CFA2AA-1F16-402E-A112-A70D0D38198B}
 418184 {69A31991-EE83-413D-AC6D-E563BA167ADA}
 418185 {3B62E930-05B3-45E1-A1C7-424065D05F44}
 418186 {F89A9796-9AA4-4E96-A383-CC845CDC5BEA}
 418187 {87DC4415-B556-42EF-A2E1-F9809AECAE99}
 418188 {172A69FE-EAB0-4DBD-835A-CE93B79DE90B}
 418189 {B94533BC-5AD1-4AD5-AD95-F1602860ACF6}
 418190 {F924E036-987A-44C6-9E4F-72D5275CC857}
 418191 {6E694E9B-CBAA-4492-92B3-67BC0674E59F}
 418192 {D36DE864-F4A9-4549-89D4-51C31A4C2A0A}
 418193 {7799ABA2-86FA-416E-B044-5C3ED5A87C11}
 418194 {422ADF17-861D-4E94-A54D-029B112A5EC5}
 418195 {9782EF76-B37A-46D0-AC7B-1AB695AA416E}
 418196 {ECE28FE1-2AC7-4B7D-BC50-6A5086A77DFC}
 418197 {8BDF5288-4441-4899-B569-FFEE9FA988A8}
 418198 {B18BB8AF-41EA-4785-AB8C-786F1FC0C048}
 418199 {E5B98BF9-41EE-4399-808E-4C48B564C577}
 418200 {E105AF54-DE9C-40CB-93FD-D47F8635E18E}
 418201 {792BBB5A-4E76-4F18-AAEA-5551C0E1DFDD}
 418202 {8DB056BA-A36F-4131-8942-7597061FEC04}
 418203 {3FA138D4-2182-4CCF-86CC-C526CA19EE54}
 418204 {72CD3E37-0288-4F62-8CF4-D3E009C1BF72}
 418205 {28B075EE-BB49-4A7E-BC08-6AB1988BE4E8}
 418206 {FA2598A7-B29F-452E-B3C4-77E9DC1C6753}
 418207 {10335234-052D-4508-8776-C6E4D4954651}
 418208 {7D6F8961-62FE-43B1-BC0F-629A189EAA1D}
 418209 {D21C8369-E6C7-4E9E-90F2-7A00804E7783}
 418210 {1E7C4E1F-F708-4F6A-B800-99D23877CC91}
 418211 {B1BDA656-1C9A-4BC4-B438-20C20CB3A4BF}
 418212 {549BA9FA-93A0-48EC-A396-FE1D57007F42}
 418213 {94597544-4ABD-4C79-BA4D-8C1AAC003CF0}
 418214 {7002B83A-F0C0-473D-B259-E9EEF6214E1C}
 418215 {02A25B43-3EB8-40BD-8026-946B9E212EB6}
 418216 {DF9B7A09-A35F-498C-BB99-1FD6E6CDF997}
 418217 {482767D1-780A-4E40-A3C2-19E1AD5670FC}
 418218 {06C47C3D-F252-4C3E-943E-F98619549707}
 418219 {C054B05C-EB2A-4141-8483-7CB19108651C}
 418220 {C898EF2D-8E50-41B5-A5BD-C6268F551E4E}
 418221 {416E9AAB-46A7-4C46-875E-EF60B4F81F5F}
 418222 {B2E6C496-E744-4EBE-B1B1-9B7F9B29A0C6}
 418223 {76691A5D-622E-46E7-893E-9645AE1E3864}
 418224 {71CD6D51-284A-46C9-8240-AC60D568DF03}
 418225 {9CA18596-EBD9-47B5-8349-A7CF0A4E7319}
 418226 {90D25117-395B-41EB-B803-EC34568486A5}
 418227 {FA9DF12E-6A33-44F7-BC70-8AEB55FABBD1}
 418228 {55B8FFF9-EFC3-4B02-84DD-634BB67A0924}
 418229 {971FEA68-5E84-4BB7-BD37-3750E551B642}
 418230 {35B2E1E4-E128-4D89-964D-79B809A4686F}
 418231 {9B7F2BF6-37EE-4443-92AA-A5CA2E0274A0}
 418232 {19D918BF-4146-4C75-B9F5-8578F51524D5}
 418233 {25E73A59-D81A-4F52-92D6-E0EDCEED7908}
 418234 {F5ABDEFE-6B36-4B72-85A1-79E15F911217}
 418235 {7A7F66EA-1D6A-4A4A-B0FA-1C994ED94BDD}
 418236 {E76029A3-80D2-4A75-BFD9-7F3F163BE301}
 418237 {74E6168C-9271-477A-B919-22DC9B58BA24}
 418238 {A78384DA-CFAE-40A7-A516-487F94884F5C}
 418239 {CF19373B-13E4-4D44-9243-D884BA0285F2}
 418240 {46B55676-CEC4-465E-A28C-E89B40402371}
 418241 {0549D91A-6BBF-4814-841C-40E16A20E6D3}
 418242 {810369FB-02DB-452C-8B3F-7796D8904DA6}
 418243 {505D0980-493D-4F9E-8634-A8BCFD26532E}
 418244 {B0582290-F75F-46BE-9AF0-6E2BE6F08FE4}
 418245 {93801BCD-155D-4F90-B4BE-E585C551AE69}
 418246 {0391EC01-D046-4C7D-B1C9-F48064C3715F}
 418247 {B6FFB945-1D14-4D46-9280-7115316D0B1E}
 418248 {FE9A6E9E-10ED-4E24-8CE2-72BF0D75E340}
 418249 {C74E8CF0-534E-42FE-B474-B45EBEFC33D1}
 418250 {365330C1-64F8-4B86-8485-856FD4C18A3A}
 418251 {C7D8CBDB-CD10-4600-9005-C6CAEC90FA51}
 418252 {39351365-E15F-41B0-B730-42419A85EC66}
 418253 {908BD85A-89BC-4ED4-9C36-A85D8CF031A1}
 418254 {2C40C635-2F70-4E0A-A5D2-482A352C3311}
 418255 {9C1AD0A4-556D-4D65-AEB7-276A24CCB5A5}
 418256 {C087D445-FD2F-486D-A327-E789145470B2}
 418257 {CB4EE904-44CF-4D12-A5A1-2492546333C7}
 418258 {EA981ED5-C045-4E5F-8065-25F9C59C6D16}
 418259 {94047932-BB75-4072-8C4C-CADF69CCEE6F}
 418260 {3D9E5B50-7189-4988-9620-C55FD2C4F352}
 418261 {600B1832-F1DC-4004-BE55-255A35497351}
 418262 {59438D05-CF71-4743-BE1D-59398615D275}
 418263 {6D01CC5C-26AC-4ADA-8D85-3E69EE5551C3}
 418264 {247B8DE2-330F-49A9-952B-D5BD94129863}
 418265 {8C121281-8144-497F-87F7-EC17DCDFBC22}
 418266 {7C740A6B-3BCC-4A9B-9A5E-19286189A5B3}
 418267 {D5D0C0FC-EAF0-4167-9FC8-491956DB8A7E}
 418268 {4A72CF3D-10D8-44BC-8286-95BCFD75FFAF}
 418269 {9F7CDA2F-BB78-494E-8E7B-0E2696F38571}
 418270 {F633A1E8-94FF-4F2C-A6E3-B4CE8617F8F8}
 418271 {967D8417-E7A0-4B62-895E-F074EAB6496B}
 418272 {1980A819-9386-4AE2-AE4D-1412CD51BA05}
 418273 {A37587A0-D24D-4764-9CE6-1CA449D5896C}
 418274 {C9AE12D8-8235-4F3C-9C79-43601A883C99}
 418275 {5EB2BC33-F094-49A4-B542-48EE8881FE07}
 418276 {89695388-9AA9-4E10-A209-BB076239556F}
 418277 {D1EB4549-E006-4228-8A42-1C6206D0E662}
 418278 {DFAE9C92-7DF9-4018-9050-12BDEA766A43}
 418279 {E091572C-3A7A-453A-8305-501CDDD430BF}
 418280 {8AA488A9-9403-43A0-99CA-0119DDA8F835}
 418281 {8DAAEC36-5612-40A5-826E-E65BDD38BD33}
 418282 {9556AE4C-0769-4159-A65D-50DB8E21081B}
 418283 {12B4D738-85A1-45B4-8265-53B012582D5B}
 418284 {4065C052-D7CB-450B-9AED-A51FDF630DA1}
 418285 {615956AD-4542-42C8-BEF8-CD56D01496FD}
 418286 {7F8E3BC6-9F2B-46D1-A825-263E4D3F685C}
 418287 {9534F885-9799-4B7E-B24E-4000091B92C7}
 418288 {D0AEFAEA-98D3-4B77-8243-D585392865DA}
 418289 {7B5B3442-39EC-42B7-B9AE-93CF83C383D6}
 418290 {19C71C8E-FB8A-4D5C-843F-4AF01BBE06FA}
 418291 {AF01D768-9D62-45B3-90C7-9D8D6A303397}
 418292 {22D9E4DB-DDBA-4EF4-85B3-1C0A8181BE49}
 418293 {38152F15-6941-453E-94C3-8B362FE32B0B}
 418294 {DB9AAD87-FD90-4FB1-A92F-147966845E48}
 418295 {CF1A69C0-9AAE-47B3-BEE8-EA7EEC3B0D92}
 418296 {9CBDEA82-F131-439C-AFF4-211C51F5A690}
 418297 {7B7E5328-DAC5-463C-A452-34290187A6B4}
 418298 {429A4166-C4BD-4BA0-9B86-642EF3AE4803}
 418299 {DBCF68B0-7C75-4A99-B610-89968352587E}
 418300 {E0426366-47B1-4EE3-A916-429573575AEE}
 418301 {E5B9014A-1C63-4AE3-908B-D32B4CA69EFA}
 418302 {205EF6D6-6FB5-4DE5-BE5A-A4751ADAD33D}
 418303 {087F2EA6-DAC3-4BDB-9F6E-8F7B4D7E1166}
 418304 {BD8BD099-77C9-4F53-87B5-F989801A6FDF}
 418305 {24930EFE-3BD4-4F1B-833E-5374126ED5D9}
 418306 {C0978EEB-5CE3-4ED4-8149-D65D1CA3DE74}
 418307 {6AD2F683-EF1C-410B-A907-E7D85DB9C9C1}
 418308 {3DF86AE1-286D-4F1A-AD84-EA1547415320}
 418309 {4DACE8B6-B708-48DD-9AA1-AE66AE447405}
 418310 {748C7506-A166-4E70-A245-E970A6C8C999}
 418311 {02CBC975-7073-4031-992B-56E0F0BF3631}
 418312 {0383DB25-CB1C-4A4D-A2BC-9D3FDF982EAD}
 418313 {6AEF3FA3-1D41-44FC-A5BF-56625F3DF2CB}
 418314 {6EEC6F01-26E1-4FF7-9D48-EC60E2200671}
 418315 {834D4EC4-6426-4CC8-9F06-5CA4C36FA452}
 418316 {0A95CF84-2BA1-4465-ACC8-4AF628328C0C}
 418317 {2C81043F-78F3-4793-9777-98E79D6E7336}
 418318 {0B6C48D8-1B3B-44FF-B144-3E5F13B643A4}
 418319 {2A731F26-61FD-4883-8DB3-79939ECD2C90}
 418320 {9B784CA5-DA4A-4344-8FBC-99D8B7839347}
 418321 {590FFFFD-2903-4DB1-BA1C-41F58BADA006}
 418322 {6A419E83-EB8D-412C-9CF1-73FAADAE7ADB}
 418323 {24719FD1-792E-4541-8301-5E281CFEEC3B}
 418324 {609F52E1-7D9C-47E8-9AB1-C3D7D906CE00}
 418325 {A45D2E58-1389-4524-A39C-10919E4E410F}
 418326 {B098D284-3723-41A3-A8F8-2B2F036138A9}
 418327 {716D0770-2DFC-42EE-A637-50342B464C01}
 418328 {6C32F73D-2EBD-43ED-B391-4B1922DF03B3}
 418329 {3DA206E0-841E-41BE-8F97-11C2D15C6F1A}
 418330 {CE3EE23C-9F8C-4FF6-A04A-BD1577E38D4D}
 418331 {1391F30E-F699-4E5C-966B-F2DD6FAB6930}
 418332 {751EF9BB-CA53-4CD2-BF6E-A8CD8BC2D2C4}
 418333 {C7057F4D-1138-44F9-A6B1-425B2E95FC39}
 418334 {FCC80728-13D4-4C78-A580-89F2441805E6}
 418335 {D9C60FFD-F39A-4755-AF9D-6F0C4EFFD407}
 418336 {A1CFCB71-772A-49F1-A1EB-1B76CB49D5EE}
 418337 {D3F4B0AC-15A5-4D67-B5F5-05C26B47C789}
 418338 {F1610A6C-77E4-4329-9889-1B3D60DB0D9A}
 418339 {F05640F6-2BCF-47F4-A5D6-4F9227CDBAAC}
 418340 {B95E3D2D-EFB5-4E30-9E3F-C79EE0739BC2}
 418341 {E97B5A74-519F-46BC-9583-0B6BC4A34178}
 418342 {AD3B47EA-8B04-4CE6-BC24-6CE208ACF661}
 418343 {FF3737DF-C8C9-4B61-8CC1-8B0041B62C44}
 418344 {58FA6D2B-AFB1-402F-B475-B4FCC41439ED}
 418345 {086748D0-B1C5-44DD-B573-5F15ACBC5795}
 418346 {A8DF048C-4585-4CBF-A493-8F59EE077ACB}
 418347 {C26A2CD7-D976-43C9-9A8B-C8AEB1D3C8DF}
 418348 {70954C8E-FAF3-44AF-84B5-470E0E4A4F35}
 418349 {48397055-37FD-4147-9179-EDB74D3F86AE}
 418350 {67B6F116-C0F7-48D5-AF47-6A70072E25C1}
 418351 {A4750AA4-6A94-4FF7-9A3F-71A59C09899F}
 418352 {C0E27BAF-50C1-4EF2-BB15-E2445E0F0D5E}
 418353 {3982FC44-FE22-4FE9-AF14-6CA9C2E9EF15}
 418354 {8E518D96-BC0A-45F8-B2CB-4A553F791B64}
 418355 {E9101167-3C64-456C-85DC-98DFAF0937BD}
 418356 {D9A6E77D-CEA9-40E9-A882-34D0D609F9A6}
 418357 {65CB8A14-B6DC-4443-AC4B-71EC93C985AB}
 418358 {252ECD18-5558-41FC-AE38-C4D246345D47}
 418359 {DFDDB014-4E62-4EA7-9266-4C4DD032A129}
 418360 {29D93C5A-F3B1-49CA-A729-F270F262BCCF}
 418361 {1A520A10-65CD-4B6A-8277-4B46DE6A14AE}
 418362 {D1FAB273-2576-411B-8E22-F524C72AB4EF}
 418363 {FF33D7BC-C3FD-439D-9A02-96AFCBBBBB08}
 418364 {C6A2729E-5F76-4E1F-B472-F21064D926EE}
 418365 {1BCA9D46-7127-44E3-B889-F4AFDC0521B9}
 418366 {ED11F5F0-C04E-4D8D-9D28-0B5E7ADB4845}
 418367 {A5BAD776-4B63-49B6-936E-250B46766D56}
 418368 {EB7CED02-0093-487C-8C14-719412066B02}
 418369 {8355FAA6-7912-4B0C-B5AE-65C645E2FCA6}
 418370 {CCF37E6F-5E47-4110-9CC6-B3BF52C38D7A}
 418371 {2B12BC69-1E3A-45CF-8089-1E12C7766137}
 418372 {EBACF3E6-B383-424D-9858-0B2B4D8246F7}
 418373 {BC7ED114-06F6-45D3-8E89-995594E7A91F}
 418374 {4055236A-66C5-474F-88A5-5C8E7C82B4E1}
 418375 {3F4BE8ED-5A32-4A2C-AC9F-143035BBA367}
 418376 {2F34E708-51DC-44F3-B2FD-037AB93A9FAD}
 418377 {4DD7E694-9570-4122-9AC6-50E8AC86C24E}
 418378 {F120D647-B318-4F36-AED4-6856ACFCCE7C}
 418379 {88758A78-A294-4F9A-B44A-AB1536543311}
 418380 {8E7752CA-530B-49D4-94F6-C581527B9044}
 418381 {6A8FA942-D2FD-44E0-AC3C-F4D2A326A23F}
 418382 {249A7B85-465C-4D07-B694-F87D23C7B407}
 418383 {B8AF4600-AEC0-4BBC-81BC-61D24FECCEBD}
 418384 {66417F5B-C201-4F8C-9106-8D027B108A32}
 418385 {AF0E8434-E5F7-4C63-8E89-2F1A4B5C9E6C}
 418386 {FFEA28F2-0784-4E2C-B525-2EFA75AAF8F8}
 418387 {B191208C-04DB-446D-A1CD-F72215ABFBF0}
 418388 {1B678CD0-599D-4E75-96C4-3CB7484C2D82}
 418389 {32746237-DE53-4384-B176-F4CE46370419}
 418390 {A505697B-EB70-4423-9357-4C899C9D4361}
 418391 {9C0C2C3A-01F7-460D-96EF-24E7A9208AD6}
 418392 {4F76DF98-BD9A-4333-B762-12981B84C685}
 418393 {C129A557-C9B9-4F4F-B70C-774D29CA5DB0}
 418394 {739F76F7-C0B3-4E99-A81A-D7CB8174ACEC}
 418395 {9116BE7F-BEEF-42DF-A7A9-CD905BBD46CC}
 418396 {E2C16F36-6C69-4675-8356-EB12657C07E7}
 418397 {1971F73C-06F2-432A-9685-1817AD2F2CCD}
 418398 {B3E08FB2-8462-4BA4-81D3-4EF2859B5F2D}
 418399 {2F654B32-0DCE-4A7D-8D21-223A65DE45EA}
 418400 {77A44FB3-DAFE-4353-89D9-A45F9DA605F0}
 418401 {A9256B17-20D4-476A-AAB8-821B3C361D11}
 418402 {47EC52FA-B76B-411A-91A1-D29817D2640D}
 418403 {14B6F20F-B6F8-4865-AE68-077568E9C9E0}
 418404 {ADD8C95C-5018-4C33-8653-DDEF2505E923}
 418405 {74473188-DCC5-41D0-BBD0-************}
 418406 {6621CF22-4F01-4F84-885D-33E53BA21680}
 418407 {22D35724-2BFF-4A0F-9D54-AD6BAA5E4914}
 418408 {0F31929E-122A-4D2E-A4BF-4C720C9FCFD9}
 418409 {B36178DB-56AF-47D1-BF2B-5640CDA64C71}
 418410 {56FF11A6-1B3C-4B9D-9381-57CEEA691B5E}
 418411 {58E0D313-0667-4567-AFBF-08CD13011D65}
 418412 {0D4FE620-BF0D-46A7-95AF-59A63BD39168}
 418413 {B7D13662-B55B-42EF-ADBB-5316E3FE7709}
 418414 {2DEA01EA-89A4-4CBF-932B-67E5A8FCF87A}
 418415 {A4B3D11D-44BA-4553-9162-180DC43B54ED}
 418416 {346DD4A3-BB1F-4F65-A62A-D66085D8AF3A}
 418417 {385100C5-D6C8-4E09-9010-E27E6B86951B}
 418418 {25AED798-DEA4-49F1-88DA-B6E873B70ED5}
 418419 {952DE99B-AA2F-42D0-ABCA-CCFC1A849D6D}
 546891 {FB53D145-9F71-4028-9D13-B29D16C75096}
 546892 {9E95371F-BB4A-4ECB-8C1B-136A410EF2B4}
 546893 {9E8E0AFF-CDFE-4908-BDBD-6A620E2FFD3A}
 546894 {7B79A7B6-C46A-4178-BC9F-768E9DA79FC1}
 546895 {68F8E41B-68D9-4688-B8A1-7FD317909B6E}
 546896 {72904C96-15B0-4D55-A61C-1D62067A53E0}
 546897 {2778EDD8-B11E-4C49-87F7-BE27FD22FF26}
 546898 {16B18FC0-7BB9-4142-A085-72080622A92B}
 546899 {68463CB6-83E9-43BA-A68E-4071BFB1BF3A}
 546900 {54E79C70-6886-4386-8AB5-BA6958B3005D}
 546901 {57FB183E-F8E7-4AF1-AA7A-CEF04FE201B1}
 546902 {0AC99042-2685-484E-BE76-D1CA0DFC1501}
 546903 {9B611860-2EF4-477C-A83E-7424E3BCFAFF}
 546904 {85F05803-4A3F-4FCE-9516-502E016EFBB4}
 546905 {DADF382A-881E-478E-AB34-412A1C29E602}
 546906 {40EDA9FA-F210-4C49-850D-780F76F3C25B}
 546907 {1FAEB021-2EFB-4A06-8293-A632F1FFA6FF}
 546908 {BC7CE5FE-808B-4F66-966F-54DE6D51D871}
 546909 {3F3ADA83-8020-4096-8830-D28F62781C5C}
 546910 {4E259807-4149-493D-AA28-3271CA1F6D08}
 546911 {17A45D8F-676E-4238-9E0B-1DAAEE2749DA}
 546912 {1986425A-0948-4D47-88A2-336C3F43F8C6}
 546913 {79557582-8341-49C5-8E2D-749F28A4E4EB}
 546914 {3A5EBC51-531C-498C-AB63-175A0EDBD7F2}
 546915 {CDC70F93-EE8B-4796-BF13-E68231DC8C93}
 546916 {B56C77DD-3B9A-4260-A862-7D97CD1AFA38}
 546917 {F416A7FF-7A3F-4634-8DD5-0E9CF17DA219}
 546918 {6F167A60-683E-4187-A1EE-19F091506BAE}
 546919 {4EFA51CE-B10D-41CC-9CD7-D698537E43C7}
 546920 {DEFA5655-714E-40A8-B6CF-F7911A5761CA}
 546921 {B6163D5E-2D51-42E3-B31F-5DAFEA51934F}
 546922 {E4C905E7-60E7-4CD5-8829-F1CED02D5D85}
 546923 {C6705ED6-62E1-4442-B395-2AF97139BD47}
 546924 {02138748-7DD3-4166-B6CF-C514A2A480FD}
 546925 {A24132FF-D9D8-4C7D-9F16-5D7E19CA1B21}
 546926 {CDD076B9-D12C-4A64-8D95-C4CD7347A6D1}
 546927 {230EFF5F-7D3E-40D4-9E4C-F5717BC75000}
 546928 {69DFCE7B-9380-46C6-AF57-05B1229E8BFD}
 546929 {17903ED1-2650-481C-B7CD-0199B04B842D}
 546930 {5767A8FD-687E-49D6-98C4-8EFD412E7F72}
 546931 {103A943B-89DA-4EF5-8A1A-E85655E6B80D}
 546932 {CF0CA2B3-1E2A-48CC-94B5-D419E4EFA215}
 547127 {CE6DC321-AAB5-40DC-8E8A-02A40C488C7B}
 547128 {1329BF0D-1C14-488C-848B-A8B178759F8B}
 547129 {6611611D-B5CA-410D-BD38-F04F12912C3B}
 547130 {938228D1-4500-4D5A-A223-AEDFDBA553B3}
 547131 {439B77AC-05E7-4558-A52E-412EC7B1866A}
 547132 {28D10D88-2D6C-4667-9FF0-1E0687C43FAF}
 547133 {5E06C0DB-A19F-4F83-8179-3223408B5DD0}
 547134 {8B005CB3-35E3-43EF-BDE6-B6D1A50A946E}
 547135 {3FDA4647-450A-4168-AD7A-23317724464A}
 547136 {E5B86AEA-A52A-474E-A567-2F27A812395D}
 547137 {D46FD66E-31B0-4E8E-B065-6D5017B7963C}
 547138 {7F8FD5DB-77B5-4013-A480-934D5ED94BC9}
 547139 {6CE693E8-DE23-4F6F-BB75-0BFC183A75C2}
 547140 {92CF313F-D201-4C4C-9BF9-5D6773E1E872}
 547141 {216F67D1-27FD-4EBE-B338-5EC6D4B45CD9}
 547142 {A7825CA7-397D-4E62-AF29-FC80C0A6D593}
 547143 {79F1CD32-9CEF-40BE-95DB-3415543BE2BD}
 547144 {42F403D8-46EA-47DD-AE90-076F8C32CADC}
 547145 {DA087D35-AAD9-47D3-80E5-14D263797E77}
 547146 {5C38C34D-9A7A-4CC5-A057-B7B891893A4D}
 547147 {3D015FBA-ECD5-4FDF-AE5B-74AB0334D91D}
 547148 {1CE791A3-BE35-4891-9DB2-032243FAFEF5}
 547149 {B51DE85E-1F98-4FFF-8AE8-41DB55102C3E}
 547150 {41FF4209-AEEA-440A-A209-7DA9EA7B9E91}
 547151 {DA588BC8-7B2B-4C8C-A276-B00ECE6B54DB}
 547152 {28F934A7-7FBF-4E57-BBB1-BAE44F498787}
 547153 {8A170627-B269-4943-BB53-9B25F0B616C6}
 547154 {ADD8A11C-B378-4763-82F4-5A1F8CBBFAEA}
 547155 {4194E2F3-5C6C-42DA-8BA8-8EF10316F804}
 547156 {DAA50445-B52A-4093-B79C-2D18193990C1}
 547157 {4BF83B39-C83A-45A2-AA51-3255A08403AB}
 547158 {11BB54E0-992B-499D-BB4D-2E41A1260BCE}
 547159 {0B04068E-7AF9-465C-AFB8-41EEC6903D5C}
 547160 {C81BD36B-5FFE-482A-BBB6-7159A5C756BD}
 547161 {023DD4E6-F4B2-49B6-BC47-CEF8C7FFA310}
 547162 {B6717378-80DF-405A-BCD1-292A059ABACC}
 547163 {11F33A9E-C18C-4A6C-8918-7DA5F84FA6D6}
 547164 {209D144E-F0AB-404D-A789-DA4906202EB6}
 547165 {F7F54139-AE51-42CC-96A7-8031172FFB8B}
 547166 {471A5DD3-F671-478F-903D-395F1275A49F}
 547167 {3FFE2B86-2C05-4847-8BE6-B2A36B79FD4E}
 547168 {BCB65222-AA66-49E3-962A-7702718AC460}
 547169 {1F2526F1-7E17-447F-BF1E-25E96F6F43FE}
 547170 {0EFA751C-8C13-4133-9D2A-5D72FFABE394}
 547171 {4BA1F7F4-223A-462D-8415-CF6BBE80BF2A}
 547172 {114C64F7-59CB-4D17-9CE0-EE0C1DDD37E9}
 547173 {6901B41D-43FD-4F82-BF85-771F2BACFB76}
 547174 {9FDBBA0F-96F8-4F62-B4E7-BD74BBBC47BE}
 547175 {A9B35F97-217A-4755-9BCC-604ECB687A4C}
 547176 {EFE20BF9-0375-4167-B5B3-E867B53A0BD8}
 547177 {9143AED7-D413-41E5-AC23-3BC51179EE73}
 547178 {DE3EF93C-5381-4EA7-84D3-8A5AB46F2062}
 547179 {1B096923-AC3A-4719-AB8C-FFD8BDA26246}
 547180 {15B33E84-BB3F-48F6-90D8-AA9CC88B7561}
 547181 {243F7350-5CF4-4E3E-A261-C5EF56DC79E6}
 547182 {40011D25-3023-4940-9635-A7EA8044BF3C}
 547183 {9DD3E534-5C7A-49F3-9937-2F39773ECD28}
 547184 {A6B6B092-27D7-4BB3-B68E-02D890A10AD9}
 547185 {D9D8A695-5A1E-4BF4-8052-3D275BA8C068}
 547186 {A904FC50-36B1-41CB-B332-801C83F62441}
 547187 {B2CA6039-EE0C-4AC0-AA28-FB12AA705271}
 547188 {69822902-ADD9-4137-B6D8-185E18573AA5}
 547189 {274FB0E4-46D9-4314-AB7A-B6BA57EA2795}
 547190 {FF1518C4-4920-4034-9AEC-06FDE207785D}
 547191 {1A78D095-50DB-4678-BD48-510AA7866E9E}
 547192 {396D6D6A-D13E-4E88-8EAE-AE0AC125C01A}
 547193 {EC829508-EB0C-4AC3-A22F-72EDEE454610}
 547194 {8FC10024-B07A-4DA3-A17E-0CD7351911D6}
 547195 {D21B0C06-B3E0-4C86-918B-8E5391099F31}
 547196 {7D7C340C-E13B-4778-A957-2111ECE9CDD6}
 547197 {AB974C65-D62D-44B3-B527-6E4420F6D0E3}
 547198 {43C566D5-F051-41F7-9611-DFB87CE97D8B}
 547199 {F55D8B09-C1CE-43D2-B6A5-9326B7608DF7}
 547200 {D19037E5-CC58-4FFA-BF7A-2D5A196ADC84}
 547201 {808D5836-E850-4DB3-A366-4C790E8E3600}
 547202 {528DE32B-E723-403D-9969-EC5B660959B7}
 547203 {2A9531F2-C315-46DB-BA49-00BC5502147F}
 547204 {856982DB-D5C3-4235-8C7A-B04C90F367DF}
 547205 {122DA016-4CCF-40C8-9637-89E3A00CAE8B}
 547206 {F69EC850-137B-4764-A11C-B69C7EA42961}
 547207 {2CA47508-C2C8-42DF-A8AC-743129C9A59C}
 547208 {508CFCC2-693E-4CD0-BFFD-0209E70D5E38}
 547209 {8E3ED9A8-443D-4EA9-A4B6-297DE914BB71}
 547210 {B13C67E3-9B25-4917-9E09-ACFF1F69F70E}
 547211 {D16CB9A9-7686-4E07-8389-3AE9F643D3A8}
 547212 {B55EB68B-524D-42BF-BA61-3D054CF0E2DC}
 547213 {4484080A-DACF-473A-A5B6-CD8B32E6F752}
 547214 {51E17CE2-FDED-4FBA-83F7-70CE47B54EC1}
 547215 {CADF4859-ADC9-4D91-9BF1-65C7F255BF3D}
 547216 {D9F06AF6-DD0C-4143-BEBD-A67F05E56410}
 547217 {60A54DD1-21EE-4EC4-B04C-454A86901BDD}
 547218 {F077C215-49F5-4D80-981A-75C53096829A}
 547219 {D468AA9B-CD2C-4D2F-84DA-8936C7D76183}
 547220 {F44F0566-E166-4423-80FC-E7CB7546E266}
 547221 {2212CBED-64A2-4CEF-88FE-4EA5AA2B11ED}
 547222 {44B2C792-D156-4058-8188-11BB67328D1A}
 547223 {0DC620F6-B8AD-4AA8-951F-EB59DEA6298A}
 547224 {D486873E-1FEA-4B7A-AAA3-BC07E5C1AD59}
 547225 {1FE3985D-D080-4EA7-93E6-635CAEB40343}
 547226 {569F49BA-77CF-40F8-8EDD-4C4EEC7073FC}
 554779 {4C244519-1890-40D0-9A3B-B84CA5F3F0A1}
 580037 {1D97B6AF-45CA-48D2-9607-858E5612A365}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>53F75850-A881-4892-AC59-5E5A234AFA44</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1331628228</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1331628228</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>FFCA4FF9-7FD8-475A-BAEC-CB98B22A512E</a:ObjectID>
<a:Name>常见字段命名规范</a:Name>
<a:Code>常见字段命名规范</a:Code>
<a:CreationDate>1331628575</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332232982</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:TextSymbol Id="o5">
<a:Text>{\rtf1\ansi\ansicpg936\deff0\deflang1033\deflangfe2052{\fonttbl{\f0\fmodern\fprq6\fcharset134 \&#39;cb\&#39;ce\&#39;cc\&#39;e5;}{\f1\fnil\fcharset0 Microsoft Sans Serif;}{\f2\fswiss\fprq2\fcharset0 Calibri;}{\f3\fnil\fcharset134 \&#39;cb\&#39;ce\&#39;cc\&#39;e5;}}
{\*\generator Msftedit 5.41.21.2509;}\viewkind4\uc1\pard\lang2052\b\f0\fs20\&#39;b3\&#39;a3\&#39;bc\&#39;fb\&#39;d7\&#39;d6\&#39;b6\&#39;ce\&#39;c3\&#39;fc\&#39;c3\&#39;fb\&#39;b9\&#39;e6\&#39;b7\&#39;b6\&#39;a3\&#39;ba\par
\b0\&#39;ce\&#39;ef\&#39;c1\&#39;cf\&#39;b1\&#39;e0\&#39;c2\&#39;eb\&#39;a3\&#39;ba\lang1033\f1 FMaterialId\par
\lang2052\f0\&#39;b5\&#39;a5\&#39;be\&#39;dd\&#39;c8\&#39;d5\&#39;c6\&#39;da\&#39;a3\&#39;ba\lang1033\f1 FDate\par
\lang2052\f0\&#39;b5\&#39;a5\&#39;be\&#39;dd\&#39;d7\&#39;b4\&#39;cc\&#39;ac\&#39;a3\&#39;ba\lang1033\f1 FBillStatus\par
\lang2052\f0\&#39;b5\&#39;a5\&#39;be\&#39;dd\&#39;b1\&#39;e0\&#39;ba\&#39;c5\&#39;a3\&#39;ba\lang1033\f1 FBillNo\par
\lang2052\f0\&#39;d4\&#39;b4\&#39;b5\&#39;a5\&#39;c0\&#39;e0\&#39;d0\&#39;cd\&#39;a3\&#39;ba\lang1033\f1 FSrcBillType\par
\lang2052\f0\&#39;d4\&#39;b4\&#39;b5\&#39;a5\&#39;b1\&#39;e0\&#39;ba\&#39;c5\&#39;a3\&#39;ba\lang1033\f1 FSrcBillNo\par
\lang2052\f0\&#39;d4\&#39;b4\&#39;b5\&#39;a5\&#39;b7\&#39;d6\&#39;c2\&#39;bc\&#39;a3\&#39;ba\lang1033\f1 FSrcEntryId\par
\lang2052\f0\&#39;d4\&#39;b4\&#39;b5\&#39;a5\&#39;c4\&#39;da\&#39;c2\&#39;eb\&#39;a3\&#39;ba\lang1033\f1 FSrcInterId\par
\par
\lang2052\f0\&#39;bb\&#39;f9\&#39;b1\&#39;be\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;a3\&#39;ba\lang1033\f1 FBaseUnitId\par
\lang2052\f0\&#39;bb\&#39;f9\&#39;b1\&#39;be\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;ca\&#39;fd\&#39;c1\&#39;bf\&#39;a3\&#39;ba\lang1033\f1 FBaseUnitQty\par
\lang2052\f0\&#39;bb\&#39;f9\&#39;b1\&#39;be\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;b9\&#39;d8\&#39;c1\&#39;aa\&#39;ca\&#39;fd\&#39;c1\&#39;bf\&#39;a3\&#39;ba\lang1033\kerning2\f2\fs21 FBASEJOINQTY\par
\par
\lang2052\kerning0\f0\fs20\&#39;d2\&#39;b5\&#39;ce\&#39;f1\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;a3\&#39;ba\lang1033\f1 FUnitId\par
\lang2052\f0\&#39;d2\&#39;b5\&#39;ce\&#39;f1\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;ca\&#39;fd\&#39;c1\&#39;bf\&#39;a3\&#39;ba\lang1033\f1 FQty\par
\par
\lang2052\f0\&#39;cf\&#39;fa\&#39;ca\&#39;db\&#39;b6\&#39;a9\&#39;b5\&#39;a5\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;c4\&#39;da\&#39;c2\&#39;eb\&#39;a3\&#39;ba\lang1033\f1 FOrderUnitID\par
\lang2052\f0\&#39;c9\&#39;fa\&#39;b2\&#39;fa\&#39;b6\&#39;a9\&#39;b5\&#39;a5\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;c4\&#39;da\&#39;c2\&#39;eb\&#39;a3\&#39;ba\lang1033\f1 FMOUnitID\par
\par
\lang2052\f0\&#39;bf\&#39;e2\&#39;b4\&#39;e6\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;c4\&#39;da\&#39;c2\&#39;eb\&#39;a3\&#39;ba\lang1033\f1 FStockUnitID\par
\lang2052\f0\&#39;bf\&#39;e2\&#39;b4\&#39;e6\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;ca\&#39;fd\&#39;c1\&#39;bf\&#39;a3\&#39;ba\lang1033\f1 FStockUnitQTY\par
\par
\lang2052\f0\&#39;b8\&#39;a8\&#39;d6\&#39;fa\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;c4\&#39;da\&#39;c2\&#39;eb\&#39;a3\&#39;ba\lang1033\f1 FSecUnitID\par
\lang2052\f0\&#39;b8\&#39;a8\&#39;d6\&#39;fa\&#39;b5\&#39;a5\&#39;ce\&#39;bb\&#39;ca\&#39;fd\&#39;c1\&#39;bf\&#39;a3\&#39;ba\lang1033\f1 FSecUnitQTY\par
\par
\lang2052\f0\&#39;c9\&#39;fa\&#39;b2\&#39;fa\&#39;d7\&#39;e9\&#39;d6\&#39;af:\lang1033\f1 FProductOrgId\par
\lang2052\f0\&#39;b9\&#39;a9\&#39;d3\&#39;a6\&#39;d7\&#39;e9\&#39;d6\&#39;af\&#39;a3\&#39;ba\lang1033\f1 FSupplyOrgId\par
\lang2052\f0\&#39;cd\&#39;cb\&#39;bb\&#39;f5\&#39;d7\&#39;e9\&#39;d6\&#39;af\&#39;a3\&#39;ba\lang1033\f1 FRefundOrgId\lang2052\f0\par
\&#39;b7\&#39;a2\&#39;c1\&#39;cf\&#39;d7\&#39;e9\&#39;d6\&#39;af\&#39;a3\&#39;ba\lang1033\f1 FIssueOrgId\par
\lang2052\f3\fs18\&#39;ca\&#39;fd\&#39;be\&#39;dd\&#39;d7\&#39;b4\&#39;cc\&#39;ac\tab FDOCUMENTSTATUS\par
\&#39;bd\&#39;fb\&#39;d3\&#39;c3\&#39;d7\&#39;b4\&#39;cc\&#39;ac\tab FFORBIDSTATUS\tab\par
\&#39;b4\&#39;b4\&#39;bd\&#39;a8\&#39;d7\&#39;e9\&#39;d6\&#39;af\tab FCREATEORGID\tab\par
\&#39;ca\&#39;b9\&#39;d3\&#39;c3\&#39;d7\&#39;e9\&#39;d6\&#39;af\tab FUSEORGID\tab\par
\&#39;b4\&#39;b4\&#39;bd\&#39;a8\&#39;c8\&#39;cb\tab         FCREATORID\par
\&#39;b4\&#39;b4\&#39;bd\&#39;a8\&#39;c8\&#39;d5\&#39;c6\&#39;da\tab FCREATEDATE\tab\par
\&#39;d0\&#39;de\&#39;b8\&#39;c4\&#39;c8\&#39;cb\tab         FMODIFIERID\par
\&#39;d0\&#39;de\&#39;b8\&#39;c4\&#39;c8\&#39;d5\&#39;c6\&#39;da\tab FMODIFYDATE\tab\par
\&#39;c9\&#39;f3\&#39;ba\&#39;cb\&#39;c8\&#39;cb\tab         FAPPROVERID\tab\par
\&#39;c9\&#39;f3\&#39;ba\&#39;cb\&#39;c8\&#39;d5\&#39;c6\&#39;da\tab FAPPROVEDATE\tab\par
\&#39;bd\&#39;fb\&#39;d3\&#39;c3\&#39;c8\&#39;cb\tab         FFORBIDDERID\tab\par
\&#39;bd\&#39;fb\&#39;d3\&#39;c3\&#39;c8\&#39;d5\&#39;c6\&#39;da\tab FFORBIDDATE\tab\par
}
</a:Text>
<a:CreationDate>1331628675</a:CreationDate>
<a:ModificationDate>1332232982</a:ModificationDate>
<a:Rect>((-22800,-25800), (22800,25800))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>0</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontName>新宋体,8,N</a:FontName>
</o:TextSymbol>
</c:Symbols>
</o:PhysicalDiagram>
<o:PhysicalDiagram Id="o6">
<a:ObjectID>3E597AF8-489C-471F-B2EE-7AEEB3F3DB57</a:ObjectID>
<a:Name>设计说明和注意事项！</a:Name>
<a:Code>设计说明和注意事项！</a:Code>
<a:CreationDate>1331628230</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1331628687</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:RectangleSymbol Id="o7">
<a:Text>说明：
1.表、字段全部大写；

2.表Name必须“表英文名”+“(中文名)”，如：T_RP_ARPBILL(其它应收应付单)；

3.字段命名Name使用中文，code使用英文，不要中英混合；

4.表Code必须使用英文名，需要有具体含义，长度统一使用80；

5.字段Name为字段中文名称，字段说明、枚举类型必须在comment描述；

6.短整型（smallint)，整型使用int，长整型使用bigint；

7.数值型必须使用decimal(23,10)或decimal(19,6)精度要求的数据；

8.多语言字段使用nvarchar，其它字符串字段使用varchar

9.布尔型必须使用char(1)

10.如果此字段存放的是基础资料内码，必须使用BIGINT

11.注意正常表字符串字段的长度应该与多语言表里的对应字段长度保持一致

其他注意事项：

1.引用外部系统的表或公共模块中的表必须使用PDM快照（shortcut）；

2.表需要在其他包中引用必须在其他包中建立PDM快照（shortcut）；

3.模型按子系统分为多个模型文件，每个模型分多个包(如供应链、标准财务应分2个PDM文件)；

字段类型要求(类型尽量使用SQL SERVER的类型来定义)：

整数类型分3种规格，SMALLINT，INT，BIGINT；

数值类型分2种规格，DECIMAL(23，10)，DECIMAL(19,6)不允许有其他长度定义；
与单位精度无关的数量字段请使用DECIMAL(19,6)

编码字段分3种规格，NVARCHAR(30)，NVARCHAR(80)，NVARCHAR(255)；本领域统一使用长度为80.

名称字段分3种规格，NVARCHAR(50)，NVARCHAR(100)，NVARCHAR(255)；

允许使用的类型有：
       VARCHAR,NVARCHAR，IMAGE，NTEXT,SMALLINT,INT,BIGINT,CHAR,
        DATE， 对应关系请参照数据库规范；

通用字段命名：
编码、代码统一为编码；描述统一使用NVARCHAR（255）；
字段描述	字段名称
主键		FID
编码		FNUMBER
名称		FNAME
顺序号（序号）	FSEQ
描述		FDESCRIPTION
备注		FCOMMENT
组织类型	FORGTYPE
组织单元	FORGUNIT
金额		FAMOUNT
数量		FQTY
创建人		FCREATORID
创建日期	FCREATEDDATE
物料内码	FITEMID

</a:Text>
<a:CreationDate>1331628687</a:CreationDate>
<a:ModificationDate>1347416917</a:ModificationDate>
<a:Rect>((-24068,-74678), (27066,53278))</a:Rect>
<a:TextStyle>17</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontName>Arial,12,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:RectangleSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o8">
<a:ObjectID>5E094F0E-F084-463C-A97A-C0FB23719C8D</a:ObjectID>
<a:Name>04 平台管理</a:Name>
<a:Code>CPM</a:Code>
<a:CreationDate>1331628731</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332488006</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o9">
<a:ObjectID>834D4EC4-6426-4CC8-9F06-5CA4C36FA452</a:ObjectID>
<a:Name>物理模型图</a:Name>
<a:Code>物理模型图</a:Code>
<a:CreationDate>1331628731</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332232982</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:ReferenceSymbol Id="o10">
<a:CreationDate>1333257733</a:CreationDate>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Rect>((13947,2287), (14438,11100))</a:Rect>
<a:ListOfPoints>((13947,2287),(13947,8736),(14438,8736),(14438,11100))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o11"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o12"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o13"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o14">
<a:CreationDate>1333257740</a:CreationDate>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Rect>((-1050,8175), (-600,14394))</a:Rect>
<a:ListOfPoints>((-825,8175),(-825,14394))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o15"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o16"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o17"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o18">
<a:CreationDate>1333257745</a:CreationDate>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Rect>((-17776,8625), (-17326,13053))</a:Rect>
<a:ListOfPoints>((-17478,8625),(-17478,10762),(-17624,10762),(-17624,13053))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o19"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o20"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o21"/>
</c:Object>
</o:ReferenceSymbol>
<o:TableSymbol Id="o20">
<a:CreationDate>1332830919</a:CreationDate>
<a:ModificationDate>1333266233</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-22269,11776), (-10581,17324))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o22"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o12">
<a:CreationDate>1332830921</a:CreationDate>
<a:ModificationDate>1333266233</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((6681,11099), (18369,18297))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o23"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o16">
<a:CreationDate>1332830922</a:CreationDate>
<a:ModificationDate>1333266233</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-7045,12412), (4643,18784))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o24"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o19">
<a:CreationDate>1333257069</a:CreationDate>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-22467,4463), (-10007,9186))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o25"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o11">
<a:CreationDate>1333257199</a:CreationDate>
<a:ModificationDate>1340875990</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((6595,2214), (19055,6937))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o26"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o15">
<a:CreationDate>1333257200</a:CreationDate>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-7655,4014), (4805,8737))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o27"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o28">
<a:CreationDate>1334223390</a:CreationDate>
<a:ModificationDate>1340846811</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-12935,-1772), (-7087,2227))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o29"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o9"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o29">
<a:ObjectID>2A731F26-61FD-4883-8DB3-79939ECD2C90</a:ObjectID>
<a:Name>组织类型角色表</a:Name>
<a:Code>T_CDP_ORGTYPEROLE</a:Code>
<a:CreationDate>1334223390</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334226429</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o30">
<a:ObjectID>67B6F116-C0F7-48D5-AF47-6A70072E25C1</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1334223392</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340844876</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o31">
<a:ObjectID>A4750AA4-6A94-4FF7-9A3F-71A59C09899F</a:ObjectID>
<a:Name>组织类型</a:Name>
<a:Code>FORGTYPE</a:Code>
<a:CreationDate>1334223392</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335925177</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o32">
<a:ObjectID>C0E27BAF-50C1-4EF2-BB15-E2445E0F0D5E</a:ObjectID>
<a:Name>角色</a:Name>
<a:Code>FROLE</a:Code>
<a:CreationDate>1334223392</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334226429</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o33">
<a:ObjectID>3982FC44-FE22-4FE9-AF14-6CA9C2E9EF15</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1340844872</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340846809</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o30"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o33"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o33"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o27">
<a:ObjectID>9B784CA5-DA4A-4344-8FBC-99D8B7839347</a:ObjectID>
<a:Name>平台资料多语言</a:Name>
<a:Code>T_CDP_Document_L</a:Code>
<a:CreationDate>1333257200</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o34">
<a:ObjectID>A8DF048C-4585-4CBF-A493-8F59EE077ACB</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1333257200</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o35">
<a:ObjectID>C7057F4D-1138-44F9-A6B1-425B2E95FC39</a:ObjectID>
<a:Name>平台资料内码</a:Name>
<a:Code>FDocumentID</a:Code>
<a:CreationDate>1333257200</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o36">
<a:ObjectID>C26A2CD7-D976-43C9-9A8B-C8AEB1D3C8DF</a:ObjectID>
<a:Name>地区内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1333257200</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o37">
<a:ObjectID>70954C8E-FAF3-44AF-84B5-470E0E4A4F35</a:ObjectID>
<a:Name>公告标题</a:Name>
<a:Code>FSUBJECT</a:Code>
<a:CreationDate>1333257200</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o38">
<a:ObjectID>48397055-37FD-4147-9179-EDB74D3F86AE</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1333257808</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o34"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o38"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o38"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o25">
<a:ObjectID>590FFFFD-2903-4DB1-BA1C-41F58BADA006</a:ObjectID>
<a:Name>平台公告多语言</a:Name>
<a:Code>T_CDP_NOTICE_L</a:Code>
<a:CreationDate>1333257069</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o39">
<a:ObjectID>AD3B47EA-8B04-4CE6-BC24-6CE208ACF661</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1333257073</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o40">
<a:ObjectID>3DA206E0-841E-41BE-8F97-11C2D15C6F1A</a:ObjectID>
<a:Name>平台公告内码</a:Name>
<a:Code>FNOTICEID</a:Code>
<a:CreationDate>1333257745</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o41">
<a:ObjectID>FF3737DF-C8C9-4B61-8CC1-8B0041B62C44</a:ObjectID>
<a:Name>地区内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1333257073</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o42">
<a:ObjectID>58FA6D2B-AFB1-402F-B475-B4FCC41439ED</a:ObjectID>
<a:Name>公告标题</a:Name>
<a:Code>FSUBJECT</a:Code>
<a:CreationDate>1333257073</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o43">
<a:ObjectID>086748D0-B1C5-44DD-B573-5F15ACBC5795</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1333257769</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o39"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o43"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o43"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o26">
<a:ObjectID>6A419E83-EB8D-412C-9CF1-73FAADAE7ADB</a:ObjectID>
<a:Name>平台动态多语言</a:Name>
<a:Code>T_CDP_DynamicNews_L</a:Code>
<a:CreationDate>1333257199</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o44">
<a:ObjectID>F1610A6C-77E4-4329-9889-1B3D60DB0D9A</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1333257199</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o45">
<a:ObjectID>D3F4B0AC-15A5-4D67-B5F5-05C26B47C789</a:ObjectID>
<a:Name>平台动态内码</a:Name>
<a:Code>FDynamicID</a:Code>
<a:CreationDate>1333257733</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o46">
<a:ObjectID>F05640F6-2BCF-47F4-A5D6-4F9227CDBAAC</a:ObjectID>
<a:Name>地区内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1333257199</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o47">
<a:ObjectID>B95E3D2D-EFB5-4E30-9E3F-C79EE0739BC2</a:ObjectID>
<a:Name>公告标题</a:Name>
<a:Code>FSUBJECT</a:Code>
<a:CreationDate>1333257199</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o48">
<a:ObjectID>E97B5A74-519F-46BC-9583-0B6BC4A34178</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1333257705</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o44"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o48"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o48"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o23">
<a:ObjectID>0A95CF84-2BA1-4465-ACC8-4AF628328C0C</a:ObjectID>
<a:Name>平台动态</a:Name>
<a:Code>T_CDP_DynamicNews</a:Code>
<a:CreationDate>1332830921</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o49">
<a:ObjectID>A1CFCB71-772A-49F1-A1EB-1B76CB49D5EE</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FDynamicID</a:Code>
<a:CreationDate>1332831336</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1333257281</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o50">
<a:ObjectID>FF33D7BC-C3FD-439D-9A02-96AFCBBBBB08</a:ObjectID>
<a:Name>动态类型</a:Name>
<a:Code>FDynamicType</a:Code>
<a:CreationDate>1332831336</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1335164358</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o51">
<a:ObjectID>C6A2729E-5F76-4E1F-B472-F21064D926EE</a:ObjectID>
<a:Name>产品类型</a:Name>
<a:Code>FProductType</a:Code>
<a:CreationDate>1332831336</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1335164358</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o52">
<a:ObjectID>1BCA9D46-7127-44E3-B889-F4AFDC0521B9</a:ObjectID>
<a:Name>内容</a:Name>
<a:Code>FContent</a:Code>
<a:CreationDate>1332831336</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1357722301</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(Max)</a:DataType>
</o:Column>
<o:Column Id="o53">
<a:ObjectID>ED11F5F0-C04E-4D8D-9D28-0B5E7ADB4845</a:ObjectID>
<a:Name>发布人</a:Name>
<a:Code>FPublisherID</a:Code>
<a:CreationDate>1332831336</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o54">
<a:ObjectID>A5BAD776-4B63-49B6-936E-250B46766D56</a:ObjectID>
<a:Name>发布日期</a:Name>
<a:Code>FPublishTime</a:Code>
<a:CreationDate>1332831336</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1333241444</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>EB7CED02-0093-487C-8C14-719412066B02</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FInfoStatus</a:Code>
<a:CreationDate>1335490202</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335943255</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o56">
<a:ObjectID>D9C60FFD-F39A-4755-AF9D-6F0C4EFFD407</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332831336</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o49"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o56"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o56"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o24">
<a:ObjectID>2C81043F-78F3-4793-9777-98E79D6E7336</a:ObjectID>
<a:Name>平台资料</a:Name>
<a:Code>T_CDP_Document</a:Code>
<a:CreationDate>1332830922</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o57">
<a:ObjectID>751EF9BB-CA53-4CD2-BF6E-A8CD8BC2D2C4</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FDocumentID</a:Code>
<a:CreationDate>1332831562</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1333257388</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>252ECD18-5558-41FC-AE38-C4D246345D47</a:ObjectID>
<a:Name>资料类型</a:Name>
<a:Code>FType</a:Code>
<a:CreationDate>1332831562</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1335164325</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>DFDDB014-4E62-4EA7-9266-4C4DD032A129</a:ObjectID>
<a:Name>内容</a:Name>
<a:Code>FContent</a:Code>
<a:CreationDate>1332831562</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1357722286</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(Max)</a:DataType>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>29D93C5A-F3B1-49CA-A729-F270F262BCCF</a:ObjectID>
<a:Name>发布人</a:Name>
<a:Code>FPublisherID</a:Code>
<a:CreationDate>1332831562</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>1A520A10-65CD-4B6A-8277-4B46DE6A14AE</a:ObjectID>
<a:Name>发布日期</a:Name>
<a:Code>FPublishTime</a:Code>
<a:CreationDate>1332831562</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1333241450</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>D1FAB273-2576-411B-8E22-F524C72AB4EF</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FInfoStatus</a:Code>
<a:CreationDate>1335490183</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335943249</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o63">
<a:ObjectID>1391F30E-F699-4E5C-966B-F2DD6FAB6930</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332831562</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o57"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o63"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o63"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o22">
<a:ObjectID>0B6C48D8-1B3B-44FF-B144-3E5F13B643A4</a:ObjectID>
<a:Name>平台公告</a:Name>
<a:Code>T_CDP_Notice</a:Code>
<a:CreationDate>1332830919</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o64">
<a:ObjectID>6C32F73D-2EBD-43ED-B391-4B1922DF03B3</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FNOTICEID</a:Code>
<a:CreationDate>1332830930</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1333257065</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>8E518D96-BC0A-45F8-B2CB-4A553F791B64</a:ObjectID>
<a:Name>内容</a:Name>
<a:Code>FContent</a:Code>
<a:CreationDate>1332830930</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1357722272</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(Max)</a:DataType>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>E9101167-3C64-456C-85DC-98DFAF0937BD</a:ObjectID>
<a:Name>发布人</a:Name>
<a:Code>FPublisherID</a:Code>
<a:CreationDate>1332830930</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>D9A6E77D-CEA9-40E9-A882-34D0D609F9A6</a:ObjectID>
<a:Name>发布日期</a:Name>
<a:Code>FPublishTime</a:Code>
<a:CreationDate>1332830930</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>65CB8A14-B6DC-4443-AC4B-71EC93C985AB</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FInfoStatus</a:Code>
<a:CreationDate>1335490161</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335943241</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o69">
<a:ObjectID>716D0770-2DFC-42EE-A637-50342B464C01</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332830930</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o64"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o69"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o69"/>
</c:ClusterObject>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o13">
<a:ObjectID>24719FD1-792E-4541-8301-5E281CFEEC3B</a:ObjectID>
<a:Name>Reference_1</a:Name>
<a:Code>Reference_1</a:Code>
<a:CreationDate>1333257733</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o23"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o26"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o56"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o70">
<a:ObjectID>FCC80728-13D4-4C78-A580-89F2441805E6</a:ObjectID>
<a:CreationDate>1333257733</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o49"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o45"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o17">
<a:ObjectID>609F52E1-7D9C-47E8-9AB1-C3D7D906CE00</a:ObjectID>
<a:Name>Reference_2</a:Name>
<a:Code>Reference_2</a:Code>
<a:CreationDate>1333257740</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o24"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o27"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o63"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o71">
<a:ObjectID>CE3EE23C-9F8C-4FF6-A04A-BD1577E38D4D</a:ObjectID>
<a:CreationDate>1333257740</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o57"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o35"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o21">
<a:ObjectID>A45D2E58-1389-4524-A39C-10919E4E410F</a:ObjectID>
<a:Name>Reference_3</a:Name>
<a:Code>Reference_3</a:Code>
<a:CreationDate>1333257745</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o22"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o25"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o69"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o72">
<a:ObjectID>B098D284-3723-41A3-A8F8-2B2F036138A9</a:ObjectID>
<a:CreationDate>1333257745</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o64"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o40"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
</o:Package>
<o:Package Id="o73">
<a:ObjectID>30B7D1C7-DCFB-4B0C-AC50-DE706D0331DA</a:ObjectID>
<a:Name>02 用户管理</a:Name>
<a:Code>CUM</a:Code>
<a:CreationDate>1331628867</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332487999</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o74">
<a:ObjectID>6D01CC5C-26AC-4ADA-8D85-3E69EE5551C3</a:ObjectID>
<a:Name>物理模型图</a:Name>
<a:Code>物理模型图</a:Code>
<a:CreationDate>1331628867</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332232982</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:ReferenceSymbol Id="o75">
<a:CreationDate>1336034913</a:CreationDate>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Rect>((-11212,7479), (-3862,8479))</a:Rect>
<a:ListOfPoints>((-3862,7979),(-11212,7979))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o76"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o77"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o78"/>
</c:Object>
</o:ReferenceSymbol>
<o:TableSymbol Id="o79">
<a:CreationDate>1332818496</a:CreationDate>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-21507,24618), (-8677,35524))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o80"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o77">
<a:CreationDate>1332827049</a:CreationDate>
<a:ModificationDate>1335943372</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-23025,5669), (-10179,20291))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o81"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o82">
<a:CreationDate>1332828337</a:CreationDate>
<a:ModificationDate>1335165674</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-6131,14865), (5557,22887))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o83"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o76">
<a:CreationDate>1336034870</a:CreationDate>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-5458,6349), (5458,11073))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o84"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o74"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o83">
<a:ObjectID>247B8DE2-330F-49A9-952B-D5BD94129863</a:ObjectID>
<a:Name>个人项目</a:Name>
<a:Code>T_CDP_MyProject</a:Code>
<a:CreationDate>1332828337</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1333952996</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o85">
<a:ObjectID>C0978EEB-5CE3-4ED4-8149-D65D1CA3DE74</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FId</a:Code>
<a:CreationDate>1332828353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>6AD2F683-EF1C-410B-A907-E7D85DB9C9C1</a:ObjectID>
<a:Name>用户内码</a:Name>
<a:Code>FUserId</a:Code>
<a:CreationDate>1332828353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>3DF86AE1-286D-4F1A-AD84-EA1547415320</a:ObjectID>
<a:Name>企业名称</a:Name>
<a:Code>FCompany</a:Code>
<a:CreationDate>1332828353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>4DACE8B6-B708-48DD-9AA1-AE66AE447405</a:ObjectID>
<a:Name>项目名称</a:Name>
<a:Code>FProjectName</a:Code>
<a:CreationDate>1332828353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>748C7506-A166-4E70-A245-E970A6C8C999</a:ObjectID>
<a:Name>项目描述</a:Name>
<a:Code>FRemark</a:Code>
<a:CreationDate>1332828353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>02CBC975-7073-4031-992B-56E0F0BF3631</a:ObjectID>
<a:Name>担当角色</a:Name>
<a:Code>FRole</a:Code>
<a:CreationDate>1332828353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>0383DB25-CB1C-4A4D-A2BC-9D3FDF982EAD</a:ObjectID>
<a:Name>开始日期</a:Name>
<a:Code>FStartTime</a:Code>
<a:CreationDate>1332828353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>6AEF3FA3-1D41-44FC-A5BF-56625F3DF2CB</a:ObjectID>
<a:Name>结束日期</a:Name>
<a:Code>FEndTime</a:Code>
<a:CreationDate>1332828353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o93">
<a:ObjectID>6EEC6F01-26E1-4FF7-9D48-EC60E2200671</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332830294</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o85"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o93"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o93"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o84">
<a:ObjectID>8C121281-8144-497F-87F7-EC17DCDFBC22</a:ObjectID>
<a:Name>个人资质多语言</a:Name>
<a:Code>T_CDP_Qualification_L</a:Code>
<a:CreationDate>1336034870</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o94">
<a:ObjectID>205EF6D6-6FB5-4DE5-BE5A-A4751ADAD33D</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1336034870</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o95">
<a:ObjectID>1980A819-9386-4AE2-AE4D-1412CD51BA05</a:ObjectID>
<a:Name>资质内码</a:Name>
<a:Code>FId</a:Code>
<a:CreationDate>1336034913</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>087F2EA6-DAC3-4BDB-9F6E-8F7B4D7E1166</a:ObjectID>
<a:Name>地区内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1336034870</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>BD8BD099-77C9-4F53-87B5-F989801A6FDF</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FName</a:Code>
<a:CreationDate>1336034870</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o98">
<a:ObjectID>24930EFE-3BD4-4F1B-833E-5374126ED5D9</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1336034870</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o94"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o98"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o98"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o80">
<a:ObjectID>7C740A6B-3BCC-4A9B-9A5E-19286189A5B3</a:ObjectID>
<a:Name>用户附表</a:Name>
<a:Code>T_CDP_USERS</a:Code>
<a:CreationDate>1332818496</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o99">
<a:ObjectID>19C71C8E-FB8A-4D5C-843F-4AF01BBE06FA</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FEntityID</a:Code>
<a:CreationDate>1333019110</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334891666</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>AF01D768-9D62-45B3-90C7-9D8D6A303397</a:ObjectID>
<a:Name>用户内码</a:Name>
<a:Code>FUserId</a:Code>
<a:CreationDate>1332818505</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334891666</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>22D9E4DB-DDBA-4EF4-85B3-1C0A8181BE49</a:ObjectID>
<a:Name>性别</a:Name>
<a:Code>FSex</a:Code>
<a:CreationDate>1332818505</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>38152F15-6941-453E-94C3-8B362FE32B0B</a:ObjectID>
<a:Name>身份证</a:Name>
<a:Code>FSFZ</a:Code>
<a:CreationDate>1332818505</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>DB9AAD87-FD90-4FB1-A92F-147966845E48</a:ObjectID>
<a:Name>手机</a:Name>
<a:Code>FMobile</a:Code>
<a:CreationDate>1332818505</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o104">
<a:ObjectID>CF1A69C0-9AAE-47B3-BEE8-EA7EEC3B0D92</a:ObjectID>
<a:Name>邮箱</a:Name>
<a:Code>FMail</a:Code>
<a:CreationDate>1332818505</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>9CBDEA82-F131-439C-AFF4-211C51F5A690</a:ObjectID>
<a:Name>办公电话</a:Name>
<a:Code>FPhone</a:Code>
<a:CreationDate>1332818505</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>7B7E5328-DAC5-463C-A452-34290187A6B4</a:ObjectID>
<a:Name>参加工作时间</a:Name>
<a:Code>FStartWorkTime</a:Code>
<a:CreationDate>1332818505</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1336383160</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>429A4166-C4BD-4BA0-9B86-642EF3AE4803</a:ObjectID>
<a:Name>最高学历</a:Name>
<a:Code>FEducation</a:Code>
<a:CreationDate>1332819058</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334891643</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>DBCF68B0-7C75-4A99-B610-89968352587E</a:ObjectID>
<a:Name>QQ</a:Name>
<a:Code>FQQ</a:Code>
<a:CreationDate>1333005870</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333006315</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>E0426366-47B1-4EE3-A916-429573575AEE</a:ObjectID>
<a:Name>工作经验</a:Name>
<a:Code>FExperience</a:Code>
<a:CreationDate>1347415769</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o110">
<a:ObjectID>E5B9014A-1C63-4AE3-908B-D32B4CA69EFA</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332819121</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o99"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o110"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o110"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o81">
<a:ObjectID>D5D0C0FC-EAF0-4167-9FC8-491956DB8A7E</a:ObjectID>
<a:Name>个人资质</a:Name>
<a:Code>T_CDP_Qualification</a:Code>
<a:CreationDate>1332827049</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o111">
<a:ObjectID>967D8417-E7A0-4B62-895E-F074EAB6496B</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FId</a:Code>
<a:CreationDate>1332828039</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>A37587A0-D24D-4764-9CE6-1CA449D5896C</a:ObjectID>
<a:Name>申请人内码</a:Name>
<a:Code>FApplicantId</a:Code>
<a:CreationDate>1332828063</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>C9AE12D8-8235-4F3C-9C79-43601A883C99</a:ObjectID>
<a:Name>认证类型</a:Name>
<a:Code>FQUALIFICATIONTYPE</a:Code>
<a:CreationDate>1347415634</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>5EB2BC33-F094-49A4-B542-48EE8881FE07</a:ObjectID>
<a:Name>产品类型</a:Name>
<a:Code>FProductTYPE</a:Code>
<a:CreationDate>1335165346</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>89695388-9AA9-4E10-A209-BB076239556F</a:ObjectID>
<a:Name>领域</a:Name>
<a:Code>FMODULE</a:Code>
<a:CreationDate>1347415634</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>FB53D145-9F71-4028-9D13-B29D16C75096</a:ObjectID>
<a:Name>实施能力</a:Name>
<a:Code>FCAPABILITY</a:Code>
<a:CreationDate>1357722338</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>D1EB4549-E006-4228-8A42-1C6206D0E662</a:ObjectID>
<a:Name>资质类型</a:Name>
<a:Code>FType</a:Code>
<a:CreationDate>1332828063</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>DFAE9C92-7DF9-4018-9050-12BDEA766A43</a:ObjectID>
<a:Name>申请日期</a:Name>
<a:Code>FApplyTime</a:Code>
<a:CreationDate>1332828063</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>E091572C-3A7A-453A-8305-501CDDD430BF</a:ObjectID>
<a:Name>申请说明</a:Name>
<a:Code>FApplyRemark</a:Code>
<a:CreationDate>1332828063</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>8AA488A9-9403-43A0-99CA-0119DDA8F835</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1332828063</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>8DAAEC36-5612-40A5-826E-E65BDD38BD33</a:ObjectID>
<a:Name>认证分数</a:Name>
<a:Code>FScore</a:Code>
<a:CreationDate>1333005918</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1342601180</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>decimal(10,2)</a:DataType>
<a:Length>10</a:Length>
<a:Precision>2</a:Precision>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>9556AE4C-0769-4159-A65D-50DB8E21081B</a:ObjectID>
<a:Name>资质证书编号</a:Name>
<a:Code>FQualificationNo</a:Code>
<a:CreationDate>1333005918</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333006315</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>12B4D738-85A1-45B4-8265-53B012582D5B</a:ObjectID>
<a:Name>生效日期</a:Name>
<a:Code>FEffectiveTime</a:Code>
<a:CreationDate>1333005918</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333006315</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>4065C052-D7CB-450B-9AED-A51FDF630DA1</a:ObjectID>
<a:Name>失效日期</a:Name>
<a:Code>FExpirationTime</a:Code>
<a:CreationDate>1333005918</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333006315</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>615956AD-4542-42C8-BEF8-CD56D01496FD</a:ObjectID>
<a:Name>审批人内码</a:Name>
<a:Code>FApproverId</a:Code>
<a:CreationDate>1332828063</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>7F8E3BC6-9F2B-46D1-A825-263E4D3F685C</a:ObjectID>
<a:Name>审批日期</a:Name>
<a:Code>FApproveTime</a:Code>
<a:CreationDate>1332828063</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>9534F885-9799-4B7E-B24E-4000091B92C7</a:ObjectID>
<a:Name>审批说明</a:Name>
<a:Code>FApproveRemark</a:Code>
<a:CreationDate>1332828063</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>D0AEFAEA-98D3-4B77-8243-D585392865DA</a:ObjectID>
<a:Name>申请企业</a:Name>
<a:Code>FAPPLYORGID</a:Code>
<a:CreationDate>1335165346</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o129">
<a:ObjectID>7B5B3442-39EC-42B7-B9AE-93CF83C383D6</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FInfoStatus</a:Code>
<a:CreationDate>1335943300</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335943370</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o130">
<a:ObjectID>F633A1E8-94FF-4F2C-A6E3-B4CE8617F8F8</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332830289</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o111"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o130"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o130"/>
</c:ClusterObject>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o78">
<a:ObjectID>4A72CF3D-10D8-44BC-8286-95BCFD75FFAF</a:ObjectID>
<a:Name>Reference_8</a:Name>
<a:Code>Reference_8</a:Code>
<a:CreationDate>1336034913</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o81"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o84"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o130"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o131">
<a:ObjectID>9F7CDA2F-BB78-494E-8E7B-0E2696F38571</a:ObjectID>
<a:CreationDate>1336034913</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o111"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o95"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
</o:Package>
<o:Package Id="o132">
<a:ObjectID>C9B663F2-573C-49B4-98F1-63024EE703A9</a:ObjectID>
<a:Name>01 基础框架</a:Name>
<a:Code>CAF</a:Code>
<a:CreationDate>1331628803</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332487903</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o133">
<a:ObjectID>8355FAA6-7912-4B0C-B5AE-65C645E2FCA6</a:ObjectID>
<a:Name>物理模型图</a:Name>
<a:Code>物理模型图</a:Code>
<a:CreationDate>1331628803</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332232982</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:ReferenceSymbol Id="o134">
<a:CreationDate>1335165162</a:CreationDate>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Rect>((-3071,4501), (-2621,10500))</a:Rect>
<a:ListOfPoints>((-2846,4501),(-2846,10500))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o135"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o136"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o137"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o138">
<a:CreationDate>1335165210</a:CreationDate>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Rect>((15021,2500), (15471,11700))</a:Rect>
<a:ListOfPoints>((15246,2500),(15246,11700))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o139"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o140"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o141"/>
</c:Object>
</o:ReferenceSymbol>
<o:TableSymbol Id="o142">
<a:CreationDate>1333951736</a:CreationDate>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-4018,17288), (8442,22011))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o143"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o144">
<a:CreationDate>1333951965</a:CreationDate>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-17233,17739), (-6317,22462))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o145"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o146">
<a:CreationDate>1334300108</a:CreationDate>
<a:ModificationDate>1335165674</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-20308,7313), (-9392,13687))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o147"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o136">
<a:CreationDate>1335164713</a:CreationDate>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-6342,9563), (6118,14286))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o148"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o140">
<a:CreationDate>1335164957</a:CreationDate>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((7270,9453), (19730,15001))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o149"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o135">
<a:CreationDate>1335165141</a:CreationDate>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-9530,415), (2930,5139))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o150"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o139">
<a:CreationDate>1335165143</a:CreationDate>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((9542,-1010), (20458,3714))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o151"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o133"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o148">
<a:ObjectID>CCF37E6F-5E47-4110-9CC6-B3BF52C38D7A</a:ObjectID>
<a:Name>菜单内容</a:Name>
<a:Code>T_CDP_MenuContent</a:Code>
<a:CreationDate>1335164713</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o152">
<a:ObjectID>AF0E8434-E5F7-4C63-8E89-2F1A4B5C9E6C</a:ObjectID>
<a:Name>菜单内容内码</a:Name>
<a:Code>FMenuContentID</a:Code>
<a:CreationDate>1335164847</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>385100C5-D6C8-4E09-9010-E27E6B86951B</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1335164847</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o154">
<a:ObjectID>25AED798-DEA4-49F1-88DA-B6E873B70ED5</a:ObjectID>
<a:Name>参数</a:Name>
<a:Code>FPARAM</a:Code>
<a:CreationDate>1335164847</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>952DE99B-AA2F-42D0-ABCA-CCFC1A849D6D</a:ObjectID>
<a:Name>顺序</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1335164847</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o156">
<a:ObjectID>66417F5B-C201-4F8C-9106-8D027B108A32</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1335165175</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o152"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o156"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o156"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o147">
<a:ObjectID>2B12BC69-1E3A-45CF-8089-1E12C7766137</a:ObjectID>
<a:Name>工作桌面</a:Name>
<a:Code>T_CDP_Workspace</a:Code>
<a:CreationDate>1334212089</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334301576</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o157">
<a:ObjectID>B36178DB-56AF-47D1-BF2B-5640CDA64C71</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FWorkspaceID</a:Code>
<a:CreationDate>1334212092</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334565640</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>56FF11A6-1B3C-4B9D-9381-57CEEA691B5E</a:ObjectID>
<a:Name>组织编码</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1334212092</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334226429</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>58E0D313-0667-4567-AFBF-08CD13011D65</a:ObjectID>
<a:Name>角色</a:Name>
<a:Code>FROLE</a:Code>
<a:CreationDate>1334212092</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334226429</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>0D4FE620-BF0D-46A7-95AF-59A63BD39168</a:ObjectID>
<a:Name>认证类型</a:Name>
<a:Code>FType</a:Code>
<a:CreationDate>1334212092</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165641</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>B7D13662-B55B-42EF-ADBB-5316E3FE7709</a:ObjectID>
<a:Name>工作桌面</a:Name>
<a:Code>FWorkspace</a:Code>
<a:CreationDate>1334213500</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334226429</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>2DEA01EA-89A4-4CBF-932B-67E5A8FCF87A</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>Fdescription</a:Code>
<a:CreationDate>1334213500</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334226429</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>A4B3D11D-44BA-4553-9162-180DC43B54ED</a:ObjectID>
<a:Name>方法</a:Name>
<a:Code>FMethod</a:Code>
<a:CreationDate>1335165588</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o164">
<a:ObjectID>346DD4A3-BB1F-4F65-A62A-D66085D8AF3A</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1334212193</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334226429</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o157"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o164"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o164"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o149">
<a:ObjectID>4055236A-66C5-474F-88A5-5C8E7C82B4E1</a:ObjectID>
<a:Name>菜单明细内容</a:Name>
<a:Code>T_CDP_MenuDetailContent</a:Code>
<a:CreationDate>1335164957</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o165">
<a:ObjectID>6A8FA942-D2FD-44E0-AC3C-F4D2A326A23F</a:ObjectID>
<a:Name>明细内码</a:Name>
<a:Code>FDetailContentID</a:Code>
<a:CreationDate>1335164957</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o166">
<a:ObjectID>9116BE7F-BEEF-42DF-A7A9-CD905BBD46CC</a:ObjectID>
<a:Name>菜单内容内码</a:Name>
<a:Code>FMenuContentID</a:Code>
<a:CreationDate>1335165047</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o167">
<a:ObjectID>E2C16F36-6C69-4675-8356-EB12657C07E7</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1335164957</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>1971F73C-06F2-432A-9685-1817AD2F2CCD</a:ObjectID>
<a:Name>参数</a:Name>
<a:Code>FPARAM</a:Code>
<a:CreationDate>1335164957</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335231988</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>B3E08FB2-8462-4BA4-81D3-4EF2859B5F2D</a:ObjectID>
<a:Name>顺序</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1335164957</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o170">
<a:ObjectID>8E7752CA-530B-49D4-94F6-C581527B9044</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1335165168</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o165"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o170"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o170"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o150">
<a:ObjectID>3F4BE8ED-5A32-4A2C-AC9F-143035BBA367</a:ObjectID>
<a:Name>菜单内容多语言</a:Name>
<a:Code>T_CDP_MenuContent_L</a:Code>
<a:CreationDate>1335165141</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335489501</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o171">
<a:ObjectID>9C0C2C3A-01F7-460D-96EF-24E7A9208AD6</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1335165141</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335497830</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o172">
<a:ObjectID>FFEA28F2-0784-4E2C-B525-2EFA75AAF8F8</a:ObjectID>
<a:Name>菜单内容内码</a:Name>
<a:Code>FMenuContentID</a:Code>
<a:CreationDate>1335165175</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o173">
<a:ObjectID>4F76DF98-BD9A-4333-B762-12981B84C685</a:ObjectID>
<a:Name>地区内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1335165141</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o174">
<a:ObjectID>C129A557-C9B9-4F4F-B70C-774D29CA5DB0</a:ObjectID>
<a:Name>名字</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1335165141</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o175">
<a:ObjectID>739F76F7-C0B3-4E99-A81A-D7CB8174ACEC</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1335165141</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o171"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o175"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o175"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o151">
<a:ObjectID>2F34E708-51DC-44F3-B2FD-037AB93A9FAD</a:ObjectID>
<a:Name>菜单明细内容多语言</a:Name>
<a:Code>T_CDP_MenuDetailContent_L</a:Code>
<a:CreationDate>1335165143</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335489518</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o176">
<a:ObjectID>B191208C-04DB-446D-A1CD-F72215ABFBF0</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1335165143</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335497838</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>249A7B85-465C-4D07-B694-F87D23C7B407</a:ObjectID>
<a:Name>明细内码</a:Name>
<a:Code>FDetailContentID</a:Code>
<a:CreationDate>1335165210</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>1B678CD0-599D-4E75-96C4-3CB7484C2D82</a:ObjectID>
<a:Name>地区内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1335165143</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>32746237-DE53-4384-B176-F4CE46370419</a:ObjectID>
<a:Name>名字</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1335165143</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o180">
<a:ObjectID>A505697B-EB70-4423-9357-4C899C9D4361</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1335165143</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o176"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o180"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o180"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o145">
<a:ObjectID>EBACF3E6-B383-424D-9858-0B2B4D8246F7</a:ObjectID>
<a:Name>CDP菜单</a:Name>
<a:Code>T_CDP_MENU</a:Code>
<a:CreationDate>1333951965</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o181">
<a:ObjectID>ADD8C95C-5018-4C33-8653-DDEF2505E923</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FMENUID</a:Code>
<a:CreationDate>1333951965</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334565743</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>74473188-DCC5-41D0-BBD0-************</a:ObjectID>
<a:Name>工作桌面内码</a:Name>
<a:Code>FWorkspaceID</a:Code>
<a:CreationDate>1333951965</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334565743</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>6621CF22-4F01-4F84-885D-33E53BA21680</a:ObjectID>
<a:Name>菜单内码</a:Name>
<a:Code>FMenuContentID</a:Code>
<a:CreationDate>1333951965</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335172602</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>22D35724-2BFF-4A0F-9D54-AD6BAA5E4914</a:ObjectID>
<a:Name>可见性</a:Name>
<a:Code>FVISIBLE</a:Code>
<a:CreationDate>1333951965</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1361518754</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o185">
<a:ObjectID>0F31929E-122A-4D2E-A4BF-4C720C9FCFD9</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1333951965</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o181"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o185"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o185"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o143">
<a:ObjectID>BC7ED114-06F6-45D3-8E89-995594E7A91F</a:ObjectID>
<a:Name>CDP菜单明细</a:Name>
<a:Code>T_CDP_MENUDETAIL</a:Code>
<a:CreationDate>1333951736</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o186">
<a:ObjectID>2F654B32-0DCE-4A7D-8D21-223A65DE45EA</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FMenuDetailID</a:Code>
<a:CreationDate>1333951755</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334565886</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>77A44FB3-DAFE-4353-89D9-A45F9DA605F0</a:ObjectID>
<a:Name>菜单内码</a:Name>
<a:Code>FMENUID</a:Code>
<a:CreationDate>1334565770</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334565925</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o188">
<a:ObjectID>A9256B17-20D4-476A-AAB8-821B3C361D11</a:ObjectID>
<a:Name>菜单明细内码</a:Name>
<a:Code>FDetailContentID</a:Code>
<a:CreationDate>1333951755</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335172732</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o189">
<a:ObjectID>47EC52FA-B76B-411A-91A1-D29817D2640D</a:ObjectID>
<a:Name>可见性</a:Name>
<a:Code>FVISIBLE</a:Code>
<a:CreationDate>1333951755</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1361518762</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o190">
<a:ObjectID>14B6F20F-B6F8-4865-AE68-077568E9C9E0</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1333951755</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o186"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o190"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o190"/>
</c:ClusterObject>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o137">
<a:ObjectID>4DD7E694-9570-4122-9AC6-50E8AC86C24E</a:ObjectID>
<a:Name>Reference_6</a:Name>
<a:Code>Reference_6</a:Code>
<a:CreationDate>1335165162</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o148"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o150"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o156"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o191">
<a:ObjectID>B8AF4600-AEC0-4BBC-81BC-61D24FECCEBD</a:ObjectID>
<a:CreationDate>1335165175</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o152"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o172"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o141">
<a:ObjectID>F120D647-B318-4F36-AED4-6856ACFCCE7C</a:ObjectID>
<a:Name>Reference_7</a:Name>
<a:Code>Reference_7</a:Code>
<a:CreationDate>1335165210</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o149"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o151"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o170"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o192">
<a:ObjectID>88758A78-A294-4F9A-B44A-AB1536543311</a:ObjectID>
<a:CreationDate>1335165210</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o165"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o177"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
</o:Package>
<o:Package Id="o193">
<a:ObjectID>2F86DD4B-F2F0-4B9C-A6C8-C2E4307C0A16</a:ObjectID>
<a:Name>03 企业管理</a:Name>
<a:Code>CEM</a:Code>
<a:CreationDate>1331628846</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332487964</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o194">
<a:ObjectID>FEE117FB-7B4A-4864-B15A-0F72E5C58AEC</a:ObjectID>
<a:Name>物理模型图</a:Name>
<a:Code>物理模型图</a:Code>
<a:CreationDate>1331628846</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1332232982</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:ReferenceSymbol Id="o195">
<a:CreationDate>1333260256</a:CreationDate>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Rect>((-12069,31154), (-1879,32497))</a:Rect>
<a:ListOfPoints>((-1879,32497),(-6097,32497),(-6097,31154),(-12069,31154))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o196"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o197"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o198"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o199">
<a:CreationDate>1336035031</a:CreationDate>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Rect>((-17588,375), (-1838,4771))</a:Rect>
<a:ListOfPoints>((-1838,375),(-1838,4771),(-17588,4771))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o200"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o201"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o202"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o203">
<a:CreationDate>1347591621</a:CreationDate>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Rect>((18929,7423), (19929,13198))</a:Rect>
<a:ListOfPoints>((19429,7423),(19429,13198))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o204"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o205"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o206"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o207">
<a:CreationDate>1347591899</a:CreationDate>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Rect>((18802,-3746), (19802,-601))</a:Rect>
<a:ListOfPoints>((19302,-3746),(19302,-601))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o208"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o209"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o210"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o211">
<a:CreationDate>1357723075</a:CreationDate>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Rect>((-11062,14181), (-5662,17544))</a:Rect>
<a:ListOfPoints>((-5662,17544),(-5662,14181),(-11062,14181))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o212"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o201"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o213"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o214">
<a:CreationDate>1357723089</a:CreationDate>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Rect>((-10537,11414), (-262,11864))</a:Rect>
<a:ListOfPoints>((-262,11639),(-10537,11639))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o215"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o201"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o216"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o217">
<a:CreationDate>1357723098</a:CreationDate>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Rect>((-9712,6275), (-187,6725))</a:Rect>
<a:ListOfPoints>((-187,6500),(-9712,6500))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o218"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o201"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o219"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o220">
<a:CreationDate>1357723199</a:CreationDate>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Rect>((7313,18836), (14213,19286))</a:Rect>
<a:ListOfPoints>((14213,19061),(7313,19061))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o221"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o212"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o222"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o223">
<a:CreationDate>1357723310</a:CreationDate>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Rect>((7088,11125), (16538,12162))</a:Rect>
<a:ListOfPoints>((7088,11125),(11233,11125),(11233,12162),(16538,12162))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o215"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o205"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o224"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o225">
<a:CreationDate>1357723317</a:CreationDate>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Rect>((6938,612), (13688,5442))</a:Rect>
<a:ListOfPoints>((6938,5442),(6938,612),(13688,612))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o218"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o209"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o226"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o227">
<a:CreationDate>1357779685</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Rect>((563,-36619), (8273,-36169))</a:Rect>
<a:ListOfPoints>((8273,-36394),(563,-36394))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o228"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o229"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o230"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o231">
<a:CreationDate>1357779690</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Rect>((563,-41487), (7826,-41037))</a:Rect>
<a:ListOfPoints>((7826,-41262),(563,-41262))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o232"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o229"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o233"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o234">
<a:CreationDate>1357779691</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Rect>((-787,-50289), (6188,-49839))</a:Rect>
<a:ListOfPoints>((6188,-50064),(-787,-50064))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o235"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o229"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o236"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o237">
<a:CreationDate>1357784027</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Rect>((-12487,-71119), (1609,-64958))</a:Rect>
<a:ListOfPoints>((1609,-71119),(-7293,-71119),(-7293,-64958),(-12487,-64958))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>12615680</a:LineColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>CENTER 0 新宋体,8,N
SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o238"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o239"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o240"/>
</c:Object>
</o:ReferenceSymbol>
<o:TableSymbol Id="o197">
<a:CreationDate>1332819185</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-22121,26546), (-10047,32920))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o241"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o242">
<a:CreationDate>1332828806</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-24070,-32776), (-11222,-23196))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o243"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o244">
<a:CreationDate>1332829136</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((7784,-30068), (20630,-21220))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o245"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o201">
<a:CreationDate>1332830352</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-23565,1454), (-9173,16076))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o246"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o196">
<a:CreationDate>1333260238</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-4398,29777), (8448,36975))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o247"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o248">
<a:CreationDate>1333953004</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((11144,31002), (22832,35001))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o249"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o250">
<a:CreationDate>1335489815</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-23809,-21337), (-12713,-14965))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o251"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o200">
<a:CreationDate>1336035002</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-7858,-4312), (3058,412))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o252"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o253">
<a:CreationDate>1338971980</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((12443,24776), (19883,28775))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o254"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o255">
<a:CreationDate>1339399553</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((12107,-18378), (24567,-9530))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o256"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o257">
<a:CreationDate>1339815453</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-24171,-13686), (-13255,-8963))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o258"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o259">
<a:CreationDate>1339815453</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-9705,-14284), (1983,-6262))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o260"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o261">
<a:CreationDate>1340011141</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-9193,-30112), (3267,-25389))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o262"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o263">
<a:CreationDate>1340270709</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-9034,-23436), (5358,-17064))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o264"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o265">
<a:CreationDate>1340786538</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-3492,22949), (8968,28497))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o266"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o212">
<a:CreationDate>1347416505</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-5926,14530), (8852,20078))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o267"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o221">
<a:CreationDate>1347416506</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((12985,17523), (26991,22246))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o268"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o215">
<a:CreationDate>1347528040</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-661,9203), (7937,13202))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o269"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o218">
<a:CreationDate>1347528040</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-1037,4028), (7561,8027))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o270"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o205">
<a:CreationDate>1347591314</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((14080,11062), (24996,15785))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o271"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o204">
<a:CreationDate>1347591314</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((14654,4091), (25570,8814))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o272"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o209">
<a:CreationDate>1347591315</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((13179,-1913), (24095,2810))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o273"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o208">
<a:CreationDate>1347591315</a:CreationDate>
<a:ModificationDate>1357724679</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((13394,-8434), (25082,-3711))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o274"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o275">
<a:CreationDate>1357723624</a:CreationDate>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-25028,-48848), (-11796,-39176))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o276"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o229">
<a:CreationDate>1357723998</a:CreationDate>
<a:ModificationDate>1357784508</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-10253,-59910), (2979,-32914))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o277"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o228">
<a:CreationDate>1357779613</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((7096,-38911), (16080,-34912))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o278"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o232">
<a:CreationDate>1357779614</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((6421,-44011), (15405,-40012))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o279"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o235">
<a:CreationDate>1357779615</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((5014,-55174), (17860,-45502))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o280"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o239">
<a:CreationDate>1357782418</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-25726,-83932), (-10948,-57760))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o281"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o238">
<a:CreationDate>1357782419</a:CreationDate>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-3638,-79021), (9788,-63121))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o282"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o194"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o243">
<a:ObjectID>909E6B5A-6F64-4E59-9223-6C8170A5AF44</a:ObjectID>
<a:Name>企业申请历史</a:Name>
<a:Code>T_CDP_HISTORYAPPLY</a:Code>
<a:CreationDate>1332828806</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o283">
<a:ObjectID>39351365-E15F-41B0-B730-42419A85EC66</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FId</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o284">
<a:ObjectID>908BD85A-89BC-4ED4-9C36-A85D8CF031A1</a:ObjectID>
<a:Name>用户内码</a:Name>
<a:Code>FApplicantId</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334041411</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o285">
<a:ObjectID>2C40C635-2F70-4E0A-A5D2-482A352C3311</a:ObjectID>
<a:Name>申请日期</a:Name>
<a:Code>FApplyTime</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o286">
<a:ObjectID>9C1AD0A4-556D-4D65-AEB7-276A24CCB5A5</a:ObjectID>
<a:Name>申请企业</a:Name>
<a:Code>FCompany</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o287">
<a:ObjectID>C087D445-FD2F-486D-A327-E789145470B2</a:ObjectID>
<a:Name>申请说明</a:Name>
<a:Code>FApplyRemark</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o288">
<a:ObjectID>CB4EE904-44CF-4D12-A5A1-2492546333C7</a:ObjectID>
<a:Name>审批人</a:Name>
<a:Code>FApproverId</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o289">
<a:ObjectID>EA981ED5-C045-4E5F-8065-25F9C59C6D16</a:ObjectID>
<a:Name>审批日期</a:Name>
<a:Code>FApproveTime</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o290">
<a:ObjectID>94047932-BB75-4072-8C4C-CADF69CCEE6F</a:ObjectID>
<a:Name>审批说明</a:Name>
<a:Code>FApproveRemark</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o291">
<a:ObjectID>3D9E5B50-7189-4988-9620-C55FD2C4F352</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1332828936</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o292">
<a:ObjectID>600B1832-F1DC-4004-BE55-255A35497351</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FInfoStatus</a:Code>
<a:CreationDate>1335490089</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335943271</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o293">
<a:ObjectID>59438D05-CF71-4743-BE1D-59398615D275</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332830276</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o283"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o293"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o293"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o245">
<a:ObjectID>81C3E670-1E7A-4DB4-A26F-2D355252209E</a:ObjectID>
<a:Name>企业邀请历史</a:Name>
<a:Code>T_CDP_HISTORYINVITE</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o294">
<a:ObjectID>0549D91A-6BBF-4814-841C-40E16A20E6D3</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FId</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o295">
<a:ObjectID>810369FB-02DB-452C-8B3F-7796D8904DA6</a:ObjectID>
<a:Name>被邀请人内码</a:Name>
<a:Code>FApplicantId</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334041376</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o296">
<a:ObjectID>505D0980-493D-4F9E-8634-A8BCFD26532E</a:ObjectID>
<a:Name>邀请日期</a:Name>
<a:Code>FInviteTime</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334045036</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o297">
<a:ObjectID>B0582290-F75F-46BE-9AF0-6E2BE6F08FE4</a:ObjectID>
<a:Name>邀请企业</a:Name>
<a:Code>FCompany</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o298">
<a:ObjectID>93801BCD-155D-4F90-B4BE-E585C551AE69</a:ObjectID>
<a:Name>邀请说明</a:Name>
<a:Code>FInviteRemark</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334045036</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o299">
<a:ObjectID>0391EC01-D046-4C7D-B1C9-F48064C3715F</a:ObjectID>
<a:Name>邀请人</a:Name>
<a:Code>FInviterID</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334045036</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o300">
<a:ObjectID>B6FFB945-1D14-4D46-9280-7115316D0B1E</a:ObjectID>
<a:Name>接受日期</a:Name>
<a:Code>FApproveTime</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o301">
<a:ObjectID>FE9A6E9E-10ED-4E24-8CE2-72BF0D75E340</a:ObjectID>
<a:Name>接受说明</a:Name>
<a:Code>FApproveRemark</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o302">
<a:ObjectID>C74E8CF0-534E-42FE-B474-B45EBEFC33D1</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1332829136</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o303">
<a:ObjectID>365330C1-64F8-4B86-8485-856FD4C18A3A</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FInfoStatus</a:Code>
<a:CreationDate>1335490065</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335943278</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o304">
<a:ObjectID>C7D8CBDB-CD10-4600-9005-C6CAEC90FA51</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332830273</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o294"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o304"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o304"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o252">
<a:ObjectID>567588CA-C2C2-4F95-8303-5AB444F32030</a:ObjectID>
<a:Name>企业资质多语言</a:Name>
<a:Code>T_CDP_HISTORYAUTHORIZE_L</a:Code>
<a:CreationDate>1336035002</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o305">
<a:ObjectID>74E6168C-9271-477A-B919-22DC9B58BA24</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1336035002</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o306">
<a:ObjectID>9149BEB7-C3D3-4558-BBBB-3DD2205F9CF7</a:ObjectID>
<a:Name>资质内码</a:Name>
<a:Code>FId</a:Code>
<a:CreationDate>1336035002</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o307">
<a:ObjectID>A78384DA-CFAE-40A7-A516-487F94884F5C</a:ObjectID>
<a:Name>地区内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1336035002</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o308">
<a:ObjectID>CF19373B-13E4-4D44-9243-D884BA0285F2</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FName</a:Code>
<a:CreationDate>1336035002</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o309">
<a:ObjectID>46B55676-CEC4-465E-A28C-E89B40402371</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1336035002</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o305"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o309"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o309"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o254">
<a:ObjectID>7C14438B-C7FC-4E9A-BEB1-76F07D0A0E1E</a:ObjectID>
<a:Name>项目用户角色</a:Name>
<a:Code>T_CDP_USERROLE</a:Code>
<a:CreationDate>1338971980</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o310">
<a:ObjectID>25E73A59-D81A-4F52-92D6-E0EDCEED7908</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FDetailID</a:Code>
<a:CreationDate>1338971984</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o311">
<a:ObjectID>F5ABDEFE-6B36-4B72-85A1-79E15F911217</a:ObjectID>
<a:Name>角色内码</a:Name>
<a:Code>FENTITYID</a:Code>
<a:CreationDate>1338971984</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o312">
<a:ObjectID>7A7F66EA-1D6A-4A4A-B0FA-1C994ED94BDD</a:ObjectID>
<a:Name>用户内码</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1338971984</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o313">
<a:ObjectID>E76029A3-80D2-4A75-BFD9-7F3F163BE301</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1338971984</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o310"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o313"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o313"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o262">
<a:ObjectID>BBED6F62-1727-48A5-A82D-AE0DC1674063</a:ObjectID>
<a:Name>企业虚机数量控制</a:Name>
<a:Code>T_CDP_VMControl</a:Code>
<a:CreationDate>1340011141</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340011280</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o314">
<a:ObjectID>55B8FFF9-EFC3-4B02-84DD-634BB67A0924</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1340011144</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340011280</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o315">
<a:ObjectID>971FEA68-5E84-4BB7-BD37-3750E551B642</a:ObjectID>
<a:Name>企业等级</a:Name>
<a:Code>FRank</a:Code>
<a:CreationDate>1340011144</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340011280</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o316">
<a:ObjectID>35B2E1E4-E128-4D89-964D-79B809A4686F</a:ObjectID>
<a:Name>开发项目数量</a:Name>
<a:Code>FDevProjectNumber</a:Code>
<a:CreationDate>1340011144</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340011280</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o317">
<a:ObjectID>9B7F2BF6-37EE-4443-92AA-A5CA2E0274A0</a:ObjectID>
<a:Name>测试虚机数量</a:Name>
<a:Code>FTestVmNumber</a:Code>
<a:CreationDate>1340011144</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340011280</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o318">
<a:ObjectID>19D918BF-4146-4C75-B9F5-8578F51524D5</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1340011144</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340011280</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o314"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o318"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o318"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o266">
<a:ObjectID>F095D3A2-8875-406B-A976-F3E76AC9346E</a:ObjectID>
<a:Name>项目开发</a:Name>
<a:Code>T_CDP_DEVPROJECT</a:Code>
<a:CreationDate>1340786538</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340787284</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o319">
<a:ObjectID>B2E6C496-E744-4EBE-B1B1-9B7F9B29A0C6</a:ObjectID>
<a:Name>项目内码</a:Name>
<a:Code>FOrgId</a:Code>
<a:CreationDate>1340786540</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340787284</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o320">
<a:ObjectID>76691A5D-622E-46E7-893E-9645AE1E3864</a:ObjectID>
<a:Name>构建包名称</a:Name>
<a:Code>FPackageName</a:Code>
<a:CreationDate>1340786540</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340787284</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o321">
<a:ObjectID>71CD6D51-284A-46C9-8240-AC60D568DF03</a:ObjectID>
<a:Name>构建包主版本</a:Name>
<a:Code>FVersionMajor</a:Code>
<a:CreationDate>1340786540</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340787284</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o322">
<a:ObjectID>9CA18596-EBD9-47B5-8349-A7CF0A4E7319</a:ObjectID>
<a:Name>构建包子版本</a:Name>
<a:Code>FVersionMinor</a:Code>
<a:CreationDate>1340786540</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340787284</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o323">
<a:ObjectID>90D25117-395B-41EB-B803-EC34568486A5</a:ObjectID>
<a:Name>开发虚拟机Id</a:Name>
<a:Code>FVMId</a:Code>
<a:CreationDate>1340786540</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340787284</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o324">
<a:ObjectID>FA9DF12E-6A33-44F7-BC70-8AEB55FABBD1</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1340786729</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340787284</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o319"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o324"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o324"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o249">
<a:ObjectID>A8477336-927B-4893-80C7-08FC9B85E63B</a:ObjectID>
<a:Name>项目附表</a:Name>
<a:Code>T_CDP_PROJECT</a:Code>
<a:CreationDate>1333953004</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o325">
<a:ObjectID>7002B83A-F0C0-473D-B259-E9EEF6214E1C</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FEntryId</a:Code>
<a:CreationDate>1333953017</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338971851</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o326">
<a:ObjectID>02A25B43-3EB8-40BD-8026-946B9E212EB6</a:ObjectID>
<a:Name>项目内码</a:Name>
<a:Code>FOrgID</a:Code>
<a:CreationDate>1338971533</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o327">
<a:ObjectID>DF9B7A09-A35F-498C-BB99-1FD6E6CDF997</a:ObjectID>
<a:Name>项目描述</a:Name>
<a:Code>FDescription</a:Code>
<a:CreationDate>1333953017</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
</o:Column>
<o:Column Id="o328">
<a:ObjectID>482767D1-780A-4E40-A3C2-19E1AD5670FC</a:ObjectID>
<a:Name>计划开始日期</a:Name>
<a:Code>FSTARTDATE</a:Code>
<a:CreationDate>1338971533</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o329">
<a:ObjectID>06C47C3D-F252-4C3E-943E-F98619549707</a:ObjectID>
<a:Name>计划结束日期</a:Name>
<a:Code>FENDDATE</a:Code>
<a:CreationDate>1338971533</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o330">
<a:ObjectID>C054B05C-EB2A-4141-8483-7CB19108651C</a:ObjectID>
<a:Name>项目状态</a:Name>
<a:Code>FSTATUS</a:Code>
<a:CreationDate>1338971533</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o331">
<a:ObjectID>C898EF2D-8E50-41B5-A5BD-C6268F551E4E</a:ObjectID>
<a:Name>结束日期</a:Name>
<a:Code>FCLOSEDATE</a:Code>
<a:CreationDate>1338971533</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1338972220</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o332">
<a:ObjectID>416E9AAB-46A7-4C46-875E-EF60B4F81F5F</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1333953017</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333965071</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o325"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o332"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o332"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o251">
<a:ObjectID>6AF8B337-A79B-4E35-A064-CD22BB13C200</a:ObjectID>
<a:Name>企业服务历史</a:Name>
<a:Code>T_CDP_HistoryCompany</a:Code>
<a:CreationDate>1335489815</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335490275</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o333">
<a:ObjectID>FA2598A7-B29F-452E-B3C4-77E9DC1C6753</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FHistoryid</a:Code>
<a:CreationDate>1335489864</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340844852</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o334">
<a:ObjectID>10335234-052D-4508-8776-C6E4D4954651</a:ObjectID>
<a:Name>用户内码</a:Name>
<a:Code>FUserId</a:Code>
<a:CreationDate>1335489864</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335490275</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o335">
<a:ObjectID>7D6F8961-62FE-43B1-BC0F-629A189EAA1D</a:ObjectID>
<a:Name>企业内码</a:Name>
<a:Code>ForgId</a:Code>
<a:CreationDate>1335489864</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335490275</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o336">
<a:ObjectID>D21C8369-E6C7-4E9E-90F2-7A00804E7783</a:ObjectID>
<a:Name>角色</a:Name>
<a:Code>Role</a:Code>
<a:CreationDate>1335489864</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335490275</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o337">
<a:ObjectID>1E7C4E1F-F708-4F6A-B800-99D23877CC91</a:ObjectID>
<a:Name>服务开始时间</a:Name>
<a:Code>FStartTime</a:Code>
<a:CreationDate>1335489864</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335490275</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o338">
<a:ObjectID>B1BDA656-1C9A-4BC4-B438-20C20CB3A4BF</a:ObjectID>
<a:Name>服务结束时间</a:Name>
<a:Code>FEndTime</a:Code>
<a:CreationDate>1335489864</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335490275</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o339">
<a:ObjectID>549BA9FA-93A0-48EC-A396-FE1D57007F42</a:ObjectID>
<a:Name>其他表内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1335605097</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335606572</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o340">
<a:ObjectID>94597544-4ABD-4C79-BA4D-8C1AAC003CF0</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1340844848</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340846809</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o333"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o340"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o340"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o264">
<a:ObjectID>ED56EE28-D23E-40E2-A3F3-B4E7E1F937BA</a:ObjectID>
<a:Name>构建日志</a:Name>
<a:Code>T_CDP_BuildLog</a:Code>
<a:CreationDate>1340270709</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340270939</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o341">
<a:ObjectID>ECE28FE1-2AC7-4B7D-BC50-6A5086A77DFC</a:ObjectID>
<a:Name>主键ID</a:Name>
<a:Code>FId</a:Code>
<a:CreationDate>1340270713</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340270939</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o342">
<a:ObjectID>8BDF5288-4441-4899-B569-FFEE9FA988A8</a:ObjectID>
<a:Name>构建描述</a:Name>
<a:Code>FRemark</a:Code>
<a:CreationDate>1340270713</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340270939</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o343">
<a:ObjectID>B18BB8AF-41EA-4785-AB8C-786F1FC0C048</a:ObjectID>
<a:Name>构建发起时间</a:Name>
<a:Code>FStartTime</a:Code>
<a:CreationDate>1340270713</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340270939</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o344">
<a:ObjectID>E5B98BF9-41EE-4399-808E-4C48B564C577</a:ObjectID>
<a:Name>构建状态</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1340270713</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340875958</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o345">
<a:ObjectID>E105AF54-DE9C-40CB-93FD-D47F8635E18E</a:ObjectID>
<a:Name>本次构建的包名称</a:Name>
<a:Code>FPackageName</a:Code>
<a:CreationDate>1340270713</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340270939</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o346">
<a:ObjectID>792BBB5A-4E76-4F18-AAEA-5551C0E1DFDD</a:ObjectID>
<a:Name>本次构建的包版本</a:Name>
<a:Code>FVersion</a:Code>
<a:CreationDate>1340270713</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340270939</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o347">
<a:ObjectID>8DB056BA-A36F-4131-8942-7597061FEC04</a:ObjectID>
<a:Name>构建编号</a:Name>
<a:Code>FBuildId</a:Code>
<a:CreationDate>1340701903</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340702535</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o348">
<a:ObjectID>3FA138D4-2182-4CCF-86CC-C526CA19EE54</a:ObjectID>
<a:Name>项目内码</a:Name>
<a:Code>FProjectId</a:Code>
<a:CreationDate>1340760739</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340760807</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o349">
<a:ObjectID>72CD3E37-0288-4F62-8CF4-D3E009C1BF72</a:ObjectID>
<a:Name>日志信息</a:Name>
<a:Code>FMessage</a:Code>
<a:CreationDate>1340960561</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340963171</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>ntext</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o350">
<a:ObjectID>28B075EE-BB49-4A7E-BC08-6AB1988BE4E8</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1340270926</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340270939</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o341"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o350"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o350"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o260">
<a:ObjectID>04ADEB9F-A69E-49BC-B008-938658341A8D</a:ObjectID>
<a:Name>业务对象</a:Name>
<a:Code>T_CDP_BusinessObject</a:Code>
<a:CreationDate>1339815453</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o351">
<a:ObjectID>F89A9796-9AA4-4E96-A383-CC845CDC5BEA</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1339815987</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o352">
<a:ObjectID>87DC4415-B556-42EF-A2E1-F9809AECAE99</a:ObjectID>
<a:Name>项目内码</a:Name>
<a:Code>FProjectID</a:Code>
<a:CreationDate>1339816057</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o353">
<a:ObjectID>172A69FE-EAB0-4DBD-835A-CE93B79DE90B</a:ObjectID>
<a:Name>key</a:Name>
<a:Code>FKey</a:Code>
<a:CreationDate>1339816057</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o354">
<a:ObjectID>B94533BC-5AD1-4AD5-AD95-F1602860ACF6</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FName</a:Code>
<a:CreationDate>1339816057</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o355">
<a:ObjectID>F924E036-987A-44C6-9E4F-72D5275CC857</a:ObjectID>
<a:Name>子系统key</a:Name>
<a:Code>FSubSystemKey</a:Code>
<a:CreationDate>1339816057</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o356">
<a:ObjectID>6E694E9B-CBAA-4492-92B3-67BC0674E59F</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDescription</a:Code>
<a:CreationDate>1339816057</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o357">
<a:ObjectID>D36DE864-F4A9-4549-89D4-51C31A4C2A0A</a:ObjectID>
<a:Name>模式类型</a:Name>
<a:Code>FModelType</a:Code>
<a:CreationDate>1339816057</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o358">
<a:ObjectID>7799ABA2-86FA-416E-B044-5C3ED5A87C11</a:ObjectID>
<a:Name>基对象key</a:Name>
<a:Code>FBaseObjectKey</a:Code>
<a:CreationDate>1339816057</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o359">
<a:ObjectID>422ADF17-861D-4E94-A54D-029B112A5EC5</a:ObjectID>
<a:Name>开发类型</a:Name>
<a:Code>FDevTypeValue</a:Code>
<a:CreationDate>1341889512</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1341890199</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o360">
<a:ObjectID>9782EF76-B37A-46D0-AC7B-1AB695AA416E</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1339816057</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o351"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o360"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o360"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o258">
<a:ObjectID>EDDD63F1-E1C9-43B0-9760-0D8D77F92FBB</a:ObjectID>
<a:Name>资源</a:Name>
<a:Code>T_CDP_Resource</a:Code>
<a:CreationDate>1339815453</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o361">
<a:ObjectID>7CFEB017-7402-4290-8F8C-3A35AD46254B</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1339815552</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o362">
<a:ObjectID>72B4481C-39FB-4142-83FE-5B443EA6724C</a:ObjectID>
<a:Name>项目内码</a:Name>
<a:Code>FProjectID</a:Code>
<a:CreationDate>1339815552</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o363">
<a:ObjectID>36CFA2AA-1F16-402E-A112-A70D0D38198B</a:ObjectID>
<a:Name>资源名称</a:Name>
<a:Code>FName</a:Code>
<a:CreationDate>1339815552</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1342678465</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o364">
<a:ObjectID>69A31991-EE83-413D-AC6D-E563BA167ADA</a:ObjectID>
<a:Name>资源类型</a:Name>
<a:Code>FRESOURCETYPE</a:Code>
<a:CreationDate>1339815552</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339838520</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o365">
<a:ObjectID>3B62E930-05B3-45E1-A1C7-424065D05F44</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1339816035</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339817578</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o361"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o365"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o365"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o256">
<a:ObjectID>C27B78DF-21B5-41AD-953B-85AC262D6747</a:ObjectID>
<a:Name>项目体验环境</a:Name>
<a:Code>t_CDP_Environment</a:Code>
<a:CreationDate>1339399553</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339399885</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o366">
<a:ObjectID>B2670A29-5C6E-43AD-95B4-46617B5D76B6</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FEnvironmentID</a:Code>
<a:CreationDate>1339399556</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339399885</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o367">
<a:ObjectID>A1E856FB-E35E-4610-B265-94704F40FB02</a:ObjectID>
<a:Name>项目内码</a:Name>
<a:Code>FPROJECTID</a:Code>
<a:CreationDate>1339399556</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339399885</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o368">
<a:ObjectID>F926DC2A-5213-4109-A23D-3C037D9D2781</a:ObjectID>
<a:Name>环境使用的包</a:Name>
<a:Code>FBUILDERNUMBER</a:Code>
<a:CreationDate>1339399556</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1341538335</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o369">
<a:ObjectID>DDC9EA98-B872-44F9-A53F-AE5828C1F508</a:ObjectID>
<a:Name>环境者内码</a:Name>
<a:Code>FENVCREATORID</a:Code>
<a:CreationDate>1339399556</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339399885</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o370">
<a:ObjectID>95B233F5-6294-4780-B6AF-61586AC76A2B</a:ObjectID>
<a:Name>环境创建时间</a:Name>
<a:Code>FENVCREATEDATE</a:Code>
<a:CreationDate>1339399556</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339399885</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o371">
<a:ObjectID>6005D237-3FD3-4286-8DBE-CD5D9DADC9DD</a:ObjectID>
<a:Name>环境关闭时间</a:Name>
<a:Code>FENVCLOSEDATE</a:Code>
<a:CreationDate>1339399556</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339399885</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o372">
<a:ObjectID>424D17CE-D8A4-4B2A-91F6-8CA4F241BD47</a:ObjectID>
<a:Name>环境状态</a:Name>
<a:Code>FSTATUS</a:Code>
<a:CreationDate>1339399556</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339399885</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o373">
<a:ObjectID>AAF517C2-D8EC-4FC8-9A7E-C1FF1F197670</a:ObjectID>
<a:Name>测试体验环境描述</a:Name>
<a:Code>FDescription</a:Code>
<a:CreationDate>1339665456</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339665544</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o374">
<a:ObjectID>131931FF-AA30-434D-B97F-2B5BBB67466B</a:ObjectID>
<a:Name>虚机内码</a:Name>
<a:Code>FVMID</a:Code>
<a:CreationDate>1342865170</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1342869864</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o375">
<a:ObjectID>5B57EFF5-BD8C-44C5-A5C1-873F534D5E28</a:ObjectID>
<a:Name>构建包内码</a:Name>
<a:Code>FPkgID</a:Code>
<a:CreationDate>1342866982</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1342869864</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o376">
<a:ObjectID>933625F1-6999-4F26-8EAF-9D0036165B07</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1339399850</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1339399885</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o366"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o376"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o376"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o241">
<a:ObjectID>0F5B4DBD-4C5D-4D90-ADE0-64381F9EB2E7</a:ObjectID>
<a:Name>企业附表</a:Name>
<a:Code>T_CDP_COMPANY</a:Code>
<a:CreationDate>1332819185</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o377">
<a:ObjectID>9C7EE500-F88D-4419-B8A6-3A8E18C93B5C</a:ObjectID>
<a:Name>分录内码</a:Name>
<a:Code>FEntryId</a:Code>
<a:CreationDate>1335163993</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o378">
<a:ObjectID>A39576C3-F5F0-4A88-B323-59871FAA0EAC</a:ObjectID>
<a:Name>组织内码</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1332819188</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1335164234</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o379">
<a:ObjectID>F4FEBB29-EAA9-49A7-B4E3-D9C7535DE636</a:ObjectID>
<a:Name>企业类别</a:Name>
<a:Code>FType</a:Code>
<a:CreationDate>1332819188</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332819836</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o380">
<a:ObjectID>BE021D41-886E-470E-97BF-656D06E7FE77</a:ObjectID>
<a:Name>注册资本</a:Name>
<a:Code>FRegCapital</a:Code>
<a:CreationDate>1332819188</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1337756309</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o381">
<a:ObjectID>1E91C9DE-58CD-4224-8D0B-6DFF0406996F</a:ObjectID>
<a:Name>联系人内码</a:Name>
<a:Code>FCONTACTID</a:Code>
<a:CreationDate>1332819437</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1335164157</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o382">
<a:ObjectID>976F06C6-CC5A-4BF0-A330-E6CD93D17E12</a:ObjectID>
<a:Name>省份</a:Name>
<a:Code>FPROVINCE</a:Code>
<a:CreationDate>1336462370</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336462442</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o383">
<a:ObjectID>68C086E3-A7D9-437C-8CF5-5A7E3B93FEDF</a:ObjectID>
<a:Name>城市</a:Name>
<a:Code>FCity</a:Code>
<a:CreationDate>1332828665</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1335164157</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o384">
<a:ObjectID>C98C7CFD-307C-4A85-8741-7A34A429E651</a:ObjectID>
<a:Name>业务描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1332819437</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1333261371</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
</o:Column>
<o:Column Id="o385">
<a:ObjectID>3D42B1F6-32CB-4344-9094-B90E252D77D4</a:ObjectID>
<a:Name>注册日期</a:Name>
<a:Code>FRegisterTime</a:Code>
<a:CreationDate>1337305021</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1337305248</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o386">
<a:ObjectID>50D5FDB7-B166-4B64-8D15-680335266D90</a:ObjectID>
<a:Name>人员规模</a:Name>
<a:Code>FSize</a:Code>
<a:CreationDate>1340268158</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340269100</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o387">
<a:ObjectID>FF36918F-B621-43B4-965D-9C88CB352DB5</a:ObjectID>
<a:Name>公司股权构成</a:Name>
<a:Code>FEQUITYSTRUCTURE</a:Code>
<a:CreationDate>1340268569</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340269100</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o388">
<a:ObjectID>BB4E0A25-DB9E-4ABB-AEB2-1A9B1B52A014</a:ObjectID>
<a:Name>企业法人是金蝶离职员工</a:Name>
<a:Code>FISKINGDEER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o389">
<a:ObjectID>87EB811F-3005-4D0B-92D8-9F476100D0F4</a:ObjectID>
<a:Name>企业网址</a:Name>
<a:Code>FWEBSITE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o390">
<a:ObjectID>9A0BE983-2D10-4826-9F3E-9D457DCB703F</a:ObjectID>
<a:Name>企业营业执照</a:Name>
<a:Code>FLICENSE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>image</a:DataType>
</o:Column>
<o:Column Id="o391">
<a:ObjectID>8CD95126-242F-4D8D-A04A-BA8AE04E5BC1</a:ObjectID>
<a:Name>邮政编码</a:Name>
<a:Code>FCDPPOSTCODE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o392">
<a:ObjectID>FD1C756C-B77D-4FBC-9B2A-B52DB0F2635A</a:ObjectID>
<a:Name>传真号码</a:Name>
<a:Code>FCDPFAX</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o393">
<a:ObjectID>9A28E6AE-637E-468B-9C0D-7131F7F27AD3</a:ObjectID>
<a:Name>企业与金蝶有合作历史</a:Name>
<a:Code>FHASHISTORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o394">
<a:ObjectID>CC82A7BE-890E-4788-AD65-DEB3089375FC</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332830280</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o377"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o394"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o394"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o247">
<a:ObjectID>72025001-3818-4065-8A95-B439E6C21680</a:ObjectID>
<a:Name>企业附表多语言</a:Name>
<a:Code>T_CDP_COMPANY_L</a:Code>
<a:CreationDate>1333260238</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333952763</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o395">
<a:ObjectID>04470161-80D3-470D-AC87-9E43CD6357E8</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1333260238</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o396">
<a:ObjectID>71AA6D47-5775-4188-9E2B-BD4FBCC82A28</a:ObjectID>
<a:Name>分录内码</a:Name>
<a:Code>FEntryId</a:Code>
<a:CreationDate>1335164194</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o397">
<a:ObjectID>01E52DE5-0427-4FA6-B5EE-66E48B2343FB</a:ObjectID>
<a:Name>地区内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1333260238</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o398">
<a:ObjectID>FD0C3C09-8F47-4635-8D18-1904F7FC6A23</a:ObjectID>
<a:Name>企业资质</a:Name>
<a:Code>FCertification</a:Code>
<a:CreationDate>1333260238</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o399">
<a:ObjectID>2EED0ADF-D73A-41D6-A8C2-1478D739B8B9</a:ObjectID>
<a:Name>企业法人</a:Name>
<a:Code>FLEGALPERSON</a:Code>
<a:CreationDate>1333260302</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o400">
<a:ObjectID>75F45058-167A-49CF-B0F4-BDBA8BBA49CF</a:ObjectID>
<a:Name>企业地址</a:Name>
<a:Code>FCDPADDRESS</a:Code>
<a:CreationDate>1340616521</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1340616605</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o401">
<a:ObjectID>C03F65D8-8177-459D-AB77-06536C3175A4</a:ObjectID>
<a:Name>企业注册地址</a:Name>
<a:Code>FREGISTERADDRESS</a:Code>
<a:CreationDate>1347416010</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o402">
<a:ObjectID>3220B564-0FDC-4518-BB50-319026CC55A3</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1333260238</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o395"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o402"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o402"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o270">
<a:ObjectID>F63F5A97-713B-4206-985E-946EE09CEAB7</a:ObjectID>
<a:Name>企业授权范围-机构</a:Name>
<a:Code>T_CDP_HISTORYAUTHORIZE_I</a:Code>
<a:CreationDate>1347528040</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o403">
<a:ObjectID>CEC67B14-C7EC-450A-82FB-41394A309778</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1347528331</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o404">
<a:ObjectID>1C153248-1359-4078-AE06-7EBE8B48CE6D</a:ObjectID>
<a:Name>认证表内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1347528331</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o405">
<a:ObjectID>A73CFD41-E17B-4952-83B1-0A585F749387</a:ObjectID>
<a:Name>机构内码</a:Name>
<a:Code>FInstitutionID</a:Code>
<a:CreationDate>1347528331</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o406">
<a:ObjectID>B6260C37-0001-4999-899A-FAC8AE63D40E</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1347528331</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o403"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o406"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o406"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o269">
<a:ObjectID>AA212284-9D03-4F60-8330-F6AAC4D8B426</a:ObjectID>
<a:Name>企业授权范围-区域</a:Name>
<a:Code>T_CDP_HISTORYAUTHORIZE_R</a:Code>
<a:CreationDate>1347528040</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o407">
<a:ObjectID>3EC61085-7FDB-4724-BE5A-7094E26B02A0</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1347528070</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o408">
<a:ObjectID>E98F7991-687D-4CD5-9081-7693145C63A8</a:ObjectID>
<a:Name>认证表内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1347528070</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o409">
<a:ObjectID>16A1A7EA-BCE4-4788-8FFB-10DF672B5DCF</a:ObjectID>
<a:Name>区域代码</a:Name>
<a:Code>FRegionID</a:Code>
<a:CreationDate>1347528070</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o410">
<a:ObjectID>F037B95D-558F-4AA3-94E6-6C46FB664E39</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1347528070</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o407"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o410"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o410"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o273">
<a:ObjectID>AC4ADA54-1436-45E9-865B-4C67C719822F</a:ObjectID>
<a:Name>机构</a:Name>
<a:Code>T_CDP_Institution</a:Code>
<a:CreationDate>1347591315</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o411">
<a:ObjectID>3EE092CB-00E6-4852-8FC1-C44DF8610312</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1347591744</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o412">
<a:ObjectID>5AFEAECB-49D9-47E0-B636-443775CD6317</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1347591744</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o413">
<a:ObjectID>4BC2908B-F6CB-48D6-A4DB-4ED6B1AEA4DD</a:ObjectID>
<a:Name>数据状态</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1347591744</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o414">
<a:ObjectID>D3B039AA-1E66-4B65-B6DD-C9482AF5C3D4</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1347591744</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o415">
<a:ObjectID>DC5727CF-D405-451E-B3FF-9C9359261DB8</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1347591744</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o411"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o415"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o415"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o274">
<a:ObjectID>731CE24F-A1A9-41CA-ACDC-199EC98832B5</a:ObjectID>
<a:Name>机构-多语言</a:Name>
<a:Code>T_CDP_Institution_L</a:Code>
<a:CreationDate>1347591315</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o416">
<a:ObjectID>0D20D21F-622E-4151-A9DB-5928579154BE</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1347591906</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o417">
<a:ObjectID>88D1A45F-CDCA-4535-A6BA-827EA8C31D59</a:ObjectID>
<a:Name>机构内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1347591899</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o418">
<a:ObjectID>F35C93E7-3A51-4B03-A5E4-ACFEAD47D697</a:ObjectID>
<a:Name>多语言内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1347591906</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o419">
<a:ObjectID>DD9D54DF-EFA6-4453-98D3-36937D539EE5</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1347591906</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o420">
<a:ObjectID>98BCC2A0-E990-4355-833A-92812C20C1C8</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1347591906</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o416"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o420"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o420"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o271">
<a:ObjectID>C854FB4A-F434-4160-B939-7DF505CC8C48</a:ObjectID>
<a:Name>区域</a:Name>
<a:Code>T_CDP_Region</a:Code>
<a:CreationDate>1347591314</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o421">
<a:ObjectID>149938F1-24A8-4F04-80D7-20C8154142E0</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1347591324</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o422">
<a:ObjectID>00EC0A7A-91E5-4B0C-9413-CD7B1B7BCEB2</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1347591324</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o423">
<a:ObjectID>B200C25E-2FA8-4C82-9807-584D05B6BD6F</a:ObjectID>
<a:Name>数据状态</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1347591324</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o424">
<a:ObjectID>260E4866-C66E-469C-8E9F-C84F90B17C5C</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1347591324</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o425">
<a:ObjectID>7824D3E7-7EA9-41B3-8428-26B9172AF55B</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1347591613</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o421"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o425"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o425"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o272">
<a:ObjectID>5B06B976-A12A-4CD0-9BA7-171134272AA0</a:ObjectID>
<a:Name>区域-多语言</a:Name>
<a:Code>T_CDP_Region_L</a:Code>
<a:CreationDate>1347591314</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o426">
<a:ObjectID>18B8DCFD-1A75-4189-84FB-761338619D4A</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1347591626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o427">
<a:ObjectID>EFA26C85-9F28-467C-8852-F8C7FED210B3</a:ObjectID>
<a:Name>区域内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1347591621</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o428">
<a:ObjectID>FB4CDC4A-046C-44DC-8903-8BE15A8652FF</a:ObjectID>
<a:Name>语言内码</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1347591626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o429">
<a:ObjectID>2B898DD7-C003-4853-A532-0041E752ABDE</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1347591626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o430">
<a:ObjectID>3563A78A-3D1F-4343-85CC-C0E73D29FA95</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1347591626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o426"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o430"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o430"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o267">
<a:ObjectID>C27DC3D0-27A8-4EDE-9C6E-F5CC3CD86768</a:ObjectID>
<a:Name>企业认证-产品类型</a:Name>
<a:Code>T_CDP_HISTORYAUTHORIZE_P</a:Code>
<a:CreationDate>1347416505</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347845660</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o431">
<a:ObjectID>9D06409B-927B-4D7D-B5A3-0706321AA2B7</a:ObjectID>
<a:Name>企业认证内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1347416526</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o432">
<a:ObjectID>6BC5FA2F-5094-41C6-847E-89294DA03E98</a:ObjectID>
<a:Name>产品表内码</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1347416526</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347417023</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o433">
<a:ObjectID>96628690-46AA-43C2-86DD-EA7086FC195D</a:ObjectID>
<a:Name>产品辅助资料内码</a:Name>
<a:Code>FPRODUCTTYPE</a:Code>
<a:CreationDate>1347416526</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o434">
<a:ObjectID>6E351729-8EBA-47B3-8F5E-1E28630335F7</a:ObjectID>
<a:Name>从事产品人数</a:Name>
<a:Code>FPROJECTNUMBER</a:Code>
<a:CreationDate>1347416526</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o435">
<a:ObjectID>4F6EEA1E-7F57-4494-A5D3-3CD20F2D7872</a:ObjectID>
<a:Name>是否申请该产品认证</a:Name>
<a:Code>FISSELECTED</a:Code>
<a:CreationDate>1347416526</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o436">
<a:ObjectID>E9C26354-FA1E-4B0B-AD97-3925866CB921</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1347417014</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347417080</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o432"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o436"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o436"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o268">
<a:ObjectID>53E44287-3EB2-4EBA-959F-4BA00F18DD78</a:ObjectID>
<a:Name>企业认证-领域</a:Name>
<a:Code>T_CDP_HISTORYAUTHORIZE_M</a:Code>
<a:CreationDate>1347416506</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347845671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o437">
<a:ObjectID>3EB8B0A7-0DE6-426B-B374-1C40706D67F8</a:ObjectID>
<a:Name>产品表内码</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1347416732</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o438">
<a:ObjectID>87B3BBD8-374B-4DF2-AFDE-3BE551EEC851</a:ObjectID>
<a:Name>领域表内码</a:Name>
<a:Code>FDetailID</a:Code>
<a:CreationDate>1347416732</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347417029</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o439">
<a:ObjectID>D0ED184B-F982-49FC-9B0B-80D45DC4FF01</a:ObjectID>
<a:Name>是否选择该领域</a:Name>
<a:Code>FMODULESELECTED</a:Code>
<a:CreationDate>1347416732</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o440">
<a:ObjectID>6B4DF3B9-0494-43A7-862F-F5178DE0A0A2</a:ObjectID>
<a:Name>领域辅助资料内码</a:Name>
<a:Code>FMODULE</a:Code>
<a:CreationDate>1347416732</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o441">
<a:ObjectID>0787836E-1DF3-412D-B6F8-876D888AD63C</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1347417024</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347417080</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o438"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o441"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o441"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o276">
<a:ObjectID>68F8E41B-68D9-4688-B8A1-7FD317909B6E</a:ObjectID>
<a:Name>导入数据</a:Name>
<a:Code>T_CDP_HistoryInptData</a:Code>
<a:CreationDate>1357723624</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o442">
<a:ObjectID>72904C96-15B0-4D55-A61C-1D62067A53E0</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o443">
<a:ObjectID>2778EDD8-B11E-4C49-87F7-BE27FD22FF26</a:ObjectID>
<a:Name>用户文件</a:Name>
<a:Code>FUSERFILE</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o444">
<a:ObjectID>16B18FC0-7BB9-4142-A085-72080622A92B</a:ObjectID>
<a:Name>公司文件</a:Name>
<a:Code>FCOMPANYFILE</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o445">
<a:ObjectID>68463CB6-83E9-43BA-A68E-4071BFB1BF3A</a:ObjectID>
<a:Name>个人认证文件</a:Name>
<a:Code>FQUALIFICATIONFILE</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o446">
<a:ObjectID>54E79C70-6886-4386-8AB5-BA6958B3005D</a:ObjectID>
<a:Name>企业认证文件</a:Name>
<a:Code>FCERTIFICATIONFILE</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o447">
<a:ObjectID>57FB183E-F8E7-4AF1-AA7A-CEF04FE201B1</a:ObjectID>
<a:Name>输出目录</a:Name>
<a:Code>FOUTPUTPATH</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o448">
<a:ObjectID>0AC99042-2685-484E-BE76-D1CA0DFC1501</a:ObjectID>
<a:Name>产生的最小编码</a:Name>
<a:Code>FMINNO</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o449">
<a:ObjectID>9B611860-2EF4-477C-A83E-7424E3BCFAFF</a:ObjectID>
<a:Name>数据导入人</a:Name>
<a:Code>FINPUTUSERID</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o450">
<a:ObjectID>85F05803-4A3F-4FCE-9516-502E016EFBB4</a:ObjectID>
<a:Name>导入日期</a:Name>
<a:Code>FINPUTDATE</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o451">
<a:ObjectID>DADF382A-881E-478E-AB34-412A1C29E602</a:ObjectID>
<a:Name>导入结果</a:Name>
<a:Code>FRESULT</a:Code>
<a:CreationDate>1357723626</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o452">
<a:ObjectID>40EDA9FA-F210-4C49-850D-780F76F3C25B</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1357723948</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o442"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o452"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o452"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o246">
<a:ObjectID>A5D4200C-D174-4047-8356-9A813A6AD722</a:ObjectID>
<a:Name>企业认证历史</a:Name>
<a:Code>T_CDP_HISTORYAUTHORIZE</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o453">
<a:ObjectID>380741B0-51F9-4D31-A365-CDE5FEE570F6</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FId</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o454">
<a:ObjectID>92983808-923B-496D-9A58-26811BC76478</a:ObjectID>
<a:Name>认证类型</a:Name>
<a:Code>FQUALIFICATIONTYPE</a:Code>
<a:CreationDate>1335164452</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347416485</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o455">
<a:ObjectID>B9E9646A-9C70-49E6-AD9D-931A1C05D7BE</a:ObjectID>
<a:Name>申请人内码</a:Name>
<a:Code>FAppliantId</a:Code>
<a:CreationDate>1332830353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o456">
<a:ObjectID>6110FEE1-55B0-4C2C-8127-84E1A727B94D</a:ObjectID>
<a:Name>申请日期</a:Name>
<a:Code>FApplyTime</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o457">
<a:ObjectID>BCD86854-FC6E-464C-B4DC-5DBBB1FC3A6E</a:ObjectID>
<a:Name>申请企业</a:Name>
<a:Code>FCOMPANY</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334134014</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o458">
<a:ObjectID>494E45B2-36B3-4290-B492-DAF95699B668</a:ObjectID>
<a:Name>申请说明</a:Name>
<a:Code>FApplyRemark</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o459">
<a:ObjectID>9BA8FBD7-AF88-4C79-BE4C-81BC45A4C3BD</a:ObjectID>
<a:Name>审批人</a:Name>
<a:Code>FApproverId</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o460">
<a:ObjectID>42FF6A96-9CAD-4F6F-ABBB-21BE329E85D4</a:ObjectID>
<a:Name>审批日期</a:Name>
<a:Code>FApproveTime</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o461">
<a:ObjectID>677C9415-7ABD-4997-8E8B-E40F83F7DD98</a:ObjectID>
<a:Name>审批说明</a:Name>
<a:Code>FApproveRemark</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o462">
<a:ObjectID>4D410166-3DE8-417F-B335-E3068DCB2F05</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>FStatus</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o463">
<a:ObjectID>66C91791-D30C-4164-8173-784FC6A3BF53</a:ObjectID>
<a:Name>企业等级</a:Name>
<a:Code>FRank</a:Code>
<a:CreationDate>1332830353</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1334631513</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o464">
<a:ObjectID>DEA7C71F-9779-4828-8DDE-02BF102B5CCE</a:ObjectID>
<a:Name>资质证书编号</a:Name>
<a:Code>FNo</a:Code>
<a:CreationDate>1334631017</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334654388</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o465">
<a:ObjectID>1BB63B8B-D87F-4D21-B8CE-8BFD8B503B97</a:ObjectID>
<a:Name>资质生效日期</a:Name>
<a:Code>FStartTime</a:Code>
<a:CreationDate>1334631017</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334654388</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o466">
<a:ObjectID>297EE92C-D2F5-4500-B4C9-E83A5E3CFAEE</a:ObjectID>
<a:Name>资质失效日期</a:Name>
<a:Code>FEndTime</a:Code>
<a:CreationDate>1334631017</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334654388</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o467">
<a:ObjectID>4ABEAD84-E128-443A-B2A1-B404F927A4FC</a:ObjectID>
<a:Name>企业认证信息内码</a:Name>
<a:Code>FAUTHORIZEID</a:Code>
<a:CreationDate>1334638272</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1334654388</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o468">
<a:ObjectID>A0188C10-C653-4A6C-B4BD-CC32B4A7A4EA</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FInfoStatus</a:Code>
<a:CreationDate>1335489993</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335943289</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o469">
<a:ObjectID>72C43738-4021-4774-88CB-3FB6F5CD83C5</a:ObjectID>
<a:Name>开发技术人数</a:Name>
<a:Code>FDEVELOPERNUMBER</a:Code>
<a:CreationDate>1347416298</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o470">
<a:ObjectID>9225EAC5-24E3-459D-B342-77D35084CC10</a:ObjectID>
<a:Name>开发交付人数</a:Name>
<a:Code>FDEVELOPERNUMBER2</a:Code>
<a:CreationDate>1347416298</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o471">
<a:ObjectID>2388E819-E406-4381-931F-8734B1DE4943</a:ObjectID>
<a:Name>实施交付人数</a:Name>
<a:Code>FImplementNumber</a:Code>
<a:CreationDate>1347416298</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o472">
<a:ObjectID>7F424704-179D-475B-A0D8-841DAFDC2B68</a:ObjectID>
<a:Name>保证金</a:Name>
<a:Code>FPREMIUM</a:Code>
<a:CreationDate>1347526998</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o473">
<a:ObjectID>05D8C27A-3947-483C-9446-E7CCE3F6D6DE</a:ObjectID>
<a:Name>合同到期日期</a:Name>
<a:Code>FCONTRACTENDDATE</a:Code>
<a:CreationDate>1347526998</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o474">
<a:ObjectID>2B2F4AF6-DFCA-49C8-960B-0AE993207EAD</a:ObjectID>
<a:Name>企业业务授权范围</a:Name>
<a:Code>FRANGE</a:Code>
<a:CreationDate>1347526998</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347591303</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o475">
<a:ObjectID>661DDA36-E709-40A1-87C9-946660B9FDF1</a:ObjectID>
<a:Name>企业所属区域</a:Name>
<a:Code>FRegion</a:Code>
<a:CreationDate>1347527946</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o476">
<a:ObjectID>CBA80B90-738B-49FE-B275-E10DE67134A5</a:ObjectID>
<a:Name>企业所属机构</a:Name>
<a:Code>FInstitution</a:Code>
<a:CreationDate>1347527946</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347528943</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o477">
<a:ObjectID>9E95371F-BB4A-4ECB-8C1B-136A410EF2B4</a:ObjectID>
<a:Name>认证备注</a:Name>
<a:Code>FNOTES</a:Code>
<a:CreationDate>1353031118</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o478">
<a:ObjectID>9E8E0AFF-CDFE-4908-BDBD-6A620E2FFD3A</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FBILLNO</a:Code>
<a:CreationDate>1348470910</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o479">
<a:ObjectID>7B79A7B6-C46A-4178-BC9F-768E9DA79FC1</a:ObjectID>
<a:Name>产品简介</a:Name>
<a:Code>FPRODUCT</a:Code>
<a:CreationDate>1353031118</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o480">
<a:ObjectID>697B2033-F4DB-46AC-A7A5-D2EE66BE3601</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1332830352</a:CreationDate>
<a:Creator>kevin_b_yang</a:Creator>
<a:ModificationDate>1332831786</a:ModificationDate>
<a:Modifier>kevin_b_yang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o453"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o480"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o480"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o281">
<a:ObjectID>5C38C34D-9A7A-4CC5-A057-B7B891893A4D</a:ObjectID>
<a:Name>申请体验</a:Name>
<a:Code>T_CDP_Practice</a:Code>
<a:CreationDate>1357782418</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o481">
<a:ObjectID>3D015FBA-ECD5-4FDF-AE5B-74AB0334D91D</a:ObjectID>
<a:Name>申请内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o482">
<a:ObjectID>1CE791A3-BE35-4891-9DB2-032243FAFEF5</a:ObjectID>
<a:Name>申请单号</a:Name>
<a:Code>FBILLNO</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o483">
<a:ObjectID>B51DE85E-1F98-4FFF-8AE8-41DB55102C3E</a:ObjectID>
<a:Name>应用名称</a:Name>
<a:Code>FAPPLICATIONID</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o484">
<a:ObjectID>41FF4209-AEEA-440A-A209-7DA9EA7B9E91</a:ObjectID>
<a:Name>申请日期</a:Name>
<a:Code>FAPPLYTIME</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o485">
<a:ObjectID>DA588BC8-7B2B-4C8C-A276-B00ECE6B54DB</a:ObjectID>
<a:Name>申请说明</a:Name>
<a:Code>FAPPLYREMARKS</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o486">
<a:ObjectID>28F934A7-7FBF-4E57-BBB1-BAE44F498787</a:ObjectID>
<a:Name>申请人</a:Name>
<a:Code>FAPPLICANTID</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o487">
<a:ObjectID>8A170627-B269-4943-BB53-9B25F0B616C6</a:ObjectID>
<a:Name>申请人姓名</a:Name>
<a:Code>FAPPLICANTNAME</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o488">
<a:ObjectID>ADD8A11C-B378-4763-82F4-5A1F8CBBFAEA</a:ObjectID>
<a:Name>申请人手机</a:Name>
<a:Code>FMOBILEH</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o489">
<a:ObjectID>4194E2F3-5C6C-42DA-8BA8-8EF10316F804</a:ObjectID>
<a:Name>申请人邮箱</a:Name>
<a:Code>FEMAILH</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o490">
<a:ObjectID>DAA50445-B52A-4093-B79C-2D18193990C1</a:ObjectID>
<a:Name>所属企业</a:Name>
<a:Code>FAPPLYORGID</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o491">
<a:ObjectID>4BF83B39-C83A-45A2-AA51-3255A08403AB</a:ObjectID>
<a:Name>所属企业名称</a:Name>
<a:Code>FORGNAME</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o492">
<a:ObjectID>11BB54E0-992B-499D-BB4D-2E41A1260BCE</a:ObjectID>
<a:Name>所属企业所在省份</a:Name>
<a:Code>FCPROVINCEH</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o493">
<a:ObjectID>0B04068E-7AF9-465C-AFB8-41EEC6903D5C</a:ObjectID>
<a:Name>所属企业所在城市</a:Name>
<a:Code>FCCITYH</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o494">
<a:ObjectID>C81BD36B-5FFE-482A-BBB6-7159A5C756BD</a:ObjectID>
<a:Name>所属企业类型</a:Name>
<a:Code>FCTYPEH</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o495">
<a:ObjectID>023DD4E6-F4B2-49B6-BC47-CEF8C7FFA310</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FINFOSTATUS</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o496">
<a:ObjectID>B6717378-80DF-405A-BCD1-292A059ABACC</a:ObjectID>
<a:Name>审批结果</a:Name>
<a:Code>FSTATUS</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o497">
<a:ObjectID>11F33A9E-C18C-4A6C-8918-7DA5F84FA6D6</a:ObjectID>
<a:Name>体验环境地址</a:Name>
<a:Code>FPRACTICESITE</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o498">
<a:ObjectID>209D144E-F0AB-404D-A789-DA4906202EB6</a:ObjectID>
<a:Name>体验账套</a:Name>
<a:Code>FPRACTICEDB</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o499">
<a:ObjectID>F7F54139-AE51-42CC-96A7-8031172FFB8B</a:ObjectID>
<a:Name>体验账号</a:Name>
<a:Code>FPRACTICEUSER</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o500">
<a:ObjectID>471A5DD3-F671-478F-903D-395F1275A49F</a:ObjectID>
<a:Name>密码</a:Name>
<a:Code>FPRACTICEPASSWORD</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o501">
<a:ObjectID>3FFE2B86-2C05-4847-8BE6-B2A36B79FD4E</a:ObjectID>
<a:Name>远程连接地址</a:Name>
<a:Code>FREMOTEADDRESS</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o502">
<a:ObjectID>BCB65222-AA66-49E3-962A-7702718AC460</a:ObjectID>
<a:Name>远程连接账号</a:Name>
<a:Code>FREMOTEUSER</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o503">
<a:ObjectID>1F2526F1-7E17-447F-BF1E-25E96F6F43FE</a:ObjectID>
<a:Name>远程连接密码</a:Name>
<a:Code>FREMOTEPASSWORD</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o504">
<a:ObjectID>0EFA751C-8C13-4133-9D2A-5D72FFABE394</a:ObjectID>
<a:Name>审批日期</a:Name>
<a:Code>FAPPROVEDATE</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o505">
<a:ObjectID>4BA1F7F4-223A-462D-8415-CF6BBE80BF2A</a:ObjectID>
<a:Name>体验开始日期</a:Name>
<a:Code>FSTARTDATE</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o506">
<a:ObjectID>114C64F7-59CB-4D17-9CE0-EE0C1DDD37E9</a:ObjectID>
<a:Name>体验结束日期</a:Name>
<a:Code>FENDDATE</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o507">
<a:ObjectID>6901B41D-43FD-4F82-BF85-771F2BACFB76</a:ObjectID>
<a:Name>审批意见</a:Name>
<a:Code>FAPPROVEREMARKS</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o508">
<a:ObjectID>9FDBBA0F-96F8-4F62-B4E7-BD74BBBC47BE</a:ObjectID>
<a:Name>审批备注</a:Name>
<a:Code>FAPPROVENOTE</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o509">
<a:ObjectID>A9B35F97-217A-4755-9BCC-604ECB687A4C</a:ObjectID>
<a:Name>审批人</a:Name>
<a:Code>FAPPROVEID</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o510">
<a:ObjectID>EFE20BF9-0375-4167-B5B3-E867B53A0BD8</a:ObjectID>
<a:Name>审批人姓名</a:Name>
<a:Code>FAPPROVENAME</a:Code>
<a:CreationDate>1357782432</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o511">
<a:ObjectID>9143AED7-D413-41E5-AC23-3BC51179EE73</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1357783658</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o481"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o511"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o511"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o282">
<a:ObjectID>DE3EF93C-5381-4EA7-84D3-8A5AB46F2062</a:ObjectID>
<a:Name>申请体验-审批历史</a:Name>
<a:Code>T_CDP_Practice_H</a:Code>
<a:CreationDate>1357782419</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o512">
<a:ObjectID>1B096923-AC3A-4719-AB8C-FFD8BDA26246</a:ObjectID>
<a:Name>申请内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1357784027</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o513">
<a:ObjectID>15B33E84-BB3F-48F6-90D8-AA9CC88B7561</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1357783989</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o514">
<a:ObjectID>243F7350-5CF4-4E3E-A261-C5EF56DC79E6</a:ObjectID>
<a:Name>审批结果</a:Name>
<a:Code>FSTATUSH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o515">
<a:ObjectID>40011D25-3023-4940-9635-A7EA8044BF3C</a:ObjectID>
<a:Name>审批意见</a:Name>
<a:Code>FAPPROVEREMARKSH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o516">
<a:ObjectID>9DD3E534-5C7A-49F3-9937-2F39773ECD28</a:ObjectID>
<a:Name>审批备注</a:Name>
<a:Code>FAPPROVENOTEH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o517">
<a:ObjectID>A6B6B092-27D7-4BB3-B68E-02D890A10AD9</a:ObjectID>
<a:Name>审批日期</a:Name>
<a:Code>FAPPROVEDATEH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o518">
<a:ObjectID>D9D8A695-5A1E-4BF4-8052-3D275BA8C068</a:ObjectID>
<a:Name>体验开始日期</a:Name>
<a:Code>FSTARTDATEH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o519">
<a:ObjectID>A904FC50-36B1-41CB-B332-801C83F62441</a:ObjectID>
<a:Name>体验结束日期</a:Name>
<a:Code>FENDDATEH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o520">
<a:ObjectID>B2CA6039-EE0C-4AC0-AA28-FB12AA705271</a:ObjectID>
<a:Name>体验环境地址</a:Name>
<a:Code>FPRACTICESITEH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o521">
<a:ObjectID>69822902-ADD9-4137-B6D8-185E18573AA5</a:ObjectID>
<a:Name>体验账套</a:Name>
<a:Code>FPRACTICEDBH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o522">
<a:ObjectID>274FB0E4-46D9-4314-AB7A-B6BA57EA2795</a:ObjectID>
<a:Name>体验账号</a:Name>
<a:Code>FPRACTICEUSERH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o523">
<a:ObjectID>FF1518C4-4920-4034-9AEC-06FDE207785D</a:ObjectID>
<a:Name>密码</a:Name>
<a:Code>FPRACTICEPASSWORDH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o524">
<a:ObjectID>1A78D095-50DB-4678-BD48-510AA7866E9E</a:ObjectID>
<a:Name>远程连接地址</a:Name>
<a:Code>FREMOTEADDRESSH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o525">
<a:ObjectID>396D6D6A-D13E-4E88-8EAE-AE0AC125C01A</a:ObjectID>
<a:Name>远程连接账号</a:Name>
<a:Code>FREMOTEUSERH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o526">
<a:ObjectID>EC829508-EB0C-4AC3-A22F-72EDEE454610</a:ObjectID>
<a:Name>远程连接密码</a:Name>
<a:Code>FREMOTEPASSWORDH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o527">
<a:ObjectID>8FC10024-B07A-4DA3-A17E-0CD7351911D6</a:ObjectID>
<a:Name>审批人</a:Name>
<a:Code>FAPPROVEIDH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o528">
<a:ObjectID>D21B0C06-B3E0-4C86-918B-8E5391099F31</a:ObjectID>
<a:Name>审批人姓名</a:Name>
<a:Code>FAPPROVENAMEH</a:Code>
<a:CreationDate>1357784064</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o529">
<a:ObjectID>7D7C340C-E13B-4778-A957-2111ECE9CDD6</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1357784030</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o513"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o529"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o529"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o279">
<a:ObjectID>AB974C65-D62D-44B3-B527-6E4420F6D0E3</a:ObjectID>
<a:Name>应用发布-领域</a:Name>
<a:Code>T_CDP_PublishApp_R</a:Code>
<a:CreationDate>1357779614</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o530">
<a:ObjectID>43C566D5-F051-41F7-9611-DFB87CE97D8B</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1357779769</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o531">
<a:ObjectID>F55D8B09-C1CE-43D2-B6A5-9326B7608DF7</a:ObjectID>
<a:Name>应用发布内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1357779690</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o532">
<a:ObjectID>D19037E5-CC58-4FFA-BF7A-2D5A196ADC84</a:ObjectID>
<a:Name>领域内码</a:Name>
<a:Code>FDomain</a:Code>
<a:CreationDate>1357779769</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o533">
<a:ObjectID>808D5836-E850-4DB3-A366-4C790E8E3600</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1357779769</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o530"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o533"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o533"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o280">
<a:ObjectID>528DE32B-E723-403D-9969-EC5B660959B7</a:ObjectID>
<a:Name>应用发布-审批历史</a:Name>
<a:Code>T_CDP_PublishApp_H</a:Code>
<a:CreationDate>1357779615</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o534">
<a:ObjectID>2A9531F2-C315-46DB-BA49-00BC5502147F</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o535">
<a:ObjectID>856982DB-D5C3-4235-8C7A-B04C90F367DF</a:ObjectID>
<a:Name>应用发布内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1357779691</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o536">
<a:ObjectID>122DA016-4CCF-40C8-9637-89E3A00CAE8B</a:ObjectID>
<a:Name>审批结果</a:Name>
<a:Code>FSTATUSH</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o537">
<a:ObjectID>F69EC850-137B-4764-A11C-B69C7EA42961</a:ObjectID>
<a:Name>审批意见</a:Name>
<a:Code>FAPPROVEREMARKSH</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o538">
<a:ObjectID>2CA47508-C2C8-42DF-A8AC-743129C9A59C</a:ObjectID>
<a:Name>审批时间</a:Name>
<a:Code>FAPPROVEDATEH</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o539">
<a:ObjectID>508CFCC2-693E-4CD0-BFFD-0209E70D5E38</a:ObjectID>
<a:Name>审批备注</a:Name>
<a:Code>FAPPROVENOTEH</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o540">
<a:ObjectID>8E3ED9A8-443D-4EA9-A4B6-297DE914BB71</a:ObjectID>
<a:Name>审批人</a:Name>
<a:Code>FAPPROVEIDH</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o541">
<a:ObjectID>B13C67E3-9B25-4917-9E09-ACFF1F69F70E</a:ObjectID>
<a:Name>审批人姓名</a:Name>
<a:Code>FAPPROVENAMEH</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o542">
<a:ObjectID>D16CB9A9-7686-4E07-8389-3AE9F643D3A8</a:ObjectID>
<a:Name>应用生效日期</a:Name>
<a:Code>FSTARTDATEH</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o543">
<a:ObjectID>B55EB68B-524D-42BF-BA61-3D054CF0E2DC</a:ObjectID>
<a:Name>应用失效日期</a:Name>
<a:Code>FENDDATEH</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o544">
<a:ObjectID>4484080A-DACF-473A-A5B6-CD8B32E6F752</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1357779842</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o534"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o544"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o544"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o278">
<a:ObjectID>51E17CE2-FDED-4FBA-83F7-70CE47B54EC1</a:ObjectID>
<a:Name>应用发布-行业</a:Name>
<a:Code>T_CDP_PublishApp_I</a:Code>
<a:CreationDate>1357779613</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o545">
<a:ObjectID>CADF4859-ADC9-4D91-9BF1-65C7F255BF3D</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1357779701</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o546">
<a:ObjectID>D9F06AF6-DD0C-4143-BEBD-A67F05E56410</a:ObjectID>
<a:Name>应用发布内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1357779685</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o547">
<a:ObjectID>60A54DD1-21EE-4EC4-B04C-454A86901BDD</a:ObjectID>
<a:Name>行业内码</a:Name>
<a:Code>FIndustry</a:Code>
<a:CreationDate>1357779701</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o548">
<a:ObjectID>F077C215-49F5-4D80-981A-75C53096829A</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1357779701</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o545"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o548"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o548"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o277">
<a:ObjectID>1FAEB021-2EFB-4A06-8293-A632F1FFA6FF</a:ObjectID>
<a:Name>发布应用</a:Name>
<a:Code>T_CDP_PublishApp</a:Code>
<a:CreationDate>1357723998</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o549">
<a:ObjectID>BC7CE5FE-808B-4F66-966F-54DE6D51D871</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357779577</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o550">
<a:ObjectID>1D97B6AF-45CA-48D2-9607-858E5612A365</a:ObjectID>
<a:Name>应用简称</a:Name>
<a:Code>FNumber</a:Code>
<a:CreationDate>1362562076</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1362562155</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o551">
<a:ObjectID>3F3ADA83-8020-4096-8830-D28F62781C5C</a:ObjectID>
<a:Name>应用名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357779577</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o552">
<a:ObjectID>4E259807-4149-493D-AA28-3271CA1F6D08</a:ObjectID>
<a:Name>申请日期</a:Name>
<a:Code>FAPPLYTIME</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o553">
<a:ObjectID>17A45D8F-676E-4238-9E0B-1DAAEE2749DA</a:ObjectID>
<a:Name>申请人</a:Name>
<a:Code>FAPPLIANTID</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o554">
<a:ObjectID>1986425A-0948-4D47-88A2-336C3F43F8C6</a:ObjectID>
<a:Name>申请人姓名</a:Name>
<a:Code>FAPPLICANTNAME</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357779577</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o555">
<a:ObjectID>79557582-8341-49C5-8E2D-749F28A4E4EB</a:ObjectID>
<a:Name>项目内码</a:Name>
<a:Code>FPROJECTID</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o556">
<a:ObjectID>3A5EBC51-531C-498C-AB63-175A0EDBD7F2</a:ObjectID>
<a:Name>应用来源</a:Name>
<a:Code>FDEVELOPETYPE</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o557">
<a:ObjectID>CDC70F93-EE8B-4796-BF13-E68231DC8C93</a:ObjectID>
<a:Name>联系人内码</a:Name>
<a:Code>FCONTACTID</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o558">
<a:ObjectID>B56C77DD-3B9A-4260-A862-7D97CD1AFA38</a:ObjectID>
<a:Name>联系人手机</a:Name>
<a:Code>FCONTACTIDMOBLE</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357779577</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o559">
<a:ObjectID>F416A7FF-7A3F-4634-8DD5-0E9CF17DA219</a:ObjectID>
<a:Name>联系人姓名</a:Name>
<a:Code>FCONTACTNAME</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357779577</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o560">
<a:ObjectID>6F167A60-683E-4187-A1EE-19F091506BAE</a:ObjectID>
<a:Name>构建版本</a:Name>
<a:Code>FPKGID</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357779577</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o561">
<a:ObjectID>CE6DC321-AAB5-40DC-8E8A-02A40C488C7B</a:ObjectID>
<a:Name>产品类型</a:Name>
<a:Code>FPRODUCT</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o562">
<a:ObjectID>1329BF0D-1C14-488C-848B-A8B178759F8B</a:ObjectID>
<a:Name>适用版本</a:Name>
<a:Code>FVERSION</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o563">
<a:ObjectID>4EFA51CE-B10D-41CC-9CD7-D698537E43C7</a:ObjectID>
<a:Name>版本说明</a:Name>
<a:Code>FNOTE</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357779577</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o564">
<a:ObjectID>DEFA5655-714E-40A8-B6CF-F7911A5761CA</a:ObjectID>
<a:Name>应用简介</a:Name>
<a:Code>FREMARKS</a:Code>
<a:CreationDate>1357724013</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357779577</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o565">
<a:ObjectID>6611611D-B5CA-410D-BD38-F04F12912C3B</a:ObjectID>
<a:Name>是否可以体验</a:Name>
<a:Code>FCANPRACTICE</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o566">
<a:ObjectID>938228D1-4500-4D5A-A223-AEDFDBA553B3</a:ObjectID>
<a:Name>标题</a:Name>
<a:Code>FTITLE</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o567">
<a:ObjectID>439B77AC-05E7-4558-A52E-412EC7B1866A</a:ObjectID>
<a:Name>关键字</a:Name>
<a:Code>FKEYWORDS</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o568">
<a:ObjectID>28D10D88-2D6C-4667-9FF0-1E0687C43FAF</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o569">
<a:ObjectID>5E06C0DB-A19F-4F83-8179-3223408B5DD0</a:ObjectID>
<a:Name>应用详细</a:Name>
<a:Code>FDETAIL</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o570">
<a:ObjectID>8B005CB3-35E3-43EF-BDE6-B6D1A50A946E</a:ObjectID>
<a:Name>所属企业</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o571">
<a:ObjectID>4C244519-1890-40D0-9A3B-B84CA5F3F0A1</a:ObjectID>
<a:Name>应用提供商</a:Name>
<a:Code>FAPPORGNAME</a:Code>
<a:CreationDate>1358925986</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1358926125</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o572">
<a:ObjectID>3FDA4647-450A-4168-AD7A-23317724464A</a:ObjectID>
<a:Name>信息状态</a:Name>
<a:Code>FINFOSTATUS</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o573">
<a:ObjectID>E5B86AEA-A52A-474E-A567-2F27A812395D</a:ObjectID>
<a:Name>审批日期</a:Name>
<a:Code>FAPPROVEDATE</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o574">
<a:ObjectID>D46FD66E-31B0-4E8E-B065-6D5017B7963C</a:ObjectID>
<a:Name>审批结果</a:Name>
<a:Code>FSTATUS</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o575">
<a:ObjectID>7F8FD5DB-77B5-4013-A480-934D5ED94BC9</a:ObjectID>
<a:Name>审批人</a:Name>
<a:Code>FAPPROVEID</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o576">
<a:ObjectID>6CE693E8-DE23-4F6F-BB75-0BFC183A75C2</a:ObjectID>
<a:Name>审批人姓名</a:Name>
<a:Code>FAPPROVENAME</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o577">
<a:ObjectID>92CF313F-D201-4C4C-9BF9-5D6773E1E872</a:ObjectID>
<a:Name>审批意见</a:Name>
<a:Code>FAPPROVEREMARKS</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o578">
<a:ObjectID>216F67D1-27FD-4EBE-B338-5EC6D4B45CD9</a:ObjectID>
<a:Name>应用生效日期</a:Name>
<a:Code>FSTARTDATE</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o579">
<a:ObjectID>A7825CA7-397D-4E62-AF29-FC80C0A6D593</a:ObjectID>
<a:Name>应用失效日期</a:Name>
<a:Code>FENDDATE</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o580">
<a:ObjectID>79F1CD32-9CEF-40BE-95DB-3415543BE2BD</a:ObjectID>
<a:Name>审批备注</a:Name>
<a:Code>FAPPROVENOTE</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o581">
<a:ObjectID>42F403D8-46EA-47DD-AE90-076F8C32CADC</a:ObjectID>
<a:Name>应用LOGO</a:Name>
<a:Code>FLOGO</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:DataType>nvarchar(400)</a:DataType>
<a:Length>400</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o582">
<a:ObjectID>DA087D35-AAD9-47D3-80E5-14D263797E77</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1357778753</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o549"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o582"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o582"/>
</c:ClusterObject>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o198">
<a:ObjectID>38438672-5078-4F60-AEF9-E8785AEDB5D5</a:ObjectID>
<a:Name>Reference_4</a:Name>
<a:Code>Reference_4</a:Code>
<a:CreationDate>1333260256</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1333266185</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o241"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o247"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o394"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o583">
<a:ObjectID>AFCE58B1-4B43-40B8-BA30-5DA313C99B09</a:ObjectID>
<a:CreationDate>1335164194</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1335165671</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o377"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o396"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o210">
<a:ObjectID>36CFC585-BB66-492A-8477-0F0BF9F60850</a:ObjectID>
<a:Name>Reference_10</a:Name>
<a:Code>Reference_10</a:Code>
<a:CreationDate>1347591899</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o273"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o274"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o415"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o584">
<a:ObjectID>EBF34315-C130-4128-9258-4A729647EF47</a:ObjectID>
<a:CreationDate>1347591899</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o411"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o417"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o206">
<a:ObjectID>86A06AA5-6657-422C-9F3D-AD69E9FF432A</a:ObjectID>
<a:Name>Reference_5</a:Name>
<a:Code>Reference_5</a:Code>
<a:CreationDate>1347591621</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o271"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o272"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o425"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o585">
<a:ObjectID>A01FFAC3-9863-4A4E-9BAF-E7ADE35A049E</a:ObjectID>
<a:CreationDate>1347591621</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1347592130</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o421"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o427"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o213">
<a:ObjectID>B6163D5E-2D51-42E3-B31F-5DAFEA51934F</a:ObjectID>
<a:Name>Reference_11</a:Name>
<a:Code>Reference_11</a:Code>
<a:CreationDate>1357723075</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o246"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o267"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o480"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o586">
<a:ObjectID>E4C905E7-60E7-4CD5-8829-F1CED02D5D85</a:ObjectID>
<a:CreationDate>1357723075</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o453"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o431"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o216">
<a:ObjectID>C6705ED6-62E1-4442-B395-2AF97139BD47</a:ObjectID>
<a:Name>Reference_12</a:Name>
<a:Code>Reference_12</a:Code>
<a:CreationDate>1357723089</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o246"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o269"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o480"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o587">
<a:ObjectID>02138748-7DD3-4166-B6CF-C514A2A480FD</a:ObjectID>
<a:CreationDate>1357723089</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o453"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o408"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o219">
<a:ObjectID>A24132FF-D9D8-4C7D-9F16-5D7E19CA1B21</a:ObjectID>
<a:Name>Reference_13</a:Name>
<a:Code>Reference_13</a:Code>
<a:CreationDate>1357723098</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o246"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o270"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o480"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o588">
<a:ObjectID>CDD076B9-D12C-4A64-8D95-C4CD7347A6D1</a:ObjectID>
<a:CreationDate>1357723098</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o453"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o404"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o222">
<a:ObjectID>230EFF5F-7D3E-40D4-9E4C-F5717BC75000</a:ObjectID>
<a:Name>Reference_14</a:Name>
<a:Code>Reference_14</a:Code>
<a:CreationDate>1357723199</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o267"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o268"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o436"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o589">
<a:ObjectID>69DFCE7B-9380-46C6-AF57-05B1229E8BFD</a:ObjectID>
<a:CreationDate>1357723199</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o432"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o437"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o224">
<a:ObjectID>17903ED1-2650-481C-B7CD-0199B04B842D</a:ObjectID>
<a:Name>Reference_15</a:Name>
<a:Code>Reference_15</a:Code>
<a:CreationDate>1357723310</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o271"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o269"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o425"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o590">
<a:ObjectID>5767A8FD-687E-49D6-98C4-8EFD412E7F72</a:ObjectID>
<a:CreationDate>1357723310</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o421"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o409"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o226">
<a:ObjectID>103A943B-89DA-4EF5-8A1A-E85655E6B80D</a:ObjectID>
<a:Name>Reference_16</a:Name>
<a:Code>Reference_16</a:Code>
<a:CreationDate>1357723317</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o273"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o270"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o415"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o591">
<a:ObjectID>CF0CA2B3-1E2A-48CC-94B5-D419E4EFA215</a:ObjectID>
<a:CreationDate>1357723317</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357724635</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o411"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o405"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o202">
<a:ObjectID>50AC7B5D-B5E0-41F2-AD9B-81F5945D5682</a:ObjectID>
<a:Name>Reference_9</a:Name>
<a:Code>Reference_9</a:Code>
<a:CreationDate>1336035031</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o246"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o252"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o480"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o592">
<a:ObjectID>48DAA61D-635E-4F42-BE24-7EC4B03D6479</a:ObjectID>
<a:CreationDate>1336035031</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1336035119</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o453"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o306"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o240">
<a:ObjectID>1FE3985D-D080-4EA7-93E6-635CAEB40343</a:ObjectID>
<a:Name>Reference_20</a:Name>
<a:Code>Reference_20</a:Code>
<a:CreationDate>1357784027</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o281"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o282"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o511"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o593">
<a:ObjectID>569F49BA-77CF-40F8-8EDD-4C4EEC7073FC</a:ObjectID>
<a:CreationDate>1357784027</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o481"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o512"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o230">
<a:ObjectID>D468AA9B-CD2C-4D2F-84DA-8936C7D76183</a:ObjectID>
<a:Name>Reference_17</a:Name>
<a:Code>Reference_17</a:Code>
<a:CreationDate>1357779685</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o277"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o278"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o582"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o594">
<a:ObjectID>F44F0566-E166-4423-80FC-E7CB7546E266</a:ObjectID>
<a:CreationDate>1357779685</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o549"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o546"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o233">
<a:ObjectID>2212CBED-64A2-4CEF-88FE-4EA5AA2B11ED</a:ObjectID>
<a:Name>Reference_18</a:Name>
<a:Code>Reference_18</a:Code>
<a:CreationDate>1357779690</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o277"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o279"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o582"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o595">
<a:ObjectID>44B2C792-D156-4058-8188-11BB67328D1A</a:ObjectID>
<a:CreationDate>1357779690</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o549"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o531"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o236">
<a:ObjectID>0DC620F6-B8AD-4AA8-951F-EB59DEA6298A</a:ObjectID>
<a:Name>Reference_19</a:Name>
<a:Code>Reference_19</a:Code>
<a:CreationDate>1357779691</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o277"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o280"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o582"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o596">
<a:ObjectID>D486873E-1FEA-4B7A-AAA3-BC07E5C1AD59</a:ObjectID>
<a:CreationDate>1357779691</a:CreationDate>
<a:Creator>rol_shen</a:Creator>
<a:ModificationDate>1357784503</a:ModificationDate>
<a:Modifier>rol_shen</a:Modifier>
<c:Object1>
<o:Column Ref="o549"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o535"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o6"/>
</c:DefaultDiagram>
<c:DefaultGroups>
<o:Group Id="o597">
<a:ObjectID>D90E10F2-745C-4E2E-B49B-6169358B530D</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1331628228</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1331628228</a:ModificationDate>
<a:Modifier>haifeng_hua</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o598">
<a:ObjectID>693A5237-96CB-4DBF-996B-FBA68FC70F9A</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1331628228</a:CreationDate>
<a:Creator>haifeng_hua</a:Creator>
<a:ModificationDate>1350621663</a:ModificationDate>
<a:Modifier>weixy</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/sqlsv2k5.xdb</a:TargetModelURL>
<a:TargetModelID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>