<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1448012647" ExtractionBranch="1" ExtractionDate="1448012647" ExtractionId="613" ExtractionVersion="109" ID="{F4B68CBD-D456-4B30-9FED-FDCA19175A59}" Label="" LastModificationDate="1435285813" Name="SRM供应商协同管理" Objects="754" RepositoryId="{204F22CD-E48A-45EC-A1CE-6B2B8788870D}" Symbols="26" Target="Microsoft SQL Server 2005" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>F4B68CBD-D456-4B30-9FED-FDCA19175A59</a:ObjectID>
<a:Name>SRM供应商协同管理</a:Name>
<a:Code>SRM供应商协同管理</a:Code>
<a:CreationDate>1393208215</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1407463052</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=0
RefrUpdateConstraint=0
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=C:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=

[ModelOptions\Default Opts\FRMEOBJ&lt;&lt;ResourcePool&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMEOBJ&lt;&lt;WorkloadGroup&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMESOB&lt;&lt;ResourcePool&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMESOB&lt;&lt;WorkloadGroup&gt;&gt;]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {204F22CD-E48A-45EC-A1CE-6B2B8788870D}
MODEL_ID 613
MODEL_VRSN 109
BRANCH_ID 1
EXT_DATE 1448012647
EXT_END 1448012647
OBJECTS 
BEG_BLOCK 
 613 {F4B68CBD-D456-4B30-9FED-FDCA19175A59}
 623 {90659D3D-9F7B-42CA-BDB7-E7D0B536F61C}
 624 {596A7943-697D-49C0-8D1F-157C3620F969}
 625 {758E9E6F-94CD-4707-83C4-A0AABF017EE9}
 626 {F2BFFF5E-A270-40F0-B34D-03FED765AACB}
 627 {D69DA1ED-5713-40A3-8790-D976857E374C}
 628 {2A73C605-A712-41DC-ADCF-F779C209F073}
 629 {12FC884C-7A98-4D0B-A077-6A882452EAFD}
 631 {467CF773-98A4-4D7B-88DF-4D03BB51624C}
 633 {EDDF5C70-8AA9-4F23-8D6A-B8F9735D707C}
 634 {63392BFF-9C2B-42CB-8029-2F7F51F63530}
 635 {00605373-EA9B-4524-A435-A7CD195D84AA}
 636 {4F061E70-D2F5-47D8-B7E3-1317C4EFD819}
 637 {06D6999A-C2A7-4292-8660-5EDBBF0822CA}
 638 {C96944AF-B664-4978-A044-C0B012BC035E}
 639 {D48D7D2C-044B-42F7-9D97-5882983924DB}
 640 {F735DA35-4E7F-4F4A-BD37-FF8AFF07BEFC}
 641 {2F6EB8DA-CB8A-4F97-8EDF-13930593FC0E}
 642 {8C2AF297-CFF4-4172-84F8-2837CF5C1223}
 643 {4196F1D1-19B2-4AB4-8B7E-0B363663E0E8}
 644 {80F89FE8-1886-4405-A92E-092CC49F4E4A}
 645 {36F6319C-8E92-437E-9D94-56E229DE0D90}
 647 {AF3E2EDF-A375-4BE2-AFAA-08B169B68535}
 648 {CA90DB4E-C38F-4E69-BCE1-DAC8713B875C}
 649 {8DC7B4C8-FBF0-49E5-8FF6-7B23AE977605}
 650 {BA674544-B30D-4881-A9F1-E9D26FFA3C9C}
 651 {232F12BD-C26D-42D8-AF92-177135663333}
 652 {FB94A3E4-F1AB-414D-AC3B-57F40608DF2E}
 653 {26BD7055-60F2-47A1-A280-9BF104A5FC30}
 654 {10D1B44A-42D2-4A56-8605-013865270A12}
 655 {847FCF00-F21C-4E2C-B019-4A904C5284B6}
 656 {0EC61441-BF98-4D28-89C4-884FEDD05C38}
 657 {8702BF4B-BDB3-4788-9E5E-3F4D6D50D6B9}
 658 {1C2C693A-7D8F-43F6-86A9-0CA3D2A329E7}
 659 {07EB9A4F-36D3-4D23-9879-16CD185A2C2B}
 660 {6C904705-87E3-456D-9EFB-FF256EAD9629}
 661 {C34EFDB3-3A9D-4854-94A0-A0390F4BE413}
 662 {2AEB55D3-2ED7-41B2-BD50-6D458A825DDB}
 663 {8C7D39BE-7BA7-4B1F-B638-F01A5662731B}
 664 {363532C0-9F57-461B-B095-40C0DBB76584}
 665 {91872C2C-326D-4DC3-B6B2-7192EF48BA24}
 666 {33E2ED9E-BB0E-4148-9E2D-4B4B13D75A2E}
 667 {A9E8ADE8-98ED-41FC-847F-148A3C355544}
 668 {C7EDC7E9-AF85-41DD-8241-2991B012FA28}
 669 {E78D26BE-1A3A-41A2-8B11-6130834FFC17}
 670 {C052CE4A-9232-4BE7-A103-123630D3E60C}
 671 {368D75D6-A596-4430-A2B1-FA9063362A56}
 672 {F069DE15-F380-45FE-8125-14A6300BDC75}
 673 {FE1F11FD-F16A-400F-8B0C-0D5E65F6B21A}
 674 {37AEF00F-1AD9-4F7B-A49F-C35B95A3B22B}
 675 {7E8E3375-03F8-4E59-80BB-7DEB7170544A}
 676 {27E4AC70-4DAB-45DB-9E96-486748FD7CB5}
 677 {04A1A420-062D-4BC8-8F8E-73A08C180CF0}
 678 {6305BB79-EB2F-4F2B-881E-A72A9B1EC98D}
 684 {2E851D45-E4E7-4737-8EA1-5BC06F2CD942}
 685 {EE1C8ADD-569C-4F36-8E4E-3D58FA4316ED}
 686 {DA284784-5804-4FEB-80D2-D121005DCB48}
 687 {DCA63045-83DD-4FB1-BEF5-FC0D479A039E}
 688 {F1E210C4-A978-438C-8AAA-FD9ACBA33141}
 689 {8D9D9186-3B39-4E40-97E4-53E90CD1B848}
 690 {2C4CAD10-C7FF-4A6F-A8CD-8E5B3DBB1C8F}
 691 {2EFA24A7-27C8-42B0-B6A4-9FE7554D8C98}
 692 {E27B5949-4A72-4BF8-B4DE-3E36235FC7F5}
 693 {DDF665CA-1DF4-4D73-8FE5-CACDAFFD7783}
 694 {1903E9B7-3845-4828-86AD-C85FEE7695FA}
 695 {A55D5A44-7237-4EF9-BC30-597C27E1F989}
 696 {8D84D413-899E-4CFF-8D8F-F391CD1572BD}
 697 {F2E66863-10DD-44BB-8481-852D628B94CF}
 698 {75044067-50AD-4B77-9CD1-D8249E933C75}
 699 {C24D0079-C974-4B95-B0FE-2AF78F40C51F}
 700 {93704228-3191-45BE-9394-E44AA37B8AED}
 701 {BE6E8999-76B9-4812-B575-DFF0DA1AF2FA}
 702 {297C3BA4-9AF4-4F87-BC4C-68C40B0AEAA0}
 703 {A1896043-1C7C-4267-9284-BBC028F5507E}
 704 {725E6E7C-B0E1-4E61-92DD-589AB9B43273}
 705 {0C1CF114-97D3-45ED-8148-C375C31A33E0}
 706 {68730C17-01B6-4891-8596-930FD201068D}
 707 {259E0139-90F3-4B97-A801-5F2B13FB8886}
 708 {181D9B0B-526E-4990-8749-FB84D09856AB}
 709 {6984010D-1FD8-4060-BB8F-B32D5A9F4923}
 710 {26CD18A5-371C-4383-8914-9626135C73CB}
 711 {D36BCAC6-E553-464A-A5CC-102C55CDFA76}
 712 {C888DE4D-BC62-41B7-AC54-94C31BC81239}
 713 {44F6FE9D-3A6C-478D-9278-22476BE32299}
 714 {08EA768E-7A98-4210-ABFA-29EC2F5FD6D2}
 715 {E5A3A201-B9EC-414C-ABCD-ADD076385A8F}
 716 {B184C1DC-6379-4250-93C6-1CAA8C9F6968}
 717 {84BAD2E6-02CF-40CD-8644-CAD96E9612A9}
 718 {20E3CFEB-9130-47C7-832F-C7DF38AA7FEA}
 719 {F4597921-5FA9-4E3F-AEFC-96E6B933E715}
 720 {0BFCE3DF-DCDC-4D6F-A4A5-845813A7CBCE}
 721 {4784B15C-8873-4B12-9B1C-23FDF598385E}
 722 {CE37EFC0-77DA-46BD-AE3F-A2C9D25331B5}
 723 {E89EA3D3-184D-4480-9DC4-8EE5842EE427}
 724 {8513D86A-1E87-4702-A6E6-71D9C1672678}
 725 {A4B87735-2276-43AC-BB68-405E0290117B}
 726 {105011DF-F665-4A28-A071-E2A1701F9B90}
 727 {A7E512E9-D907-40AE-B52F-2712C5AEABB0}
 728 {BEE14EEB-D25B-40A3-9350-180EB7A44B02}
 729 {3E62D391-AF1A-48D7-B903-C275FAB46D5E}
 730 {F9490419-1C26-44FE-8E4D-A9C07677DB48}
 731 {918F793A-36F2-484B-9E09-2E0B31B24D88}
 732 {3F3BD815-5DCB-4FAC-9C0B-834AC0957D18}
 733 {9F8CA620-7767-4B75-93A2-51DA9EEA3A6B}
 734 {FC3D8049-CC97-4AE3-B3E1-6394DBD2B31F}
 735 {2C13D38A-E0AE-4428-9D7B-5FA01CFAEAFC}
 736 {6BA24D8D-71F8-4577-ABAC-515A7777B489}
 737 {9A0D81D6-ED94-4E1E-87B0-CE4CF2A4B1AC}
 738 {171B8E00-3C67-4913-A5CA-2D2C74D8590D}
 739 {ADF42F0C-B934-4AB3-88F8-7433E8DD522D}
 740 {4B441CE7-B28A-4776-8D68-E2E06A667FFE}
 741 {C7330AC1-C4C2-42F6-81CC-D76758F74179}
 742 {E46998E9-A815-427C-9165-2459163808D1}
 743 {30FB33E1-4147-473B-8F42-FD3EB9C536AF}
 744 {3193E86D-B311-4626-9B57-29AFF30BC4C5}
 745 {A15F0B4B-D4B4-4AD4-A707-CD9CD076BC75}
 746 {93E84A50-96C2-447E-8578-8C1F3274F94A}
 747 {9179091A-4E46-4F78-BC05-91E411A7F5FA}
 748 {0FF6816B-0CBF-4827-A509-ECF39C3B4DF2}
 749 {7D79AD6A-590E-40EE-98FF-050E1A6FC734}
 750 {E5470965-2001-4D5A-B8B5-6D22A5A16FE9}
 751 {3244AE39-2684-42C5-9D63-7A6EAC10CACF}
 752 {7595C76E-3587-4AC2-89DF-F13D0AF3C6B7}
 753 {3F785483-E337-4B15-B407-A86220827682}
 754 {FBCCA3B0-1AEA-46FF-B90C-5ECAF00B8AEC}
 755 {64915AFF-AC38-489A-9FD8-DA2CCE093311}
 756 {8ED9D033-7C67-416A-950B-2F26B9D174A7}
 757 {B0F655E8-1F09-4AD6-A3F3-9227C8294F90}
 758 {C2049E7E-8577-414C-9F36-52E446E1B42A}
 759 {9243A42E-A26A-49AD-B3CC-544CAED51A9A}
 760 {5A98724D-AAC5-4B5D-8BD5-F4F2B83C065F}
 761 {6B143576-DF6F-451C-95CE-D173C9973994}
 762 {7E2E6EC8-B8E5-403B-883C-0C38321512A4}
 763 {C733FE1E-24F6-4475-A997-8A7795914D2E}
 764 {440B5D34-8B95-4C36-B85A-C54B81B58278}
 765 {D18FBC7F-00C6-4B45-8A81-D15D93BD3972}
 766 {F62503D7-6A65-4F53-AC30-E6536824EEAF}
 767 {CF737EF8-E1E8-4F50-9970-CE2F07686048}
 768 {E90A3CF9-D449-484F-A75F-6DA6B9803367}
 769 {2B00B905-F016-4CF9-9707-BEFCCD3310FF}
 770 {DA954DF6-99F9-427A-B69C-FC9038979F60}
 771 {0EE1B592-F7CA-47C4-960E-AB60984C6990}
 772 {59AAFDBE-A028-41DA-864F-2F864C27ABE6}
 773 {E2590FB8-5285-49AD-B29D-8E9E639E3D6F}
 774 {4687F6DC-7D95-4DD5-8AEF-A881B82629E9}
 775 {C5BDF456-F518-4A85-9F97-A9E5BADC2F9C}
 776 {8A68176E-CAAE-4D0C-8044-62BC881F595E}
 777 {D4468469-92AF-4DD4-A453-EB0DAF9E60DF}
 778 {D42B517B-85FE-42FF-AD3E-6BE34B390E76}
 779 {2188FB7E-4B86-4D36-BBB1-549ECE6F932B}
 780 {F120EFA5-B13C-42E9-92E9-A53EB33086F4}
 781 {24B9994A-FFE1-40A6-847F-D98919F6F2C6}
 782 {FE6834B5-482D-4415-8831-3D15E0C4D39D}
 783 {27BF802C-DF0E-4ABC-96E6-652A1ED0BE4E}
 784 {CFA63028-C85C-43EC-9C02-A810C03012FD}
 785 {D8D1967D-C6D9-416E-BC8D-16FFEBEBD4A3}
 786 {8B1A0951-FAED-45DF-A95C-81E8BA50650B}
 787 {D128C973-A3B8-40F8-9DEB-1CB0BFBEF5DB}
 788 {C86AC6E4-D2B3-4AB3-8C77-DA77E84AE408}
 789 {9C1588A3-3DD8-45ED-9AC6-B5658B6951A8}
 790 {8A5857A9-C5C3-466E-A100-4EA90DA10D47}
 791 {BF4213BB-D0F4-46C5-9CA3-03CFB831824B}
 797 {CC1AEE43-DB3E-44F2-891C-D3AA492863B5}
 798 {74C5EC35-A584-42F2-B49F-D0BFFF6F8799}
 799 {069BA624-7171-4C88-A0AF-BD29A70042F3}
 800 {9D2AC2F9-69F4-41E9-96D4-10AC7C42ABB3}
 801 {5AFADA62-FA37-4222-83BC-BCB399B5583E}
 802 {559DFF46-222C-4BB2-A1EB-762658628AF8}
 803 {6BF2E89D-219C-4CAC-9CF0-A60472B2AAD2}
 804 {00D1F655-D1F0-4234-AFA3-802DCA28E466}
 805 {7B895B94-373A-4FF1-BE51-A8102CA83122}
 807 {5D4E00A8-D9FB-402F-BA4F-E7A2ADC90B37}
 808 {3048CD6D-7583-4783-9387-1E3B099CC67A}
 809 {35EE3DD5-7370-4D50-B6D3-EF082E5345D8}
 810 {82224532-0C34-4DB6-A8D2-806A16613D76}
 819 {42B0A135-0BA3-40F2-BD26-0CFC5B1C9735}
 820 {18BC38A6-7EAA-4784-84A4-447BFF79985E}
 821 {B6356D70-611E-4C77-A645-3A0F1D9E6D1A}
 822 {FBBA561B-87D4-4AE2-B5E3-49C92F70F785}
 823 {50AE46BC-6F24-49A8-BD47-70321FA5F260}
 824 {ECA1652B-9B12-4828-BF42-A9EBEC731DDB}
 825 {A420B498-5FD1-4126-A43A-08C06C04C1BB}
 826 {D8E224F9-B347-41F1-92D4-4DA6928AEEF5}
 827 {86571AE5-A50F-4568-801A-07260DD7BB06}
 828 {450CE90B-0620-4C35-AC26-CA13553ABAFB}
 829 {8A8904E0-F186-4ECB-9644-3C69A7CC4352}
 830 {889D20A9-A978-4647-821A-6EEA816A32DA}
 831 {2C0B9F5A-FBCE-43F0-A1DA-ADF9E7C32C5C}
 832 {D3FBF9C3-7F59-435B-A220-3E9F98FC1C11}
 833 {E99C60E3-8861-4D37-AE77-037775F47409}
 834 {4F06CC0A-4FC2-4CDF-9EB5-6FA14E77BFC3}
 835 {86E98EBC-47E0-46CA-A06B-C6C2248AA5F5}
 836 {54D48804-AF0C-431D-AA43-ABB812DD15A6}
 837 {83E555F7-A5B0-4285-91FB-5E02FA24C33C}
 838 {4794E846-A6E9-40E2-930C-509B1942D187}
 840 {EB04967F-3552-4B61-9E48-26C802054E46}
 841 {50D14AE0-AC63-44CF-BD13-D2AB0CB94D11}
 842 {72A0D622-6C11-4104-A6C0-E3BC03A0EA78}
 846 {BDA087BF-5E92-47B4-9709-AFDEE74B197F}
 847 {589B7DC8-1448-4848-8630-7184D61983F3}
 848 {4AD7864F-303E-4A2C-8658-A074C00EC6F4}
 849 {226C4A9A-3136-431E-A484-296895488DF4}
 850 {3FECC97B-79DF-4C2C-A522-B32EE5CD3DDB}
 851 {DAA8C02D-2B9D-47C0-8DBF-720789CC4D53}
 852 {263F127C-DF6F-4FAA-BC55-80EA952775D4}
 853 {D441E2CC-A420-4EAA-9BEE-79A27E70DB56}
 854 {76C74D91-4DD4-43FA-B153-A25679029C42}
 855 {487253FA-4F8C-438F-B58C-A6CD9165477A}
 856 {81C148E5-2E71-47D6-B993-D6A799E67AA7}
 857 {799CCC56-9A23-461D-9322-EDA7F7604025}
 858 {2A8F0474-F0CB-4905-9865-62F5F0A0D7CB}
 859 {E50A67EA-2502-4A01-9FB3-7601EE33C9B0}
 860 {25CA85A6-2233-4221-BA76-36538C94F1DF}
 861 {D508E306-D110-44BC-9361-CE5C9E22D669}
 862 {65F067BA-7793-41B2-8B02-C8779382BD48}
 863 {102B9AAC-322F-4847-87F7-59E9EE581979}
 864 {E1D7A675-18A5-4296-A5A5-9C9815274C31}
 865 {34772101-8D38-46DC-A2FD-7A8C27849CBD}
 866 {B72E0502-9AD2-407A-8970-BC4B23268374}
 867 {E209C5B5-0098-40A8-BCCB-1BA89C04E7A0}
 868 {C8DE783A-4E2F-4FD9-8FB8-03B89BC39941}
 869 {A6A88D3F-0F0D-4322-BA82-4BE3DD2B351A}
 870 {C23A702B-635A-4261-BFDC-8AEDF6FFBE36}
 871 {B2BC74C6-DAED-4CA1-B71C-578CB93F4640}
 872 {FA4821D2-F97E-48AF-AC68-21D9368F2643}
 873 {55D385DE-FCF4-468A-BDE8-417D5A809E0F}
 874 {2F462F71-9149-4B16-AF9E-A2326F4153FD}
 875 {8722F486-98CB-43EC-A57D-0DA7790E4A86}
 876 {B07C6F06-60E7-4AAB-B572-E3E15DB71D02}
 877 {1967F353-C998-417D-89DF-D7D38801558B}
 906 {AB326DEB-130F-4DA7-8844-40B6CDD111F7}
 907 {C357490D-278B-4E27-8707-0A078376911B}
 908 {CF32E1BA-A9A3-44AB-BBD6-37988D00D2C7}
 909 {BC88D997-C9CA-4C92-ACF0-5465D9795752}
 910 {C18BA330-A59D-4125-952A-4E224AEDA20B}
 912 {5C456957-842E-423C-B669-0E517F4EC7C1}
 913 {60A75739-6664-47BA-9FA3-7CF90C28BC9D}
 914 {C953FA13-6ABD-4DD6-B629-BA3BA53248FF}
 915 {178F6E25-F519-49ED-81BF-85D84B8184E7}
 917 {DD4F6532-7DFA-4FA8-BA40-2E4804CDDC11}
 918 {7B496D33-A3AA-4409-9296-7B882C491B09}
 919 {43B99E93-EBD3-4E63-A653-EE59AAE81193}
 920 {0ADBFCDD-1C70-4506-BDEB-4BF1D17F065F}
 921 {BEAF3386-2458-4552-8CF8-EF053F3BF3F4}
 922 {B36F9BE2-3F32-4700-BA83-15B60C98F4C9}
 923 {80AA3A0F-38FF-449F-98AD-7C4C8530E89A}
 924 {2C7E3856-1E98-4B9C-A740-8E3E093166C5}
 925 {A0D8E064-9C18-4D61-9E2D-61EB3AD0C38B}
 926 {460F4A6A-A663-4ED7-9C7D-26046E99FFDB}
 928 {13873FEE-C1B5-4DE2-837B-0304F0BF70AD}
 967 {D17C9CB0-FCE0-4899-BC88-31C8ADA08CC5}
 968 {B13D7762-70A2-4A6F-BDD4-36273BBAB72C}
 969 {407AC88F-1C05-42B3-8D0F-11CA5695E50D}
 970 {4D0D28B6-0212-4D69-BF92-5BBE884023D5}
 971 {9D6D13BF-3402-401A-8773-583A431682A2}
 972 {2CD74618-911A-4A1A-80A5-72AC74607586}
 973 {CE94A083-AA65-44F7-A06F-49922070C311}
 974 {DFFDF3B0-CC24-430D-AAC8-63565B0A889D}
 975 {B884D1C7-A671-4FE9-9906-91DC4216714C}
 976 {001B89F6-E930-4158-8071-6D9A04F51DD1}
 977 {CDAAF98C-6A75-4AC4-BACE-68B17AE5966E}
 978 {67493640-B7C7-418E-87E3-617EC666EE3D}
 979 {019A0A2D-8AFF-4978-A44D-49CF9FD9D381}
 980 {6D2CC401-E6D4-4587-AA7B-93F8D9E48D04}
 981 {35B45FB9-9737-40F8-AD70-C19AB8C5D3DD}
 982 {5F6A8EDF-0233-4F3A-833B-FE65BEA23073}
 983 {44788D39-D2D4-4339-A55D-CD27613C09D8}
 984 {3C813592-0F0B-4786-8FC3-E470B6379922}
 985 {815AC18A-E5B8-4947-ABF8-78FCBC40C312}
 986 {91479C2E-5D67-4A2E-8B27-87E05715705F}
 987 {1971B8A7-D8B5-431A-AA63-7086CC678636}
 988 {DD668499-0EA7-4AC9-94D3-752EDDDC422D}
 989 {77CDF24E-1AB8-4B52-A51E-DB2D3387886B}
 990 {FEB6530A-F196-4592-9513-E2DF97BE36CD}
 991 {82D322F2-1B89-4311-9FCC-844649A53FE8}
 1025 {63DCFEBD-4CB9-4DB0-B4C9-48A9AF543130}
 1026 {2EF687E7-2E11-458A-8EBF-A178A3CA1182}
 1027 {3162C822-C0BB-4149-8382-40B2E6EFB9DF}
 1028 {21303067-F8AC-4CCA-ADAE-5FD422E09635}
 1029 {986CD649-FE12-4980-B638-5E718920C2F4}
 1030 {5D6186F9-3BF7-4A04-A0BA-9D3B04EB91D0}
 1031 {DF8EBDAC-C86C-4889-9759-0AF6B17B7E49}
 1032 {05F4795D-DD9D-4FE8-8CE5-93A208911458}
 1033 {0D02464C-A4A2-4AFB-B38D-CAB8B8CC4DDC}
 1034 {B5733AEF-470A-4B7A-BDCA-4DEAEA7B7105}
 1078 {8E6F722E-80A1-402F-B0B1-89633546AA75}
 1079 {ADEDD751-C29F-4091-AD39-599308D1B236}
 1171 {CC6A3191-32A8-4542-8046-A65F84D29608}
 1271 {9C622F89-D460-48D6-ADAF-5C544857958C}
 1272 {11FC6629-88EE-4D89-ACA6-5D1EA3D18705}
 1273 {A51B82BE-516E-44AB-A741-8ED886F18CB4}
 1274 {08261F82-7971-489D-A9F9-98B8A448C540}
 1275 {8A64089D-B9C8-4C2A-B14C-E05D69C83DB2}
 1276 {4EAF863E-2EB8-44E5-BE08-729B596BA8B2}
 1277 {46B6B05B-20A5-4643-A79E-1DFFA36FABFD}
 1278 {5F69636E-2DFB-423C-B2CE-FD3011A748D3}
 1279 {7DF0FA16-B970-426D-9CCF-20760A775812}
 1280 {25CF748F-0D84-4373-9756-75DB328A448A}
 1281 {51EAAE28-DEB5-4B0A-AB88-30C37E314563}
 1282 {B75932EA-D818-4872-8E57-0638C3BF0A05}
 1283 {B4218874-B127-4B02-A8B0-889CBDA3C202}
 1284 {D01D4597-2BC7-4D53-BF62-5F417BFC7211}
 1285 {A991F7DB-71F7-4C9A-849B-68CDB904B734}
 1286 {BE5ADABC-2A09-4AE1-84E6-D5EB841DBC66}
 1287 {DAE55CA4-866D-4959-A230-A00BD78FF444}
 1288 {C511BA58-850D-4F8F-8735-2C6EE4A95720}
 1289 {AA3DF1CF-45D9-4025-8686-B3212E718F88}
 1290 {15E7896F-5D81-4C3E-B998-36C9F208D8DA}
 1291 {867C1EBE-D965-493E-8F07-A6E278196379}
 1292 {CABEA107-8ED9-409A-873E-5FAA7EA6F6DA}
 1372 {D6794A9F-607E-4638-9666-F4DCAD808287}
 1373 {1C4E8C48-4A6E-4570-AE8C-410D7BBBE491}
 1375 {C76ABE6C-259D-4EC3-ABB6-309B9055065A}
 1376 {EE576050-D950-41D4-965A-4F49ECCAD155}
 1377 {D4EDE680-4F65-4E16-A472-6114FECEC806}
 1378 {88DA1B58-6D95-4473-BFE8-3E9F2DCFF780}
 1379 {49542B5C-1C79-4F66-9DAA-142A72FB963E}
 1380 {8160ADE6-FFA8-4FC5-AA19-F3D92B36279C}
 1381 {D86953D1-D3EE-47CA-BC15-3C851E93720B}
 1382 {FFF66353-3995-4B47-9CB3-3BEDB978900B}
 1383 {51C9A279-658E-413E-B3C7-C9B4BD2061B7}
 1384 {930E367F-816B-4341-B58F-08D76F6D1EB1}
 1428 {37970B6B-24D8-48F2-862C-73809C25E675}
 1429 {9F01A9B4-0D48-464B-97A8-BAFD4B3B9553}
 1432 {50CA46CD-92A2-4B8A-AF44-15624ECE2866}
 1528 {749588F1-1680-4835-AEF7-26A6B33015FF}
 1529 {7D8C8208-D427-4D3F-A80C-9DA9127B4BE3}
 1530 {FA9C9CBD-4D7E-4DC3-8817-B8BF52E4D717}
 1531 {4F3FD06D-4EE4-4E12-A828-959CBD0F04C0}
 1532 {39EDCB65-885E-4480-9090-E3F032F2DB6D}
 1533 {80B5F0CB-0F42-437F-B397-A4BF35702B15}
 1534 {9B7AEF19-59D3-4FD3-BC81-F9317C75A02C}
 1535 {FC7F4399-EA43-4834-9362-EA385A4F8463}
 1538 {3DAD00A1-4EDA-4A54-842D-42DAACD0C6F4}
 1539 {46032699-1DF4-4017-BAD3-10E6E95D9382}
 1540 {B1B4B00A-5246-49AF-9AF8-48C2463BCDD5}
 1541 {2C0BDB4B-ADFF-4BA0-A801-F5B22ECEFDD2}
 1542 {9FFAFDA1-CC7D-4CE3-BAD8-D35628D67DA6}
 1543 {9113F448-C632-4E49-814B-4489AE57634E}
 1544 {60E76D47-F72C-4776-819B-1FC624CAAFC7}
 1545 {AE4EB8DA-EF11-455E-AA65-B2C2FF97EEA4}
 1546 {3B98FB48-B26D-4C6E-BEE6-33B3775E7ECC}
 1547 {F58548F8-53DB-41E1-BAF9-75EDFF1DE287}
 1548 {4E0A19ED-416D-481A-B41C-2069F37F7F2D}
 1549 {D4AE022C-CC98-464E-A60B-6D2BC53D6042}
 1550 {1A3917D6-311F-4F4F-AA7F-5D7EE76D84F4}
 1551 {148E088F-F05F-4EF1-A22A-767E60406E15}
 1552 {026AB40A-337F-4D27-AEEF-D6524E4B656D}
 1553 {5226EE37-D688-495D-AD84-C52DAA987558}
 1554 {82170C96-A093-4A13-A45A-6614BF4FCFE1}
 1555 {D3F02CBE-8F1D-4E02-B658-8FD0050C90FF}
 1556 {2D8E5153-C0A2-486C-86B1-8018DD4ED576}
 1557 {EFC9FF4F-02B9-440A-BF7E-5E1D08D61283}
 1558 {485E30A4-18D8-43C9-8065-1039231D76DF}
 1607 {85042730-3314-4B37-B3B0-205450E755DF}
 1608 {F9E390AC-9A7D-4C33-9BB7-755581EE37A1}
 1609 {7C3096A9-2B86-44A9-9466-2824FCB67F1A}
 1610 {BE8387C9-9158-4FEF-8F22-18DD1860277C}
 1611 {32267E23-6A92-4A6E-973F-058EE63316AB}
 1612 {B5486200-8250-4641-8AD2-94A5C552AF1B}
 1658 {35B80D8E-4DFD-464C-AE8D-0528AC6DD6C0}
 1659 {B11FB7C8-2642-46F8-82FA-F4C845980A7A}
 1660 {C49C06F0-3779-4CA9-8777-2A4D73A13BF1}
 1661 {C02E8389-68D4-4569-BD5C-E0EF321E8C70}
 1662 {2699A16E-DDB9-454E-BCD4-8200ED3C951C}
 1663 {BC30492C-E24F-47BA-A3E6-8249AF55CF93}
 1664 {49B4C471-BBED-46A6-A793-6367F5EA6C3E}
 1665 {FE07B3E9-CD41-4993-A963-8FBAE3E71796}
 1666 {86DE4498-5282-4023-82F9-64067F395BE0}
 1667 {8069328A-E2B7-4476-AD57-5126327A3CD8}
 1668 {D0811350-9537-48A9-A336-880D935DB9F3}
 1669 {0FF7393D-1C58-4F06-9ABA-F0CA3D889D99}
 1710 {36D12435-9FE3-4678-920F-33B1AFC3186D}
 1771 {DD17C94C-78C3-4908-BB4F-9836B2FC3B5C}
 1772 {880BC3AC-AB91-4DB2-8CED-2E2179D27CF8}
 1773 {39931F0F-F9A3-4BE9-A32A-DE0B38A4CFC6}
 1774 {3E914A98-7D3F-493B-B1D7-3A5B34872818}
 1775 {36B01FF5-C8C9-4696-B399-267CEAA27BA6}
 1826 {8D33ADCD-0D62-4618-A9A7-C9B7E32C46BC}
 1827 {EB2DDF3C-9DBA-4C51-8D23-0269604603C8}
 1828 {3C9C5154-33BD-4839-83DC-EFC036FFA2F4}
 1829 {D68F54C6-37CD-4313-A59A-B84CCDDF30E3}
 1830 {94067731-E015-4136-AA6F-8345FA12AB87}
 1831 {34E0FEE9-9B64-446E-8CF2-519CAC757110}
 1832 {99A89113-50F8-47E1-A8A8-6C88035AD372}
 1833 {B7C669E3-684F-4103-90E1-4CE0615A6678}
 1834 {E95E940A-B5DB-46BE-A50D-11B5BE209056}
 1835 {341D3E62-5DBF-4585-8E66-F3E5EF9D0B12}
 1836 {49267BFE-372A-4920-8994-39428014F276}
 1837 {0FDCC4DA-C132-40FA-A70B-ED55A99AC943}
 1838 {9FE58172-304E-493F-8F1E-58287863B036}
 1839 {607D338A-8F0A-4868-9A35-5F346DAFC774}
 1840 {447D0B3C-EC99-4350-A3FC-4D141E49CD03}
 1841 {96E33437-453D-45DC-A779-0A358D955798}
 1842 {E383781B-3AB5-4513-BA49-21883B540990}
 1843 {EEFCFD34-C645-4457-B2B5-ACF496EDFFDA}
 1844 {18980B4F-6AA8-49BE-AA68-01E947BF13D8}
 1845 {7686859F-2CFE-44BB-A2AD-2BBB78A32B2D}
 1884 {25C46F99-5318-4E19-AC56-C62FE70F7F75}
 1885 {81135A85-C988-4734-89AD-F9C1575C83BF}
 1886 {9CB8DF43-F57F-4E3B-A6B6-5FB930D22B76}
 1887 {BB3ABE2E-BCF9-4A5D-AC47-A915AE01C0D0}
 1888 {C6E7C178-CAF9-4C31-A3FC-46C1186F5E9E}
 1889 {9475E15F-1E93-4AB9-88B6-B5A6410C7035}
 1890 {1368270D-309F-4DAF-BB96-33338832A26D}
 1984 {B45BE2F8-6680-419C-B384-715616A74A71}
 2034 {98A95488-DD71-489B-8D65-40D098F0FC1B}
 2035 {14113FAB-9B06-42B3-A110-A06EA3CFC6A3}
 2036 {B8FCBA5C-78BF-4F28-A043-A07617B61C48}
 2037 {232852DC-F402-4B73-BFB5-A393FCCB4A00}
 2038 {1F162286-C42C-4102-AC5C-B62061FEA6CF}
 2104 {5C853DE9-BF89-4400-90D4-2CBD38239EDA}
 2209 {9C3628F7-8D01-4009-A8C3-7EA969E398F2}
 2210 {DF8CBF7A-9523-47DF-8764-348336DC17D1}
 2265 {28EC756B-E7CB-4824-9918-C7B5C4243A69}
 2266 {49680E18-4868-4169-A088-06397BE2EB8E}
 2267 {7651E94B-583A-4EA7-AD5A-9A945AD958BD}
 2268 {E4DF18B5-B9F6-483F-B963-A559215A6B33}
 2269 {1B266BB4-BAD1-49ED-8D9A-13599BD0BE48}
 2270 {2EF783C5-0B85-4402-8E9F-5BBC72216ECC}
 2271 {210F4196-4689-4C8C-9E9A-CF47C513190C}
 2272 {E8CF9F93-433A-441D-814A-66FB5A534839}
 2273 {8D242117-8FCC-4E14-BAAD-3EA08722F882}
 2320 {4944FF3B-95C4-4951-B708-6550A030CFA9}
 2373 {731470BE-2B62-46B3-ADD3-9535E373BCB1}
 2374 {3582C46D-AE0E-462D-A53C-3FEB60320F30}
 2375 {BAB5007B-2D83-4C37-9957-1BF0BF6B2DCD}
 2376 {0A2E581B-47D4-4293-B634-0097A23C5612}
 2377 {20135ED3-15E9-4A33-871C-9DA8B6EE8D0E}
 2378 {A4355095-3209-42D3-ABB3-EBC402E75745}
 2379 {99842F77-1389-417E-852C-0BE1F51E6CE8}
 2425 {735E76C2-99EA-4509-A460-664D3F19455F}
 2426 {9082EFAE-796D-4A7C-B203-A0AF965B0AC6}
 2525 {0B4E5AA5-858A-4588-83E5-E4DB1E357216}
 2578 {C5129FA9-0174-4743-9238-AB672D2EE613}
 2648 {8DCFF4E7-927E-48E5-B64F-7D04E99BFF1A}
 2649 {C240B181-34B8-4903-80CC-6F0A2147183B}
 2650 {304E6FCA-6E14-481E-A001-0EFD399D9D8C}
 2651 {B352419C-925A-454F-99F5-F40957AD5EDB}
 2700 {0BA51702-83E2-4A8A-AE73-198C1D71D87C}
 2701 {97EBC779-ED85-42ED-80B3-22133655C867}
 2702 {5033557C-D3A7-47A7-A208-DBBE1DCF36E2}
 2703 {7F847B6B-0D4D-45B7-86B6-118E8A89DB84}
 2704 {B7422342-DD1B-4AA1-9165-24D6F20A6D8A}
 2705 {EF3F7474-BF5E-4997-BAE9-66039F1F2584}
 2706 {C95882E1-2604-4E29-95C5-60071BE7B810}
 2707 {465A08C3-7AFA-4DCD-8BD9-9F05D140F1D4}
 2708 {7B46FB96-8770-46F0-BE60-EDE6DC650DCE}
 2709 {8D768D9E-D4F0-4D32-9218-70903F8DA9DD}
 2710 {02757A2E-D9AC-43D1-B626-E0CB4EA786D6}
 2711 {361F0C0E-E8C9-4B12-8869-86A4C54B7114}
 2712 {DC0DFE12-2093-4B4E-A719-3945F3208FCF}
 2713 {7EEA2AD1-5ECA-4356-A766-59E0B8FB2EA6}
 2714 {EB441387-E33F-4272-8609-D7CA6869778B}
 2715 {97D2D4C0-FCAD-446E-AFDC-6C38507B6F8B}
 2717 {5C65E314-D12F-4538-8FB8-DD66AA8EE673}
 2718 {2C7CD70C-1A80-41C6-8867-81FD488C4B8D}
 2719 {C4BDAFF6-834E-48EE-984B-68D58FE1DCF7}
 2720 {1B586756-EE55-4420-BBA1-861E20597F25}
 2850 {F1C1FA43-7DC3-49B2-B897-6AD23EE232FB}
 2851 {5A57E9D4-A4EC-4199-B4EE-166B0EC391D6}
 2852 {A7D5A27B-E1A5-473D-AA61-77E8ECF13F09}
 2854 {6E384490-6030-48BB-B083-CDCE46F459B3}
 2855 {D0FB027B-0068-48BB-A298-8D5BA2DE9E80}
 2856 {33D82624-4796-4420-AFA6-1D43578F2A9C}
 2857 {691C67DB-0181-4F30-B353-4B0E89E8A159}
 2858 {5DE385F5-1716-4B56-81C8-C10D21047517}
 2859 {93D60441-A9F9-471E-BF00-9E2C9644ED00}
 2860 {90953954-0AD5-4442-862C-302D5E416237}
 2861 {66C132FE-5AAF-4339-84E0-B3A3969D9984}
 2862 {F3859BE9-3522-423F-8126-3D04B5F06E90}
 2952 {72FB39C6-F62F-47B5-AAEE-40AF922D9F70}
 2953 {318E2055-ED57-4778-A3C7-8341E88012A0}
 2954 {F6264504-4D24-4DA5-8DBA-E7085B090C41}
 2955 {D591E421-BFB1-42D0-9FE1-7431470C99E9}
 2956 {50E0BCC2-2A2E-4738-B005-AF7DF21B5606}
 2957 {2C968BE1-F5E4-476C-B39D-B7B2B8407A82}
 2958 {4D0A911D-1BEB-4472-B1D6-5ABDBA041E2C}
 2959 {3DAB1D6A-5858-4CA5-A3C4-FBC9CD7D9707}
 2960 {8ACF7F14-B1A9-4F8D-8F58-8D02A4048216}
 2961 {4FE0C2CA-8F0E-479F-B617-6EE53E855A7F}
 2962 {6A504943-7B4D-4EEF-AF45-334132CBC7A9}
 2963 {4F605B54-810F-41C0-9CA5-3776CE18BC26}
 3054 {2EEE91F5-AA96-4400-A9BA-7449BEB1E482}
 3110 {D82557A2-6218-4A8A-A825-E6BE85B7883F}
 3111 {971C945F-8B7F-4652-95A9-DBBBCD461D18}
 3165 {D9E71BAD-C8EB-40A3-8CF8-B31601096920}
 3265 {0FE4B971-AE8F-441B-B411-A0B0384B69A1}
 3266 {19DD38E0-44EA-4F01-B5F0-B1AD2AB27C01}
 3267 {E8BDE4B6-8E69-4FB2-A409-EBFA03E74C17}
 3269 {E1F98D53-CA0D-4E25-8F97-26E388E91526}
 3270 {62FC5651-73DC-45AE-93A6-C394FE64D7FC}
 3271 {B0EA7CCA-01C1-460F-AF8E-6F0CE08B5902}
 3272 {9E787E67-4E99-4C5D-B605-7E852678A52F}
 3365 {1E7F202E-D6C5-4752-9A5E-B38C1EE9AC0F}
 3366 {4473D9A5-5F51-49E0-9641-4920005DE69F}
 3420 {14BEE1FE-0D9B-4A92-99F0-6798B00F0484}
 3482 {37AAEEC4-B7C0-47D7-A398-0CCA85CD6064}
 3483 {CC009FBF-54C0-4A75-9077-405F9107C74B}
 3484 {41DFA60A-5A7C-485F-B744-ABBDDB06018B}
 3485 {076C1BCD-5FA0-4254-9CFC-C361697CE137}
 3582 {0BF4F8CE-90E3-42EC-9F53-E22105A494EA}
 3583 {C09BD91D-66FF-487A-AE2B-49F2762EC8C1}
 3584 {46B53CDE-AE4A-4C59-AFCC-FE097C5FC562}
 3585 {9AB1488F-D91B-4D5E-BA0F-E61EA381F96D}
 3586 {0CD79941-C92A-4A5E-8DB4-B04E403A185C}
 3587 {B2592C89-1DF7-460E-B04F-2801A645C684}
 3588 {E1136464-6356-49BE-8B12-D2622B5067EA}
 3635 {E61DDEF3-DD19-45A3-A162-440367AFDBEA}
 3688 {0C0F73C6-CCA2-4944-92E1-190D8637866A}
 3689 {16570F0A-9E54-480F-83EB-45EA2D82B3AA}
 3788 {76DC9C59-A2B7-4AA7-9AA8-CC1DA5B65E5B}
 3789 {CFA5072A-AF2C-459E-8E47-DCE8EF933139}
 3790 {3F51E3BF-347B-4345-8BAD-8EF9E79D9904}
 3791 {9A2CA6FB-9D84-41A3-B337-4D432149D3E8}
 3792 {CFF34FF3-3755-4BC7-B0FB-7B9AEBA2E765}
 3793 {AFB451F4-C474-419A-8C13-CB9493A184C2}
 3794 {4A84DF8B-94A1-4FE6-B22B-D990DC41171A}
 3795 {6A986449-71B3-46DE-80F1-50F0EF41770F}
 3796 {3C1A86DB-7740-4231-9725-C94EBA94CCC3}
 3797 {F23A77DB-E86C-425B-9BF2-CC9F580098CE}
 3910 {698EADCC-5565-4F9B-8754-5219DF1DB88B}
 3911 {CDF48FC7-35CE-4869-998F-78EBAFA910BE}
 3912 {F7DDCBCB-B0E5-4A63-89ED-4442F9F7E6D6}
 3913 {229D4C72-AB23-46B9-B237-37B0B3930DA5}
 3914 {F3F27663-D615-4E15-B7E5-76FA00A640AE}
 3915 {868AB8AB-E7B1-4DF1-A230-03EF9FF39C26}
 3916 {6704CF96-55A3-499E-A858-A985CD07E104}
 3917 {FC3EF0D6-AB28-4858-B245-7702CDF5953F}
 3962 {0B74D692-E222-446E-800C-E12F19A1FB8A}
 4066 {474561A6-7704-4EEC-8115-61062D615840}
 4266 {98502C55-F6E7-4668-848D-575B6F81835E}
 4267 {521BADA7-DB1D-405D-A4B6-FC006F8E2B24}
 4268 {0C80DE82-55A5-4D2D-9D1A-0DE443EF42B6}
 4269 {F7C49103-6786-4D4B-BFB2-3B883CEAD291}
 4270 {4F6EF23F-4F37-4C4B-B211-89492BFF5915}
 4271 {0E33FC8E-CDB5-46DB-8AE9-2F297BAAC543}
 4272 {D833B0B2-02A8-433C-88E3-B45A1B677973}
 4273 {C368D5CA-5CB0-4A91-8143-072CD6A7BD1D}
 4274 {0D2DB4E0-1519-4651-92FE-DBCE41156B9F}
 4275 {C321090C-791C-4CB4-A9BA-01E41F02EF45}
 4276 {7105087C-CE2F-401B-9ADB-6564DCE2F2F9}
 4277 {825D2A5A-8AC7-43C7-806B-D01F93BE5597}
 4278 {F27E2D7A-5464-4DA1-ABB9-6AF6E3729DA4}
 4279 {07F2B33B-DC4C-430D-9936-E6543C55A225}
 4280 {76A6D0BA-5BBC-4025-8AA9-BD4D7C9D1F2A}
 4281 {4916E302-73DE-4181-9F37-E6B98A4366F3}
 4282 {F0112C21-D0BB-49A2-992F-A44DB980AA4E}
 4283 {DD950A6A-DE87-490C-8DC2-99CFDAB9D012}
 4284 {3CD69F9C-49ED-46E5-B180-CF0F4DFE4DDA}
 4285 {A7784A50-4278-4357-BC93-8E65D7BDD3EA}
 4286 {73C56031-DAA0-40F6-B024-E4B23A999D11}
 4287 {15E51CEE-9B06-4C47-8177-FEABD1C58A28}
 4288 {F766914B-B4B2-4FE3-ABCA-A2A87AE9751B}
 4289 {2A0C0416-B347-4272-B249-FC9AA05F74AC}
 4290 {82E621BC-A884-4E05-B5EC-A3367B3A7350}
 4291 {04F1009C-78CA-480F-B1D3-25DE441C1F8D}
 4292 {97E80E1B-9203-4C05-90ED-D1E742999070}
 4293 {E9C4E477-27B7-4D10-99C3-D94050F9FFFD}
 4294 {40922C52-AE90-49D6-944A-DB4D7258BC66}
 4295 {EF5F9E91-B37C-4BB0-B56A-05E601FD5C8D}
 4296 {7122E202-1FBC-4F92-9B46-C3A8751037CC}
 4297 {1E86D87A-533F-4E38-81DD-AFE28F979BAA}
 4298 {80769D01-C096-4AF9-843B-3DE176E842F0}
 4299 {EC72CF7E-DF93-41E1-B6F7-DF3996D11F52}
 4300 {789BD433-1147-40F8-8D39-69A0E9D1B391}
 4301 {E697BC25-6E8D-49BB-B53C-03353C46417C}
 4302 {48CA24DB-453B-4EA1-B9C1-B5EA475D3B3F}
 4303 {81D356AB-3E54-4978-87EB-39B48C29ABCC}
 4304 {F3B72F18-8E4C-4EA6-AC46-AEF87F839B1A}
 4366 {031B0B12-8D0E-46D4-A25F-33BE6F159FBD}
 4367 {7E79EB88-9CD5-4DB6-97A3-11EF531FB4BD}
 4368 {B89395E8-E76B-47E8-B971-8A25BB3AD63F}
 4369 {35130A12-E19D-40E8-B216-17B9E82CBC4A}
 4370 {B0F4AFEF-CEE0-439D-8BD1-9657129C2570}
 4371 {4C0DE104-8FF8-4D9A-93E0-2956457AB171}
 4372 {0A68E0F7-E3A1-441A-897D-77EF87E93A87}
 4373 {45C463AD-FAFB-4F91-97EC-AA3ABE868D10}
 4573 {2D6899C0-F242-4966-9045-662027FBF5B7}
 4626 {2A230661-EE1F-4301-9561-8574B0002837}
 4926 {EE79B4EF-5A7A-4E76-8807-AB37821E623D}
 4927 {8608ED8E-FA74-483B-8FE9-77A07CC7A381}
 4928 {A1FF9B15-7CC1-4836-A77E-99EC7A3B56DA}
 4929 {96667908-97ED-4A30-8DB5-B002F92566BA}
 4930 {8D81BD8C-CA23-4695-9889-CC1EE7E8ED76}
 4931 {980E570A-C06B-4346-BCD4-CE08E4AA20E9}
 4932 {8ACCD04C-FE98-476D-BBF2-C9EA028FEB3A}
 5026 {DDD8CC14-B3E1-4085-BFF8-6A70C07D6FE4}
 5027 {6DAC3982-90C5-46FC-B17A-EAEB139AAD0F}
 5028 {F0A36787-8958-426F-B6D1-7BF68C500FC2}
 5081 {E9461355-022B-495B-9914-98664B6B0085}
 5082 {A8C32832-4A3F-4231-AADD-2BAAAE5F2312}
 5181 {FD13A792-F8C2-4507-9DDF-577055BAC889}
 5182 {28BBAD6F-04F8-463E-B26D-0534762A950B}
 5183 {C0078579-8FFA-42E8-9927-33DC7DDB2CB6}
 5184 {B05FC5D4-EFCE-441C-92A3-9BAD8A8108DC}
 5185 {E7DE59BD-DE4B-4364-9F72-3D48D8B40A99}
 5186 {EDC3A8D8-726F-4694-BA03-27DB8F0FF89E}
 5187 {88C28C0F-700D-402E-A4F6-FD2CDFB07915}
 5188 {B3B2DFC8-9464-444C-9B86-842AA1644C40}
 5189 {1BE0E72D-1A80-4ADE-9671-A453421FA81F}
 5190 {3F025B38-5F42-41C2-AEDA-E7D904CC637F}
 5234 {F33A5E3E-F72C-480B-B856-D67D7E08527B}
 5235 {1643C2C2-9EEB-4D82-B5DD-73F77738EFA8}
 5236 {A02F616A-C657-4E2D-A276-C39D354F0F3A}
 5237 {07063030-57AC-49DA-A17F-181E1DB40087}
 5238 {D7A75543-CB18-475B-8F62-F417CEDC4DB6}
 5239 {CD36E6E9-27C8-42F5-A5AD-2EE696E80F5C}
 5240 {38BDE35C-23E9-49ED-9FEE-3952E457003F}
 5241 {2A1DAD29-5B54-40D8-997C-EB43B6FC0AA2}
 5242 {870CC790-44FC-45E3-8574-23186472022C}
 5243 {5979FA90-8055-440F-95B1-D6A6E2158DE6}
 5244 {347E6EF7-681A-4237-B435-C9E16308BAF0}
 5245 {3BB39E33-231F-4646-9C4D-6C50B6495540}
 5246 {933EF5B0-6DEF-4CEA-9130-68D2CC67A906}
 5247 {4DDCBF2F-4C21-4BB8-8C1E-BBAE822747BF}
 5248 {084E181F-0C2E-4926-9124-D2FC140DAE0B}
 5434 {7ADA9925-2C0D-4620-B469-2ADA6C6C6AC4}
 5494 {8AF9DFBC-DC6A-4BDD-8872-6C61F6BC143C}
 5594 {8930C934-8AAE-44A6-A88E-E11A56BBADB4}
 5595 {0C6CBEC4-FB04-40EE-86E5-9DE6D0A456AD}
 5596 {334F7816-73D6-4997-86ED-531153017983}
 5597 {EE217817-7B18-40C2-A522-6E1D6897CFBF}
 5598 {4000C23F-9F61-44DE-904C-82F40A78750B}
 5599 {3FE03925-F275-423B-8D95-E383C1C84CD8}
 5646 {FFC99284-6251-449B-B2C6-499D8E18C33C}
 5647 {D5E68F14-CCE6-4225-8858-4F57D4B9C114}
 5747 {6EACA4CA-6A6B-485C-88E6-A6748C02CC58}
 5846 {8D080B7D-78B1-417C-BE5B-B3DAE78E7AF3}
 5946 {EE6F259A-F498-47E9-823B-7918EC005E25}
 5947 {5F91294B-1D76-4071-BA82-B48F334C39AC}
 5948 {B4536754-A3C9-4F66-AB4E-9D05099D218A}
 5949 {4EAE03FB-39DD-4929-9F5D-624AA02DB510}
 5950 {F5958461-FA6D-46E7-8858-585662F4A15F}
 6051 {9D36642F-AD47-4A19-AE02-E70C5A074432}
 6052 {1EF67467-80F4-4094-86C8-817DF67CD285}
 6053 {A41A1F17-7B2E-4009-BFC1-7F2D162F066B}
 6054 {36046698-ACFE-414D-9414-4A28D82F62E1}
 6107 {2F671C53-AAB5-4399-AE7A-1AA951D02014}
 6160 {34912176-7D0A-433A-8217-EDB339C579F4}
 6161 {D3E95B8D-DE34-4602-8A97-0BACF177C0FD}
 6260 {EB9B3DE2-48DC-4B0D-AF27-4421A9A63AD3}
 6360 {14F31B6B-7CD3-4E4B-B125-4D5701CE2D44}
 6361 {20950D18-2B97-49CA-B5EC-1C8EC635E802}
 6362 {61639346-E5A2-43AF-9C1D-87D37D4B4788}
 6467 {8C55E2E7-9BFB-4C29-94FA-95B9598CB967}
 6520 {66C31DAC-65CD-42EB-9A5F-890320AE560C}
 6521 {D8FFE041-B5E0-4A83-9EE2-1FB3AECA48B8}
 6522 {26DE518A-20C8-4B9B-8B1D-359D28F3300A}
 6523 {E32FDA99-8331-4F42-ABF3-1E572E27F415}
 6573 {9E3DB5E4-F42F-4335-94BD-0BEAFB05EEAA}
 6574 {8A3737CB-1BC7-42E6-865C-A18CD090E73B}
 6575 {0019C774-F5FB-44CA-90BF-0B0F8CCDBE20}
 6576 {6D534101-BEF7-4986-BB2F-751FDC5184DC}
 6629 {2EEA7757-DBCF-45B1-85DE-E69218877C5F}
 6630 {94D8C763-A084-42DF-A04A-FB8F8BD86B25}
 6631 {40548C16-F759-4D8E-B7D2-D4AB9C2FC874}
 6632 {7F8504C5-0084-40B3-AB07-B99F3E69A5A0}
 6633 {B267DCF4-FBB1-4781-8349-B4850C2F3E3C}
 6729 {A1FED08E-7790-48A8-AA13-4A0B9A885D4A}
 6784 {FE70A8D1-FC20-434D-834B-A8FAB7676896}
 6785 {D59A684D-6640-40C8-8C7E-D37BA7DE2B09}
 6837 {D09EA263-1D33-4702-8FAF-60AC3F1035E2}
 6945 {ABAE12CA-D093-4D9B-8566-FAC821C3A193}
 6998 {B015EC43-7C24-40D4-8239-A62710864854}
 6999 {2341F4AA-22FA-4A32-93EE-C33FE5B8A158}
 7000 {BB6DD89F-741A-496D-9519-A1F7DE41A7EF}
 7001 {239B11DB-70D7-4855-9FB4-49C6F1F3BBCF}
 7002 {8B9444A0-A26D-4B42-B524-CBCE8B6BC383}
 7003 {A988CC2F-41E0-4065-95E0-3DC835BA21F0}
 7004 {9F4DA74E-3172-4FE2-9C2B-210568D4224E}
 7005 {5315D0EB-5C2F-4E50-9069-B5E645D889A8}
 7098 {0C04147E-79BA-44CC-8F0D-43BA35C26494}
 7198 {E96903D7-8F10-49FD-8471-15522BD66CE8}
 7199 {60B160FB-2072-4307-AB65-A3DCA048C8B0}
 7306 {11C1266B-8F6D-4801-A4E7-94F9911C3299}
 7308 {CA5EFAFB-86B5-4175-8333-D5FA3B8579EC}
 7309 {7BC425F6-11A8-424D-9583-FCE05191229D}
 7310 {FCDBC62B-D83B-40FA-B436-28CA702AFD7F}
 7456 {38C03260-7312-479D-BEEF-A3451F21E7F5}
 7457 {DF4CC14A-6374-4A12-A047-31399BFE56B7}
 7458 {DBE439E4-0A8A-4573-B6E6-380ABF0FF849}
 7459 {E4F53B9A-7CD0-4BF0-837F-1D44A76F0529}
 7460 {93E3C252-04EC-4250-AA45-66DCFE025A0A}
 7461 {C8C06E9C-9ED0-4A6D-97F3-23C994DC8527}
 7462 {B6F7BC16-ED04-460F-9572-52B563E940C7}
 7463 {5A821F4A-7B8B-4901-9FE6-1FCA560F0FCD}
 7464 {E7D8D1CE-2219-4D85-8B0F-5143E4D98B51}
 7465 {72FEF463-0572-4F88-9145-27C4088AA7BF}
 7466 {F9103061-8190-4A83-9E27-E0531FDD1A3B}
 7467 {687D214E-37A2-4BCC-9B33-EC6316C38B44}
 7468 {5F4ABDCB-CC8B-42B4-8127-3D0A5630662C}
 7469 {F5DEAE56-7522-4DF8-9727-DC8763EC635B}
 7470 {19540CA2-0B51-42E3-A47D-A0C56940AE27}
 7471 {129DBD99-45FC-4D32-8E5C-54C53F976896}
 7472 {632577CC-E624-4591-B4A7-A8B1D1358360}
 7473 {D097822A-85A0-41A3-A934-9F7B347128E3}
 7474 {72351DF9-F52A-434F-B114-3139D1A58B49}
 7475 {D4D6965A-16D3-478F-8D77-970A078D96E6}
 7476 {D3103432-07F1-4D4F-ACF2-5415FD85F916}
 7477 {6C205085-B1C1-4215-B5D2-9219177B9740}
 7478 {ADEC2DD8-6AFD-4A11-8A7A-4CD7899C72EC}
 7479 {9BF40AD9-6EEA-45F4-A132-5C79F73C4539}
 7480 {2C1851AE-85A4-462B-9C37-591C8DD6D00E}
 7481 {46B1D856-82E3-415D-89BC-BE5710B98A05}
 7482 {36663E46-1BE5-408A-8D84-A8DE24EA6ED2}
 7483 {CAC3FE73-8454-4732-B3D3-C7CAD3B2C414}
 7484 {603FD89B-891E-4332-9B00-F55EB80078F0}
 7485 {CB86F3D4-E08E-4912-B938-F1C2282BAFBD}
 7486 {00DFACDE-91EA-4D20-9B43-DA1BD250D28E}
 7487 {BBA86311-0EA4-4673-8CAE-191CF1B4F85C}
 7488 {AC863558-068F-41CD-B465-F9705D469D01}
 7489 {24249545-B750-4FFE-9978-A0AF19488D59}
 7490 {EACD3F67-E61F-47F5-883B-BA6F46D6D83D}
 7491 {6B96A29D-C460-40B8-A7CE-7E95AB691CEA}
 7492 {0BDB5D41-43A7-466D-97E3-F848910184D3}
 7606 {EFB4F19C-84EF-438C-9839-FBB33128B104}
 7607 {F51DE2F8-7B5F-4E76-8F3C-891E42FCC386}
 7608 {FC1033AA-B1C4-4425-BF77-BD6F33EE6CCF}
 7706 {6A9359B7-A735-4D8C-964B-7DAFB69FB1B8}
 7806 {1D684FD2-4F6D-40AE-9BA1-B6C4868EEFB7}
 7807 {FFC641C5-D9E1-4E21-967E-F27BBDD816AB}
 7906 {F0C74B9C-8CF8-44A7-84CF-FF95AD991A08}
 7907 {434E310D-7FAF-4E4A-A8C7-FF358D5E3296}
 8010 {8180C5F1-743F-4D69-B7E8-D8FE977EB09E}
 8063 {39E7B046-15BA-42BC-A1DF-568282DCAE55}
 8224 {102E1292-841C-4EAD-87D0-B0B5531C55E6}
 8328 {12937BDC-DA56-4277-962F-5C14599D2C86}
 8329 {6D997E5B-3723-4081-9867-41F67F135A66}
 8330 {4AF8C4D1-D597-4638-B1E3-A1DD2F644F22}
 8331 {882657D7-5AB3-4243-ADFE-A0D503CCF465}
 8332 {BC8EDD33-B848-41A4-8319-90781BC56DB6}
 8333 {6FB1DF37-EEEE-4F34-B9C4-038B3C443DE3}
 8334 {7D3941B6-234A-40DD-AA33-290587211629}
 8335 {98FB9417-D789-4631-A01F-DDFD6E64EFC2}
 8336 {0C6DD773-F100-4C70-8189-4E3CF3800242}
 8380 {E36A4CD4-FB1D-4BD7-ADE9-CA79D2669313}
 8381 {AC543CFB-F8F3-4618-A2CA-95EBE9B593A8}
 8382 {48F40460-14E4-4D03-926B-A6511BB0B0EE}
 8437 {161C6343-A405-40DA-977F-A3275A4B13C0}
 8438 {959D6CAB-2F8F-4EC9-A1F2-67EA01E6A080}
 8439 {99395CA6-EADF-473C-BCCC-7C94567F2AF6}
 8440 {5D1067AA-1A2F-47C1-B2E1-A33C9318E465}
 8441 {8F008784-AD26-4E00-8ECA-F8B217318B9E}
 8442 {08BCA632-66EA-4BE2-A964-E6F3F4E28400}
 8443 {822E71BC-0895-4ADB-934D-2825393FD3DC}
 8444 {5F99BDCB-378D-48B1-907D-C710BE40F944}
 8445 {793547CA-7498-4D46-8EA8-EC5173B0B4E2}
 8446 {1295EE21-64DC-4660-8EFC-3A0B7E5D8086}
 8447 {94581A9C-CF0B-45BF-AC0B-3493BEED2DEF}
 8448 {C053D5F6-594A-470D-9F5C-C8B77266CAB7}
 8489 {264D8445-E6A2-47FF-9BED-68EEED4A4CD1}
 8552 {7767D708-733B-4FAD-9C28-ADA4E8EF372D}
 8605 {AB0D5B2A-78D1-4827-9B5C-048EE563B3FF}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>90659D3D-9F7B-42CA-BDB7-E7D0B536F61C</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1396754514</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396754514</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>596A7943-697D-49C0-8D1F-157C3620F969</a:ObjectID>
<a:Name>SRM供应商协同管理</a:Name>
<a:Code>SRM供应商协同管理</a:Code>
<a:CreationDate>1396754514</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396754617</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:TextSymbol Id="o5">
<a:CreationDate>1418622011</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-30487,-31612), (25988,24413))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>Arial,8,N</a:FontName>
</o:TextSymbol>
<o:TextSymbol Id="o6">
<a:Text>FCERTIFICATEDSTATUS</a:Text>
<a:CreationDate>1418967609</a:CreationDate>
<a:ModificationDate>1418968138</a:ModificationDate>
<a:Rect>((-98444,-160641), (93946,37861))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>Arial,8,N</a:FontName>
</o:TextSymbol>
<o:PackageSymbol Id="o7">
<a:CreationDate>1400570077</a:CreationDate>
<a:ModificationDate>1400570523</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-18788,-109635), (-1730,-98425))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>11711154</a:LineColor>
<a:FillColor>12648447</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Package Ref="o8"/>
</c:Object>
</o:PackageSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o8">
<a:ObjectID>8E6F722E-80A1-402F-B0B1-89633546AA75</a:ObjectID>
<a:Name>SRM</a:Name>
<a:Code>SRM</a:Code>
<a:CreationDate>1400570077</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1400570523</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o9">
<a:ObjectID>ADEDD751-C29F-4091-AD39-599308D1B236</a:ObjectID>
<a:Name>SRM</a:Name>
<a:Code>SRM</a:Code>
<a:CreationDate>1400570110</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1400570523</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:TextSymbol Id="o10">
<a:Text>FINQUIRYSTATUS</a:Text>
<a:CreationDate>1411558487</a:CreationDate>
<a:ModificationDate>1411558606</a:ModificationDate>
<a:Rect>((-32850,-26887), (32850,26888))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>Arial,8,N</a:FontName>
</o:TextSymbol>
<o:TableSymbol Id="o11">
<a:CreationDate>1400570437</a:CreationDate>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-23997,4433), (-10276,15744))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o12"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o13">
<a:CreationDate>1400570439</a:CreationDate>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-11178,10804), (-1345,16272))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o14"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o15">
<a:CreationDate>1400570441</a:CreationDate>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-2698,11141), (8324,15635))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o16"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o17">
<a:CreationDate>1400570451</a:CreationDate>
<a:ModificationDate>1420794297</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((22433,-16718), (36592,-5407))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o18"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o19">
<a:CreationDate>1400570453</a:CreationDate>
<a:ModificationDate>1411111780</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((16153,6907), (28922,16270))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o20"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o21">
<a:CreationDate>1400570466</a:CreationDate>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-24035,-18557), (-12488,-12115))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o22"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o23">
<a:CreationDate>1400570471</a:CreationDate>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-30222,-24071), (-15001,-19577))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o24"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o25">
<a:CreationDate>1400570477</a:CreationDate>
<a:ModificationDate>1411486454</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-12071,-29808), (5399,-24340))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o26"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o27">
<a:CreationDate>1400570480</a:CreationDate>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-9747,-17128), (5024,-3870))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o28"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o29">
<a:CreationDate>1400570483</a:CreationDate>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((7052,-11994), (19873,-3605))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o30"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o31">
<a:CreationDate>1405339698</a:CreationDate>
<a:ModificationDate>1405339880</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((8063,-21182), (24337,-14740))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o32"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o33">
<a:CreationDate>1408021253</a:CreationDate>
<a:ModificationDate>1411111780</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-25386,18753), (-12413,24221))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o34"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o35">
<a:CreationDate>1410928398</a:CreationDate>
<a:ModificationDate>1411486454</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-22702,25488), (-7553,43618))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o36"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o37">
<a:CreationDate>1410929885</a:CreationDate>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-2473,21236), (15750,40340))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o38"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o39">
<a:CreationDate>1411111463</a:CreationDate>
<a:ModificationDate>1411111764</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-6487,4915), (10986,9409))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o40"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o41">
<a:CreationDate>1411485046</a:CreationDate>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-42381,11576), (-26108,17044))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o42"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o43">
<a:CreationDate>1411485881</a:CreationDate>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-42021,29916), (-22749,38306))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o44"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o45">
<a:CreationDate>1420792517</a:CreationDate>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-42298,-12590), (-27074,8462))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o46"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o47">
<a:CreationDate>1420793355</a:CreationDate>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-48298,-25452), (-29925,-11218))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o48"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o9"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o49">
<a:ObjectID>3162C822-C0BB-4149-8382-40B2E6EFB9DF</a:ObjectID>
<a:Name>T_SRM_COMPANYTYPE(企业类型)</a:Name>
<a:Code>T_SRM_COMPANYTYPE</a:Code>
<a:CreationDate>1399277887</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o50">
<a:ObjectID>21303067-F8AC-4CCA-ADAE-5FD422E09635</a:ObjectID>
<a:Name>企业类型</a:Name>
<a:Code>FTYPEID</a:Code>
<a:CreationDate>1399277909</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o51">
<a:ObjectID>986CD649-FE12-4980-B638-5E718920C2F4</a:ObjectID>
<a:Name>企业父类型</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>1399277909</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o52">
<a:ObjectID>5D6186F9-3BF7-4A04-A0BA-9D3B04EB91D0</a:ObjectID>
<a:Name>类型编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1399277909</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o53">
<a:ObjectID>DF8EBDAC-C86C-4889-9759-0AF6B17B7E49</a:ObjectID>
<a:Name>类型名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1399277909</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o54">
<a:ObjectID>05F4795D-DD9D-4FE8-8CE5-93A208911458</a:ObjectID>
<a:Name>级数</a:Name>
<a:Code>FLEVEL</a:Code>
<a:CreationDate>1399279453</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>0D02464C-A4A2-4AFB-B38D-CAB8B8CC4DDC</a:ObjectID>
<a:Name>是否明细</a:Name>
<a:Code>FISDETAIL</a:Code>
<a:CreationDate>1399279584</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o56">
<a:ObjectID>B5733AEF-470A-4B7A-BDCA-4DEAEA7B7105</a:ObjectID>
<a:Name>PK_SRM_COMPANYTYPE</a:Name>
<a:Code>PK_SRM_COMPANYTYPE</a:Code>
<a:CreationDate>1399277909</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_COMPANYTYPE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o50"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o56"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o56"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o57">
<a:ObjectID>F735DA35-4E7F-4F4A-BD37-FF8AFF07BEFC</a:ObjectID>
<a:Name>T_SRM_BASTRADE(行业)</a:Name>
<a:Code>T_SRM_BASTRADE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>行业</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o58">
<a:ObjectID>D36BCAC6-E553-464A-A5CC-102C55CDFA76</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FTRADEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>C888DE4D-BC62-41B7-AC54-94C31BC81239</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>44F6FE9D-3A6C-478D-9278-22476BE32299</a:ObjectID>
<a:Name>级次</a:Name>
<a:Code>FLEVEL</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>08EA768E-7A98-4210-ABFA-29EC2F5FD6D2</a:ObjectID>
<a:Name>父节点</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>63DCFEBD-4CB9-4DB0-B4C9-48A9AF543130</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1399277774</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>2EF687E7-2E11-458A-8EBF-A178A3CA1182</a:ObjectID>
<a:Name>明细级</a:Name>
<a:Code>FISDETAIL</a:Code>
<a:CreationDate>1399279498</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1399282475</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o64">
<a:ObjectID>E5A3A201-B9EC-414C-ABCD-ADD076385A8F</a:ObjectID>
<a:Name>PK_SRM_BASTRADE</a:Name>
<a:Code>PK_SRM_BASTRADE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASTRADE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o58"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o64"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o64"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o65">
<a:ObjectID>63392BFF-9C2B-42CB-8029-2F7F51F63530</a:ObjectID>
<a:Name>T_SRM_MENUTYPE_L(菜单类别多语言)</a:Name>
<a:Code>T_SRM_MENUTYPE_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o66">
<a:ObjectID>27BF802C-DF0E-4ABC-96E6-652A1ED0BE4E</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>CFA63028-C85C-43EC-9C02-A810C03012FD</a:ObjectID>
<a:Name>菜单Id</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>D8D1967D-C6D9-416E-BC8D-16FFEBEBD4A3</a:ObjectID>
<a:Name>语言</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>8B1A0951-FAED-45DF-A95C-81E8BA50650B</a:ObjectID>
<a:Name>菜单名称</a:Name>
<a:Code>FMENUNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o70">
<a:ObjectID>D128C973-A3B8-40F8-9DEB-1CB0BFBEF5DB</a:ObjectID>
<a:Name>PK_SRM_MENUTYPE_L</a:Name>
<a:Code>PK_SRM_MENUTYPE_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_MENUTYPE_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o66"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o70"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o70"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o71">
<a:ObjectID>06D6999A-C2A7-4292-8660-5EDBBF0822CA</a:ObjectID>
<a:Name>T_SRM_COMPANYCONTACT(公司联系人)</a:Name>
<a:Code>T_SRM_COMPANYCONTACT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o72">
<a:ObjectID>FBCCA3B0-1AEA-46FF-B90C-5ECAF00B8AEC</a:ObjectID>
<a:Name>企业ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903693</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o73">
<a:ObjectID>64915AFF-AC38-489A-9FD8-DA2CCE093311</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903699</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>8ED9D033-7C67-416A-950B-2F26B9D174A7</a:ObjectID>
<a:Name>联系人姓名</a:Name>
<a:Code>FCONTACTNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>B0F655E8-1F09-4AD6-A3F3-9227C8294F90</a:ObjectID>
<a:Name>固定电话-国别</a:Name>
<a:Code>FTEL_INTER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>C2049E7E-8577-414C-9F36-52E446E1B42A</a:ObjectID>
<a:Name>固定电话-区号</a:Name>
<a:Code>FTEL_CODE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>9243A42E-A26A-49AD-B3CC-544CAED51A9A</a:ObjectID>
<a:Name>固定电话</a:Name>
<a:Code>FTEL_D</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>5A98724D-AAC5-4B5D-8BD5-F4F2B83C065F</a:ObjectID>
<a:Name>手机</a:Name>
<a:Code>FCONTACTMOBILE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>6B143576-DF6F-451C-95CE-D173C9973994</a:ObjectID>
<a:Name>传真-国别</a:Name>
<a:Code>FFAX_INTER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o80">
<a:ObjectID>7E2E6EC8-B8E5-403B-883C-0C38321512A4</a:ObjectID>
<a:Name>传真-地区编码</a:Name>
<a:Code>FFAX_CODE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o81">
<a:ObjectID>C733FE1E-24F6-4475-A997-8A7795914D2E</a:ObjectID>
<a:Name>传真</a:Name>
<a:Code>FFAX_D</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o82">
<a:ObjectID>440B5D34-8B95-4C36-B85A-C54B81B58278</a:ObjectID>
<a:Name>国家</a:Name>
<a:Code>FADDRESS_C</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905296</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>D18FBC7F-00C6-4B45-8A81-D15D93BD3972</a:ObjectID>
<a:Name>省份</a:Name>
<a:Code>FADDRESS_P</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905300</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>F62503D7-6A65-4F53-AC30-E6536824EEAF</a:ObjectID>
<a:Name>城市</a:Name>
<a:Code>FADDRESS_CITY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905305</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>CF737EF8-E1E8-4F50-9970-CE2F07686048</a:ObjectID>
<a:Name>地区</a:Name>
<a:Code>FADDRESS_R</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905311</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>E90A3CF9-D449-484F-A75F-6DA6B9803367</a:ObjectID>
<a:Name>地址</a:Name>
<a:Code>FADDRESS</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>2B00B905-F016-4CF9-9707-BEFCCD3310FF</a:ObjectID>
<a:Name>邮编</a:Name>
<a:Code>FPOSTCODE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402905289</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o88">
<a:ObjectID>DA954DF6-99F9-427A-B69C-FC9038979F60</a:ObjectID>
<a:Name>PK_SRM_COMPANYCONTACT</a:Name>
<a:Code>PK_SRM_COMPANYCONTACT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_COMPANYCONTACT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o73"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o88"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o88"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o89">
<a:ObjectID>D48D7D2C-044B-42F7-9D97-5882983924DB</a:ObjectID>
<a:Name>T_SRM_BASTRADE_L(行业多语言)</a:Name>
<a:Code>T_SRM_BASTRADE_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o90">
<a:ObjectID>B184C1DC-6379-4250-93C6-1CAA8C9F6968</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>84BAD2E6-02CF-40CD-8644-CAD96E9612A9</a:ObjectID>
<a:Name>行业主键</a:Name>
<a:Code>FTRADEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>20E3CFEB-9130-47C7-832F-C7DF38AA7FEA</a:ObjectID>
<a:Name>语言主键</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>F4597921-5FA9-4E3F-AEFC-96E6B933E715</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o94">
<a:ObjectID>0BFCE3DF-DCDC-4D6F-A4A5-845813A7CBCE</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o95">
<a:ObjectID>4784B15C-8873-4B12-9B1C-23FDF598385E</a:ObjectID>
<a:Name>PK_SRM_BASTRADE_L</a:Name>
<a:Code>PK_SRM_BASTRADE_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASTRADE_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o90"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o95"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o95"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o96">
<a:ObjectID>2F6EB8DA-CB8A-4F97-8EDF-13930593FC0E</a:ObjectID>
<a:Name>T_SRM_BASREGION_L(地区多语言)</a:Name>
<a:Code>T_SRM_BASREGION_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o97">
<a:ObjectID>0C1CF114-97D3-45ED-8148-C375C31A33E0</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>68730C17-01B6-4891-8596-930FD201068D</a:ObjectID>
<a:Name>地区</a:Name>
<a:Code>FREGIONID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>259E0139-90F3-4B97-A801-5F2B13FB8886</a:ObjectID>
<a:Name>多语言</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>181D9B0B-526E-4990-8749-FB84D09856AB</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>6984010D-1FD8-4060-BB8F-B32D5A9F4923</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o102">
<a:ObjectID>26CD18A5-371C-4383-8914-9626135C73CB</a:ObjectID>
<a:Name>PK_SRM_BASREGION_L</a:Name>
<a:Code>PK_SRM_BASREGION_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASREGION_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o97"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o102"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o102"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o103">
<a:ObjectID>8C2AF297-CFF4-4172-84F8-2837CF5C1223</a:ObjectID>
<a:Name>T_SRM_BASREGION(地区)</a:Name>
<a:Code>T_SRM_BASREGION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o104">
<a:ObjectID>93704228-3191-45BE-9394-E44AA37B8AED</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FREGIONID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>BE6E8999-76B9-4812-B575-DFF0DA1AF2FA</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>297C3BA4-9AF4-4F87-BC4C-68C40B0AEAA0</a:ObjectID>
<a:Name>城市</a:Name>
<a:Code>FCITYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>A1896043-1C7C-4267-9284-BBC028F5507E</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o108">
<a:ObjectID>725E6E7C-B0E1-4E61-92DD-589AB9B43273</a:ObjectID>
<a:Name>PK_SRM_BASREGION</a:Name>
<a:Code>PK_SRM_BASREGION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASREGION</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o104"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o108"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o108"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o109">
<a:ObjectID>4196F1D1-19B2-4AB4-8B7E-0B363663E0E8</a:ObjectID>
<a:Name>T_SRM_BASPROVINCE(省份)</a:Name>
<a:Code>T_SRM_BASPROVINCE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o110">
<a:ObjectID>A55D5A44-7237-4EF9-BC30-597C27E1F989</a:ObjectID>
<a:Name>省份</a:Name>
<a:Code>FPROVINCEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>8D84D413-899E-4CFF-8D8F-F391CD1572BD</a:ObjectID>
<a:Name>国家</a:Name>
<a:Code>FCOUNTRYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>F2E66863-10DD-44BB-8481-852D628B94CF</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>75044067-50AD-4B77-9CD1-D8249E933C75</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o114">
<a:ObjectID>C24D0079-C974-4B95-B0FE-2AF78F40C51F</a:ObjectID>
<a:Name>PK_SRM_BASPROVINCE</a:Name>
<a:Code>PK_SRM_BASPROVINCE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASPROVINCE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o110"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o114"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o114"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o115">
<a:ObjectID>80F89FE8-1886-4405-A92E-092CC49F4E4A</a:ObjectID>
<a:Name>T_SRM_BASPRODCATEGORY_L(多语言)</a:Name>
<a:Code>T_SRM_BASPRODCATEGORY_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o116">
<a:ObjectID>8D9D9186-3B39-4E40-97E4-53E90CD1B848</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>2C4CAD10-C7FF-4A6F-A8CD-8E5B3DBB1C8F</a:ObjectID>
<a:Name>产品类目</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>2EFA24A7-27C8-42B0-B6A4-9FE7554D8C98</a:ObjectID>
<a:Name>语言</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>E27B5949-4A72-4BF8-B4DE-3E36235FC7F5</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>DDF665CA-1DF4-4D73-8FE5-CACDAFFD7783</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o121">
<a:ObjectID>1903E9B7-3845-4828-86AD-C85FEE7695FA</a:ObjectID>
<a:Name>PK_SRM_BASPRODCATEGORY_L</a:Name>
<a:Code>PK_SRM_BASPRODCATEGORY_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASPRODCATEGORY_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o116"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o121"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o121"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o122">
<a:ObjectID>36F6319C-8E92-437E-9D94-56E229DE0D90</a:ObjectID>
<a:Name>T_SRM_BASPRODCATEGORY(产品类目)</a:Name>
<a:Code>T_SRM_BASPRODCATEGORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o123">
<a:ObjectID>2E851D45-E4E7-4737-8EA1-5BC06F2CD942</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>EE1C8ADD-569C-4F36-8E4E-3D58FA4316ED</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>DA284784-5804-4FEB-80D2-D121005DCB48</a:ObjectID>
<a:Name>级数</a:Name>
<a:Code>FLEVEL</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>DCA63045-83DD-4FB1-BEF5-FC0D479A039E</a:ObjectID>
<a:Name>父节点</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>749588F1-1680-4835-AEF7-26A6B33015FF</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1403832417</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>7D8C8208-D427-4D3F-A80C-9DA9127B4BE3</a:ObjectID>
<a:Name>是否明细级</a:Name>
<a:Code>FISDETAIL</a:Code>
<a:CreationDate>1403832417</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o129">
<a:ObjectID>F1E210C4-A978-438C-8AAA-FD9ACBA33141</a:ObjectID>
<a:Name>PK_SRM_BASPRODCATEGORY</a:Name>
<a:Code>PK_SRM_BASPRODCATEGORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASPRODCATEGORY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o123"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o129"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o129"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o130">
<a:ObjectID>AF3E2EDF-A375-4BE2-AFAA-08B169B68535</a:ObjectID>
<a:Name>T_SRM_BASCOUNTRY_L(国家多语言)</a:Name>
<a:Code>T_SRM_BASCOUNTRY_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o131">
<a:ObjectID>FE1F11FD-F16A-400F-8B0C-0D5E65F6B21A</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o132">
<a:ObjectID>37AEF00F-1AD9-4F7B-A49F-C35B95A3B22B</a:ObjectID>
<a:Name>国家主键</a:Name>
<a:Code>FCOUNTRYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o133">
<a:ObjectID>7E8E3375-03F8-4E59-80BB-7DEB7170544A</a:ObjectID>
<a:Name>语言</a:Name>
<a:Code>FLOCALEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>27E4AC70-4DAB-45DB-9E96-486748FD7CB5</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>04A1A420-062D-4BC8-8F8E-73A08C180CF0</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o136">
<a:ObjectID>6305BB79-EB2F-4F2B-881E-A72A9B1EC98D</a:ObjectID>
<a:Name>PK_SRM_BASCOUNTRY_L</a:Name>
<a:Code>PK_SRM_BASCOUNTRY_L</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASCOUNTRY_L</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o131"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o136"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o136"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o16">
<a:ObjectID>CA90DB4E-C38F-4E69-BCE1-DAC8713B875C</a:ObjectID>
<a:Name>T_SRM_BASCOUNTRY(国家)</a:Name>
<a:Code>T_SRM_BASCOUNTRY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o137">
<a:ObjectID>E78D26BE-1A3A-41A2-8B11-6130834FFC17</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FCOUNTRYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>C052CE4A-9232-4BE7-A103-123630D3E60C</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o139">
<a:ObjectID>368D75D6-A596-4430-A2B1-FA9063362A56</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o140">
<a:ObjectID>F069DE15-F380-45FE-8125-14A6300BDC75</a:ObjectID>
<a:Name>PK_SRM_BASCOUNTRY</a:Name>
<a:Code>PK_SRM_BASCOUNTRY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASCOUNTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o137"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o140"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o140"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o14">
<a:ObjectID>8DC7B4C8-FBF0-49E5-8FF6-7B23AE977605</a:ObjectID>
<a:Name>T_SRM_BASCITY(城市)</a:Name>
<a:Code>T_SRM_BASCITY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o141">
<a:ObjectID>363532C0-9F57-461B-B095-40C0DBB76584</a:ObjectID>
<a:Name>城市</a:Name>
<a:Code>FCITYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o142">
<a:ObjectID>91872C2C-326D-4DC3-B6B2-7192EF48BA24</a:ObjectID>
<a:Name>省份</a:Name>
<a:Code>FPROVINCEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>33E2ED9E-BB0E-4148-9E2D-4B4B13D75A2E</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>A9E8ADE8-98ED-41FC-847F-148A3C355544</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o145">
<a:ObjectID>C7EDC7E9-AF85-41DD-8241-2991B012FA28</a:ObjectID>
<a:Name>PK_SRM_BASCITY</a:Name>
<a:Code>PK_SRM_BASCITY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_BASCITY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o141"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o145"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o145"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o12">
<a:ObjectID>BA674544-B30D-4881-A9F1-E9D26FFA3C9C</a:ObjectID>
<a:Name>T_SRM_ANNOUNCEMENT(企业公告)</a:Name>
<a:Code>T_SRM_ANNOUNCEMENT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o146">
<a:ObjectID>26BD7055-60F2-47A1-A280-9BF104A5FC30</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>10D1B44A-42D2-4A56-8605-013865270A12</a:ObjectID>
<a:Name>标题</a:Name>
<a:Code>FTITLE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>847FCF00-F21C-4E2C-B019-4A904C5284B6</a:ObjectID>
<a:Name>类别</a:Name>
<a:Code>FNOTICETYPE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570902</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:Comment>1：紧急公告
2：重要公告
3：普通公告</a:Comment>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o149">
<a:ObjectID>0EC61441-BF98-4D28-89C4-884FEDD05C38</a:ObjectID>
<a:Name>内容</a:Name>
<a:Code>FCONTENT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o150">
<a:ObjectID>8702BF4B-BDB3-4788-9E5E-3F4D6D50D6B9</a:ObjectID>
<a:Name>发布人</a:Name>
<a:Code>FPUBLISHER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>1C2C693A-7D8F-43F6-86A9-0CA3D2A329E7</a:ObjectID>
<a:Name>发布日期</a:Name>
<a:Code>FPUBLISHDATE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o152">
<a:ObjectID>07EB9A4F-36D3-4D23-9879-16CD185A2C2B</a:ObjectID>
<a:Name>发布状态</a:Name>
<a:Code>FPUBLISHSTATUS</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:Comment>0:未发布
1:已发布</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>6C904705-87E3-456D-9EFB-FF256EAD9629</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o154">
<a:ObjectID>C34EFDB3-3A9D-4854-94A0-A0390F4BE413</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>2AEB55D3-2ED7-41B2-BD50-6D458A825DDB</a:ObjectID>
<a:Name>企业ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o156">
<a:ObjectID>8C7D39BE-7BA7-4B1F-B638-F01A5662731B</a:ObjectID>
<a:Name>PK_SRM_ANNOUNCEMENT</a:Name>
<a:Code>PK_SRM_ANNOUNCEMENT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_ANNOUNCEMENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o146"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o156"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o156"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o157">
<a:ObjectID>C953FA13-6ABD-4DD6-B629-BA3BA53248FF</a:ObjectID>
<a:Name>T_SRM_PERMISSIONITEM(权限项目)</a:Name>
<a:Code>T_SRM_PERMISSIONITEM</a:Code>
<a:CreationDate>1397043086</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o158">
<a:ObjectID>178F6E25-F519-49ED-81BF-85D84B8184E7</a:ObjectID>
<a:Name>权限项目ID</a:Name>
<a:Code>FITEMID</a:Code>
<a:CreationDate>1397043108</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>DD4F6532-7DFA-4FA8-BA40-2E4804CDDC11</a:ObjectID>
<a:Name>对象类型</a:Name>
<a:Code>FPERMISSIONTYPEID</a:Code>
<a:CreationDate>1397043108</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404722446</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>7B496D33-A3AA-4409-9296-7B882C491B09</a:ObjectID>
<a:Name>项目名称</a:Name>
<a:Code>FPERMISSIONNAME</a:Code>
<a:CreationDate>1397043108</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o161">
<a:ObjectID>43B99E93-EBD3-4E63-A653-EE59AAE81193</a:ObjectID>
<a:Name>PK_SRM_PERMISSIONITEM</a:Name>
<a:Code>PK_SRM_PERMISSIONITEM</a:Code>
<a:CreationDate>1397044526</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_PERMISSIONITEM</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o158"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o161"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o161"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o162">
<a:ObjectID>0ADBFCDD-1C70-4506-BDEB-4BF1D17F065F</a:ObjectID>
<a:Name>T_SRM_SUBSYS(子系统)</a:Name>
<a:Code>T_SRM_SUBSYS</a:Code>
<a:CreationDate>1397044768</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o163">
<a:ObjectID>BEAF3386-2458-4552-8CF8-EF053F3BF3F4</a:ObjectID>
<a:Name>系统ID</a:Name>
<a:Code>FSUBSYSID</a:Code>
<a:CreationDate>1397044768</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1400570929</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>B36F9BE2-3F32-4700-BA83-15B60C98F4C9</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1397044768</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o165">
<a:ObjectID>80AA3A0F-38FF-449F-98AD-7C4C8530E89A</a:ObjectID>
<a:Name>PK_SRM_SUBSYS</a:Name>
<a:Code>PK_SRM_SUBSYS</a:Code>
<a:CreationDate>1397044768</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_SUBSYS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o163"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o165"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o165"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o166">
<a:ObjectID>9C622F89-D460-48D6-ADAF-5C544857958C</a:ObjectID>
<a:Name>T_SRM_CURRENCY(币别)</a:Name>
<a:Code>T_SRM_CURRENCY</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403591659</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o167">
<a:ObjectID>11FC6629-88EE-4D89-ACA6-5D1EA3D18705</a:ObjectID>
<a:Name>币别ID</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402368573</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>A51B82BE-516E-44AB-A741-8ED886F18CB4</a:ObjectID>
<a:Name>代码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402368573</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>08261F82-7971-489D-A9F9-98B8A448C540</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402368573</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>8A64089D-B9C8-4C2A-B14C-E05D69C83DB2</a:ObjectID>
<a:Name>符合</a:Name>
<a:Code>FSYSMBOL</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402368573</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o171">
<a:ObjectID>4EAF863E-2EB8-44E5-BE08-729B596BA8B2</a:ObjectID>
<a:Name>缩写</a:Name>
<a:Code>FCODE</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402368573</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o172">
<a:ObjectID>46B6B05B-20A5-4643-A79E-1DFFA36FABFD</a:ObjectID>
<a:Name>单价精度</a:Name>
<a:Code>FPRICEDIGITS</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402368573</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>2</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o173">
<a:ObjectID>5F69636E-2DFB-423C-B2CE-FD3011A748D3</a:ObjectID>
<a:Name>金额精度</a:Name>
<a:Code>FAMOUNTDIGITS</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402368573</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>2</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o174">
<a:ObjectID>7DF0FA16-B970-426D-9CCF-20760A775812</a:ObjectID>
<a:Name>PK_SRM_CURRENCY</a:Name>
<a:Code>PK_SRM_CURRENCY</a:Code>
<a:CreationDate>1402363599</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402368573</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_CURRENCY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o167"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o174"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o174"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o175">
<a:ObjectID>4E0A19ED-416D-481A-B41C-2069F37F7F2D</a:ObjectID>
<a:Name>T_SRM_PURPRODPROP(供应产品属性)</a:Name>
<a:Code>T_SRM_PURPRODPROP</a:Code>
<a:CreationDate>1403837017</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404192782</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o176">
<a:ObjectID>D4AE022C-CC98-464E-A60B-6D2BC53D6042</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1403837017</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>1A3917D6-311F-4F4F-AA7F-5D7EE76D84F4</a:ObjectID>
<a:Name>产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1403837017</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>148E088F-F05F-4EF1-A22A-767E60406E15</a:ObjectID>
<a:Name>属性名</a:Name>
<a:Code>FPROPNAME</a:Code>
<a:CreationDate>1403837017</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>026AB40A-337F-4D27-AEEF-D6524E4B656D</a:ObjectID>
<a:Name>属性值</a:Name>
<a:Code>FPROPVALUE</a:Code>
<a:CreationDate>1403837017</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o180">
<a:ObjectID>5226EE37-D688-495D-AD84-C52DAA987558</a:ObjectID>
<a:Name>PK_SRM_PURPRODPROP</a:Name>
<a:Code>PK_SRM_PURPRODPROP</a:Code>
<a:CreationDate>1403837017</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404192794</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_PURPRODPROP</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o176"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o180"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o180"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o181">
<a:ObjectID>35B80D8E-4DFD-464C-AE8D-0528AC6DD6C0</a:ObjectID>
<a:Name>T_SRM_SALPRODIMG(销售产品图片信息)</a:Name>
<a:Code>T_SRM_SALPRODIMG</a:Code>
<a:CreationDate>1404268609</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o182">
<a:ObjectID>B11FB7C8-2642-46F8-82FA-F4C845980A7A</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1404268609</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>C49C06F0-3779-4CA9-8777-2A4D73A13BF1</a:ObjectID>
<a:Name>产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1404268609</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>C02E8389-68D4-4569-BD5C-E0EF321E8C70</a:ObjectID>
<a:Name>小图路径</a:Name>
<a:Code>FSMALLIMGPATH</a:Code>
<a:CreationDate>1404268609</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>2699A16E-DDB9-454E-BCD4-8200ED3C951C</a:ObjectID>
<a:Name>大图路径</a:Name>
<a:Code>FLARGEIMGPATH</a:Code>
<a:CreationDate>1404268609</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o186">
<a:ObjectID>BC30492C-E24F-47BA-A3E6-8249AF55CF93</a:ObjectID>
<a:Name>PK_SRM_SALPRODIMG</a:Name>
<a:Code>PK_SRM_SALPRODIMG</a:Code>
<a:CreationDate>1404268609</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_SALPRODIMG</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o182"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o186"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o186"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o187">
<a:ObjectID>8D33ADCD-0D62-4618-A9A7-C9B7E32C46BC</a:ObjectID>
<a:Name>T_SRM_COMPANYADDRESS(公司交货地址)</a:Name>
<a:Code>T_SRM_COMPANYADDRESS</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o188">
<a:ObjectID>EB2DDF3C-9DBA-4C51-8D23-0269604603C8</a:ObjectID>
<a:Name>公司</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o189">
<a:ObjectID>3C9C5154-33BD-4839-83DC-EFC036FFA2F4</a:ObjectID>
<a:Name>分录</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o190">
<a:ObjectID>D68F54C6-37CD-4313-A59A-B84CCDDF30E3</a:ObjectID>
<a:Name>收货人</a:Name>
<a:Code>FRECEIVER</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o191">
<a:ObjectID>94067731-E015-4136-AA6F-8345FA12AB87</a:ObjectID>
<a:Name>省份</a:Name>
<a:Code>FPROVINCEID</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o192">
<a:ObjectID>34E0FEE9-9B64-446E-8CF2-519CAC757110</a:ObjectID>
<a:Name>城市</a:Name>
<a:Code>FCITYID</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o193">
<a:ObjectID>99A89113-50F8-47E1-A8A8-6C88035AD372</a:ObjectID>
<a:Name>地区</a:Name>
<a:Code>FREGIONID</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>B7C669E3-684F-4103-90E1-4CE0615A6678</a:ObjectID>
<a:Name>地址</a:Name>
<a:Code>FADDRESS</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>E95E940A-B5DB-46BE-A50D-11B5BE209056</a:ObjectID>
<a:Name>邮编</a:Name>
<a:Code>FPOSTCODE</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>341D3E62-5DBF-4585-8E66-F3E5EF9D0B12</a:ObjectID>
<a:Name>电话_国际</a:Name>
<a:Code>FTEL_INTER</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>49267BFE-372A-4920-8994-39428014F276</a:ObjectID>
<a:Name>电话_区号</a:Name>
<a:Code>FTEL_CODE</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>0FDCC4DA-C132-40FA-A70B-ED55A99AC943</a:ObjectID>
<a:Name>电话</a:Name>
<a:Code>FTEL</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>9FE58172-304E-493F-8F1E-58287863B036</a:ObjectID>
<a:Name>手机</a:Name>
<a:Code>FPHONE</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>607D338A-8F0A-4868-9A35-5F346DAFC774</a:ObjectID>
<a:Name>是否默认</a:Name>
<a:Code>FISDEFAULT</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o201">
<a:ObjectID>447D0B3C-EC99-4350-A3FC-4D141E49CD03</a:ObjectID>
<a:Name>PK_SRM_COMPANYADDRESS</a:Name>
<a:Code>PK_SRM_COMPANYADDRESS</a:Code>
<a:CreationDate>1404712874</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_COMPANYADDRESS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o189"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o201"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o201"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o202">
<a:ObjectID>96E33437-453D-45DC-A779-0A358D955798</a:ObjectID>
<a:Name>T_SRM_PERMISSIONTYPE(权限类别)</a:Name>
<a:Code>T_SRM_PERMISSIONTYPE</a:Code>
<a:CreationDate>1404722527</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o203">
<a:ObjectID>E383781B-3AB5-4513-BA49-21883B540990</a:ObjectID>
<a:Name>类型ID</a:Name>
<a:Code>FPERMISSIONTYPEID</a:Code>
<a:CreationDate>1404722527</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>EEFCFD34-C645-4457-B2B5-ACF496EDFFDA</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1404722527</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o205">
<a:ObjectID>18980B4F-6AA8-49BE-AA68-01E947BF13D8</a:ObjectID>
<a:Name>子系统</a:Name>
<a:Code>FSUBSYSID</a:Code>
<a:CreationDate>1404722928</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o206">
<a:ObjectID>7686859F-2CFE-44BB-A2AD-2BBB78A32B2D</a:ObjectID>
<a:Name>PK_SRM_PERMISSIONTYPE</a:Name>
<a:Code>PK_SRM_PERMISSIONTYPE</a:Code>
<a:CreationDate>1404722527</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404724051</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_PERMISSIONTYPE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o203"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o206"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o206"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o32">
<a:ObjectID>25C46F99-5318-4E19-AC56-C62FE70F7F75</a:ObjectID>
<a:Name>T_SRM_PURPRODPRICE(采购产品价格信息)</a:Name>
<a:Code>T_SRM_PURPRODPRICE</a:Code>
<a:CreationDate>1405339698</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1405339880</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o207">
<a:ObjectID>81135A85-C988-4734-89AD-F9C1575C83BF</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1405339698</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1405339880</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o208">
<a:ObjectID>9CB8DF43-F57F-4E3B-A6B6-5FB930D22B76</a:ObjectID>
<a:Name>产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1405339698</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1405339880</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o209">
<a:ObjectID>BB3ABE2E-BCF9-4A5D-AC47-A915AE01C0D0</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>1405339698</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1405339880</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o210">
<a:ObjectID>C6E7C178-CAF9-4C31-A3FC-46C1186F5E9E</a:ObjectID>
<a:Name>单价</a:Name>
<a:Code>FQUOTEPRICE</a:Code>
<a:CreationDate>1405339698</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1405430936</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o211">
<a:ObjectID>9475E15F-1E93-4AB9-88B6-B5A6410C7035</a:ObjectID>
<a:Name>数量段</a:Name>
<a:Code>FINTERVAL</a:Code>
<a:CreationDate>1405339698</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1405339880</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o212">
<a:ObjectID>1368270D-309F-4DAF-BB96-33338832A26D</a:ObjectID>
<a:Name>PK_SRM_PURPRODPRICE</a:Name>
<a:Code>PK_SRM_PURPRODPRICE</a:Code>
<a:CreationDate>1405339698</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1405339880</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_PURPRODPRICE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o207"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o212"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o212"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o213">
<a:ObjectID>98A95488-DD71-489B-8D65-40D098F0FC1B</a:ObjectID>
<a:Name>T_SRM_PURPRODATTACH(产品附件)</a:Name>
<a:Code>T_SRM_PURPRODATTACH</a:Code>
<a:CreationDate>1405503051</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1405504038</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o214">
<a:ObjectID>14113FAB-9B06-42B3-A110-A06EA3CFC6A3</a:ObjectID>
<a:Name>产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1405503051</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1405504038</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o215">
<a:ObjectID>B8FCBA5C-78BF-4F28-A043-A07617B61C48</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1405503051</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1405504038</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o216">
<a:ObjectID>232852DC-F402-4B73-BFB5-A393FCCB4A00</a:ObjectID>
<a:Name>附件地址</a:Name>
<a:Code>FATTACHURL</a:Code>
<a:CreationDate>1405503051</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1405504038</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o217">
<a:ObjectID>5C853DE9-BF89-4400-90D4-2CBD38239EDA</a:ObjectID>
<a:Name>附件名</a:Name>
<a:Code>FATTACHNAME</a:Code>
<a:CreationDate>1405588293</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1405588539</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o218">
<a:ObjectID>1F162286-C42C-4102-AC5C-B62061FEA6CF</a:ObjectID>
<a:Name>PK_SRM_PURPRODATTACH</a:Name>
<a:Code>PK_SRM_PURPRODATTACH</a:Code>
<a:CreationDate>1405503051</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1405504038</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_PURPRODATTACH</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o215"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o218"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o218"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o219">
<a:ObjectID>758E9E6F-94CD-4707-83C4-A0AABF017EE9</a:ObjectID>
<a:Name>T_SRM_USERROLE(用户角色对应关系)</a:Name>
<a:Code>T_SRM_USERROLE</a:Code>
<a:CreationDate>1394071317</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1406268100</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>用户角色对应关系</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o220">
<a:ObjectID>DAA8C02D-2B9D-47C0-8DBF-720789CC4D53</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1394071408</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o221">
<a:ObjectID>263F127C-DF6F-4FAA-BC55-80EA952775D4</a:ObjectID>
<a:Name>角色ID</a:Name>
<a:Code>FROLEID</a:Code>
<a:CreationDate>1394071408</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o222">
<a:ObjectID>D441E2CC-A420-4EAA-9BEE-79A27E70DB56</a:ObjectID>
<a:Name>PK_SRM_USERROLE</a:Name>
<a:Code>PK_SRM_USERROLE</a:Code>
<a:CreationDate>1394071408</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_USERROLE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o220"/>
<o:Column Ref="o221"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o222"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o222"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o223">
<a:ObjectID>2A73C605-A712-41DC-ADCF-F779C209F073</a:ObjectID>
<a:Name>T_SRM_ROLE(角色)</a:Name>
<a:Code>T_SRM_ROLE</a:Code>
<a:CreationDate>1394069816</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1406268100</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>角色</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o224">
<a:ObjectID>ECA1652B-9B12-4828-BF42-A9EBEC731DDB</a:ObjectID>
<a:Name>角色ID</a:Name>
<a:Code>FROLEID</a:Code>
<a:CreationDate>1394069816</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o225">
<a:ObjectID>A420B498-5FD1-4126-A43A-08C06C04C1BB</a:ObjectID>
<a:Name>角色编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1394069816</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o226">
<a:ObjectID>D8E224F9-B347-41F1-92D4-4DA6928AEEF5</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1394069816</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o227">
<a:ObjectID>86571AE5-A50F-4568-801A-07260DD7BB06</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1394069816</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o228">
<a:ObjectID>450CE90B-0620-4C35-AC26-CA13553ABAFB</a:ObjectID>
<a:Name>PK_SRM_ROLE</a:Name>
<a:Code>PK_SRM_ROLE</a:Code>
<a:CreationDate>1394069816</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_ROLE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o224"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o228"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o228"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o229">
<a:ObjectID>2C7E3856-1E98-4B9C-A740-8E3E093166C5</a:ObjectID>
<a:Name>T_SRM_USERPERMISSION(用户权限表)</a:Name>
<a:Code>T_SRM_USERPERMISSION</a:Code>
<a:CreationDate>1397045642</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o230">
<a:ObjectID>A0D8E064-9C18-4D61-9E2D-61EB3AD0C38B</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1397045671</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o231">
<a:ObjectID>460F4A6A-A663-4ED7-9C7D-26046E99FFDB</a:ObjectID>
<a:Name>权限ID</a:Name>
<a:Code>FPERMISSIONID</a:Code>
<a:CreationDate>1397045671</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o232">
<a:ObjectID>13873FEE-C1B5-4DE2-837B-0304F0BF70AD</a:ObjectID>
<a:Name>PK_SRM_USERPERMISSION</a:Name>
<a:Code>PK_SRM_USERPERMISSION</a:Code>
<a:CreationDate>1397045671</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_USERPERMISSION</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o230"/>
<o:Column Ref="o231"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o232"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o232"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o233">
<a:ObjectID>8DCFF4E7-927E-48E5-B64F-7D04E99BFF1A</a:ObjectID>
<a:Name>T_SRM_USERMSGCONFIG(用户消息配置)</a:Name>
<a:Code>T_SRM_USERMSGCONFIG</a:Code>
<a:CreationDate>1407482255</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407482999</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o234">
<a:ObjectID>C240B181-34B8-4903-80CC-6F0A2147183B</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1407482255</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407482999</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o235">
<a:ObjectID>304E6FCA-6E14-481E-A001-0EFD399D9D8C</a:ObjectID>
<a:Name>接收消息配置</a:Name>
<a:Code>FACCEPT</a:Code>
<a:CreationDate>1407482255</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407482999</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o236">
<a:ObjectID>B352419C-925A-454F-99F5-F40957AD5EDB</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1407482255</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407482999</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o234"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o236"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o236"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o237">
<a:ObjectID>691C67DB-0181-4F30-B353-4B0E89E8A159</a:ObjectID>
<a:Name>T_SRM_USERBUSMSGSTATUS(用户业务消息状态表)</a:Name>
<a:Code>T_SRM_USERBUSMSGSTATUS</a:Code>
<a:CreationDate>1407746753</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o238">
<a:ObjectID>5DE385F5-1716-4B56-81C8-C10D21047517</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1407746753</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o239">
<a:ObjectID>93D60441-A9F9-471E-BF00-9E2C9644ED00</a:ObjectID>
<a:Name>消息ID</a:Name>
<a:Code>FMSGID</a:Code>
<a:CreationDate>1407746753</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407751691</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o240">
<a:ObjectID>90953954-0AD5-4442-862C-302D5E416237</a:ObjectID>
<a:Name>已读</a:Name>
<a:Code>FHASREAD</a:Code>
<a:CreationDate>1407746753</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o241">
<a:ObjectID>66C132FE-5AAF-4339-84E0-B3A3969D9984</a:ObjectID>
<a:Name>已删除</a:Name>
<a:Code>FHASDELETE</a:Code>
<a:CreationDate>1407746753</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o242">
<a:ObjectID>F3859BE9-3522-423F-8126-3D04B5F06E90</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1407746753</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o238"/>
<o:Column Ref="o239"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o242"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o242"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o243">
<a:ObjectID>4D0A911D-1BEB-4472-B1D6-5ABDBA041E2C</a:ObjectID>
<a:Name>T_SRM_USERSYSMSGSTATUS(用户系统消息状态表)</a:Name>
<a:Code>T_SRM_USERSYSMSGSTATUS</a:Code>
<a:CreationDate>1407808965</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o244">
<a:ObjectID>3DAB1D6A-5858-4CA5-A3C4-FBC9CD7D9707</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1407808965</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o245">
<a:ObjectID>8ACF7F14-B1A9-4F8D-8F58-8D02A4048216</a:ObjectID>
<a:Name>消息ID</a:Name>
<a:Code>FMSGID</a:Code>
<a:CreationDate>1407808965</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o246">
<a:ObjectID>4FE0C2CA-8F0E-479F-B617-6EE53E855A7F</a:ObjectID>
<a:Name>已读</a:Name>
<a:Code>FHASREAD</a:Code>
<a:CreationDate>1407808965</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o247">
<a:ObjectID>6A504943-7B4D-4EEF-AF45-334132CBC7A9</a:ObjectID>
<a:Name>已删除</a:Name>
<a:Code>FHASDELETE</a:Code>
<a:CreationDate>1407808965</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o248">
<a:ObjectID>4F605B54-810F-41C0-9CA5-3776CE18BC26</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1407808965</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o244"/>
<o:Column Ref="o245"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o248"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o248"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o249">
<a:ObjectID>4F061E70-D2F5-47D8-B7E3-1317C4EFD819</a:ObjectID>
<a:Name>T_SRM_FAVORITE(收藏夹)</a:Name>
<a:Code>T_SRM_FAVORITE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o250">
<a:ObjectID>0EE1B592-F7CA-47C4-960E-AB60984C6990</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o251">
<a:ObjectID>59AAFDBE-A028-41DA-864F-2F864C27ABE6</a:ObjectID>
<a:Name>收藏信息类别</a:Name>
<a:Code>FTYPE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405751598</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:Comment>p:商品信息
R:求购信息
C:</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o252">
<a:ObjectID>E2590FB8-5285-49AD-B29D-8E9E639E3D6F</a:ObjectID>
<a:Name>收藏产品Id</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405751579</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o253">
<a:ObjectID>4687F6DC-7D95-4DD5-8AEF-A881B82629E9</a:ObjectID>
<a:Name>收藏人</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405751591</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o254">
<a:ObjectID>C5BDF456-F518-4A85-9F97-A9E5BADC2F9C</a:ObjectID>
<a:Name>收藏日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405751755</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o255">
<a:ObjectID>D82557A2-6218-4A8A-A825-E6BE85B7883F</a:ObjectID>
<a:Name>企业ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1407897228</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1407897540</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o256">
<a:ObjectID>8A68176E-CAAE-4D0C-8044-62BC881F595E</a:ObjectID>
<a:Name>PK_SRM_FAVORITE</a:Name>
<a:Code>PK_SRM_FAVORITE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_FAVORITE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o250"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o256"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o256"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o257">
<a:ObjectID>731470BE-2B62-46B3-ADD3-9535E373BCB1</a:ObjectID>
<a:Name>T_SRM_FAVORITESUPPLIER(收藏夹-供应商)</a:Name>
<a:Code>T_SRM_FAVORITESUPPLIER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1407301950</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o258">
<a:ObjectID>3582C46D-AE0E-462D-A53C-3FEB60320F30</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1407301950</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o259">
<a:ObjectID>BAB5007B-2D83-4C37-9957-1BF0BF6B2DCD</a:ObjectID>
<a:Name>收藏信息类别</a:Name>
<a:Code>FTYPE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1407301950</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o260">
<a:ObjectID>0A2E581B-47D4-4293-B634-0097A23C5612</a:ObjectID>
<a:Name>收藏的供应商Id</a:Name>
<a:Code>FSUPPLIERORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1407897480</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o261">
<a:ObjectID>20135ED3-15E9-4A33-871C-9DA8B6EE8D0E</a:ObjectID>
<a:Name>收藏人</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1407301950</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o262">
<a:ObjectID>A4355095-3209-42D3-ABB3-EBC402E75745</a:ObjectID>
<a:Name>收藏日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1407301950</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o263">
<a:ObjectID>971C945F-8B7F-4652-95A9-DBBBCD461D18</a:ObjectID>
<a:Name>企业ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1407897453</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1407897540</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o264">
<a:ObjectID>99842F77-1389-417E-852C-0BE1F51E6CE8</a:ObjectID>
<a:Name>PK_SRM_FAVORITESUPPLIER</a:Name>
<a:Code>PK_SRM_FAVORITESUPPLIER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1407301950</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_FAVORITESUPPLIER</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o258"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o264"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o264"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o265">
<a:ObjectID>37AAEEC4-B7C0-47D7-A398-0CCA85CD6064</a:ObjectID>
<a:Name>T_SRM_COMPANYPERMISSION(公司权限表)</a:Name>
<a:Code>T_SRM_COMPANYPERMISSION</a:Code>
<a:CreationDate>1409056963</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1409057354</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o266">
<a:ObjectID>CC009FBF-54C0-4A75-9077-405F9107C74B</a:ObjectID>
<a:Name>公司ID</a:Name>
<a:Code>FCompanyId</a:Code>
<a:CreationDate>1409056963</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1409057354</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o267">
<a:ObjectID>41DFA60A-5A7C-485F-B744-ABBDDB06018B</a:ObjectID>
<a:Name>权限类别ID</a:Name>
<a:Code>FPermissionTypeId</a:Code>
<a:CreationDate>1409056963</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1409057354</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o268">
<a:ObjectID>076C1BCD-5FA0-4254-9CFC-C361697CE137</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1409056963</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1409057354</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o266"/>
<o:Column Ref="o267"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o268"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o268"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o269">
<a:ObjectID>C09BD91D-66FF-487A-AE2B-49F2762EC8C1</a:ObjectID>
<a:Name>T_SRM_SALPRODCUST(销售商品定向发布客户)</a:Name>
<a:Code>T_SRM_SALPRODCUST</a:Code>
<a:CreationDate>1409107661</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409107923</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o270">
<a:ObjectID>46B53CDE-AE4A-4C59-AFCC-FE097C5FC562</a:ObjectID>
<a:Name>产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1409107661</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409107923</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o271">
<a:ObjectID>9AB1488F-D91B-4D5E-BA0F-E61EA381F96D</a:ObjectID>
<a:Name>客户ID</a:Name>
<a:Code>FCUSTORGID</a:Code>
<a:CreationDate>1409107661</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409107923</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o272">
<a:ObjectID>0CD79941-C92A-4A5E-8DB4-B04E403A185C</a:ObjectID>
<a:Name>发布日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1409107661</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409107923</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o273">
<a:ObjectID>B2592C89-1DF7-460E-B04F-2801A645C684</a:ObjectID>
<a:Name>发布者</a:Name>
<a:Code>FCREATOR</a:Code>
<a:CreationDate>1409107661</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409107923</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o274">
<a:ObjectID>E1136464-6356-49BE-8B12-D2622B5067EA</a:ObjectID>
<a:Name>PK_SRM_SALPRODCUST</a:Name>
<a:Code>PK_SRM_SALPRODCUST</a:Code>
<a:CreationDate>1409107661</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409107923</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_SALPRODCUST</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o270"/>
<o:Column Ref="o271"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o274"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o274"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o22">
<a:ObjectID>00605373-EA9B-4524-A435-A7CD195D84AA</a:ObjectID>
<a:Name>T_SRM_MENUTYPE(菜单类别)</a:Name>
<a:Code>T_SRM_MENUTYPE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o275">
<a:ObjectID>D4468469-92AF-4DD4-A453-EB0DAF9E60DF</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o276">
<a:ObjectID>D42B517B-85FE-42FF-AD3E-6BE34B390E76</a:ObjectID>
<a:Name>菜单编码</a:Name>
<a:Code>FMENUCODE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o277">
<a:ObjectID>2188FB7E-4B86-4D36-BBB1-549ECE6F932B</a:ObjectID>
<a:Name>父菜单</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o278">
<a:ObjectID>F120EFA5-B13C-42E9-92E9-A53EB33086F4</a:ObjectID>
<a:Name>默认Controller</a:Name>
<a:Code>FCONTROLLER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1401866211</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o279">
<a:ObjectID>CC6A3191-32A8-4542-8046-A65F84D29608</a:ObjectID>
<a:Name>默认Action</a:Name>
<a:Code>FACTION</a:Code>
<a:CreationDate>1401866132</a:CreationDate>
<a:Creator>Rd_xiaofeng_cheng</a:Creator>
<a:ModificationDate>1401867007</a:ModificationDate>
<a:Modifier>Rd_xiaofeng_cheng</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o280">
<a:ObjectID>24B9994A-FFE1-40A6-847F-D98919F6F2C6</a:ObjectID>
<a:Name>菜单分组</a:Name>
<a:Code>FMENUGROUP</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o281">
<a:ObjectID>E61DDEF3-DD19-45A3-A162-440367AFDBEA</a:ObjectID>
<a:Name>权限项目</a:Name>
<a:Code>FPERMISSIONITEM</a:Code>
<a:CreationDate>1409188421</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409193637</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o282">
<a:ObjectID>FE6834B5-482D-4415-8831-3D15E0C4D39D</a:ObjectID>
<a:Name>PK_SRM_MENUTYPE</a:Name>
<a:Code>PK_SRM_MENUTYPE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_MENUTYPE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o275"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o282"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o282"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o283">
<a:ObjectID>698EADCC-5565-4F9B-8754-5219DF1DB88B</a:ObjectID>
<a:Name>T_SRM_ARTICLE(客服消息)</a:Name>
<a:Code>T_SRM_ARTICLE</a:Code>
<a:CreationDate>1409795748</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409813874</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o284">
<a:ObjectID>CDF48FC7-35CE-4869-998F-78EBAFA910BE</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1409795748</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409813874</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o285">
<a:ObjectID>F7DDCBCB-B0E5-4A63-89ED-4442F9F7E6D6</a:ObjectID>
<a:Name>标题</a:Name>
<a:Code>FTITLE</a:Code>
<a:CreationDate>1409795748</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409813874</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o286">
<a:ObjectID>229D4C72-AB23-46B9-B237-37B0B3930DA5</a:ObjectID>
<a:Name>内容</a:Name>
<a:Code>FCONTENT</a:Code>
<a:CreationDate>1409795748</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409813874</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>ntext</a:DataType>
</o:Column>
<o:Column Id="o287">
<a:ObjectID>F3F27663-D615-4E15-B7E5-76FA00A640AE</a:ObjectID>
<a:Name>类型</a:Name>
<a:Code>FTYPE</a:Code>
<a:CreationDate>1409797489</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409813874</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o288">
<a:ObjectID>868AB8AB-E7B1-4DF1-A230-03EF9FF39C26</a:ObjectID>
<a:Name>日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1409811251</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409813874</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o289">
<a:ObjectID>6704CF96-55A3-499E-A858-A985CD07E104</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1409811268</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409813874</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o290">
<a:ObjectID>FC3EF0D6-AB28-4858-B245-7702CDF5953F</a:ObjectID>
<a:Name>PK_SRM_ARTICLE</a:Name>
<a:Code>PK_SRM_ARTICLE</a:Code>
<a:CreationDate>1409795748</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409813874</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_ARTICLE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o284"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o290"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o290"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o291">
<a:ObjectID>F1C1FA43-7DC3-49B2-B897-6AD23EE232FB</a:ObjectID>
<a:Name>T_SRM_BUSINESSMSG(业务消息)</a:Name>
<a:Code>T_SRM_BUSINESSMSG</a:Code>
<a:CreationDate>1407745554</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o292">
<a:ObjectID>5A57E9D4-A4EC-4199-B4EE-166B0EC391D6</a:ObjectID>
<a:Name>消息ID</a:Name>
<a:Code>FMSGID</a:Code>
<a:CreationDate>1407745554</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407751755</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o293">
<a:ObjectID>A7D5A27B-E1A5-473D-AA61-77E8ECF13F09</a:ObjectID>
<a:Name>标题</a:Name>
<a:Code>FTITLE</a:Code>
<a:CreationDate>1407745554</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o294">
<a:ObjectID>6E384490-6030-48BB-B083-CDCE46F459B3</a:ObjectID>
<a:Name>消息内容</a:Name>
<a:Code>FCONTENT</a:Code>
<a:CreationDate>1407745554</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1410917651</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o295">
<a:ObjectID>D0FB027B-0068-48BB-A298-8D5BA2DE9E80</a:ObjectID>
<a:Name>发送时间</a:Name>
<a:Code>FSENDDATE</a:Code>
<a:CreationDate>1407745554</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>getdate()</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o296">
<a:ObjectID>0B74D692-E222-446E-800C-E12F19A1FB8A</a:ObjectID>
<a:Name>链接</a:Name>
<a:Code>FLINK</a:Code>
<a:CreationDate>1409826383</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1410917651</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;#&#39;</a:DefaultValue>
<a:DataType>varchar(1000)</a:DataType>
<a:Length>1000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o297">
<a:ObjectID>33D82624-4796-4420-AFA6-1D43578F2A9C</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1407745554</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407747617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o292"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o297"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o297"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o34">
<a:ObjectID>E8BDE4B6-8E69-4FB2-A409-EBFA03E74C17</a:ObjectID>
<a:Name>T_SRM_QUOTEATTACH(报价附件)</a:Name>
<a:Code>T_SRM_QUOTEATTACH</a:Code>
<a:CreationDate>1408002043</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408021490</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o298">
<a:ObjectID>E1F98D53-CA0D-4E25-8F97-26E388E91526</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1408002043</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408002373</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o299">
<a:ObjectID>8608ED8E-FA74-483B-8FE9-77A07CC7A381</a:ObjectID>
<a:Name>内码FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1411110829</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411111764</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o300">
<a:ObjectID>62FC5651-73DC-45AE-93A6-C394FE64D7FC</a:ObjectID>
<a:Name>附件地址</a:Name>
<a:Code>FATTACHURL</a:Code>
<a:CreationDate>1408002043</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408002373</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o301">
<a:ObjectID>B0EA7CCA-01C1-460F-AF8E-6F0CE08B5902</a:ObjectID>
<a:Name>附件名</a:Name>
<a:Code>FATTACHNAME</a:Code>
<a:CreationDate>1408002043</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408002373</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o302">
<a:ObjectID>9E787E67-4E99-4C5D-B605-7E852678A52F</a:ObjectID>
<a:Name>PK_SRM_QUOTEATTACH</a:Name>
<a:Code>PK_SRM_QUOTEATTACH</a:Code>
<a:CreationDate>1408002043</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408002373</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_QUOTEATTACH</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o298"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o302"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o302"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o303">
<a:ObjectID>A991F7DB-71F7-4C9A-849B-68CDB904B734</a:ObjectID>
<a:Name>T_SRM_PURSUPTYPE(自定义供应商类别)</a:Name>
<a:Code>T_SRM_PURSUPTYPE</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o304">
<a:ObjectID>BE5ADABC-2A09-4AE1-84E6-D5EB841DBC66</a:ObjectID>
<a:Name>本公司ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o305">
<a:ObjectID>DAE55CA4-866D-4959-A230-A00BD78FF444</a:ObjectID>
<a:Name>类型ID</a:Name>
<a:Code>FTYPEID</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o306">
<a:ObjectID>C511BA58-850D-4F8F-8735-2C6EE4A95720</a:ObjectID>
<a:Name>类型名称</a:Name>
<a:Code>FTYPENAME</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o307">
<a:ObjectID>AA3DF1CF-45D9-4025-8686-B3212E718F88</a:ObjectID>
<a:Name>级次</a:Name>
<a:Code>FLEVEL</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o308">
<a:ObjectID>15E7896F-5D81-4C3E-B998-36C9F208D8DA</a:ObjectID>
<a:Name>是否明细</a:Name>
<a:Code>FISDETAIL</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o309">
<a:ObjectID>867C1EBE-D965-493E-8F07-A6E278196379</a:ObjectID>
<a:Name>父节点</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>1402907942</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409535138</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o310">
<a:ObjectID>6DAC3982-90C5-46FC-B17A-EAEB139AAD0F</a:ObjectID>
<a:Name>全称</a:Name>
<a:Code>FLONGNAME</a:Code>
<a:CreationDate>1411113941</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411115987</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o311">
<a:ObjectID>CABEA107-8ED9-409A-873E-5FAA7EA6F6DA</a:ObjectID>
<a:Name>PK_SRM_PURSUPTYPE</a:Name>
<a:Code>PK_SRM_PURSUPTYPE</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_PURSUPTYPE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o305"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o311"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o311"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o312">
<a:ObjectID>031B0B12-8D0E-46D4-A25F-33BE6F159FBD</a:ObjectID>
<a:Name>T_SRM_SALCUSTTYPE(自定义客户类别)</a:Name>
<a:Code>T_SRM_SALCUSTTYPE</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1410941023</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o313">
<a:ObjectID>7E79EB88-9CD5-4DB6-97A3-11EF531FB4BD</a:ObjectID>
<a:Name>本公司ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1410932761</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o314">
<a:ObjectID>B89395E8-E76B-47E8-B971-8A25BB3AD63F</a:ObjectID>
<a:Name>类型ID</a:Name>
<a:Code>FTYPEID</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1410932761</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o315">
<a:ObjectID>35130A12-E19D-40E8-B216-17B9E82CBC4A</a:ObjectID>
<a:Name>类型名称</a:Name>
<a:Code>FTYPENAME</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1410932761</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o316">
<a:ObjectID>B0F4AFEF-CEE0-439D-8BD1-9657129C2570</a:ObjectID>
<a:Name>级次</a:Name>
<a:Code>FLEVEL</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1410932761</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o317">
<a:ObjectID>4C0DE104-8FF8-4D9A-93E0-2956457AB171</a:ObjectID>
<a:Name>是否明细</a:Name>
<a:Code>FISDETAIL</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1410932761</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o318">
<a:ObjectID>0A68E0F7-E3A1-441A-897D-77EF87E93A87</a:ObjectID>
<a:Name>父节点</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>1402907942</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1410932761</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o319">
<a:ObjectID>F0A36787-8958-426F-B6D1-7BF68C500FC2</a:ObjectID>
<a:Name>全称</a:Name>
<a:Code>FLONGNAME</a:Code>
<a:CreationDate>1411113985</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411115987</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o320">
<a:ObjectID>45C463AD-FAFB-4F91-97EC-AA3ABE868D10</a:ObjectID>
<a:Name>PK_SRM_SALCUSTTYPE</a:Name>
<a:Code>PK_SRM_SALCUSTTYPE</a:Code>
<a:CreationDate>1402907775</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1410941068</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_SALCUSTTYPE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o314"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o320"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o320"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o321">
<a:ObjectID>FD13A792-F8C2-4507-9DDF-577055BAC889</a:ObjectID>
<a:Name>T_SRM_CUSTSALPRODCATEGORY(自定义销售产品分类)</a:Name>
<a:Code>T_SRM_CUSTSALPRODCATEGORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o322">
<a:ObjectID>28BBAD6F-04F8-463E-B26D-0534762A950B</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o323">
<a:ObjectID>C0078579-8FFA-42E8-9927-33DC7DDB2CB6</a:ObjectID>
<a:Name>分类编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o324">
<a:ObjectID>B05FC5D4-EFCE-441C-92A3-9BAD8A8108DC</a:ObjectID>
<a:Name>父分类</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o325">
<a:ObjectID>E7DE59BD-DE4B-4364-9F72-3D48D8B40A99</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1404193026</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o326">
<a:ObjectID>EDC3A8D8-726F-4694-BA03-27DB8F0FF89E</a:ObjectID>
<a:Name>所属公司</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1404193026</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o327">
<a:ObjectID>88C28C0F-700D-402E-A4F6-FD2CDFB07915</a:ObjectID>
<a:Name>级次</a:Name>
<a:Code>FLEVEL</a:Code>
<a:CreationDate>1404193026</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o328">
<a:ObjectID>B3B2DFC8-9464-444C-9B86-842AA1644C40</a:ObjectID>
<a:Name>明细级</a:Name>
<a:Code>FISDETAIL</a:Code>
<a:CreationDate>1404193026</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o329">
<a:ObjectID>1BE0E72D-1A80-4ADE-9671-A453421FA81F</a:ObjectID>
<a:Name>全称</a:Name>
<a:Code>FLONGNAME</a:Code>
<a:CreationDate>1411113893</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o330">
<a:ObjectID>3F025B38-5F42-41C2-AEDA-E7D904CC637F</a:ObjectID>
<a:Name>PK_SRM_CUSTSALPRODCATEGORY</a:Name>
<a:Code>PK_SRM_CUSTSALPRODCATEGORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411353293</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_CUSTSALPRODCATEGORY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o322"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o330"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o330"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o24">
<a:ObjectID>EDDF5C70-8AA9-4F23-8D6A-B8F9735D707C</a:ObjectID>
<a:Name>T_SRM_CUSTPRODCATEGORY(自定义采购产品分类)</a:Name>
<a:Code>T_SRM_CUSTPRODCATEGORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411350632</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o331">
<a:ObjectID>C86AC6E4-D2B3-4AB3-8C77-DA77E84AE408</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1410944736</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o332">
<a:ObjectID>9C1588A3-3DD8-45ED-9AC6-B5658B6951A8</a:ObjectID>
<a:Name>分类编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o333">
<a:ObjectID>8A5857A9-C5C3-466E-A100-4EA90DA10D47</a:ObjectID>
<a:Name>父分类</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o334">
<a:ObjectID>82170C96-A093-4A13-A45A-6614BF4FCFE1</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1404193026</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404206268</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o335">
<a:ObjectID>D3F02CBE-8F1D-4E02-B658-8FD0050C90FF</a:ObjectID>
<a:Name>所属公司</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1404193026</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404206268</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o336">
<a:ObjectID>2D8E5153-C0A2-486C-86B1-8018DD4ED576</a:ObjectID>
<a:Name>级次</a:Name>
<a:Code>FLEVEL</a:Code>
<a:CreationDate>1404193026</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404206268</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o337">
<a:ObjectID>EFC9FF4F-02B9-440A-BF7E-5E1D08D61283</a:ObjectID>
<a:Name>明细级</a:Name>
<a:Code>FISDETAIL</a:Code>
<a:CreationDate>1404193026</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404206268</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o338">
<a:ObjectID>DDD8CC14-B3E1-4085-BFF8-6A70C07D6FE4</a:ObjectID>
<a:Name>全称</a:Name>
<a:Code>FLONGNAME</a:Code>
<a:CreationDate>1411113893</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411115987</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o339">
<a:ObjectID>BF4213BB-D0F4-46C5-9CA3-03CFB831824B</a:ObjectID>
<a:Name>PK_SRM_CUSTPRODCATEGORY</a:Name>
<a:Code>PK_SRM_CUSTPRODCATEGORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404193192</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_CUSTPRODCATEGORY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o331"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o339"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o339"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o42">
<a:ObjectID>F33A5E3E-F72C-480B-B856-D67D7E08527B</a:ObjectID>
<a:Name>T_SRM_QUOTEINFOATTACH(报价产品附件)</a:Name>
<a:Code>T_SRM_QUOTEINFOATTACH</a:Code>
<a:CreationDate>1411485046</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o340">
<a:ObjectID>1643C2C2-9EEB-4D82-B5DD-73F77738EFA8</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1411485046</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o341">
<a:ObjectID>A02F616A-C657-4E2D-A276-C39D354F0F3A</a:ObjectID>
<a:Name>内码FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1411485046</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o342">
<a:ObjectID>07063030-57AC-49DA-A17F-181E1DB40087</a:ObjectID>
<a:Name>附件地址</a:Name>
<a:Code>FATTACHURL</a:Code>
<a:CreationDate>1411485046</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o343">
<a:ObjectID>D7A75543-CB18-475B-8F62-F417CEDC4DB6</a:ObjectID>
<a:Name>附件名</a:Name>
<a:Code>FATTACHNAME</a:Code>
<a:CreationDate>1411485046</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o344">
<a:ObjectID>CD36E6E9-27C8-42F5-A5AD-2EE696E80F5C</a:ObjectID>
<a:Name>PK_SRM_QUOTEINFOATTACH</a:Name>
<a:Code>PK_SRM_QUOTEINFOATTACH</a:Code>
<a:CreationDate>1411485046</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_QUOTEINFOATTACH</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o340"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o344"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o344"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o44">
<a:ObjectID>38BDE35C-23E9-49ED-9FEE-3952E457003F</a:ObjectID>
<a:Name>T_SRM_RECEIVEINQUIRY（报价方接收询价信息表）</a:Name>
<a:Code>T_SRM_RECEIVEINQUIRY</a:Code>
<a:CreationDate>1411485881</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o345">
<a:ObjectID>2A1DAD29-5B54-40D8-997C-EB43B6FC0AA2</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1411485881</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o346">
<a:ObjectID>870CC790-44FC-45E3-8574-23186472022C</a:ObjectID>
<a:Name>报价方(组织公司)</a:Name>
<a:Code>FQUOTEID</a:Code>
<a:CreationDate>1411485881</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o347">
<a:ObjectID>5979FA90-8055-440F-95B1-D6A6E2158DE6</a:ObjectID>
<a:Name>询价方(组织公司)</a:Name>
<a:Code>FINQUIRYID</a:Code>
<a:CreationDate>1411486062</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o348">
<a:ObjectID>347E6EF7-681A-4237-B435-C9E16308BAF0</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FDATE</a:Code>
<a:CreationDate>1411485881</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o349">
<a:ObjectID>3BB39E33-231F-4646-9C4D-6C50B6495540</a:ObjectID>
<a:Name>创建用户</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1411485881</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o350">
<a:ObjectID>933EF5B0-6DEF-4CEA-9130-68D2CC67A906</a:ObjectID>
<a:Name>创建组织（公司）</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1411485881</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o351">
<a:ObjectID>4DDCBF2F-4C21-4BB8-8C1E-BBAE822747BF</a:ObjectID>
<a:Name>询价ID</a:Name>
<a:Code>FINQUIRYBILLID</a:Code>
<a:CreationDate>1411486062</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o352">
<a:ObjectID>084E181F-0C2E-4926-9124-D2FC140DAE0B</a:ObjectID>
<a:Name>PK_SRM_RECEIVEINQUIRY</a:Name>
<a:Code>PK_SRM_RECEIVEINQUIRY</a:Code>
<a:CreationDate>1411485881</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411486442</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_RECEIVEINQUIRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o345"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o352"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o352"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o26">
<a:ObjectID>12FC884C-7A98-4D0B-A077-6A882452EAFD</a:ObjectID>
<a:Name>T_SRM_SALPRODPROP(产品自定义属性)</a:Name>
<a:Code>T_SRM_SALPRODPROP</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404192950</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o353">
<a:ObjectID>42B0A135-0BA3-40F2-BD26-0CFC5B1C9735</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411624464</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o354">
<a:ObjectID>18BC38A6-7EAA-4784-84A4-447BFF79985E</a:ObjectID>
<a:Name>产品id</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411624468</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o355">
<a:ObjectID>B6356D70-611E-4C77-A645-3A0F1D9E6D1A</a:ObjectID>
<a:Name>属性名称</a:Name>
<a:Code>FPROPNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411624477</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o356">
<a:ObjectID>FBBA561B-87D4-4AE2-B5E3-49C92F70F785</a:ObjectID>
<a:Name>属性值</a:Name>
<a:Code>FPROPVALUE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1411624482</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o357">
<a:ObjectID>7ADA9925-2C0D-4620-B469-2ADA6C6C6AC4</a:ObjectID>
<a:Name>属性类型</a:Name>
<a:Code>FPROPTYPE</a:Code>
<a:CreationDate>1411624418</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411624576</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o358">
<a:ObjectID>50AE46BC-6F24-49A8-BD47-70321FA5F260</a:ObjectID>
<a:Name>PK_SRM_SALPRODPROP</a:Name>
<a:Code>PK_SRM_SALPRODPROP</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404192963</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_SALPRODPROP</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o353"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o358"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o358"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o359">
<a:ObjectID>49B4C471-BBED-46A6-A793-6367F5EA6C3E</a:ObjectID>
<a:Name>T_SRM_SALPRODPRICE(销售产品价格信息)</a:Name>
<a:Code>T_SRM_SALPRODPRICE</a:Code>
<a:CreationDate>1404268841</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o360">
<a:ObjectID>FE07B3E9-CD41-4993-A963-8FBAE3E71796</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1404268866</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o361">
<a:ObjectID>86DE4498-5282-4023-82F9-64067F395BE0</a:ObjectID>
<a:Name>产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1404268866</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o362">
<a:ObjectID>8069328A-E2B7-4476-AD57-5126327A3CD8</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>1404268866</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404284785</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o363">
<a:ObjectID>36D12435-9FE3-4678-920F-33B1AFC3186D</a:ObjectID>
<a:Name>数量段</a:Name>
<a:Code>FINTERVAL</a:Code>
<a:CreationDate>1404365844</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404381130</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o364">
<a:ObjectID>D0811350-9537-48A9-A336-880D935DB9F3</a:ObjectID>
<a:Name>单价</a:Name>
<a:Code>FQUOTEPRICE</a:Code>
<a:CreationDate>1404268866</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411624555</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o365">
<a:ObjectID>0FF7393D-1C58-4F06-9ABA-F0CA3D889D99</a:ObjectID>
<a:Name>PK_SRM_SALPRODPRICE</a:Name>
<a:Code>PK_SRM_SALPRODPRICE</a:Code>
<a:CreationDate>1404268866</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1405339776</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_SALPRODPRICE</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o360"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o365"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o365"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o40">
<a:ObjectID>A1FF9B15-7CC1-4836-A77E-99EC7A3B56DA</a:ObjectID>
<a:Name>T_SRM_INQUIRYSUPPLIER（定向询价供应商）</a:Name>
<a:Code>T_SRM_INQUIRYSUPPLIER</a:Code>
<a:CreationDate>1411111463</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411111764</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o366">
<a:ObjectID>96667908-97ED-4A30-8DB5-B002F92566BA</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1411111463</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411111764</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o367">
<a:ObjectID>8D81BD8C-CA23-4695-9889-CC1EE7E8ED76</a:ObjectID>
<a:Name>内码FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1411111463</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411111764</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o368">
<a:ObjectID>980E570A-C06B-4346-BCD4-CE08E4AA20E9</a:ObjectID>
<a:Name>供应商ID</a:Name>
<a:Code>FSUPPLIERID</a:Code>
<a:CreationDate>1411111463</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411111764</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o369">
<a:ObjectID>8AF9DFBC-DC6A-4BDD-8872-6C61F6BC143C</a:ObjectID>
<a:Name>询价状态</a:Name>
<a:Code>FINQUIRYSTATUS</a:Code>
<a:CreationDate>1411630240</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411630608</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o370">
<a:ObjectID>8ACCD04C-FE98-476D-BBF2-C9EA028FEB3A</a:ObjectID>
<a:Name>PK_SRM_INQUIRYSUPPLIER</a:Name>
<a:Code>PK_SRM_INQUIRYSUPPLIER</a:Code>
<a:CreationDate>1411111463</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411111764</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_INQUIRYSUPPLIER</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o366"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o370"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o370"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o371">
<a:ObjectID>8160ADE6-FFA8-4FC5-AA19-F3D92B36279C</a:ObjectID>
<a:Name>T_SRM_INQUIRYSUPPLY(询价指定供应商)</a:Name>
<a:Code>T_SRM_INQUIRYSUPPLY</a:Code>
<a:CreationDate>1403233480</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o372">
<a:ObjectID>D86953D1-D3EE-47CA-BC15-3C851E93720B</a:ObjectID>
<a:Name>询价单ID</a:Name>
<a:Code>FINQUIRYID</a:Code>
<a:CreationDate>1403233480</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o373">
<a:ObjectID>FFF66353-3995-4B47-9CB3-3BEDB978900B</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1403233480</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409535163</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o374">
<a:ObjectID>51C9A279-658E-413E-B3C7-C9B4BD2061B7</a:ObjectID>
<a:Name>指定公司</a:Name>
<a:Code>FSUPPLYORGID</a:Code>
<a:CreationDate>1403233480</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o375">
<a:ObjectID>930E367F-816B-4341-B58F-08D76F6D1EB1</a:ObjectID>
<a:Name>PK_SRM_INQUIRYSUPPLY</a:Name>
<a:Code>PK_SRM_INQUIRYSUPPLY</a:Code>
<a:CreationDate>1403233480</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_INQUIRYSUPPLY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o373"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o375"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o375"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o376">
<a:ObjectID>8930C934-8AAE-44A6-A88E-E11A56BBADB4</a:ObjectID>
<a:Name>T_SRM_PURPRODIMG(采购产品图片)</a:Name>
<a:Code>T_SRM_PURPRODIMG</a:Code>
<a:CreationDate>1411635896</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411637045</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o377">
<a:ObjectID>0C6CBEC4-FB04-40EE-86E5-9DE6D0A456AD</a:ObjectID>
<a:Name>产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1411635896</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411637045</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o378">
<a:ObjectID>334F7816-73D6-4997-86ED-531153017983</a:ObjectID>
<a:Name>小图路径</a:Name>
<a:Code>FSMALLIMGPATH</a:Code>
<a:CreationDate>1411635896</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411637045</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o379">
<a:ObjectID>EE217817-7B18-40C2-A522-6E1D6897CFBF</a:ObjectID>
<a:Name>大图路径</a:Name>
<a:Code>FLARGEIMGPATH</a:Code>
<a:CreationDate>1411635896</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411637045</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o380">
<a:ObjectID>4000C23F-9F61-44DE-904C-82F40A78750B</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1411635896</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411637045</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o381">
<a:ObjectID>3FE03925-F275-423B-8D95-E383C1C84CD8</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1411635896</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1411637045</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o380"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o381"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o381"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o382">
<a:ObjectID>8D768D9E-D4F0-4D32-9218-70903F8DA9DD</a:ObjectID>
<a:Name>T_SRM_INQUIRYATTACH(询价附件)</a:Name>
<a:Code>T_SRM_INQUIRYATTACH</a:Code>
<a:CreationDate>1407733953</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407834507</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o383">
<a:ObjectID>02757A2E-D9AC-43D1-B626-E0CB4EA786D6</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1407733953</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411637674</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o384">
<a:ObjectID>361F0C0E-E8C9-4B12-8869-86A4C54B7114</a:ObjectID>
<a:Name>分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1407733953</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o385">
<a:ObjectID>DC0DFE12-2093-4B4E-A719-3945F3208FCF</a:ObjectID>
<a:Name>附件地址</a:Name>
<a:Code>FATTACHURL</a:Code>
<a:CreationDate>1407733953</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o386">
<a:ObjectID>7EEA2AD1-5ECA-4356-A766-59E0B8FB2EA6</a:ObjectID>
<a:Name>附件名</a:Name>
<a:Code>FATTACHNAME</a:Code>
<a:CreationDate>1407733953</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o387">
<a:ObjectID>EB441387-E33F-4272-8609-D7CA6869778B</a:ObjectID>
<a:Name>PK_SRM_INQUIRYATTACH</a:Name>
<a:Code>PK_SRM_INQUIRYATTACH</a:Code>
<a:CreationDate>1407733953</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411637730</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_INQUIRYATTACH</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o384"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o387"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o387"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o388">
<a:ObjectID>EE6F259A-F498-47E9-823B-7918EC005E25</a:ObjectID>
<a:Name>T_SRM_HELPDOCS</a:Name>
<a:Code>T_SRM_HELPDOCS</a:Code>
<a:CreationDate>1411890996</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1411891305</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:Comment>帮助文档内容</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o389">
<a:ObjectID>5F91294B-1D76-4071-BA82-B48F334C39AC</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1411890996</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1411891305</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o390">
<a:ObjectID>B4536754-A3C9-4F66-AB4E-9D05099D218A</a:ObjectID>
<a:Name>小标题</a:Name>
<a:Code>FTITLE</a:Code>
<a:CreationDate>1411890996</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1411891305</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o391">
<a:ObjectID>4EAE03FB-39DD-4929-9F5D-624AA02DB510</a:ObjectID>
<a:Name>内容</a:Name>
<a:Code>FCONTENT</a:Code>
<a:CreationDate>1411890996</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1411891305</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o392">
<a:ObjectID>F5958461-FA6D-46E7-8858-585662F4A15F</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1411890996</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1411891305</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o389"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o392"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o392"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o393">
<a:ObjectID>D69DA1ED-5713-40A3-8790-D976857E374C</a:ObjectID>
<a:Name>T_SRM_SHOPPINGCART(购物车)</a:Name>
<a:Code>T_SRM_SHOPPINGCART</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o394">
<a:ObjectID>8A8904E0-F186-4ECB-9644-3C69A7CC4352</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405410548</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o395">
<a:ObjectID>889D20A9-A978-4647-821A-6EEA816A32DA</a:ObjectID>
<a:Name>商品id</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405410542</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o396">
<a:ObjectID>2C0B9F5A-FBCE-43F0-A1DA-ADF9E7C32C5C</a:ObjectID>
<a:Name>单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405410395</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o397">
<a:ObjectID>D3FBF9C3-7F59-435B-A220-3E9F98FC1C11</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405410478</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o398">
<a:ObjectID>E99C60E3-8861-4D37-AE77-037775F47409</a:ObjectID>
<a:Name>单价</a:Name>
<a:Code>FPRICE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405410487</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(19,6)</a:DataType>
<a:Length>19</a:Length>
<a:Precision>6</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o399">
<a:ObjectID>4F06CC0A-4FC2-4CDF-9EB5-6FA14E77BFC3</a:ObjectID>
<a:Name>购买人</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405410493</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o400">
<a:ObjectID>86E98EBC-47E0-46CA-A06B-C6C2248AA5F5</a:ObjectID>
<a:Name>购买日期</a:Name>
<a:Code>FDATE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o401">
<a:ObjectID>54D48804-AF0C-431D-AA43-ABB812DD15A6</a:ObjectID>
<a:Name>卖方公司</a:Name>
<a:Code>FCOMPANYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1405410525</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o402">
<a:ObjectID>36046698-ACFE-414D-9414-4A28D82F62E1</a:ObjectID>
<a:Name>买方公司</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1411969618</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411969711</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o403">
<a:ObjectID>83E555F7-A5B0-4285-91FB-5E02FA24C33C</a:ObjectID>
<a:Name>PK_SRM_SHOPPINGCART</a:Name>
<a:Code>PK_SRM_SHOPPINGCART</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_SHOPPINGCART</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o394"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o403"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o403"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o404">
<a:ObjectID>97D2D4C0-FCAD-446E-AFDC-6C38507B6F8B</a:ObjectID>
<a:Name>T_SRM_INQUIRYSUBATTACH(产品明细询价附件)</a:Name>
<a:Code>T_SRM_INQUIRYSUBATTACH</a:Code>
<a:CreationDate>1407734024</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407834507</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>询价明细附件</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o405">
<a:ObjectID>5C65E314-D12F-4538-8FB8-DD66AA8EE673</a:ObjectID>
<a:Name>父分录ID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1407734024</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1412837634</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o406">
<a:ObjectID>2C7CD70C-1A80-41C6-8867-81FD488C4B8D</a:ObjectID>
<a:Name>附件地址</a:Name>
<a:Code>FATTACHURL</a:Code>
<a:CreationDate>1407734024</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o407">
<a:ObjectID>C4BDAFF6-834E-48EE-984B-68D58FE1DCF7</a:ObjectID>
<a:Name>附件名</a:Name>
<a:Code>FATTACHNAME</a:Code>
<a:CreationDate>1407734024</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o408">
<a:ObjectID>EE79B4EF-5A7A-4E76-8807-AB37821E623D</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FDETAILID</a:Code>
<a:CreationDate>1411111216</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1412837493</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o409">
<a:ObjectID>1B586756-EE55-4420-BBA1-861E20597F25</a:ObjectID>
<a:Name>PK_SRM_INQUIRYSUBATTACH</a:Name>
<a:Code>PK_SRM_INQUIRYSUBATTACH</a:Code>
<a:CreationDate>1407734024</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411111336</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_INQUIRYSUBATTACH</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o408"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o410">
<a:ObjectID>34912176-7D0A-433A-8217-EDB339C579F4</a:ObjectID>
<a:Name>IDX_SRM_INQUIRYSUBATTACH_ATT</a:Name>
<a:Code>IDX_SRM_INQUIRYSUBATTACH_ATT</a:Code>
<a:CreationDate>1412837571</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1412838301</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o411">
<a:ObjectID>D3E95B8D-DE34-4602-8A97-0BACF177C0FD</a:ObjectID>
<a:CreationDate>1412837612</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1412838301</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<c:Column>
<o:Column Ref="o405"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o409"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o409"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o412">
<a:ObjectID>25CF748F-0D84-4373-9756-75DB328A448A</a:ObjectID>
<a:Name>T_SRM_PURSUPPLIER(管理供应商)</a:Name>
<a:Code>T_SRM_PURSUPPLIER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o413">
<a:ObjectID>51EAAE28-DEB5-4B0A-AB88-30C37E314563</a:ObjectID>
<a:Name>本公司ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o414">
<a:ObjectID>B75932EA-D818-4872-8E57-0638C3BF0A05</a:ObjectID>
<a:Name>供应商对应公司ID</a:Name>
<a:Code>FSUPORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o415">
<a:ObjectID>B4218874-B127-4B02-A8B0-889CBDA3C202</a:ObjectID>
<a:Name>自定义供应商类型</a:Name>
<a:Code>FSUPTYPEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o416">
<a:ObjectID>485E30A4-18D8-43C9-8065-1039231D76DF</a:ObjectID>
<a:Name>GUID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1404179641</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404206268</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o417">
<a:ObjectID>0019C774-F5FB-44CA-90BF-0B0F8CCDBE20</a:ObjectID>
<a:Name>企业ERP供应商ID</a:Name>
<a:Code>FERPSUPPLIERID</a:Code>
<a:CreationDate>1414461935</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1414462246</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o418">
<a:ObjectID>D01D4597-2BC7-4D53-BF62-5F417BFC7211</a:ObjectID>
<a:Name>PK_SRM_PURSUPPLIER</a:Name>
<a:Code>PK_SRM_PURSUPPLIER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1402914261</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_PURSUPPLIER</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o416"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o418"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o418"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o419">
<a:ObjectID>E4DF18B5-B9F6-483F-B963-A559215A6B33</a:ObjectID>
<a:Name>T_SRM_CUSTOMER(管理供应商)</a:Name>
<a:Code>T_SRM_CUSTOMER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o420">
<a:ObjectID>1B266BB4-BAD1-49ED-8D9A-13599BD0BE48</a:ObjectID>
<a:Name>本公司ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o421">
<a:ObjectID>2EF783C5-0B85-4402-8E9F-5BBC72216ECC</a:ObjectID>
<a:Name>客户对应公司ID</a:Name>
<a:Code>FCUSTORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o422">
<a:ObjectID>210F4196-4689-4C8C-9E9A-CF47C513190C</a:ObjectID>
<a:Name>自定义客户类型</a:Name>
<a:Code>FCUSTTYPEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o423">
<a:ObjectID>E8CF9F93-433A-441D-814A-66FB5A534839</a:ObjectID>
<a:Name>GUID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1404179641</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o424">
<a:ObjectID>9E3DB5E4-F42F-4335-94BD-0BEAFB05EEAA</a:ObjectID>
<a:Name>企业ERP客户ID</a:Name>
<a:Code>FERPCUSTID</a:Code>
<a:CreationDate>1414462060</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1414462246</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o425">
<a:ObjectID>8D242117-8FCC-4E14-BAAD-3EA08722F882</a:ObjectID>
<a:Name>PK_SRM_CUSTOMER</a:Name>
<a:Code>PK_SRM_CUSTOMER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_CUSTOMER</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o423"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o425"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o425"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o426">
<a:ObjectID>B015EC43-7C24-40D4-8239-A62710864854</a:ObjectID>
<a:Name>T_SRM_BASACCOUNTCATEGORY(账号类目)</a:Name>
<a:Code>T_SRM_BASACCOUNTCATEGORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:Comment>账号分类</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o427">
<a:ObjectID>2341F4AA-22FA-4A32-93EE-C33FE5B8A158</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o428">
<a:ObjectID>BB6DD89F-741A-496D-9519-A1F7DE41A7EF</a:ObjectID>
<a:Name>编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o429">
<a:ObjectID>239B11DB-70D7-4855-9FB4-49C6F1F3BBCF</a:ObjectID>
<a:Name>级数</a:Name>
<a:Code>FLEVEL</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o430">
<a:ObjectID>8B9444A0-A26D-4B42-B524-CBCE8B6BC383</a:ObjectID>
<a:Name>父节点</a:Name>
<a:Code>FPARENTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o431">
<a:ObjectID>A988CC2F-41E0-4065-95E0-3DC835BA21F0</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o432">
<a:ObjectID>9F4DA74E-3172-4FE2-9C2B-210568D4224E</a:ObjectID>
<a:Name>是否明细级</a:Name>
<a:Code>FISDETAIL</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o433">
<a:ObjectID>5315D0EB-5C2F-4E50-9069-B5E645D889A8</a:ObjectID>
<a:Name>PK_SRM_BASPRODCATEGORY</a:Name>
<a:Code>PK_SRM_BASPRODCATEGORY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o427"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o433"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o433"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o434">
<a:ObjectID>F2BFFF5E-A270-40F0-B34D-03FED765AACB</a:ObjectID>
<a:Name>T_SRM_USERINFO(用户)</a:Name>
<a:Code>T_SRM_USERINFO</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o435">
<a:ObjectID>4794E846-A6E9-40E2-930C-509B1942D187</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o436">
<a:ObjectID>AB326DEB-130F-4DA7-8844-40B6CDD111F7</a:ObjectID>
<a:Name>ENTRYID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1397041442</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1397046049</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o437">
<a:ObjectID>50D14AE0-AC63-44CF-BD13-D2AB0CB94D11</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o438">
<a:ObjectID>72A0D622-6C11-4104-A6C0-E3BC03A0EA78</a:ObjectID>
<a:Name>公司ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o439">
<a:ObjectID>BDA087BF-5E92-47B4-9709-AFDEE74B197F</a:ObjectID>
<a:Name>是否主账户</a:Name>
<a:Code>FISMAINUSER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o440">
<a:ObjectID>589B7DC8-1448-4848-8630-7184D61983F3</a:ObjectID>
<a:Name>固定电话</a:Name>
<a:Code>FTEL</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o441">
<a:ObjectID>4AD7864F-303E-4A2C-8658-A074C00EC6F4</a:ObjectID>
<a:Name>手机</a:Name>
<a:Code>FPHONE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o442">
<a:ObjectID>226C4A9A-3136-431E-A484-296895488DF4</a:ObjectID>
<a:Name>电子邮箱</a:Name>
<a:Code>FEMAIL</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397042142</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o443">
<a:ObjectID>EB04967F-3552-4B61-9E48-26C802054E46</a:ObjectID>
<a:Name>真实姓名</a:Name>
<a:Code>FUSERNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404453502</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o444">
<a:ObjectID>C357490D-278B-4E27-8707-0A078376911B</a:ObjectID>
<a:Name>传真</a:Name>
<a:Code>FFAX</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o445">
<a:ObjectID>CF32E1BA-A9A3-44AB-BBD6-37988D00D2C7</a:ObjectID>
<a:Name>会员账号</a:Name>
<a:Code>FUSERACCOUNT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o446">
<a:ObjectID>BC88D997-C9CA-4C92-ACF0-5465D9795752</a:ObjectID>
<a:Name>性别</a:Name>
<a:Code>FSEX</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;M&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o447">
<a:ObjectID>C18BA330-A59D-4125-952A-4E224AEDA20B</a:ObjectID>
<a:Name>会员身份</a:Name>
<a:Code>FUSERTYPE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1408351011</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;B&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o448">
<a:ObjectID>5C456957-842E-423C-B669-0E517F4EC7C1</a:ObjectID>
<a:Name>职位</a:Name>
<a:Code>FPOSITION</a:Code>
<a:CreationDate>1397042142</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404453533</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o449">
<a:ObjectID>60A75739-6664-47BA-9FA3-7CF90C28BC9D</a:ObjectID>
<a:Name>部门</a:Name>
<a:Code>FDEPTNAME</a:Code>
<a:CreationDate>1397042142</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404453538</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o450">
<a:ObjectID>DD17C94C-78C3-4908-BB4F-9836B2FC3B5C</a:ObjectID>
<a:Name>邮编</a:Name>
<a:Code>FPOSTCODE</a:Code>
<a:CreationDate>1404453965</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404458981</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o451">
<a:ObjectID>880BC3AC-AB91-4DB2-8CED-2E2179D27CF8</a:ObjectID>
<a:Name>传真区号</a:Name>
<a:Code>FFax_Code</a:Code>
<a:CreationDate>1404453965</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404458981</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o452">
<a:ObjectID>39931F0F-F9A3-4BE9-A32A-DE0B38A4CFC6</a:ObjectID>
<a:Name>传真国际号</a:Name>
<a:Code>FFax_Inter</a:Code>
<a:CreationDate>1404453965</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404458981</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o453">
<a:ObjectID>3E914A98-7D3F-493B-B1D7-3A5B34872818</a:ObjectID>
<a:Name>电话国际号</a:Name>
<a:Code>FTel_Inter</a:Code>
<a:CreationDate>1404453965</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404458981</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o454">
<a:ObjectID>36B01FF5-C8C9-4696-B399-267CEAA27BA6</a:ObjectID>
<a:Name>电话区号</a:Name>
<a:Code>FTel_Code</a:Code>
<a:CreationDate>1404453965</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404458981</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o455">
<a:ObjectID>8C55E2E7-9BFB-4C29-94FA-95B9598CB967</a:ObjectID>
<a:Name>默认角色</a:Name>
<a:Code>FDEFROLE</a:Code>
<a:CreationDate>1413510016</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1413510212</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>Sale:销售
Purchase:采购</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o456">
<a:ObjectID>66C31DAC-65CD-42EB-9A5F-890320AE560C</a:ObjectID>
<a:Name>采购关注的产品</a:Name>
<a:Code>FPURATTPROD</a:Code>
<a:CreationDate>1413790950</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1413792077</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o457">
<a:ObjectID>D8FFE041-B5E0-4A83-9EE2-1FB3AECA48B8</a:ObjectID>
<a:Name>采购关注的行业</a:Name>
<a:Code>FPURATTTRADE</a:Code>
<a:CreationDate>1413790950</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1413792077</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o458">
<a:ObjectID>26DE518A-20C8-4B9B-8B1D-359D28F3300A</a:ObjectID>
<a:Name>销售关注的产品</a:Name>
<a:Code>FSALATTPROD</a:Code>
<a:CreationDate>1413790950</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1413792077</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o459">
<a:ObjectID>E32FDA99-8331-4F42-ABF3-1E572E27F415</a:ObjectID>
<a:Name>销售关注的行业</a:Name>
<a:Code>FSALATTTRADE</a:Code>
<a:CreationDate>1413790950</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1413792077</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o460">
<a:ObjectID>ABAE12CA-D093-4D9B-8566-FAC821C3A193</a:ObjectID>
<a:Name>TOKEN</a:Name>
<a:Code>FACCESSTOKEN</a:Code>
<a:CreationDate>1418090920</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1418091030</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o461">
<a:ObjectID>0C04147E-79BA-44CC-8F0D-43BA35C26494</a:ObjectID>
<a:Name>账户类别</a:Name>
<a:Code>FCUSTOMCATEGORY</a:Code>
<a:CreationDate>1418696503</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>1418697060</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o462">
<a:ObjectID>3FECC97B-79DF-4C2C-A522-B32EE5CD3DDB</a:ObjectID>
<a:Name>PK_SRM_USER</a:Name>
<a:Code>PK_SRM_USER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_USER</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o435"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o462"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o462"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o463">
<a:ObjectID>11C1266B-8F6D-4801-A4E7-94F9911C3299</a:ObjectID>
<a:Name>T_SRM_ROLEPERMISSIONITEM(角色权限关联表)</a:Name>
<a:Code>T_SRM_ROLEPERMISSIONITEM</a:Code>
<a:CreationDate>1419837091</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>1419838937</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o464">
<a:ObjectID>CA5EFAFB-86B5-4175-8333-D5FA3B8579EC</a:ObjectID>
<a:Name>角色ID</a:Name>
<a:Code>FROLEID</a:Code>
<a:CreationDate>1419837137</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>1419838937</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o465">
<a:ObjectID>7BC425F6-11A8-424D-9583-FCE05191229D</a:ObjectID>
<a:Name>权限ID</a:Name>
<a:Code>FPERMISSIONID</a:Code>
<a:CreationDate>1419837137</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>1419838937</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o466">
<a:ObjectID>FCDBC62B-D83B-40FA-B436-28CA702AFD7F</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1419837137</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>1419838937</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o464"/>
<o:Column Ref="o465"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o466"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o466"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o20">
<a:ObjectID>B72E0502-9AD2-407A-8970-BC4B23268374</a:ObjectID>
<a:Name>T_SRM_INQUIRYPDT(询价产品)</a:Name>
<a:Code>T_SRM_INQUIRYPDT</a:Code>
<a:CreationDate>1396774011</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o467">
<a:ObjectID>E209C5B5-0098-40A8-BCCB-1BA89C04E7A0</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1396775623</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o468">
<a:ObjectID>C8DE783A-4E2F-4FD9-8FB8-03B89BC39941</a:ObjectID>
<a:Name>FENTRYID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1396775626</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o469">
<a:ObjectID>A6A88D3F-0F0D-4322-BA82-4BE3DD2B351A</a:ObjectID>
<a:Name>产品编码</a:Name>
<a:Code>FPRODID</a:Code>
<a:CreationDate>1396775683</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o470">
<a:ObjectID>49542B5C-1C79-4F66-9DAA-142A72FB963E</a:ObjectID>
<a:Name>产品名称</a:Name>
<a:Code>FPRODNAME</a:Code>
<a:CreationDate>1403232827</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o471">
<a:ObjectID>C23A702B-635A-4261-BFDC-8AEDF6FFBE36</a:ObjectID>
<a:Name>所属行业</a:Name>
<a:Code>FTRADE</a:Code>
<a:CreationDate>1396775707</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o472">
<a:ObjectID>B2BC74C6-DAED-4CA1-B71C-578CB93F4640</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>1396775805</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o473">
<a:ObjectID>FA4821D2-F97E-48AF-AC68-21D9368F2643</a:ObjectID>
<a:Name>单位</a:Name>
<a:Code>FUNIT</a:Code>
<a:CreationDate>1396775834</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o474">
<a:ObjectID>55D385DE-FCF4-468A-BDE8-417D5A809E0F</a:ObjectID>
<a:Name>产品描述</a:Name>
<a:Code>FPRODDEC</a:Code>
<a:CreationDate>1396775870</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403232915</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o475">
<a:ObjectID>2F462F71-9149-4B16-AF9E-A2326F4153FD</a:ObjectID>
<a:Name>附件地址</a:Name>
<a:Code>FURI</a:Code>
<a:CreationDate>1396775932</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403232915</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o476">
<a:ObjectID>C95882E1-2604-4E29-95C5-60071BE7B810</a:ObjectID>
<a:Name>一级分类</a:Name>
<a:Code>FFIRSTCATID</a:Code>
<a:CreationDate>1407733759</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o477">
<a:ObjectID>465A08C3-7AFA-4DCD-8BD9-9F05D140F1D4</a:ObjectID>
<a:Name>二级分类</a:Name>
<a:Code>FSECONDCATID</a:Code>
<a:CreationDate>1407733759</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o478">
<a:ObjectID>7B46FB96-8770-46F0-BE60-EDE6DC650DCE</a:ObjectID>
<a:Name>所属分类Id</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>1407733789</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o479">
<a:ObjectID>0C0F73C6-CCA2-4944-92E1-190D8637866A</a:ObjectID>
<a:Name>采购方产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1409213469</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1409215272</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o480">
<a:ObjectID>EFB4F19C-84EF-438C-9839-FBB33128B104</a:ObjectID>
<a:Name>源单(ERP)分录ID</a:Name>
<a:Code>FSOURCEENTRYID</a:Code>
<a:CreationDate>1424152644</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1424153519</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o481">
<a:ObjectID>F51DE2F8-7B5F-4E76-8F3C-891E42FCC386</a:ObjectID>
<a:Name>源单类型</a:Name>
<a:Code>FSOURCETYPE</a:Code>
<a:CreationDate>1424152644</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1424153519</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o482">
<a:ObjectID>8722F486-98CB-43EC-A57D-0DA7790E4A86</a:ObjectID>
<a:Name>PK_SRM_INQUIRYPDT</a:Name>
<a:Code>PK_SRM_INQUIRYPDT</a:Code>
<a:CreationDate>1396777002</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_INQUIRYPDT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o468"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o482"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o482"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o48">
<a:ObjectID>ADEC2DD8-6AFD-4A11-8A7A-4CD7899C72EC</a:ObjectID>
<a:Name>T_SRM_PURREQUIREMENTENTRY采购需求产品)</a:Name>
<a:Code>T_SRM_PURREQUIREMENTENTRY</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:Comment>PUR采购
REQUIREMENT需求</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o483">
<a:ObjectID>9BF40AD9-6EEA-45F4-A132-5C79F73C4539</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o484">
<a:ObjectID>2C1851AE-85A4-462B-9C37-591C8DD6D00E</a:ObjectID>
<a:Name>FENTRYID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o485">
<a:ObjectID>46B1D856-82E3-415D-89BC-BE5710B98A05</a:ObjectID>
<a:Name>产品编码</a:Name>
<a:Code>FPRODID</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o486">
<a:ObjectID>36663E46-1BE5-408A-8D84-A8DE24EA6ED2</a:ObjectID>
<a:Name>产品名称</a:Name>
<a:Code>FPRODNAME</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o487">
<a:ObjectID>CAC3FE73-8454-4732-B3D3-C7CAD3B2C414</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o488">
<a:ObjectID>603FD89B-891E-4332-9B00-F55EB80078F0</a:ObjectID>
<a:Name>单位</a:Name>
<a:Code>FUNIT</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o489">
<a:ObjectID>CB86F3D4-E08E-4912-B938-F1C2282BAFBD</a:ObjectID>
<a:Name>产品描述</a:Name>
<a:Code>FPRODDEC</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o490">
<a:ObjectID>00DFACDE-91EA-4D20-9B43-DA1BD250D28E</a:ObjectID>
<a:Name>一级分类</a:Name>
<a:Code>FFIRSTCATID</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o491">
<a:ObjectID>BBA86311-0EA4-4673-8CAE-191CF1B4F85C</a:ObjectID>
<a:Name>二级分类</a:Name>
<a:Code>FSECONDCATID</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o492">
<a:ObjectID>AC863558-068F-41CD-B465-F9705D469D01</a:ObjectID>
<a:Name>所属分类Id</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o493">
<a:ObjectID>24249545-B750-4FFE-9978-A0AF19488D59</a:ObjectID>
<a:Name>采购方产品ID</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o494">
<a:ObjectID>EACD3F67-E61F-47F5-883B-BA6F46D6D83D</a:ObjectID>
<a:Name>规格型号</a:Name>
<a:Code>FSPECIFICATION</a:Code>
<a:CreationDate>1420793928</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1427271337</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o495">
<a:ObjectID>6B96A29D-C460-40B8-A7CE-7E95AB691CEA</a:ObjectID>
<a:Name>计划采购金额</a:Name>
<a:Code>FPLANAMOUNT</a:Code>
<a:CreationDate>1420793928</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o496">
<a:ObjectID>FC1033AA-B1C4-4425-BF77-BD6F33EE6CCF</a:ObjectID>
<a:Name>(ERP)源单分录ID</a:Name>
<a:Code>FSOURCEENTRYID</a:Code>
<a:CreationDate>1424152858</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1425457113</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o497">
<a:ObjectID>FFC641C5-D9E1-4E21-967E-F27BBDD816AB</a:ObjectID>
<a:Name>源单(ERP)类型</a:Name>
<a:Code>FSOURCETYPE</a:Code>
<a:CreationDate>1425457113</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1425460653</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o498">
<a:ObjectID>0BDB5D41-43A7-466D-97E3-F848910184D3</a:ObjectID>
<a:Name>PK_SRM_PURREQUIREMENTENTRY</a:Name>
<a:Code>PK_SRM_PURREQUIREMENTENTRY</a:Code>
<a:CreationDate>1420793355</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_PURREQUIREMENTENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o484"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o498"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o498"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o36">
<a:ObjectID>98502C55-F6E7-4668-848D-575B6F81835E</a:ObjectID>
<a:Name>T_SRM_PURCHASEORDER（采购订单）</a:Name>
<a:Code>T_SRM_PURCHASEORDER</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o499">
<a:ObjectID>521BADA7-DB1D-405D-A4B6-FC006F8E2B24</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o500">
<a:ObjectID>0C80DE82-55A5-4D2D-9D1A-0DE443EF42B6</a:ObjectID>
<a:Name>订单号</a:Name>
<a:Code>FBILLNO</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1416446767</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o501">
<a:ObjectID>F7C49103-6786-4D4B-BFB2-3B883CEAD291</a:ObjectID>
<a:Name>供应商</a:Name>
<a:Code>FSUPPLIERID</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o502">
<a:ObjectID>4F6EF23F-4F37-4C4B-B211-89492BFF5915</a:ObjectID>
<a:Name>联系电话</a:Name>
<a:Code>FTELPHONE</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o503">
<a:ObjectID>0E33FC8E-CDB5-46DB-8AE9-2F297BAAC543</a:ObjectID>
<a:Name>联系人</a:Name>
<a:Code>FCONTACTNAME</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o504">
<a:ObjectID>D833B0B2-02A8-433C-88E3-B45A1B677973</a:ObjectID>
<a:Name>付款条件</a:Name>
<a:Code>FPAYCONDITION</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1415777841</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o505">
<a:ObjectID>C368D5CA-5CB0-4A91-8143-072CD6A7BD1D</a:ObjectID>
<a:Name>补充说明</a:Name>
<a:Code>FADDITIONALREMARKS</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o506">
<a:ObjectID>0D2DB4E0-1519-4651-92FE-DBCE41156B9F</a:ObjectID>
<a:Name>订单总金额</a:Name>
<a:Code>FALLAMOUNT</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o507">
<a:ObjectID>C321090C-791C-4CB4-A9BA-01E41F02EF45</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FDATE</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o508">
<a:ObjectID>7105087C-CE2F-401B-9ADB-6564DCE2F2F9</a:ObjectID>
<a:Name>创建用户</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o509">
<a:ObjectID>825D2A5A-8AC7-43C7-806B-D01F93BE5597</a:ObjectID>
<a:Name>创建组织（公司）</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1410928449</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o510">
<a:ObjectID>F27E2D7A-5464-4DA1-ABB9-6AF6E3729DA4</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1410928766</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o511">
<a:ObjectID>07F2B33B-DC4C-430D-9936-E6543C55A225</a:ObjectID>
<a:Name>订单状态</a:Name>
<a:Code>FBILLSTATUS</a:Code>
<a:CreationDate>1410928918</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o512">
<a:ObjectID>76A6D0BA-5BBC-4025-8AA9-BD4D7C9D1F2A</a:ObjectID>
<a:Name>运输方式</a:Name>
<a:Code>FSHIPPINGMETHODS</a:Code>
<a:CreationDate>1410928967</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o513">
<a:ObjectID>4916E302-73DE-4181-9F37-E6B98A4366F3</a:ObjectID>
<a:Name>预付金额</a:Name>
<a:Code>FPREPAIDAMOUNT</a:Code>
<a:CreationDate>1410928967</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o514">
<a:ObjectID>F0112C21-D0BB-49A2-992F-A44DB980AA4E</a:ObjectID>
<a:Name>预付比例</a:Name>
<a:Code>FPREPAIDRATE</a:Code>
<a:CreationDate>1410929680</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o515">
<a:ObjectID>DD950A6A-DE87-490C-8DC2-99CFDAB9D012</a:ObjectID>
<a:Name>收货地址</a:Name>
<a:Code>FDELIVERYADDRESS</a:Code>
<a:CreationDate>1410929633</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o516">
<a:ObjectID>1EF67467-80F4-4094-86C8-817DF67CD285</a:ObjectID>
<a:Name>询价单Id</a:Name>
<a:Code>FINQUIRYID</a:Code>
<a:CreationDate>1411969427</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411969711</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o517">
<a:ObjectID>2F671C53-AAB5-4399-AE7A-1AA951D02014</a:ObjectID>
<a:Name>报价单ID</a:Name>
<a:Code>FQUOTEID</a:Code>
<a:CreationDate>1412829399</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1412829520</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o518">
<a:ObjectID>A41A1F17-7B2E-4009-BFC1-7F2D162F066B</a:ObjectID>
<a:Name>订单生成类型</a:Name>
<a:Code>FORDERTYPEID</a:Code>
<a:CreationDate>1411969464</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411969711</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o519">
<a:ObjectID>14F31B6B-7CD3-4E4B-B125-4D5701CE2D44</a:ObjectID>
<a:Name>买方联系人</a:Name>
<a:Code>FCUSTCONTACTNAME</a:Code>
<a:CreationDate>1413259624</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1413260214</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o520">
<a:ObjectID>20950D18-2B97-49CA-B5EC-1C8EC635E802</a:ObjectID>
<a:Name>买方电话</a:Name>
<a:Code>FCUSTELPHONE</a:Code>
<a:CreationDate>1413259624</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1413260214</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o521">
<a:ObjectID>FE70A8D1-FC20-434D-834B-A8FAB7676896</a:ObjectID>
<a:Name>ERP采购订单ID</a:Name>
<a:Code>FERPPOBILLID</a:Code>
<a:CreationDate>1415173480</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1415175618</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o522">
<a:ObjectID>D59A684D-6640-40C8-8C7E-D37BA7DE2B09</a:ObjectID>
<a:Name>ERP销售订单ID</a:Name>
<a:Code>FERPSOBILLID</a:Code>
<a:CreationDate>1415173480</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1415175618</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o523">
<a:ObjectID>8180C5F1-743F-4D69-B7E8-D8FE977EB09E</a:ObjectID>
<a:Name>币别名称（从ERP上传）</a:Name>
<a:Code>FCURRENCYNAME</a:Code>
<a:CreationDate>1427434623</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1427434771</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o524">
<a:ObjectID>3CD69F9C-49ED-46E5-B180-CF0F4DFE4DDA</a:ObjectID>
<a:Name>PK_SRM_PURCHASEORDER</a:Name>
<a:Code>PK_SRM_PURCHASEORDER</a:Code>
<a:CreationDate>1410928398</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_PURCHASEORDER</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o499"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o524"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o524"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o525">
<a:ObjectID>72FB39C6-F62F-47B5-AAEE-40AF922D9F70</a:ObjectID>
<a:Name>T_SRM_SYSTEMMSG(系统消息)</a:Name>
<a:Code>T_SRM_SYSTEMMSG</a:Code>
<a:CreationDate>1407808562</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o526">
<a:ObjectID>318E2055-ED57-4778-A3C7-8341E88012A0</a:ObjectID>
<a:Name>消息ID</a:Name>
<a:Code>FMSGID</a:Code>
<a:CreationDate>1407808607</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407811596</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o527">
<a:ObjectID>F6264504-4D24-4DA5-8DBA-E7085B090C41</a:ObjectID>
<a:Name>标题</a:Name>
<a:Code>FTITLE</a:Code>
<a:CreationDate>1407808607</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o528">
<a:ObjectID>D591E421-BFB1-42D0-9FE1-7431470C99E9</a:ObjectID>
<a:Name>消息内容</a:Name>
<a:Code>FCONTENT</a:Code>
<a:CreationDate>1407808607</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1410917617</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o529">
<a:ObjectID>50E0BCC2-2A2E-4738-B005-AF7DF21B5606</a:ObjectID>
<a:Name>发送时间</a:Name>
<a:Code>FSENDDATE</a:Code>
<a:CreationDate>1407808607</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>getdate()</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o530">
<a:ObjectID>39E7B046-15BA-42BC-A1DF-568282DCAE55</a:ObjectID>
<a:Name>发送类型</a:Name>
<a:Code>FSENDTYPE</a:Code>
<a:CreationDate>1427448157</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1427448374</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:Comment>&#39;A&#39;系统自动发送
&#39;B&#39;管理员发送</a:Comment>
<a:DefaultValue>&#39;A&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o531">
<a:ObjectID>2C968BE1-F5E4-476C-B39D-B7B2B8407A82</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1407808607</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1407809283</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o526"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o531"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o531"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o532">
<a:ObjectID>467CF773-98A4-4D7B-88DF-4D03BB51624C</a:ObjectID>
<a:Name>T_SRM_SALPRODINFO(销售产品资料)</a:Name>
<a:Code>T_SRM_SALPRODINFO</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404192632</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o533">
<a:ObjectID>CC1AEE43-DB3E-44F2-891C-D3AA492863B5</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o534">
<a:ObjectID>74C5EC35-A584-42F2-B49F-D0BFFF6F8799</a:ObjectID>
<a:Name>产品编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o535">
<a:ObjectID>069BA624-7171-4C88-A0AF-BD29A70042F3</a:ObjectID>
<a:Name>所属分类Id</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o536">
<a:ObjectID>9D2AC2F9-69F4-41E9-96D4-10AC7C42ABB3</a:ObjectID>
<a:Name>销售单位</a:Name>
<a:Code>FUNIT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o537">
<a:ObjectID>5AFADA62-FA37-4222-83BC-BCB399B5583E</a:ObjectID>
<a:Name>图片</a:Name>
<a:Code>FIMG</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>image</a:DataType>
</o:Column>
<o:Column Id="o538">
<a:ObjectID>559DFF46-222C-4BB2-A1EB-762658628AF8</a:ObjectID>
<a:Name>图片Path1</a:Name>
<a:Code>FIMGPATH1</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o539">
<a:ObjectID>6BF2E89D-219C-4CAC-9CF0-A60472B2AAD2</a:ObjectID>
<a:Name>图片Path2</a:Name>
<a:Code>FIMGPATH2</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o540">
<a:ObjectID>00D1F655-D1F0-4234-AFA3-802DCA28E466</a:ObjectID>
<a:Name>图片Path</a:Name>
<a:Code>FIMGPATH</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o541">
<a:ObjectID>7B895B94-373A-4FF1-BE51-A8102CA83122</a:ObjectID>
<a:Name>规格型号</a:Name>
<a:Code>FSPECIFICATION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o542">
<a:ObjectID>5D4E00A8-D9FB-402F-BA4F-E7A2ADC90B37</a:ObjectID>
<a:Name>自定义分类</a:Name>
<a:Code>FCUSTOMCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1428114893</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o543">
<a:ObjectID>3048CD6D-7583-4783-9387-1E3B099CC67A</a:ObjectID>
<a:Name>一级分类</a:Name>
<a:Code>FFIRSTCATID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1406797258</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o544">
<a:ObjectID>35EE3DD5-7370-4D50-B6D3-EF082E5345D8</a:ObjectID>
<a:Name>二级分类</a:Name>
<a:Code>FSECONDCATID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o545">
<a:ObjectID>F9E390AC-9A7D-4C33-9BB7-755581EE37A1</a:ObjectID>
<a:Name>单价</a:Name>
<a:Code>FPRICE</a:Code>
<a:CreationDate>1404213259</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o546">
<a:ObjectID>7C3096A9-2B86-44A9-9466-2824FCB67F1A</a:ObjectID>
<a:Name>名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1404213259</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o547">
<a:ObjectID>BE8387C9-9158-4FEF-8F22-18DD1860277C</a:ObjectID>
<a:Name>品牌</a:Name>
<a:Code>FBRAND</a:Code>
<a:CreationDate>1404213259</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o548">
<a:ObjectID>32267E23-6A92-4A6E-973F-058EE63316AB</a:ObjectID>
<a:Name>说明</a:Name>
<a:Code>FDETAILDESC</a:Code>
<a:CreationDate>1404213357</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o549">
<a:ObjectID>B5486200-8250-4641-8AD2-94A5C552AF1B</a:ObjectID>
<a:Name>所在公司</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1404213482</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404214776</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o550">
<a:ObjectID>9C3628F7-8D01-4009-A8C3-7EA969E398F2</a:ObjectID>
<a:Name>发布状态</a:Name>
<a:Code>FRELEASESTATUS</a:Code>
<a:CreationDate>1406718059</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>1:未发布
2：已发布</a:Comment>
<a:DefaultValue>&#39;1&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o551">
<a:ObjectID>DF8CBF7A-9523-47DF-8764-348336DC17D1</a:ObjectID>
<a:Name>产品种类</a:Name>
<a:Code>FPUBLISHSET</a:Code>
<a:CreationDate>1406718647</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>A：普通
B：特供</a:Comment>
<a:DefaultValue>&#39;A&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o552">
<a:ObjectID>28EC756B-E7CB-4824-9918-C7B5C4243A69</a:ObjectID>
<a:Name>可售数量</a:Name>
<a:Code>FMAXQTY</a:Code>
<a:CreationDate>1406797084</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o553">
<a:ObjectID>49680E18-4868-4169-A088-06397BE2EB8E</a:ObjectID>
<a:Name>信息有效期</a:Name>
<a:Code>FVALIDPERIOD</a:Code>
<a:CreationDate>1406797167</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o554">
<a:ObjectID>7651E94B-583A-4EA7-AD5A-9A945AD958BD</a:ObjectID>
<a:Name>HTML</a:Name>
<a:Code>FHTML</a:Code>
<a:CreationDate>1406797269</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
</o:Column>
<o:Column Id="o555">
<a:ObjectID>735E76C2-99EA-4509-A460-664D3F19455F</a:ObjectID>
<a:Name>封装</a:Name>
<a:Code>FPACKAGE</a:Code>
<a:CreationDate>1407309694</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o556">
<a:ObjectID>9082EFAE-796D-4A7C-B203-A0AF965B0AC6</a:ObjectID>
<a:Name>类型</a:Name>
<a:Code>FTYPE</a:Code>
<a:CreationDate>1407309694</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o557">
<a:ObjectID>0B4E5AA5-858A-4588-83E5-E4DB1E357216</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1407318243</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1407318284</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o558">
<a:ObjectID>0BF4F8CE-90E3-42EC-9F53-E22105A494EA</a:ObjectID>
<a:Name>发布方式</a:Name>
<a:Code>FRELEASEMODE</a:Code>
<a:CreationDate>1409107468</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>1: 公开
2：定向</a:Comment>
<a:DefaultValue>&#39;1&#39;</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o559">
<a:ObjectID>CFF34FF3-3755-4BC7-B0FB-7B9AEBA2E765</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1409292846</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409301075</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o560">
<a:ObjectID>AFB451F4-C474-419A-8C13-CB9493A184C2</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1409292846</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o561">
<a:ObjectID>4A84DF8B-94A1-4FE6-B22B-D990DC41171A</a:ObjectID>
<a:Name>修改日期</a:Name>
<a:Code>FMODIFYDATE</a:Code>
<a:CreationDate>1409292846</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o562">
<a:ObjectID>6A986449-71B3-46DE-80F1-50F0EF41770F</a:ObjectID>
<a:Name>修改人</a:Name>
<a:Code>FMODIFIERID</a:Code>
<a:CreationDate>1409292846</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409301075</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o563">
<a:ObjectID>3C1A86DB-7740-4231-9725-C94EBA94CCC3</a:ObjectID>
<a:Name>发布日期</a:Name>
<a:Code>FPUBLISHDATE</a:Code>
<a:CreationDate>1409292954</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o564">
<a:ObjectID>F23A77DB-E86C-425B-9BF2-CC9F580098CE</a:ObjectID>
<a:Name>发布人</a:Name>
<a:Code>FPUBLISHERID</a:Code>
<a:CreationDate>1409293738</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409301075</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o565">
<a:ObjectID>2D6899C0-F242-4966-9045-662027FBF5B7</a:ObjectID>
<a:Name>到期日</a:Name>
<a:Code>FENDDATE</a:Code>
<a:CreationDate>1411022215</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o566">
<a:ObjectID>2A230661-EE1F-4301-9561-8574B0002837</a:ObjectID>
<a:Name>htmlimage</a:Name>
<a:Code>FHTML_TAG</a:Code>
<a:CreationDate>1411030743</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>image</a:DataType>
</o:Column>
<o:Column Id="o567">
<a:ObjectID>6D534101-BEF7-4986-BB2F-751FDC5184DC</a:ObjectID>
<a:Name>企业ERP产品ID</a:Name>
<a:Code>FERPPRODID</a:Code>
<a:CreationDate>1414461890</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o568">
<a:ObjectID>B267DCF4-FBB1-4781-8349-B4850C2F3E3C</a:ObjectID>
<a:Name>最小批量</a:Name>
<a:Code>FMINQTY</a:Code>
<a:CreationDate>1414742232</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1428042334</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o569">
<a:ObjectID>82224532-0C34-4DB6-A8D2-806A16613D76</a:ObjectID>
<a:Name>PK_SRM_SALPRODINFO</a:Name>
<a:Code>PK_SRM_SALPRODINFO</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404192644</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_SALPRODINFO</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o533"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o569"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o569"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o38">
<a:ObjectID>A7784A50-4278-4357-BC93-8E65D7BDD3EA</a:ObjectID>
<a:Name>T_SRM_PURCHASEORDERENTRY(采购订单明细)</a:Name>
<a:Code>T_SRM_PURCHASEORDERENTRY</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o570">
<a:ObjectID>73C56031-DAA0-40F6-B024-E4B23A999D11</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o571">
<a:ObjectID>15E51CEE-9B06-4C47-8177-FEABD1C58A28</a:ObjectID>
<a:Name>FENTRYID</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o572">
<a:ObjectID>F766914B-B4B2-4FE3-ABCA-A2A87AE9751B</a:ObjectID>
<a:Name>买方产品ID</a:Name>
<a:Code>FPURPRODUCTID</a:Code>
<a:CreationDate>1410929941</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o573">
<a:ObjectID>2A0C0416-B347-4272-B249-FC9AA05F74AC</a:ObjectID>
<a:Name>卖方产品ID</a:Name>
<a:Code>FSALPRODUCTID</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o574">
<a:ObjectID>82E621BC-A884-4E05-B5EC-A3367B3A7350</a:ObjectID>
<a:Name>产品编码</a:Name>
<a:Code>FPRODUCTNUMBER</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o575">
<a:ObjectID>04F1009C-78CA-480F-B1D3-25DE441C1F8D</a:ObjectID>
<a:Name>产品名称</a:Name>
<a:Code>FPRODUCTNAME</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411108301</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o576">
<a:ObjectID>97E80E1B-9203-4C05-90ED-D1E742999070</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o577">
<a:ObjectID>E9C4E477-27B7-4D10-99C3-D94050F9FFFD</a:ObjectID>
<a:Name>单位</a:Name>
<a:Code>FUNIT</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(10)</a:DataType>
<a:Length>10</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o578">
<a:ObjectID>40922C52-AE90-49D6-944A-DB4D7258BC66</a:ObjectID>
<a:Name>产品描述</a:Name>
<a:Code>FPRODUCTDESCRIPTION</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410937229</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o579">
<a:ObjectID>EF5F9E91-B37C-4BB0-B56A-05E601FD5C8D</a:ObjectID>
<a:Name>一级分类</a:Name>
<a:Code>FFIRSTCATID</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o580">
<a:ObjectID>7122E202-1FBC-4F92-9B46-C3A8751037CC</a:ObjectID>
<a:Name>二级分类</a:Name>
<a:Code>FSECONDCATID</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o581">
<a:ObjectID>1E86D87A-533F-4E38-81DD-AFE28F979BAA</a:ObjectID>
<a:Name>所属分类Id</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o582">
<a:ObjectID>80769D01-C096-4AF9-843B-3DE176E842F0</a:ObjectID>
<a:Name>含税单价</a:Name>
<a:Code>FTAXPRICE</a:Code>
<a:CreationDate>1410929941</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o583">
<a:ObjectID>EC72CF7E-DF93-41E1-B6F7-DF3996D11F52</a:ObjectID>
<a:Name>折扣率</a:Name>
<a:Code>FDISCOUNTRATE</a:Code>
<a:CreationDate>1410930379</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411108121</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o584">
<a:ObjectID>789BD433-1147-40F8-8D39-69A0E9D1B391</a:ObjectID>
<a:Name>交货日期</a:Name>
<a:Code>FDELIVERYDATE</a:Code>
<a:CreationDate>1410930453</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411653320</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o585">
<a:ObjectID>E697BC25-6E8D-49BB-B53C-03353C46417C</a:ObjectID>
<a:Name>单价</a:Name>
<a:Code>FPRICE</a:Code>
<a:CreationDate>1410930483</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o586">
<a:ObjectID>48CA24DB-453B-4EA1-B9C1-B5EA475D3B3F</a:ObjectID>
<a:Name>金额</a:Name>
<a:Code>FAMOUNT</a:Code>
<a:CreationDate>1410930483</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o587">
<a:ObjectID>81D356AB-3E54-4978-87EB-39B48C29ABCC</a:ObjectID>
<a:Name>订单行状态</a:Name>
<a:Code>FBILLROWSTATUS</a:Code>
<a:CreationDate>1410930543</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o588">
<a:ObjectID>61639346-E5A2-43AF-9C1D-87D37D4B4788</a:ObjectID>
<a:Name>税率</a:Name>
<a:Code>FTAXRATE</a:Code>
<a:CreationDate>1413254535</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1413260214</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o589">
<a:ObjectID>2EEA7757-DBCF-45B1-85DE-E69218877C5F</a:ObjectID>
<a:Name>下达日期</a:Name>
<a:Code>FORDERDATE</a:Code>
<a:CreationDate>1414743007</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1414745056</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o590">
<a:ObjectID>102E1292-841C-4EAD-87D0-B0B5531C55E6</a:ObjectID>
<a:Name>拒绝日期</a:Name>
<a:Code>FREJECTDATE</a:Code>
<a:CreationDate>1428317187</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1428317360</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o591">
<a:ObjectID>94D8C763-A084-42DF-A04A-FB8F8BD86B25</a:ObjectID>
<a:Name>确认日期</a:Name>
<a:Code>FCONFIRMDATE</a:Code>
<a:CreationDate>1414743007</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1428317261</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o592">
<a:ObjectID>6EACA4CA-6A6B-485C-88E6-A6748C02CC58</a:ObjectID>
<a:Name>发货日期</a:Name>
<a:Code>FSENDDATE</a:Code>
<a:CreationDate>1411653162</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411653367</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o593">
<a:ObjectID>40548C16-F759-4D8E-B7D2-D4AB9C2FC874</a:ObjectID>
<a:Name>收货日期</a:Name>
<a:Code>FRECEIPTDATE</a:Code>
<a:CreationDate>1414743007</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1414745056</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o594">
<a:ObjectID>7F8504C5-0084-40B3-AB07-B99F3E69A5A0</a:ObjectID>
<a:Name>关闭日期</a:Name>
<a:Code>FCLOSEDATE</a:Code>
<a:CreationDate>1414743007</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1414745056</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o595">
<a:ObjectID>F0C74B9C-8CF8-44A7-84CF-FF95AD991A08</a:ObjectID>
<a:Name>ERP销售订单分录ID</a:Name>
<a:Code>FERPSOEntryId</a:Code>
<a:CreationDate>1426582820</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1426583202</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o596">
<a:ObjectID>434E310D-7FAF-4E4A-A8C7-FF358D5E3296</a:ObjectID>
<a:Name>ERP采购订单分录ID</a:Name>
<a:Code>FERPPOEntryId</a:Code>
<a:CreationDate>1426582820</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1426583202</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o597">
<a:ObjectID>F3B72F18-8E4C-4EA6-AC46-AEF87F839B1A</a:ObjectID>
<a:Name>PK_SRM_PURCHASEORDERENTRY</a:Name>
<a:Code>PK_SRM_PURCHASEORDERENTRY</a:Code>
<a:CreationDate>1410929885</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410931381</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_PURCHASEORDERENTRY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o571"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o597"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o597"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o598">
<a:ObjectID>FA9C9CBD-4D7E-4DC3-8817-B8BF52E4D717</a:ObjectID>
<a:Name>T_SRM_PURPRODINFO(采购产品资料)</a:Name>
<a:Code>T_SRM_PURPRODINFO</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404192678</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o599">
<a:ObjectID>4F3FD06D-4EE4-4E12-A828-959CBD0F04C0</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FPRODUCTID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o600">
<a:ObjectID>39EDCB65-885E-4480-9090-E3F032F2DB6D</a:ObjectID>
<a:Name>产品编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1419299559</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o601">
<a:ObjectID>80B5F0CB-0F42-437F-B397-A4BF35702B15</a:ObjectID>
<a:Name>所属分类Id</a:Name>
<a:Code>FCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o602">
<a:ObjectID>9B7AEF19-59D3-4FD3-BC81-F9317C75A02C</a:ObjectID>
<a:Name>单位</a:Name>
<a:Code>FUNIT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o603">
<a:ObjectID>FC7F4399-EA43-4834-9362-EA385A4F8463</a:ObjectID>
<a:Name>图片</a:Name>
<a:Code>FIMG</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>image</a:DataType>
</o:Column>
<o:Column Id="o604">
<a:ObjectID>3DAD00A1-4EDA-4A54-842D-42DAACD0C6F4</a:ObjectID>
<a:Name>图片Path</a:Name>
<a:Code>FIMGPATH</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404366012</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o605">
<a:ObjectID>46032699-1DF4-4017-BAD3-10E6E95D9382</a:ObjectID>
<a:Name>规格型号</a:Name>
<a:Code>FSPECIFICATION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404366012</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o606">
<a:ObjectID>B1B4B00A-5246-49AF-9AF8-48C2463BCDD5</a:ObjectID>
<a:Name>企业ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o607">
<a:ObjectID>2C0BDB4B-ADFF-4BA0-A801-F5B22ECEFDD2</a:ObjectID>
<a:Name>自定义分类</a:Name>
<a:Code>FCUSTOMCATEGORYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1429457125</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o608">
<a:ObjectID>9FFAFDA1-CC7D-4CE3-BAD8-D35628D67DA6</a:ObjectID>
<a:Name>一级分类</a:Name>
<a:Code>FFIRSTCATID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1410940981</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o609">
<a:ObjectID>9113F448-C632-4E49-814B-4489AE57634E</a:ObjectID>
<a:Name>二级分类</a:Name>
<a:Code>FSECONDCATID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403859497</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o610">
<a:ObjectID>60E76D47-F72C-4776-819B-1FC624CAAFC7</a:ObjectID>
<a:Name>产品名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1403834843</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1419299619</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o611">
<a:ObjectID>AE4EB8DA-EF11-455E-AA65-B2C2FF97EEA4</a:ObjectID>
<a:Name>品牌</a:Name>
<a:Code>FBRAND</a:Code>
<a:CreationDate>1403834843</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404192737</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o612">
<a:ObjectID>3B98FB48-B26D-4C6E-BEE6-33B3775E7ECC</a:ObjectID>
<a:Name>补充说明</a:Name>
<a:Code>FDETAILDESC</a:Code>
<a:CreationDate>1403835329</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404192742</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o613">
<a:ObjectID>85042730-3314-4B37-B3B0-205450E755DF</a:ObjectID>
<a:Name>单价</a:Name>
<a:Code>FPRICE</a:Code>
<a:CreationDate>1404212990</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1404214776</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o614">
<a:ObjectID>B45BE2F8-6680-419C-B384-715616A74A71</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1405492246</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1405492433</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o615">
<a:ObjectID>4944FF3B-95C4-4951-B708-6550A030CFA9</a:ObjectID>
<a:Name>最新采购日期</a:Name>
<a:Code>FRCENTORDERDATE</a:Code>
<a:CreationDate>1407289592</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1407289818</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o616">
<a:ObjectID>76DC9C59-A2B7-4AA7-9AA8-CC1DA5B65E5B</a:ObjectID>
<a:Name>创建人</a:Name>
<a:Code>FCREATORID</a:Code>
<a:CreationDate>1409293482</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409301075</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o617">
<a:ObjectID>CFA5072A-AF2C-459E-8E47-DCE8EF933139</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FCREATEDATE</a:Code>
<a:CreationDate>1409293482</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409301075</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o618">
<a:ObjectID>3F51E3BF-347B-4345-8BAD-8EF9E79D9904</a:ObjectID>
<a:Name>修改人</a:Name>
<a:Code>FMODIFIERID</a:Code>
<a:CreationDate>1409293482</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409301075</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o619">
<a:ObjectID>9A2CA6FB-9D84-41A3-B337-4D432149D3E8</a:ObjectID>
<a:Name>修改日期</a:Name>
<a:Code>FMODIFYDATE</a:Code>
<a:CreationDate>1409293482</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1409301075</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o620">
<a:ObjectID>8A3737CB-1BC7-42E6-865C-A18CD090E73B</a:ObjectID>
<a:Name>企业ERP产品ID</a:Name>
<a:Code>FERPPRODID</a:Code>
<a:CreationDate>1414461805</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1414462246</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o621">
<a:ObjectID>F58548F8-53DB-41E1-BAF9-75EDFF1DE287</a:ObjectID>
<a:Name>PK_SRM_PURPRODINFO</a:Name>
<a:Code>PK_SRM_PURPRODINFO</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404192763</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_PURPRODINFO</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o599"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o621"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o621"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o622">
<a:ObjectID>12937BDC-DA56-4277-962F-5C14599D2C86</a:ObjectID>
<a:Name>T_SRM_USERFINDPASS(用户密码找回)</a:Name>
<a:Code>T_SRM_USERFINDPASS</a:Code>
<a:CreationDate>1431420621</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o623">
<a:ObjectID>6D997E5B-3723-4081-9867-41F67F135A66</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1431420621</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o624">
<a:ObjectID>4AF8C4D1-D597-4638-B1E3-A1DD2F644F22</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1431420621</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o625">
<a:ObjectID>882657D7-5AB3-4243-ADFE-A0D503CCF465</a:ObjectID>
<a:Name>识别码</a:Name>
<a:Code>FMEMO</a:Code>
<a:CreationDate>1431420621</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o626">
<a:ObjectID>BC8EDD33-B848-41A4-8319-90781BC56DB6</a:ObjectID>
<a:Name>日期</a:Name>
<a:Code>FFINDDATE</a:Code>
<a:CreationDate>1431420621</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o627">
<a:ObjectID>6FB1DF37-EEEE-4F34-B9C4-038B3C443DE3</a:ObjectID>
<a:Name>邮箱</a:Name>
<a:Code>FEMAIL</a:Code>
<a:CreationDate>1431420719</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o628">
<a:ObjectID>7D3941B6-234A-40DD-AA33-290587211629</a:ObjectID>
<a:Name>手机</a:Name>
<a:Code>FMOBILE</a:Code>
<a:CreationDate>1431420719</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o629">
<a:ObjectID>98FB9417-D789-4631-A01F-DDFD6E64EFC2</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>FSTATUS</a:Code>
<a:CreationDate>1431420939</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o630">
<a:ObjectID>0C6DD773-F100-4C70-8189-4E3CF3800242</a:ObjectID>
<a:Name>PK_SRM_USERFINDPASS</a:Name>
<a:Code>PK_SRM_USERFINDPASS</a:Code>
<a:CreationDate>1431420621</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1431421240</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_USERFINDPASS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o623"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o630"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o630"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o30">
<a:ObjectID>35B45FB9-9737-40F8-AD70-C19AB8C5D3DD</a:ObjectID>
<a:Name>T_SRM_QUOTEINFO（报价信息）</a:Name>
<a:Code>T_SRM_QUOTEINFO</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o631">
<a:ObjectID>5F6A8EDF-0233-4F3A-833B-FE65BEA23073</a:ObjectID>
<a:Name>报价单ID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1408001253</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o632">
<a:ObjectID>44788D39-D2D4-4339-A55D-CD27613C09D8</a:ObjectID>
<a:Name>分录主键</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1408001253</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o633">
<a:ObjectID>3C813592-0F0B-4786-8FC3-E470B6379922</a:ObjectID>
<a:Name>报价项目</a:Name>
<a:Code>FQUOTEPROJECT</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o634">
<a:ObjectID>815AC18A-E5B8-4947-ABF8-78FCBC40C312</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134251</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o635">
<a:ObjectID>91479C2E-5D67-4A2E-8B27-87E05715705F</a:ObjectID>
<a:Name>单价</a:Name>
<a:Code>FPRICE</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134256</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o636">
<a:ObjectID>1971B8A7-D8B5-431A-AA63-7086CC678636</a:ObjectID>
<a:Name>含税</a:Name>
<a:Code>FISTAX</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o637">
<a:ObjectID>DD668499-0EA7-4AC9-94D3-752EDDDC422D</a:ObjectID>
<a:Name>小计</a:Name>
<a:Code>FAMOUNT</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1407925796</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o638">
<a:ObjectID>1C4E8C48-4A6E-4570-AE8C-410D7BBBE491</a:ObjectID>
<a:Name>询价单明细ID</a:Name>
<a:Code>FINQUIRYENTRYID</a:Code>
<a:CreationDate>1403231532</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403231873</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o639">
<a:ObjectID>4473D9A5-5F51-49E0-9641-4920005DE69F</a:ObjectID>
<a:Name>税率</a:Name>
<a:Code>FTAXRATE</a:Code>
<a:CreationDate>1408004268</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408004495</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o640">
<a:ObjectID>14BEE1FE-0D9B-4A92-99F0-6798B00F0484</a:ObjectID>
<a:Name>单位</a:Name>
<a:Code>FUNIT</a:Code>
<a:CreationDate>1408021320</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408021450</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o641">
<a:ObjectID>16570F0A-9E54-480F-83EB-45EA2D82B3AA</a:ObjectID>
<a:Name>销售方产品ID</a:Name>
<a:Code>FSALPRODUCTID</a:Code>
<a:CreationDate>1409214242</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411220968</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o642">
<a:ObjectID>A8C32832-4A3F-4231-AADD-2BAAAE5F2312</a:ObjectID>
<a:Name>采购方产品ID</a:Name>
<a:Code>FPURPRODUCTID</a:Code>
<a:CreationDate>1411220898</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411222843</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o643">
<a:ObjectID>48F40460-14E4-4D03-926B-A6511BB0B0EE</a:ObjectID>
<a:Name>折扣率</a:Name>
<a:Code>FDISCOUNTRATE</a:Code>
<a:CreationDate>1431431058</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1431431286</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o644">
<a:ObjectID>77CDF24E-1AB8-4B52-A51E-DB2D3387886B</a:ObjectID>
<a:Name>PK_SRM_QUOTEINFO</a:Name>
<a:Code>PK_SRM_QUOTEINFO</a:Code>
<a:CreationDate>1397134007</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_QUOTEINFO</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o632"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o644"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o644"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o28">
<a:ObjectID>D17C9CB0-FCE0-4899-BC88-31C8ADA08CC5</a:ObjectID>
<a:Name>T_SRM_QUOTEDPRICEBILL（报价单）</a:Name>
<a:Code>T_SRM_QUOTEDPRICEBILL</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o645">
<a:ObjectID>B13D7762-70A2-4A6F-BDD4-36273BBAB72C</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1408000859</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o646">
<a:ObjectID>407AC88F-1C05-42B3-8D0F-11CA5695E50D</a:ObjectID>
<a:Name>询价方</a:Name>
<a:Code>FINQUIRYPARTY</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o647">
<a:ObjectID>4D0D28B6-0212-4D69-BF92-5BBE884023D5</a:ObjectID>
<a:Name>报价方</a:Name>
<a:Code>FBIDDER</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o648">
<a:ObjectID>9D6D13BF-3402-401A-8773-583A431682A2</a:ObjectID>
<a:Name>询价信息</a:Name>
<a:Code>FINQUIRY</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o649">
<a:ObjectID>2CD74618-911A-4A1A-80A5-72AC74607586</a:ObjectID>
<a:Name>联系人</a:Name>
<a:Code>FCONTACTNAME</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1414745984</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o650">
<a:ObjectID>CE94A083-AA65-44F7-A06F-49922070C311</a:ObjectID>
<a:Name>交易方式</a:Name>
<a:Code>FTRADETYPE</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o651">
<a:ObjectID>DFFDF3B0-CC24-430D-AAC8-63565B0A889D</a:ObjectID>
<a:Name>付款条件</a:Name>
<a:Code>FPAYMENTCONDITIONS</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1408001173</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o652">
<a:ObjectID>B884D1C7-A671-4FE9-9906-91DC4216714C</a:ObjectID>
<a:Name>税务发票类型</a:Name>
<a:Code>FTAXTYPE</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o653">
<a:ObjectID>001B89F6-E930-4158-8071-6D9A04F51DD1</a:ObjectID>
<a:Name>报价有效期</a:Name>
<a:Code>FOFFERPERIOD</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o654">
<a:ObjectID>CDAAF98C-6A75-4AC4-BACE-68B17AE5966E</a:ObjectID>
<a:Name>生产/备货周期</a:Name>
<a:Code>FPRODUCTIONCYCLE</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134295</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o655">
<a:ObjectID>67493640-B7C7-418E-87E3-617EC666EE3D</a:ObjectID>
<a:Name>附件地址</a:Name>
<a:Code>FATTACHADDRESS</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o656">
<a:ObjectID>019A0A2D-8AFF-4978-A44D-49CF9FD9D381</a:ObjectID>
<a:Name>补充说明</a:Name>
<a:Code>FREMARKS</a:Code>
<a:CreationDate>1397133710</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o657">
<a:ObjectID>D6794A9F-607E-4638-9666-F4DCAD808287</a:ObjectID>
<a:Name>询价单ID</a:Name>
<a:Code>FINQUIRYBILLID</a:Code>
<a:CreationDate>1403231387</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1408000896</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o658">
<a:ObjectID>D9E71BAD-C8EB-40A3-8CF8-B31601096920</a:ObjectID>
<a:Name>报价总金额</a:Name>
<a:Code>FALLAMOUNT</a:Code>
<a:CreationDate>1407925522</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407925970</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0.0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o659">
<a:ObjectID>0FE4B971-AE8F-441B-B411-A0B0384B69A1</a:ObjectID>
<a:Name>付款方式</a:Name>
<a:Code>FPAYWAY</a:Code>
<a:CreationDate>1408000257</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408002373</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o660">
<a:ObjectID>19DD38E0-44EA-4F01-B5F0-B1AD2AB27C01</a:ObjectID>
<a:Name>创建日期</a:Name>
<a:Code>FDATE</a:Code>
<a:CreationDate>1408002303</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408002373</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o661">
<a:ObjectID>1E7F202E-D6C5-4752-9A5E-B38C1EE9AC0F</a:ObjectID>
<a:Name>创建用户</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1408002587</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1408004495</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o662">
<a:ObjectID>E9461355-022B-495B-9914-98664B6B0085</a:ObjectID>
<a:Name>买方阅读状态</a:Name>
<a:Code>FREADSTATUS</a:Code>
<a:CreationDate>1411220517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411222843</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o663">
<a:ObjectID>D5E68F14-CCE6-4225-8858-4F57D4B9C114</a:ObjectID>
<a:Name>其它付款方式</a:Name>
<a:Code>FORDERPAY</a:Code>
<a:CreationDate>1411637815</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411638960</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(250)</a:DataType>
<a:Length>250</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o664">
<a:ObjectID>EB9B3DE2-48DC-4B0D-AF27-4421A9A63AD3</a:ObjectID>
<a:Name>单据状态</a:Name>
<a:Code>FBILLSTATUS</a:Code>
<a:CreationDate>1412992319</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1412992504</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o665">
<a:ObjectID>A1FED08E-7790-48A8-AA13-4A0B9A885D4A</a:ObjectID>
<a:Name>联系人电话</a:Name>
<a:Code>FTELPHONE</a:Code>
<a:CreationDate>1414745598</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1414746002</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o666">
<a:ObjectID>AC543CFB-F8F3-4618-A2CA-95EBE9B593A8</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1431430970</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1431431286</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o667">
<a:ObjectID>6D2CC401-E6D4-4587-AA7B-93F8D9E48D04</a:ObjectID>
<a:Name>PK_SRM_QUOTEDPRICEBILL</a:Name>
<a:Code>PK_SRM_QUOTEDPRICEBILL</a:Code>
<a:CreationDate>1397134058</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_QUOTEDPRICEBILL</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o645"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o667"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o667"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o18">
<a:ObjectID>76C74D91-4DD4-43FA-B153-A25679029C42</a:ObjectID>
<a:Name>T_SRM_INQUIRYBILL(询价单)</a:Name>
<a:Code>T_SRM_INQUIRYBILL</a:Code>
<a:CreationDate>1396773966</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o668">
<a:ObjectID>487253FA-4F8C-438F-B58C-A6CD9165477A</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1396774080</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o669">
<a:ObjectID>81C148E5-2E71-47D6-B993-D6A799E67AA7</a:ObjectID>
<a:Name>标题</a:Name>
<a:Code>FTITLE</a:Code>
<a:CreationDate>1396774069</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o670">
<a:ObjectID>799CCC56-9A23-461D-9322-EDA7F7604025</a:ObjectID>
<a:Name>报价截止日期</a:Name>
<a:Code>FQUOTEDPRICEENDDATE</a:Code>
<a:CreationDate>1396774110</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o671">
<a:ObjectID>2A8F0474-F0CB-4905-9865-62F5F0A0D7CB</a:ObjectID>
<a:Name>期望收货日期</a:Name>
<a:Code>FEXPECTEDRECEIPTDATE</a:Code>
<a:CreationDate>1396774202</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o672">
<a:ObjectID>E50A67EA-2502-4A01-9FB3-7601EE33C9B0</a:ObjectID>
<a:Name>需要报含税价</a:Name>
<a:Code>FISNEEDTAXPRICE</a:Code>
<a:CreationDate>1396774216</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o673">
<a:ObjectID>25CA85A6-2233-4221-BA76-36538C94F1DF</a:ObjectID>
<a:Name>收货地</a:Name>
<a:Code>FRECEIPTPLACE</a:Code>
<a:CreationDate>1396774247</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403233195</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o674">
<a:ObjectID>D508E306-D110-44BC-9361-CE5C9E22D669</a:ObjectID>
<a:Name>发票要求</a:Name>
<a:Code>FINVOICEREQS</a:Code>
<a:CreationDate>1396774282</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403602763</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:Comment>1：无需发票
2：普通发票
3：增值税发票</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o675">
<a:ObjectID>65F067BA-7793-41B2-8B02-C8779382BD48</a:ObjectID>
<a:Name>支持支付宝交易</a:Name>
<a:Code>FISSUPPORTALIPAY</a:Code>
<a:CreationDate>1396774616</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o676">
<a:ObjectID>102B9AAC-322F-4847-87F7-59E9EE581979</a:ObjectID>
<a:Name>所在地区</a:Name>
<a:Code>FAREA</a:Code>
<a:CreationDate>1396774907</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403233195</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o677">
<a:ObjectID>E1D7A675-18A5-4296-A5A5-9C9815274C31</a:ObjectID>
<a:Name>补充说明</a:Name>
<a:Code>FREMARKS</a:Code>
<a:CreationDate>1396774947</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1403590127</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(1000)</a:DataType>
<a:Length>1000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o678">
<a:ObjectID>C76ABE6C-259D-4EC3-ABB6-309B9055065A</a:ObjectID>
<a:Name>询价方式</a:Name>
<a:Code>FINQUIRYMODE</a:Code>
<a:CreationDate>1403233195</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o679">
<a:ObjectID>EE576050-D950-41D4-965A-4F49ECCAD155</a:ObjectID>
<a:Name>所在公司</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1403233250</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o680">
<a:ObjectID>D4EDE680-4F65-4E16-A472-6114FECEC806</a:ObjectID>
<a:Name>联系人</a:Name>
<a:Code>FCONTACTNAME</a:Code>
<a:CreationDate>1403233250</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o681">
<a:ObjectID>88DA1B58-6D95-4473-BFE8-3E9F2DCFF780</a:ObjectID>
<a:Name>联系电话</a:Name>
<a:Code>FCONTACTTEL</a:Code>
<a:CreationDate>1403233250</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403233754</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o682">
<a:ObjectID>37970B6B-24D8-48F2-862C-73809C25E675</a:ObjectID>
<a:Name>询价日期</a:Name>
<a:Code>FDATE</a:Code>
<a:CreationDate>1403589288</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403657714</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o683">
<a:ObjectID>9F01A9B4-0D48-464B-97A8-BAFD4B3B9553</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>FBILLSTATUS</a:Code>
<a:CreationDate>1403590076</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1403657714</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>报价中        ： A
报价已截止：B
已下单        ：C</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o684">
<a:ObjectID>50CA46CD-92A2-4B8A-AF44-15624ECE2866</a:ObjectID>
<a:Name>询价产品类型</a:Name>
<a:Code>FPRODUCTTYPE</a:Code>
<a:CreationDate>1403601958</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1407736517</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Comment>1：现货、标准品
2：加工、定制品
</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o685">
<a:ObjectID>0BA51702-83E2-4A8A-AE73-198C1D71D87C</a:ObjectID>
<a:Name>国家</a:Name>
<a:Code>FCOUNTRYID</a:Code>
<a:CreationDate>1407726039</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o686">
<a:ObjectID>97EBC779-ED85-42ED-80B3-22133655C867</a:ObjectID>
<a:Name>省份</a:Name>
<a:Code>FPROVINCEID</a:Code>
<a:CreationDate>1407726039</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o687">
<a:ObjectID>5033557C-D3A7-47A7-A208-DBBE1DCF36E2</a:ObjectID>
<a:Name>城市</a:Name>
<a:Code>FCITYID</a:Code>
<a:CreationDate>1407726039</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int </a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o688">
<a:ObjectID>7F847B6B-0D4D-45B7-86B6-118E8A89DB84</a:ObjectID>
<a:Name>地区</a:Name>
<a:Code>FREGIONID</a:Code>
<a:CreationDate>1407726039</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int </a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o689">
<a:ObjectID>B7422342-DD1B-4AA1-9165-24D6F20A6D8A</a:ObjectID>
<a:Name>详细地址</a:Name>
<a:Code>FDETAILADDRESS</a:Code>
<a:CreationDate>1407726039</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o690">
<a:ObjectID>EF3F7474-BF5E-4997-BAE9-66039F1F2584</a:ObjectID>
<a:Name>用户</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1407731018</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1407734572</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o691">
<a:ObjectID>474561A6-7704-4EEC-8115-61062D615840</a:ObjectID>
<a:Name>已读状态</a:Name>
<a:Code>FREADSTATUS</a:Code>
<a:CreationDate>1410792218</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1410792347</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o692">
<a:ObjectID>FFC99284-6251-449B-B2C6-499D8E18C33C</a:ObjectID>
<a:Name>交易方式</a:Name>
<a:Code>FTRADECONDITIONS</a:Code>
<a:CreationDate>1411638326</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411638960</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:Comment>ONCREDIT:赊销
ONCash:现销</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o693">
<a:ObjectID>8D080B7D-78B1-417C-BE5B-B3DAE78E7AF3</a:ObjectID>
<a:Name>报价数量</a:Name>
<a:Code>FQUOTECOUNT</a:Code>
<a:CreationDate>1411871740</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411871974</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o694">
<a:ObjectID>9D36642F-AD47-4A19-AE02-E70C5A074432</a:ObjectID>
<a:Name>下单状态</a:Name>
<a:Code>FORDERSTATUS</a:Code>
<a:CreationDate>1411969571</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1411969711</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o695">
<a:ObjectID>6A9359B7-A735-4D8C-964B-7DAFB69FB1B8</a:ObjectID>
<a:Name>单据生成类型</a:Name>
<a:Code>FBILLTYPE</a:Code>
<a:CreationDate>1424155658</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1425458961</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:Comment>0:SRM生成询价，1：ERP生成询价</a:Comment>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o696">
<a:ObjectID>E36A4CD4-FB1D-4BD7-ADE9-CA79D2669313</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1431430912</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1431431286</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o697">
<a:ObjectID>34772101-8D38-46DC-A2FD-7A8C27849CBD</a:ObjectID>
<a:Name>PK_SRM_INQUIRYBILL</a:Name>
<a:Code>PK_SRM_INQUIRYBILL</a:Code>
<a:CreationDate>1396774110</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_INQUIRYBILL</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o668"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o697"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o697"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o698">
<a:ObjectID>161C6343-A405-40DA-977F-A3275A4B13C0</a:ObjectID>
<a:Name>T_SRM_SILOG(日志)</a:Name>
<a:Code>T_SRM_SILOG</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432868430</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o699">
<a:ObjectID>959D6CAB-2F8F-4EC9-A1F2-67EA01E6A080</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892784</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o700">
<a:ObjectID>99395CA6-EADF-473C-BCCC-7C94567F2AF6</a:ObjectID>
<a:Name>用户ID</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892789</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o701">
<a:ObjectID>5D1067AA-1A2F-47C1-B2E1-A33C9318E465</a:ObjectID>
<a:Name>组织ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892794</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o702">
<a:ObjectID>8F008784-AD26-4E00-8ECA-F8B217318B9E</a:ObjectID>
<a:Name>时间</a:Name>
<a:Code>FDATETIME</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432868430</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o703">
<a:ObjectID>08BCA632-66EA-4BE2-A964-E6F3F4E28400</a:ObjectID>
<a:Name>计算机名</a:Name>
<a:Code>FCOMPUTERNAME</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892799</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>navarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o704">
<a:ObjectID>264D8445-E6A2-47FF-9BED-68EEED4A4CD1</a:ObjectID>
<a:Name>子系统</a:Name>
<a:Code>FSUBSYSTEMID</a:Code>
<a:CreationDate>1432892730</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892941</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o705">
<a:ObjectID>822E71BC-0895-4ADB-934D-2825393FD3DC</a:ObjectID>
<a:Name>IP地址</a:Name>
<a:Code>FIPADDRESS</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892809</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o706">
<a:ObjectID>5F99BDCB-378D-48B1-907D-C710BE40F944</a:ObjectID>
<a:Name>描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892813</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o707">
<a:ObjectID>793547CA-7498-4D46-8EA8-EC5173B0B4E2</a:ObjectID>
<a:Name>环境</a:Name>
<a:Code>FENVIRONMENT</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892818</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o708">
<a:ObjectID>1295EE21-64DC-4660-8EFC-3A0B7E5D8086</a:ObjectID>
<a:Name>对象</a:Name>
<a:Code>FOBJECTTYPEID</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892823</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o709">
<a:ObjectID>94581A9C-CF0B-45BF-AC0B-3493BEED2DEF</a:ObjectID>
<a:Name>操作</a:Name>
<a:Code>FOPERATENAME</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432892828</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o710">
<a:ObjectID>C053D5F6-594A-470D-9F5C-C8B77266CAB7</a:ObjectID>
<a:Name>PK_SRM_SILOG</a:Name>
<a:Code>PK_SRM_SILOG</a:Code>
<a:CreationDate>1432868048</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1432868430</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:ConstraintName>PK_SRM_SILOG</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o699"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o710"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o710"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o46">
<a:ObjectID>38C03260-7312-479D-BEEF-A3451F21E7F5</a:ObjectID>
<a:Name>T_SRM_PURREQUIREMENT(采购需求单)</a:Name>
<a:Code>T_SRM_PURREQUIREMENT</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:Comment>PUR采购
REQUIREMENT需求</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o711">
<a:ObjectID>DF4CC14A-6374-4A12-A047-31399BFE56B7</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o712">
<a:ObjectID>DBE439E4-0A8A-4573-B6E6-380ABF0FF849</a:ObjectID>
<a:Name>标题</a:Name>
<a:Code>FTITLE</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o713">
<a:ObjectID>E4F53B9A-7CD0-4BF0-837F-1D44A76F0529</a:ObjectID>
<a:Name>截止日期</a:Name>
<a:Code>FEXPIRYDATE</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o714">
<a:ObjectID>93E3C252-04EC-4250-AA45-66DCFE025A0A</a:ObjectID>
<a:Name>收货地</a:Name>
<a:Code>FRECEIPTPLACE</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o715">
<a:ObjectID>C8C06E9C-9ED0-4A6D-97F3-23C994DC8527</a:ObjectID>
<a:Name>发票要求</a:Name>
<a:Code>FINVOICEREQS</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:Comment>1：无需发票
2：普通发票
3：增值税发票</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o716">
<a:ObjectID>B6F7BC16-ED04-460F-9572-52B563E940C7</a:ObjectID>
<a:Name>所在地区</a:Name>
<a:Code>FAREA</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o717">
<a:ObjectID>5A821F4A-7B8B-4901-9FE6-1FCA560F0FCD</a:ObjectID>
<a:Name>详细(补充)说明</a:Name>
<a:Code>FREMARKS</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(1000)</a:DataType>
<a:Length>1000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o718">
<a:ObjectID>E7D8D1CE-2219-4D85-8B0F-5143E4D98B51</a:ObjectID>
<a:Name>所在公司</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1421045119</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o719">
<a:ObjectID>72FEF463-0572-4F88-9145-27C4088AA7BF</a:ObjectID>
<a:Name>联系人</a:Name>
<a:Code>FCONTACTNAME</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o720">
<a:ObjectID>F9103061-8190-4A83-9E27-E0531FDD1A3B</a:ObjectID>
<a:Name>联系电话</a:Name>
<a:Code>FCONTACTTEL</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o721">
<a:ObjectID>687D214E-37A2-4BCC-9B33-EC6316C38B44</a:ObjectID>
<a:Name>采购需求日期</a:Name>
<a:Code>FDATE</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o722">
<a:ObjectID>5F4ABDCB-CC8B-42B4-8127-3D0A5630662C</a:ObjectID>
<a:Name>状态</a:Name>
<a:Code>FBILLSTATUS</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:Comment>报价中        ： A
报价已截止：B
已下单        ：C</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o723">
<a:ObjectID>F5DEAE56-7522-4DF8-9727-DC8763EC635B</a:ObjectID>
<a:Name>国家</a:Name>
<a:Code>FCOUNTRYID</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o724">
<a:ObjectID>19540CA2-0B51-42E3-A47D-A0C56940AE27</a:ObjectID>
<a:Name>省份</a:Name>
<a:Code>FPROVINCEID</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o725">
<a:ObjectID>129DBD99-45FC-4D32-8E5C-54C53F976896</a:ObjectID>
<a:Name>城市</a:Name>
<a:Code>FCITYID</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int </a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o726">
<a:ObjectID>632577CC-E624-4591-B4A7-A8B1D1358360</a:ObjectID>
<a:Name>地区</a:Name>
<a:Code>FREGIONID</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int </a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o727">
<a:ObjectID>D097822A-85A0-41A3-A934-9F7B347128E3</a:ObjectID>
<a:Name>详细地址</a:Name>
<a:Code>FDETAILADDRESS</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o728">
<a:ObjectID>72351DF9-F52A-434F-B114-3139D1A58B49</a:ObjectID>
<a:Name>创建人(用户)</a:Name>
<a:Code>FUSERID</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o729">
<a:ObjectID>D4D6965A-16D3-478F-8D77-970A078D96E6</a:ObjectID>
<a:Name>交易方式</a:Name>
<a:Code>FTRADECONDITIONS</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:Comment>ONCREDIT:赊销
ONCash:现销</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o730">
<a:ObjectID>D3103432-07F1-4D4F-ACF2-5415FD85F916</a:ObjectID>
<a:Name>付款条件</a:Name>
<a:Code>FPAYCONDITION</a:Code>
<a:CreationDate>1420793171</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o731">
<a:ObjectID>1D684FD2-4F6D-40AE-9BA1-B6C4868EEFB7</a:ObjectID>
<a:Name>单据生成类型</a:Name>
<a:Code>FBILLTYPE</a:Code>
<a:CreationDate>1425457678</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1425460653</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:Comment>0SRM，1：ERP生成</a:Comment>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o732">
<a:ObjectID>7767D708-733B-4FAD-9C28-ADA4E8EF372D</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1433908054</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1433908125</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o733">
<a:ObjectID>6C205085-B1C1-4215-B5D2-9219177B9740</a:ObjectID>
<a:Name>PK_SRM_PURREQUIREMENT</a:Name>
<a:Code>PK_SRM_PURREQUIREMENT</a:Code>
<a:CreationDate>1420792517</a:CreationDate>
<a:Creator>rd_yaofeng_he</a:Creator>
<a:ModificationDate>1420794253</a:ModificationDate>
<a:Modifier>rd_yaofeng_he</a:Modifier>
<a:ConstraintName>PK_SRM_PURREQUIREMENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o711"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o733"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o733"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o734">
<a:ObjectID>C96944AF-B664-4978-A044-C0B012BC035E</a:ObjectID>
<a:Name>T_SRM_COMPANY(企业信息)</a:Name>
<a:Code>T_SRM_COMPANY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1406268100</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o735">
<a:ObjectID>CE37EFC0-77DA-46BD-AE3F-A2C9D25331B5</a:ObjectID>
<a:Name>企业ID</a:Name>
<a:Code>FORGID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1406268100</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o736">
<a:ObjectID>E89EA3D3-184D-4480-9DC4-8EE5842EE427</a:ObjectID>
<a:Name>企业编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1402903516</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o737">
<a:ObjectID>8513D86A-1E87-4702-A6E6-71D9C1672678</a:ObjectID>
<a:Name>企业名称</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o738">
<a:ObjectID>A4B87735-2276-43AC-BB68-405E0290117B</a:ObjectID>
<a:Name>国家</a:Name>
<a:Code>FCOUNTRYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903516</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o739">
<a:ObjectID>105011DF-F665-4A28-A071-E2A1701F9B90</a:ObjectID>
<a:Name>地区</a:Name>
<a:Code>FREGIONID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903516</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o740">
<a:ObjectID>A7E512E9-D907-40AE-B52F-2712C5AEABB0</a:ObjectID>
<a:Name>主键（子单据头）</a:Name>
<a:Code>FEntryId</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903516</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o741">
<a:ObjectID>BEE14EEB-D25B-40A3-9350-180EB7A44B02</a:ObjectID>
<a:Name>公司简介</a:Name>
<a:Code>FCOMPANYINFO</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903664</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o742">
<a:ObjectID>3E62D391-AF1A-48D7-B903-C275FAB46D5E</a:ObjectID>
<a:Name>主营产品或服务</a:Name>
<a:Code>FMAINPRODUCTION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903659</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(510)</a:DataType>
<a:Length>510</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o743">
<a:ObjectID>F9490419-1C26-44FE-8E4D-A9C07677DB48</a:ObjectID>
<a:Name>主营行业</a:Name>
<a:Code>FMAINTRADE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404376210</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o744">
<a:ObjectID>918F793A-36F2-484B-9E09-2E0B31B24D88</a:ObjectID>
<a:Name>经营模式</a:Name>
<a:Code>FBUSINESSMODE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903646</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o745">
<a:ObjectID>3F3BD815-5DCB-4FAC-9C0B-834AC0957D18</a:ObjectID>
<a:Name>提供加工/定制服务</a:Name>
<a:Code>FISCUSTOMIZEDSERVICE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1409535187</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o746">
<a:ObjectID>9F8CA620-7767-4B75-93A2-51DA9EEA3A6B</a:ObjectID>
<a:Name>公司成立时间</a:Name>
<a:Code>FBUILDYEAR</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903634</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o747">
<a:ObjectID>FC3D8049-CC97-4AE3-B3E1-6394DBD2B31F</a:ObjectID>
<a:Name>详细地址</a:Name>
<a:Code>FDETAILADDRESS</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903627</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o748">
<a:ObjectID>2C13D38A-E0AE-4428-9D7B-5FA01CFAEAFC</a:ObjectID>
<a:Name>省份</a:Name>
<a:Code>FPROVINCEID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903621</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o749">
<a:ObjectID>6BA24D8D-71F8-4577-ABAC-515A7777B489</a:ObjectID>
<a:Name>城市</a:Name>
<a:Code>FCITYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903616</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o750">
<a:ObjectID>9A0D81D6-ED94-4E1E-87B0-CE4CF2A4B1AC</a:ObjectID>
<a:Name>资产币别</a:Name>
<a:Code>FREGCURRENCYID</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903610</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o751">
<a:ObjectID>171B8E00-3C67-4913-A5CA-2D2C74D8590D</a:ObjectID>
<a:Name>资产</a:Name>
<a:Code>FREGCAPITAL</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903604</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o752">
<a:ObjectID>ADF42F0C-B934-4AB3-88F8-7433E8DD522D</a:ObjectID>
<a:Name>企业类型</a:Name>
<a:Code>FCOMPANYTYPE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1404376228</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o753">
<a:ObjectID>4B441CE7-B28A-4776-8D68-E2E06A667FFE</a:ObjectID>
<a:Name>法人</a:Name>
<a:Code>FLEGALPERSON</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903593</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o754">
<a:ObjectID>C7330AC1-C4C2-42F6-81CC-D76758F74179</a:ObjectID>
<a:Name>工商注册号</a:Name>
<a:Code>FBUSINESSREGNO</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903588</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o755">
<a:ObjectID>E46998E9-A815-427C-9165-2459163808D1</a:ObjectID>
<a:Name>管理认证体系</a:Name>
<a:Code>FMANAGESYSTEM</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903581</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o756">
<a:ObjectID>30FB33E1-4147-473B-8F42-FD3EB9C536AF</a:ObjectID>
<a:Name>厂房面积</a:Name>
<a:Code>FFACTORYAREA</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903574</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o757">
<a:ObjectID>3193E86D-B311-4626-9B57-29AFF30BC4C5</a:ObjectID>
<a:Name>月产量</a:Name>
<a:Code>FMONTHOUTPUT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903569</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o758">
<a:ObjectID>A15F0B4B-D4B4-4AD4-A707-CD9CD076BC75</a:ObjectID>
<a:Name>员工人数</a:Name>
<a:Code>FEMPNUMBER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903564</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o759">
<a:ObjectID>93E84A50-96C2-447E-8578-8C1F3274F94A</a:ObjectID>
<a:Name>主要销售区域</a:Name>
<a:Code>FMAINSALEAREA</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903558</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o760">
<a:ObjectID>9179091A-4E46-4F78-BC05-91E411A7F5FA</a:ObjectID>
<a:Name>主要客户群体</a:Name>
<a:Code>FMAINCUSTGROUP</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903553</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o761">
<a:ObjectID>0FF6816B-0CBF-4827-A509-ECF39C3B4DF2</a:ObjectID>
<a:Name>年营业额</a:Name>
<a:Code>FYEARTURNOVER</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1402903547</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o762">
<a:ObjectID>7D79AD6A-590E-40EE-98FF-050E1A6FC734</a:ObjectID>
<a:Name>年出口额</a:Name>
<a:Code>FYEAREXPORT</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o763">
<a:ObjectID>E5470965-2001-4D5A-B8B5-6D22A5A16FE9</a:ObjectID>
<a:Name>开户银行</a:Name>
<a:Code>FDEPOSITBANK</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o764">
<a:ObjectID>3244AE39-2684-42C5-9D63-7A6EAC10CACF</a:ObjectID>
<a:Name>开户账号</a:Name>
<a:Code>FBANKNO</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o765">
<a:ObjectID>7595C76E-3587-4AC2-89DF-F13D0AF3C6B7</a:ObjectID>
<a:Name>公司主页</a:Name>
<a:Code>FHOMEPAGE</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o766">
<a:ObjectID>C5129FA9-0174-4743-9238-AB672D2EE613</a:ObjectID>
<a:Name>企业描述</a:Name>
<a:Code>FDESCRIPTION</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1407463090</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o767">
<a:ObjectID>2EEE91F5-AA96-4400-A9BA-7449BEB1E482</a:ObjectID>
<a:Name>公司Logo</a:Name>
<a:Code>FLOGOPATH</a:Code>
<a:CreationDate>1407831441</a:CreationDate>
<a:Creator>rd_jim_kuang</a:Creator>
<a:ModificationDate>1407834482</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o768">
<a:ObjectID>D09EA263-1D33-4702-8FAF-60AC3F1035E2</a:ObjectID>
<a:Name>系统注册日期</a:Name>
<a:Code>FREGDATE</a:Code>
<a:CreationDate>1415589765</a:CreationDate>
<a:Creator>Rd_xiaofeng_cheng</a:Creator>
<a:ModificationDate>1415590467</a:ModificationDate>
<a:Modifier>Rd_xiaofeng_cheng</a:Modifier>
<a:DefaultValue>getdate()</a:DefaultValue>
<a:DataType>datetime</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o769">
<a:ObjectID>E96903D7-8F10-49FD-8471-15522BD66CE8</a:ObjectID>
<a:Name>禁用状态</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1418967368</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>1418968138</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:Comment>&#39;0&#39;表示未禁用，&#39;1&#39;表示禁用</a:Comment>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o770">
<a:ObjectID>60B160FB-2072-4307-AB65-A3DCA048C8B0</a:ObjectID>
<a:Name>认证状态</a:Name>
<a:Code>FCERTIFICATEDSTATUS</a:Code>
<a:CreationDate>1418967368</a:CreationDate>
<a:Creator>RD_charles_xiao</a:Creator>
<a:ModificationDate>1418968138</a:ModificationDate>
<a:Modifier>RD_charles_xiao</a:Modifier>
<a:Comment>&#39;0&#39;表示未认证，&#39;1&#39;表示认证</a:Comment>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o771">
<a:ObjectID>AB0D5B2A-78D1-4827-9B5C-048EE563B3FF</a:ObjectID>
<a:Name>产品试用天数</a:Name>
<a:Code>FDEFAULTTRIALDAYS</a:Code>
<a:CreationDate>1435284557</a:CreationDate>
<a:Creator>RD_varus_luo</a:Creator>
<a:ModificationDate>1435285813</a:ModificationDate>
<a:Modifier>RD_varus_luo</a:Modifier>
<a:DefaultValue>180</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o772">
<a:ObjectID>3F785483-E337-4B15-B407-A86220827682</a:ObjectID>
<a:Name>PK_SRM_COMPANY</a:Name>
<a:Code>PK_SRM_COMPANY</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1406268100</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:ConstraintName>PK_SRM_COMPANY</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o735"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o772"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o772"/>
</c:ClusterObject>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o773">
<a:ObjectID>FEB6530A-F196-4592-9513-E2DF97BE36CD</a:ObjectID>
<a:Name>Reference_2</a:Name>
<a:Code>Reference_2</a:Code>
<a:CreationDate>1397133720</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Generated>0</a:Generated>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o28"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o30"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o667"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o774">
<a:ObjectID>82D322F2-1B89-4311-9FCC-844649A53FE8</a:ObjectID>
<a:CreationDate>1397134058</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1397134139</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<c:Object1>
<o:Column Ref="o645"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o631"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o775">
<a:ObjectID>B07C6F06-60E7-4AAB-B572-E3E15DB71D02</a:ObjectID>
<a:Name>Reference_1</a:Name>
<a:Code>Reference_1</a:Code>
<a:CreationDate>1396775623</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1400570530</a:ModificationDate>
<a:Modifier>rd_jim_kuang</a:Modifier>
<a:Generated>0</a:Generated>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o18"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o20"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o697"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o776">
<a:ObjectID>1967F353-C998-417D-89DF-D7D38801558B</a:ObjectID>
<a:CreationDate>1396775623</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396777291</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<c:Object1>
<o:Column Ref="o668"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o467"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:DefaultGroups>
<o:Group Id="o777">
<a:ObjectID>232F12BD-C26D-42D8-AF92-177135663333</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1393208215</a:CreationDate>
<a:Creator>RD_jim_kuang</a:Creator>
<a:ModificationDate>1396754514</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o778">
<a:ObjectID>FB94A3E4-F1AB-414D-AC3B-57F40608DF2E</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1396754514</a:CreationDate>
<a:Creator>rd_kaite_he</a:Creator>
<a:ModificationDate>1396754514</a:ModificationDate>
<a:Modifier>rd_kaite_he</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/sqlsv2k5.xdb</a:TargetModelURL>
<a:TargetModelID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>