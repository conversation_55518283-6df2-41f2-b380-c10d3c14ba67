-- 收款单测试查询 - 逐步调试版本

-- 第一步：测试收款单主表是否存在
SELECT TOP 5 * FROM T_AR_RECEIVEBILL;

-- 第二步：测试收款单分录表是否存在  
-- SELECT TOP 5 * FROM T_AR_RECEIVEBILLENTRY;

-- 第三步：如果上面的表不存在，尝试其他可能的表名
-- SELECT TOP 5 * FROM T_AR_RECBILL;
-- SELECT TOP 5 * FROM T_AR_COLLECTION;
-- SELECT TOP 5 * FROM T_AR_RECEIPT;

-- 第四步：查找所有包含"收款"或"RECEIV"的表
-- SELECT TABLE_NAME 
-- FROM INFORMATION_SCHEMA.TABLES 
-- WHERE TABLE_NAME LIKE '%RECEIV%' 
--    OR TABLE_NAME LIKE '%收款%'
--    OR TABLE_NAME LIKE '%COLLECTION%'
--    OR TABLE_NAME LIKE '%RECEIPT%';

-- 第五步：查找应收相关的所有表
-- SELECT TABLE_NAME 
-- FROM INFORMATION_SCHEMA.TABLES 
-- WHERE TABLE_NAME LIKE 'T_AR_%'
-- ORDER BY TABLE_NAME;

-- 使用说明：
-- 1. 先执行第一步，如果报错说明表名不对
-- 2. 如果第一步成功，继续第二步
-- 3. 如果失败，取消注释第三步尝试其他表名
-- 4. 如果都不行，执行第四步和第五步查找正确的表名
