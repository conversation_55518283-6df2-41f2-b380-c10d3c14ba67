-- 客户欠款查询 - 日期、客户名、截至欠款
SELECT 
    CONVERT(varchar(10), GETDATE(), 120) AS '日期',
    c_l.FNAME AS '客户名',
    COALESCE(应收.应收金额, 0) - COALESCE(收款.已收金额, 0) AS '截至欠款'
FROM 
    T_BD_CUSTOMER c
    INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
    LEFT JOIN (
        -- 应收账款汇总
        SELECT 
            FCUSTID,
            SUM(FALLAMOUNT) AS 应收金额
        FROM T_AR_RECEIVABLE r
        INNER JOIN T_AR_RECEIVABLEENTRY re ON r.FID = re.FID
        WHERE r.FDOCUMENTSTATUS = 'C'  -- 已审核
        AND r.FCANCELSTATUS = 'A'      -- 未作废
        ${if(len(截止日期)==0,"","AND r.FDATE <= '"+ 截止日期 +"'")}
        GROUP BY FCUSTID
    ) 应收 ON c.FCUSTID = 应收.FCUSTID
    LEFT JOIN (
        -- 收款汇总
        SELECT 
            FCUSTID,
            SUM(FREALRECAMOUNT) AS 已收金额
        FROM T_AR_RECEIVEBILL rb
        INNER JOIN T_AR_RECEIVEBILLENTRY rbe ON rb.FID = rbe.FID
        WHERE rb.FDOCUMENTSTATUS = 'C'  -- 已审核
        AND rb.FCANCELSTATUS = 'A'      -- 未作废
        ${if(len(截止日期)==0,"","AND rb.FDATE <= '"+ 截止日期 +"'")}
        GROUP BY FCUSTID
    ) 收款 ON c.FCUSTID = 收款.FCUSTID
WHERE 
    c.FDOCUMENTSTATUS = 'C'  -- 客户已审核
    AND c.FFORBIDSTATUS = 'A'  -- 客户未禁用
    AND (COALESCE(应收.应收金额, 0) - COALESCE(收款.已收金额, 0)) > 0  -- 只显示有欠款的客户
ORDER BY 
    '截至欠款' DESC, c_l.FNAME
