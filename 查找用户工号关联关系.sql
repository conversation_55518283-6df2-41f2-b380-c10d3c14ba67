-- 查找用户和操作员表之间的关联关系，确定如何获取制单人工号

-- 1. 首先查看T_SEC_USER表结构
SELECT 
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型',
    CHARACTER_MAXIMUM_LENGTH AS '最大长度',
    IS_NULLABLE AS '是否可空',
    COLUMN_DEFAULT AS '默认值'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'T_SEC_USER'
ORDER BY ORDINAL_POSITION;

-- 2. 查看T_BD_OPERATOR表结构
SELECT 
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型',
    CHARACTER_MAXIMUM_LENGTH AS '最大长度',
    IS_NULLABLE AS '是否可空',
    COLUMN_DEFAULT AS '默认值'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'T_BD_OPERATOR'
ORDER BY ORDINAL_POSITION;

-- 3. 查看入库单主表T_STK_INSTOCK的创建人相关字段
SELECT 
    COLUMN_NAME AS '字段名',
    DATA_TYPE AS '数据类型',
    CHARACTER_MAXIMUM_LENGTH AS '最大长度'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'T_STK_INSTOCK'
    AND (COLUMN_NAME LIKE '%CREATOR%' 
         OR COLUMN_NAME LIKE '%USER%'
         OR COLUMN_NAME LIKE '%OPERATOR%')
ORDER BY ORDINAL_POSITION;

-- 4. 测试不同的关联方式来获取制单人工号
-- 方案1：通过FUSERID关联
SELECT TOP 10
    h.FBILLNO AS '入库单号',
    creator.FNAME AS '制单人姓名',
    creator.FUSERID AS '用户ID',
    op1.FNUMBER AS '工号方案1_通过FUSERID',
    op1.FNAME AS '操作员姓名1'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
LEFT JOIN T_BD_OPERATOR op1 ON creator.FUSERID = op1.FUSERID
WHERE h.FBILLNO IS NOT NULL;

-- 方案2：通过FCREATORID直接关联操作员表
SELECT TOP 10
    h.FBILLNO AS '入库单号',
    h.FCREATORID AS '创建人ID',
    op2.FNUMBER AS '工号方案2_直接关联',
    op2.FNAME AS '操作员姓名2'
FROM T_STK_INSTOCK h
LEFT JOIN T_BD_OPERATOR op2 ON h.FCREATORID = op2.FOperatorId
WHERE h.FBILLNO IS NOT NULL;

-- 方案3：通过用户名关联
SELECT TOP 10
    h.FBILLNO AS '入库单号',
    creator.FNAME AS '制单人姓名',
    creator.FNUMBER AS '用户编号',
    op3.FNUMBER AS '工号方案3_通过编号',
    op3.FNAME AS '操作员姓名3'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
LEFT JOIN T_BD_OPERATOR op3 ON creator.FNUMBER = op3.FNUMBER
WHERE h.FBILLNO IS NOT NULL;

-- 5. 检查哪些方案能成功获取到工号
SELECT 
    '方案1_通过FUSERID' AS '关联方案',
    COUNT(*) AS '总记录数',
    COUNT(op1.FNUMBER) AS '有工号记录数',
    CAST(COUNT(op1.FNUMBER) * 100.0 / COUNT(*) AS DECIMAL(5,2)) AS '成功率%'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
LEFT JOIN T_BD_OPERATOR op1 ON creator.FUSERID = op1.FUSERID
WHERE h.FBILLNO IS NOT NULL

UNION ALL

SELECT 
    '方案2_直接关联操作员',
    COUNT(*),
    COUNT(op2.FNUMBER),
    CAST(COUNT(op2.FNUMBER) * 100.0 / COUNT(*) AS DECIMAL(5,2))
FROM T_STK_INSTOCK h
LEFT JOIN T_BD_OPERATOR op2 ON h.FCREATORID = op2.FOperatorId
WHERE h.FBILLNO IS NOT NULL

UNION ALL

SELECT 
    '方案3_通过编号关联',
    COUNT(*),
    COUNT(op3.FNUMBER),
    CAST(COUNT(op3.FNUMBER) * 100.0 / COUNT(*) AS DECIMAL(5,2))
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
LEFT JOIN T_BD_OPERATOR op3 ON creator.FNUMBER = op3.FNUMBER
WHERE h.FBILLNO IS NOT NULL;

-- 6. 查看实际的数据样例来确定正确的关联方式
SELECT TOP 5
    h.FBILLNO AS '入库单号',
    h.FCREATORID AS '创建人ID',
    creator.FUSERID AS '用户表ID',
    creator.FNAME AS '用户姓名',
    creator.FNUMBER AS '用户编号',
    op.FOperatorId AS '操作员ID',
    op.FNUMBER AS '操作员工号',
    op.FNAME AS '操作员姓名'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
LEFT JOIN T_BD_OPERATOR op ON 1=1  -- 先不加关联条件，看看数据结构
WHERE h.FBILLNO IS NOT NULL
    AND creator.FUSERID IS NOT NULL
ORDER BY h.FCREATEDATE DESC;
