<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1463973069" ExtractionBranch="1" ExtractionDate="1463973069" ExtractionId="915315" ExtractionVersion="10" ID="{0D25530E-67EC-4E59-A819-B17C90EDC4D2}" Label="" LastModificationDate="1568083807" Name="TM_SCO供应链协同" Objects="203" RepositoryId="{5C34CC33-BB05-4053-9571-E184DEFF2D78}" Symbols="15" Target="Sybase SQL Anywhere 11" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>0D25530E-67EC-4E59-A819-B17C90EDC4D2</a:ObjectID>
<a:Name>TM_SCO供应链协同</a:Name>
<a:Code>TM_SCO</a:Code>
<a:CreationDate>1382685822</a:CreationDate>
<a:Creator>RD_tipi</a:Creator>
<a:ModificationDate>1384304590</a:ModificationDate>
<a:Modifier>RD_tipi</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Event&gt;&gt;]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=2
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=1
RefrUpdateConstraint=1
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=30
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=128
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=128
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {5C34CC33-BB05-4053-9571-E184DEFF2D78}
MODEL_ID 915315
MODEL_VRSN 10
BRANCH_ID 1
EXT_DATE 1463973069
EXT_END 1463973069
OBJECTS 
BEG_BLOCK 
 915315 {0D25530E-67EC-4E59-A819-B17C90EDC4D2}
 915316 {48B9A998-1155-44DA-9CCD-4374995E4F61}
 915317 {3B4EC121-1A4C-4ECC-8A4D-4C433C73844E}
 915318 {A400A9FA-0423-4686-9592-E1FF45BE8270}
 915319 {197E52B9-2DBD-41AA-BE30-633AF5610AC9}
 915320 {EFB7CCB5-D288-430D-BE12-4938CD87F139}
 915321 {02C34F06-B310-486F-8FDC-B1BC07D94D09}
 915322 {CDC60A51-D3FD-4055-9ABC-0ED81952B86F}
 915323 {34A5D931-2DB7-4141-B093-9AF31FEED757}
 915324 {E597253B-1FB0-464F-9BF7-499674C9BD43}
 915325 {C068CF23-21DF-4ED1-ACE2-D9B620009193}
 915326 {30F71705-A5A9-4BB2-BC05-DAB83DD9312A}
 915327 {EE1996DD-7DC5-4AF0-9D5F-4257AF2C78AA}
 915328 {78339338-25A4-45D1-9BA7-60310C2BF793}
 915329 {2A3E5F91-B240-4490-8F3F-5D30CD17D358}
 915330 {BDC2052C-0A61-41C2-940B-729BCFA2F02B}
 915331 {52FA208C-4A79-496B-AD45-0CDCDF5277EE}
 915332 {16413A29-5CD4-4E59-88DC-40011AF60396}
 915333 {D43646BF-166D-429D-A7EC-1313C0523DEA}
 915334 {C3C8712D-FB69-467F-AEAC-32BA686801B5}
 915335 {1C641A8A-AC66-48F4-B108-10799586D6B2}
 915336 {F037959E-3FF5-4C13-A05F-212412C47BBF}
 915337 {9077EBD2-C575-4B48-88A2-DA0A68D9006A}
 915338 {55C7B6F0-612A-476C-9F7F-3E2DECAE65D9}
 915339 {B6A73C8E-8B1A-44A1-A6C3-C55A83F92FC7}
 915340 {6E451A65-741C-40D8-8AB8-F36BBC8BB9D3}
 915341 {4C2AF82D-0CC1-4E81-B63A-BE8C53512A49}
 915342 {C7875659-34B1-49F5-9B1D-CF6C809DD6E9}
 915343 {0AECE690-520A-4242-9BF7-C6126DEAB5E9}
 915344 {F3E84398-1527-4416-A5F5-395C7EB2EBC0}
 915345 {21C4CF26-A978-47C4-9F9F-D4F94DB597DF}
 915346 {DF677ECB-5301-4EC1-92DB-0F4A09E21340}
 915347 {6C3AC084-E24B-485D-B7DD-874351AA3D3D}
 915348 {014C6052-57BB-4D24-B18A-46DA70E71EC9}
 915349 {DFA73099-58D4-4219-9A8A-65A660585B85}
 915350 {0EF964AB-66DD-4702-8EF9-3BBBCC6DD445}
 915351 {FDCB7BEA-433D-4388-82B2-FBCF64BE00F4}
 915352 {8226889D-7C24-4021-B605-55D5E09C0AFE}
 915353 {B9450F31-8CE5-4728-89AF-5069D4448A11}
 915354 {C6B60C84-0976-4BD8-83D7-D5D6B5446D0E}
 915355 {8DF56773-DA01-4FC3-9F2E-47C807B0CB3C}
 915356 {EAEAA23F-F5CD-498F-99F0-4C5A9CFF93BB}
 915357 {A7A0B8E7-3E15-4303-9BBA-F3547EA2C048}
 915358 {4AB74F58-C9CF-4D42-81B2-06A56D180A45}
 915359 {B592EC10-04A3-4F58-8B54-41A89C55841C}
 915360 {D0182B47-BF57-4B79-915A-CBB389CADD64}
 915361 {681C51B5-B808-4E71-9543-EFE7C761B8FF}
 915362 {38D368C0-39A7-49D9-978E-C3F771FCFEC6}
 915363 {77E962FD-4903-4179-AB81-BD3CF0078DED}
 915364 {4D3B95D5-05E1-4732-B9CF-BE8F3F4AA5EC}
 915365 {2946D0E5-2BA6-405B-8977-C34192CED946}
 915366 {5FBD721B-852A-4C9E-A929-8CF0F7282E4C}
 915367 {4CE1B549-91E4-44C8-8306-DDDB773352C8}
 915368 {35D11120-C4E0-4E67-A765-9BB2ECD4A409}
 915369 {F95E3A37-4706-4F8D-ADC8-195E162D30A6}
 915370 {BA7AAF49-DDD9-4336-AA1E-3FA47B9010A5}
 915371 {937DEF48-7C22-4007-A335-624F7E2B8A7B}
 915372 {4C0C10F6-F069-4210-9306-708F8207F526}
 915373 {AD201AA4-CC44-4767-B2BB-F96C5522855B}
 915374 {46934CF2-93DA-4BCF-B7EF-41C2FAF575E0}
 915375 {BAB79ACC-BE50-4ED4-A675-87DF1FDFEC9C}
 915376 {D195A8BF-B21A-45FC-824A-ED7917A1CF23}
 915377 {504736D9-9B40-4AD2-A10C-BDE5F0812A18}
 915378 {CBFBDC33-BC8A-4D41-8110-6E1E027916DF}
 915379 {8F300D3E-8270-4221-8E10-6F2140935AE3}
 915380 {2F66186B-4173-4089-8E9A-E8AEFF6FBF3C}
 915381 {2D80D10E-2A56-47E6-BE81-1EC9F7CD358E}
 915382 {9A082913-D50E-4C2B-9B80-E47828B21AFB}
 915383 {63DEE620-9FCB-4E77-B7CD-75B03CA813D3}
 915384 {245EF931-269E-4192-8F6E-EC56A115384D}
 915385 {78D0BE23-0339-4D7D-8126-93545F31C996}
 915386 {7243A0BE-82F6-4FCD-A91A-5A9FA8C51A98}
 915387 {2B370220-FDCC-4EE4-95E8-9B5F53A0CEB3}
 915388 {F1ECA332-3C16-4285-BA5C-35CDE1568EC3}
 915389 {AA0F6A0A-32F5-47B7-AFB7-087CCEC92189}
 915390 {EB0DC464-E5AA-4611-B001-4F4F2D25052C}
 915391 {26343552-5410-4458-B6BF-D3FE5F0BCF24}
 915392 {F96A4624-4CCB-4941-8915-F777E7C617DB}
 915393 {5E971BDF-D671-4BDF-8919-528EA84861DF}
 915394 {0D5E953C-76E1-40BF-86AF-EFA8DF3802FB}
 915395 {1E908303-0127-4BAD-A6C3-A146A14D28FD}
 915396 {8AD9355B-0714-4B5E-9390-F5F4FB94C748}
 915397 {ED35316B-6563-4911-9615-AEAC8459A467}
 915398 {8EA3EAC6-2759-486D-8207-C015A2340A57}
 915399 {25F2174F-7E68-42D9-8CE4-D2F5BB919ACA}
 915400 {76E6DD26-2786-435D-A938-637DBDA1797B}
 915401 {5B3F321B-AD67-4943-96F3-47F5A0B0A436}
 915402 {ACE22AAD-F47D-4EED-9A2F-FDC60CEFEA3E}
 915403 {C7689642-6E18-449D-BB72-273F34DEC628}
 915404 {08070D1A-1331-4CDD-B447-4D2CA410F1F2}
 915405 {1646FA03-8343-46E4-B6FE-5B0DF7D6D79C}
 915406 {E81106F5-49A6-4783-BAD7-5B91D65DDA98}
 915407 {94DD784D-04B0-499D-AA3C-EC0702AD36DA}
 915408 {06180907-CEFC-4BBE-A9AD-CBEA8D85CDA1}
 915409 {03829788-BCE8-4730-B6E7-AEF8F3479D45}
 915410 {863225ED-C21D-44FA-9A7F-4FDC67A09F7A}
 915411 {36F52718-88F9-4A12-B0E0-285FDE6BE056}
 915412 {D3F5D6C5-DE62-4F42-9505-6A81086029B3}
 915413 {48501CD7-EE66-4849-BDC5-CD652A8325A4}
 915414 {D69CE3BD-AEE2-4B66-8C6C-3CCA0DE00089}
 915415 {355B152D-C426-4FB2-A5AD-A82F6083CDCC}
 915416 {962F040E-EEFD-48CD-B5E0-A04A053480E2}
 915417 {E866E4EE-A36A-4B7D-965D-003BD180E3E9}
 915418 {FE3C44C4-733A-42E0-8655-EAEC96C98A54}
 915419 {CAFE916F-0C57-4634-A83E-EDDADC378B98}
 915420 {A1CFA273-B70E-4CCD-A2B7-C5C698ADFE4F}
 915421 {F8BE0FD4-9377-4C5C-A3B3-C14C340476D4}
 915422 {135B247F-C9ED-47EB-A64F-C245CEBA572E}
 915423 {84C04B9C-9F28-43E7-AFE7-F4A3D31C8F36}
 915424 {1BFB79CD-2B09-468E-B98F-888F2195D7BA}
 915425 {BE3D4D77-EAD4-4C14-833E-28F318860D02}
 915426 {9F2018FF-AF25-4C45-91FE-B9076C47BB5F}
 915427 {D9CF3F8B-C5BB-453E-972C-C3C5400D34DD}
 915428 {1B2BAC1B-4C49-4D1D-9DE5-858101ABA155}
 915429 {60A751A8-4153-443D-B6CE-D05F6FA45715}
 915430 {4404BADE-1D31-4AB1-9186-5B0347541ED0}
 915431 {688D7C11-28D2-49D1-A7FF-06C7AD612828}
 915432 {F7501505-5BD5-463F-A6F5-8D55A628FD3F}
 915433 {02D351C5-068A-4D91-A2F3-9B6ADC7A76BF}
 915434 {24BC77F6-7688-4B78-9F34-E7EFBBB0C82B}
 915435 {F5A6DBE6-06E7-44BB-8821-3D5759D1408A}
 916690 {7CE1B43D-13FF-4A68-852C-09A590E448A0}
 916691 {65A560A7-86F2-41F9-A4A1-3A62BEA89187}
 916993 {8868BE42-6240-4CBD-8ECF-7708E7FBD1AB}
 916994 {C81505E6-3434-4090-8800-68A3F7AA81FA}
 918120 {BF4D6892-9E79-4EF6-9563-10AD139F332F}
 918121 {C1689DC1-29EA-4CCA-A6A5-5839D1F27568}
 918476 {AD981423-1B07-4443-9ED7-6641B6523668}
 987954 {83BB188D-7EBD-4A26-80AA-7D317D52C5F2}
 987955 {B66E9F3C-C4FD-4309-B08E-A9885F8E307C}
 987956 {112907B9-C041-46D6-9D06-5AF2CE2B4E0C}
 987957 {5370B73E-3D32-4F0A-9A27-A0132AFFC679}
 987958 {75B5AD27-BE5B-4197-B524-32C8B81E1887}
 987959 {2B20BD58-8082-4A5C-914F-AC888E2EC4A6}
 987960 {59492D94-FBE4-4290-AF37-792FF502F512}
 987961 {596F46E5-39EA-4AA0-AFC7-99F76F1F83D1}
 995680 {52B14D78-6074-4B31-9C12-9A7D3BE86017}
 995681 {D88F97BB-D873-476D-A874-BE2B93B08D75}
 995682 {FD0D3434-41CE-463F-A0EA-5684453FC74C}
 995683 {1BDD356D-AE54-4E8C-9D6C-DA527F596AEF}
 995684 {2830F202-7467-4370-AD71-5DFC1FCEF4EC}
 995685 {C83FC4E0-D38C-45FE-8A2D-B0524B9C7183}
 995686 {6325FACF-CD69-431F-AB4F-ABD1C24C7EF1}
 995687 {487039A2-F85A-4B5F-A16A-F59558582B1F}
 995688 {AE5CFB69-8EC1-431B-A0E7-F26E3281CAB0}
 995689 {53B386C4-2C6D-4844-B839-FF2FB338C000}
 995690 {B7DE5409-829A-44A8-9375-28D2609A6979}
 995691 {317908FA-5788-4770-8BF6-8265E8BE7100}
 995692 {FB8E1810-C2BA-445F-BC2B-0103CAC057A2}
 995693 {D231DBB9-229A-453A-9DE3-29BAF517ADA5}
 995694 {5E8B6257-AA43-4783-AD2F-DD1DCDB435CA}
 995695 {4ECE563C-B7CC-43D2-BF87-DED1181C11EB}
 995696 {4346CD43-AC8F-47F9-8935-D799D1D63194}
 995697 {22BBA05B-15DB-49CA-B9B3-1A932CF9747F}
 995698 {AFCA4A8A-2762-441A-8561-FF8E1D4CF3E2}
 995699 {626848DC-03AA-43D0-A406-FDAC653280D3}
 995700 {23671FCC-7CEA-48A0-AD3C-C229580A2D48}
 995701 {B6B26915-5B60-4CA4-AC88-E54B51205C73}
 995702 {EF967B0D-51CC-4063-AA60-D002A6CB6F01}
 995703 {2D9387D3-8BEE-4DCF-82FE-E6996DE554C0}
 995704 {51DA7F70-9BB4-4FDE-9F76-5212EEA51D52}
 995705 {CC6666D1-2A61-4D7D-BA22-49E4F62F100A}
 995706 {9EB1A4A5-01F3-450C-B6F9-F7AA8235CEFB}
 995707 {DA3E1C97-6751-4C6B-B82E-CEB4068D20C9}
 995708 {BD01B20B-79E4-473B-8AFB-1581B1BFD8BF}
 995709 {DBF3CDD9-C76C-4E01-A2F9-8D4383CE9CE7}
 995710 {A2CD23A5-C907-46CF-9E37-7FDA52873654}
 995711 {601534F7-BDA0-4464-94F6-E9F81EFC8546}
 995712 {474A5B39-EADF-4D45-96E1-7ABB4D43E103}
 995713 {2B88188B-8402-42C9-A08E-292611F41027}
 995714 {FA2B9CDF-414C-4DF7-8B91-8AE006D62268}
 995715 {8E4785D4-6CA5-4C87-9E4C-639A023D8C91}
 1003310 {F0F56D6C-EAD0-4434-9BA8-E69961979D86}
 1003311 {4AE96ED9-56F5-42E4-AEAA-76FB5FBBE691}
 1003312 {19D6291E-5BCE-48FE-A37F-0271E5AC8C42}
 1003565 {0D2A255A-1690-4383-BFE9-266C9C9E0263}
 1023455 {9D37310C-3E73-43E4-B4D4-08933924E317}
 1023456 {3788E971-42F0-454B-8FA6-265DB46DE8A8}
 1023457 {E69346F6-2D11-4B2F-A235-69590DEA4336}
 1023458 {740A5C47-145C-42A9-ADD1-96DC114E0E2C}
 1023459 {3A8C26A4-05C3-4EFF-AB61-A8B65A5C6569}
 1023460 {EBB20198-0E92-4FA0-81EB-154480E685E8}
 1023461 {BBDCFD4F-94C4-4E4B-A657-C8ECF348CFFC}
 1023462 {5841D5F5-2611-4FFF-B9F3-4591FC93C6E2}
 1023463 {2C0829AC-81C0-4A57-AD22-831CC8012DFD}
 1023464 {7FC6E17D-1337-470A-B103-C18929753631}
 1023465 {C512E6FA-17E9-41FF-A71D-C688DFCE7F35}
 1023466 {E498AF57-1F16-4808-9DA8-84D3B43A4E6C}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>48B9A998-1155-44DA-9CCD-4374995E4F61</a:ObjectID>
<a:Name>Sybase SQL Anywhere 11</a:Name>
<a:Code>SYASA11</a:Code>
<a:CreationDate>1382685821</a:CreationDate>
<a:Creator>RD_tipi</a:Creator>
<a:ModificationDate>1382685821</a:ModificationDate>
<a:Modifier>RD_tipi</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>9C2029CC-5AF2-4828-AA6C-11C184EF93D8</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>3B4EC121-1A4C-4ECC-8A4D-4C433C73844E</a:ObjectID>
<a:Name>TM_SCO_ALL</a:Name>
<a:Code>PhysicalDiagram_1</a:Code>
<a:CreationDate>1382685822</a:CreationDate>
<a:Creator>RD_tipi</a:Creator>
<a:ModificationDate>1384305840</a:ModificationDate>
<a:Modifier>RD_tipi</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=800
Graphic unit=2
Window color=255 255 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255 255 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:PackageSymbol Id="o5">
<a:CreationDate>1382685906</a:CreationDate>
<a:ModificationDate>1382685921</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-2401,-1798), (2398,1801))</a:Rect>
<a:LineColor>11711154</a:LineColor>
<a:FillColor>12648447</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Package Ref="o6"/>
</c:Object>
</o:PackageSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o6">
<a:ObjectID>A400A9FA-0423-4686-9592-E1FF45BE8270</a:ObjectID>
<a:Name>供应链协同</a:Name>
<a:Code>SCO</a:Code>
<a:CreationDate>1382685906</a:CreationDate>
<a:Creator>RD_tipi</a:Creator>
<a:ModificationDate>1550728369</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Event&gt;&gt;]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o7">
<a:ObjectID>02C34F06-B310-486F-8FDC-B1BC07D94D09</a:ObjectID>
<a:Name>供应链协同</a:Name>
<a:Code>PhysicalDiagram_1</a:Code>
<a:CreationDate>1382685906</a:CreationDate>
<a:Creator>RD_tipi</a:Creator>
<a:ModificationDate>1550728561</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=800
Graphic unit=2
Window color=255 255 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255 255 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=No
Keep aspect=No
Keep center=No
Keep size=No
Width=15000
Height=7500
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:TableSymbol Id="o8">
<a:CreationDate>1384304545</a:CreationDate>
<a:ModificationDate>1396949963</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-18625,11622), (-3625,19122))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o9"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o10">
<a:CreationDate>1384304546</a:CreationDate>
<a:ModificationDate>1397086065</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-18446,1125), (-3450,8623))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o11"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o12">
<a:CreationDate>1396923925</a:CreationDate>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((269,11479), (15268,18978))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o13"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o14">
<a:CreationDate>1396923929</a:CreationDate>
<a:ModificationDate>1397086065</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((401,1084), (15400,8583))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o15"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o16">
<a:CreationDate>1396923954</a:CreationDate>
<a:ModificationDate>1397086065</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-18531,-9195), (-3532,-1696))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o17"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o18">
<a:CreationDate>1396924323</a:CreationDate>
<a:ModificationDate>1397086065</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((777,-9345), (15776,-1846))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o19"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o20">
<a:CreationDate>1397007392</a:CreationDate>
<a:ModificationDate>1397291969</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-18524,-19175), (-3525,-11676))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o21"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o22">
<a:CreationDate>1397007700</a:CreationDate>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-18599,-28200), (-3600,-20701))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o23"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o24">
<a:CreationDate>1397292046</a:CreationDate>
<a:ModificationDate>1397547149</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((676,-19086), (15675,-11587))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o25"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o26">
<a:CreationDate>1397720652</a:CreationDate>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((724,-28575), (15723,-21076))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o27"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o28">
<a:CreationDate>1456737453</a:CreationDate>
<a:ModificationDate>1456737578</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-37787,11622), (-22788,19121))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o29"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o30">
<a:CreationDate>1456737455</a:CreationDate>
<a:ModificationDate>1456737575</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-37712,972), (-22713,8471))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o31"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o32">
<a:CreationDate>1458199124</a:CreationDate>
<a:ModificationDate>1458199130</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-37523,-9903), (-22524,-2404))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o33"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o34">
<a:CreationDate>1550728369</a:CreationDate>
<a:ModificationDate>1550728372</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-56350,11472), (-41351,18971))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o35"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o7"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o15">
<a:ObjectID>CDC60A51-D3FD-4055-9ABC-0ED81952B86F</a:ObjectID>
<a:Name>TM_DRP_INVENTORYINFOBYMAT(配货的相关库存信息临时表)</a:Name>
<a:Code>TM_DRP_INVENTORYINFOBYMAT</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:Comment>获取配货的相关库存信息临时表
物料辅助属性临时表Kingdee.K3.SCM.App.DRP.Core.DRPDistributeService中使用</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o36">
<a:ObjectID>BE3D4D77-EAD4-4C14-833E-28F318860D02</a:ObjectID>
<a:Name>物料内码</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o37">
<a:ObjectID>9F2018FF-AF25-4C45-91FE-B9076C47BB5F</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FAUXPROPID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o38">
<a:ObjectID>D9CF3F8B-C5BB-453E-972C-C3C5400D34DD</a:ObjectID>
<a:Name>库存组织内码</a:Name>
<a:Code>FSTOCKORGID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o39">
<a:ObjectID>1B2BAC1B-4C49-4D1D-9DE5-858101ABA155</a:ObjectID>
<a:Name>仓库内码</a:Name>
<a:Code>FSTOCKID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o40">
<a:ObjectID>60A751A8-4153-443D-B6CE-D05F6FA45715</a:ObjectID>
<a:Name>库位内码</a:Name>
<a:Code>FSTOCKLOCID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o41">
<a:ObjectID>4404BADE-1D31-4AB1-9186-5B0347541ED0</a:ObjectID>
<a:Name>货主类型</a:Name>
<a:Code>FOWNERTYPEID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
</o:Column>
<o:Column Id="o42">
<a:ObjectID>688D7C11-28D2-49D1-A7FF-06C7AD612828</a:ObjectID>
<a:Name>货主</a:Name>
<a:Code>FOWNERID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o43">
<a:ObjectID>F7501505-5BD5-463F-A6F5-8D55A628FD3F</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o44">
<a:ObjectID>02D351C5-068A-4D91-A2F3-9B6ADC7A76BF</a:ObjectID>
<a:Name>库存主单位</a:Name>
<a:Code>FSTOREUNITID</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o45">
<a:ObjectID>24BC77F6-7688-4B78-9F34-E7EFBBB0C82B</a:ObjectID>
<a:Name>库存基本数量</a:Name>
<a:Code>FBASEQTY</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o46">
<a:ObjectID>F5A6DBE6-06E7-44BB-8821-3D5759D1408A</a:ObjectID>
<a:Name>库存数量</a:Name>
<a:Code>FSTOREQTY</a:Code>
<a:CreationDate>1384320327</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o13">
<a:ObjectID>34A5D931-2DB7-4141-B093-9AF31FEED757</a:ObjectID>
<a:Name>TM_DRP_MATERIALAUXP(物料辅助属性临时表)</a:Name>
<a:Code>TM_DRP_MATERIALAUXP</a:Code>
<a:CreationDate>1384323913</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:Comment>物料辅助属性临时表Kingdee.K3.SCM.App.DRP.Core.DRPDistributeService中使用</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o47">
<a:ObjectID>84C04B9C-9F28-43E7-AFE7-F4A3D31C8F36</a:ObjectID>
<a:Name>物料</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1384323913</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o48">
<a:ObjectID>1BFB79CD-2B09-468E-B98F-888F2195D7BA</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FAUXPROPID</a:Code>
<a:CreationDate>1384323913</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384325250</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o11">
<a:ObjectID>E597253B-1FB0-464F-9BF7-499674C9BD43</a:ObjectID>
<a:Name>TM_SPM_MATCHGROUPCONDITION(促销匹配条件分组临时表)</a:Name>
<a:Code>TM_SPM_MATCHGROUPCONDITION</a:Code>
<a:CreationDate>1384304546</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:Comment>促销匹配条件分组临时表：客户组、物料组</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o49">
<a:ObjectID>CAFE916F-0C57-4634-A83E-EDDADC378B98</a:ObjectID>
<a:Name>分组类型</a:Name>
<a:Code>FGROUPTYPE</a:Code>
<a:CreationDate>1384306104</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:Comment>C:客户组　M:物料组</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o50">
<a:ObjectID>A1CFA273-B70E-4CCD-A2B7-C5C698ADFE4F</a:ObjectID>
<a:Name>物料</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1386676921</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1386681122</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o51">
<a:ObjectID>F8BE0FD4-9377-4C5C-A3B3-C14C340476D4</a:ObjectID>
<a:Name>分组ＩＤ</a:Name>
<a:Code>FGROUPID</a:Code>
<a:CreationDate>1384306104</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o52">
<a:ObjectID>135B247F-C9ED-47EB-A64F-C245CEBA572E</a:ObjectID>
<a:Name>上级分组ＩＤ</a:Name>
<a:Code>FPARENTGROUPID</a:Code>
<a:CreationDate>1384306104</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o21">
<a:ObjectID>C068CF23-21DF-4ED1-ACE2-D9B620009193</a:ObjectID>
<a:Name>TM_RPM_CUSTOMER(客户临时表)</a:Name>
<a:Code>TM_RPM_CUSTOMER</a:Code>
<a:CreationDate>1397007392</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o53">
<a:ObjectID>355B152D-C426-4FB2-A5AD-A82F6083CDCC</a:ObjectID>
<a:Name>客户分类</a:Name>
<a:Code>FCUSTOMERTYPEID</a:Code>
<a:CreationDate>1397007396</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o54">
<a:ObjectID>962F040E-EEFD-48CD-B5E0-A04A053480E2</a:ObjectID>
<a:Name>客户</a:Name>
<a:Code>FCUSTOMERID</a:Code>
<a:CreationDate>1397007396</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>E866E4EE-A36A-4B7D-965D-003BD180E3E9</a:ObjectID>
<a:Name>客户内码</a:Name>
<a:Code>FCUSTMASTERID</a:Code>
<a:CreationDate>1397007396</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o56">
<a:ObjectID>FE3C44C4-733A-42E0-8655-EAEC96C98A54</a:ObjectID>
<a:Name>使用组织</a:Name>
<a:Code>FUSEORGID</a:Code>
<a:CreationDate>1397007396</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o23">
<a:ObjectID>30F71705-A5A9-4BB2-BC05-DAB83DD9312A</a:ObjectID>
<a:Name>TM_RPM_GROUP(物料客户组临时表)</a:Name>
<a:Code>TM_RPM_GROUP</a:Code>
<a:CreationDate>1397007700</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o57">
<a:ObjectID>D3F5D6C5-DE62-4F42-9505-6A81086029B3</a:ObjectID>
<a:Name>分组类型</a:Name>
<a:Code>FGROUPTYPE</a:Code>
<a:CreationDate>1397007707</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:Comment>C:客户组　M:物料组</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>48501CD7-EE66-4849-BDC5-CD652A8325A4</a:ObjectID>
<a:Name>分组ＩＤ</a:Name>
<a:Code>FGROUPID</a:Code>
<a:CreationDate>1397007707</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>D69CE3BD-AEE2-4B66-8C6C-3CCA0DE00089</a:ObjectID>
<a:Name>上级分组ＩＤ</a:Name>
<a:Code>FPARENTGROUPID</a:Code>
<a:CreationDate>1397007707</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o17">
<a:ObjectID>EE1996DD-7DC5-4AF0-9D5F-4257AF2C78AA</a:ObjectID>
<a:Name>TM_RPM_MATERIAL(物料临时表)</a:Name>
<a:Code>TM_RPM_MATERIAL</a:Code>
<a:CreationDate>1396923954</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397007675</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o60">
<a:ObjectID>08070D1A-1331-4CDD-B447-4D2CA410F1F2</a:ObjectID>
<a:Name>使用组织</a:Name>
<a:Code>FUSEORGID</a:Code>
<a:CreationDate>1396923960</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397007611</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>1646FA03-8343-46E4-B6FE-5B0DF7D6D79C</a:ObjectID>
<a:Name>物料分类</a:Name>
<a:Code>FMATERIALGROUPID</a:Code>
<a:CreationDate>1396923960</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>E81106F5-49A6-4783-BAD7-5B91D65DDA98</a:ObjectID>
<a:Name>物料</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1396923960</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>94DD784D-04B0-499D-AA3C-EC0702AD36DA</a:ObjectID>
<a:Name>物料内码</a:Name>
<a:Code>FMATMASTERID</a:Code>
<a:CreationDate>1397007429</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397086040</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>06180907-CEFC-4BBE-A9AD-CBEA8D85CDA1</a:ObjectID>
<a:Name>计量单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1396923960</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>03829788-BCE8-4730-B6E7-AEF8F3479D45</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1396923960</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>863225ED-C21D-44FA-9A7F-4FDC67A09F7A</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FASSISTPROPERTYID</a:Code>
<a:CreationDate>1396923960</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o67">
<a:ObjectID>36F52718-88F9-4A12-B0E0-285FDE6BE056</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1396923960</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o67"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o67"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o27">
<a:ObjectID>78339338-25A4-45D1-9BA7-60310C2BF793</a:ObjectID>
<a:Name>TM_RPM_RPRESULT(返利计算结果)</a:Name>
<a:Code>TM_RPM_RPRESULT</a:Code>
<a:CreationDate>1397720652</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o68">
<a:ObjectID>EB0DC464-E5AA-4611-B001-4F4F2D25052C</a:ObjectID>
<a:Name>结算组织</a:Name>
<a:Code>FSETTLEORGID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>26343552-5410-4458-B6BF-D3FE5F0BCF24</a:ObjectID>
<a:Name>返利政策</a:Name>
<a:Code>FRPPOLICYID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>F96A4624-4CCB-4941-8915-F777E7C617DB</a:ObjectID>
<a:Name>组号</a:Name>
<a:Code>FGROUPNUMBER</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o71">
<a:ObjectID>5E971BDF-D671-4BDF-8919-528EA84861DF</a:ObjectID>
<a:Name>返利周期</a:Name>
<a:Code>FRPCYCLEID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o72">
<a:ObjectID>0D5E953C-76E1-40BF-86AF-EFA8DF3802FB</a:ObjectID>
<a:Name>返利对象类型</a:Name>
<a:Code>FCUSTTYPEID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o73">
<a:ObjectID>1E908303-0127-4BAD-A6C3-A146A14D28FD</a:ObjectID>
<a:Name>返利对象</a:Name>
<a:Code>FCUSTOMERID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>8AD9355B-0714-4B5E-9390-F5F4FB94C748</a:ObjectID>
<a:Name>返利金额</a:Name>
<a:Code>FRPAMOUNT</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>ED35316B-6563-4911-9615-AEAC8459A467</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>8EA3EAC6-2759-486D-8207-C015A2340A57</a:ObjectID>
<a:Name>物料ID</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>25F2174F-7E68-42D9-8CE4-D2F5BB919ACA</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>76E6DD26-2786-435D-A938-637DBDA1797B</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FASSISTPROPERTYID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>5B3F321B-AD67-4943-96F3-47F5A0B0A436</a:ObjectID>
<a:Name>计量单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o80">
<a:ObjectID>ACE22AAD-F47D-4EED-9A2F-FDC60CEFEA3E</a:ObjectID>
<a:Name>赠送数量</a:Name>
<a:Code>FPRESENTQTY</a:Code>
<a:CreationDate>1397720659</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o81">
<a:ObjectID>C7689642-6E18-449D-BB72-273F34DEC628</a:ObjectID>
<a:Name>基本赠送数量</a:Name>
<a:Code>FBASEPRESENTQTY</a:Code>
<a:CreationDate>1397724776</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o9">
<a:ObjectID>2A3E5F91-B240-4490-8F3F-5D30CD17D358</a:ObjectID>
<a:Name>TM_SPM_MATCHCONDITION(促销匹配条件临时表)</a:Name>
<a:Code>TM_SPM_MATCHCONDITION</a:Code>
<a:CreationDate>1384304545</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1470807758</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:Comment>促销匹配条件临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o82">
<a:ObjectID>AD201AA4-CC44-4767-B2BB-F96C5522855B</a:ObjectID>
<a:Name>销售组织</a:Name>
<a:Code>FSALEORGID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>46934CF2-93DA-4BCF-B7EF-41C2FAF575E0</a:ObjectID>
<a:Name>客户</a:Name>
<a:Code>FCUSTOMERID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>BAB79ACC-BE50-4ED4-A675-87DF1FDFEC9C</a:ObjectID>
<a:Name>客户内码ＩＤ</a:Name>
<a:Code>FCUSTMASTERID</a:Code>
<a:CreationDate>1386644068</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1386681122</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>D195A8BF-B21A-45FC-824A-ED7917A1CF23</a:ObjectID>
<a:Name>客户分类ID</a:Name>
<a:Code>FCUSTTYPEID</a:Code>
<a:CreationDate>1418351770</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1418352053</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>504736D9-9B40-4AD2-A10C-BDE5F0812A18</a:ObjectID>
<a:Name>日期</a:Name>
<a:Code>FBIZDATE</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>CBFBDC33-BC8A-4D41-8110-6E1E027916DF</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>8F300D3E-8270-4221-8E10-6F2140935AE3</a:ObjectID>
<a:Name>物料</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>2F66186B-4173-4089-8E9A-E8AEFF6FBF3C</a:ObjectID>
<a:Name>物料内码ＩＤ</a:Name>
<a:Code>FMATMASTERID</a:Code>
<a:CreationDate>1386644068</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1386681122</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>2D80D10E-2A56-47E6-BE81-1EC9F7CD358E</a:ObjectID>
<a:Name>计量单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>9A082913-D50E-4C2B-9B80-E47828B21AFB</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>63DEE620-9FCB-4E77-B7CD-75B03CA813D3</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FATTRVALUEID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>245EF931-269E-4192-8F6E-EC56A115384D</a:ObjectID>
<a:Name>购买数量</a:Name>
<a:Code>FPURCHASEQTY</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o94">
<a:ObjectID>78D0BE23-0339-4D7D-8126-93545F31C996</a:ObjectID>
<a:Name>购买基本数量</a:Name>
<a:Code>FPURCHASEBASEQTY</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o95">
<a:ObjectID>7243A0BE-82F6-4FCD-A91A-5A9FA8C51A98</a:ObjectID>
<a:Name>购买金额</a:Name>
<a:Code>FPURCHASEAMOUNT</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1459732004</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>F0F56D6C-EAD0-4434-9BA8-E69961979D86</a:ObjectID>
<a:Name>购买数量(用于匹配累计促销)</a:Name>
<a:Code>FCOUNTPURCHASEQTY</a:Code>
<a:CreationDate>1459731934</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1459732004</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>4AE96ED9-56F5-42E4-AEAA-76FB5FBBE691</a:ObjectID>
<a:Name>购买基本数量(用于匹配累计促销)</a:Name>
<a:Code>FCOUNTPURCHASEBASEQTY</a:Code>
<a:CreationDate>1459731934</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1459732004</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>19D6291E-5BCE-48FE-A37F-0271E5AC8C42</a:ObjectID>
<a:Name>购买金额(用于匹配累计促销)</a:Name>
<a:Code>FCOUNTPURCHASEAMOUNT</a:Code>
<a:CreationDate>1459731934</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1459732004</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>2B370220-FDCC-4EE4-95E8-9B5F53A0CEB3</a:ObjectID>
<a:Name>单据ＩＤ</a:Name>
<a:Code>FBILLID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>F1ECA332-3C16-4285-BA5C-35CDE1568EC3</a:ObjectID>
<a:Name>单据分录ＩＤ</a:Name>
<a:Code>FBILLENTRYID</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>AA0F6A0A-32F5-47B7-AFB7-087CCEC92189</a:ObjectID>
<a:Name>是否整单折扣</a:Name>
<a:Code>FISBILLDISCOUNT</a:Code>
<a:CreationDate>1384304879</a:CreationDate>
<a:Creator>shengdong_chen</a:Creator>
<a:ModificationDate>1384306487</a:ModificationDate>
<a:Modifier>shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>83BB188D-7EBD-4A26-80AA-7D317D52C5F2</a:ObjectID>
<a:Name>表单ID</a:Name>
<a:Code>FFORMID</a:Code>
<a:CreationDate>1456737393</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1456737440</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o25">
<a:ObjectID>52FA208C-4A79-496B-AD45-0CDCDF5277EE</a:ObjectID>
<a:Name>TM_RPM_COUNTDATA(返利计算之返利计算临时表)</a:Name>
<a:Code>TM_RPM_COUNTDATA</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:Comment>返利计算之销售数据统计结果临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o103">
<a:ObjectID>16413A29-5CD4-4E59-88DC-40011AF60396</a:ObjectID>
<a:Name>结算组织</a:Name>
<a:Code>FSETTLEORGID</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o104">
<a:ObjectID>D43646BF-166D-429D-A7EC-1313C0523DEA</a:ObjectID>
<a:Name>返利政策</a:Name>
<a:Code>FRPPOLICYID</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>C3C8712D-FB69-467F-AEAC-32BA686801B5</a:ObjectID>
<a:Name>组号</a:Name>
<a:Code>FGROUPNUMBER</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>1C641A8A-AC66-48F4-B108-10799586D6B2</a:ObjectID>
<a:Name>返利周期</a:Name>
<a:Code>FRPCYCLEID</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>F037959E-3FF5-4C13-A05F-212412C47BBF</a:ObjectID>
<a:Name>返利对象类型</a:Name>
<a:Code>FCUSTTYPEID</a:Code>
<a:CreationDate>1397440375</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397468132</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>9077EBD2-C575-4B48-88A2-DA0A68D9006A</a:ObjectID>
<a:Name>返利对象</a:Name>
<a:Code>FCUSTOMERID</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>55C7B6F0-612A-476C-9F7F-3E2DECAE65D9</a:ObjectID>
<a:Name>购买基本数量</a:Name>
<a:Code>FBASEPURCHASEQTY</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397791498</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>B6A73C8E-8B1A-44A1-A6C3-C55A83F92FC7</a:ObjectID>
<a:Name>购买金额</a:Name>
<a:Code>FPURCHASEAMOUNT</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>6E451A65-741C-40D8-8AB8-F36BBC8BB9D3</a:ObjectID>
<a:Name>指标基本数量</a:Name>
<a:Code>FBASETARGETQTY</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>4C2AF82D-0CC1-4E81-B63A-BE8C53512A49</a:ObjectID>
<a:Name>指标金额</a:Name>
<a:Code>FTARGETAMOUNT</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>C7875659-34B1-49F5-9B1D-CF6C809DD6E9</a:ObjectID>
<a:Name>指标完成率</a:Name>
<a:Code>FTARGETRATE</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(19,6)</a:DataType>
<a:Length>19</a:Length>
<a:Precision>6</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>0AECE690-520A-4242-9BF7-C6126DEAB5E9</a:ObjectID>
<a:Name>实际销售基本数量</a:Name>
<a:Code>FBASEACTUALQTY</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>F3E84398-1527-4416-A5F5-395C7EB2EBC0</a:ObjectID>
<a:Name>实际销售金额</a:Name>
<a:Code>FACTUALAMOUNT</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>21C4CF26-A978-47C4-9F9F-D4F94DB597DF</a:ObjectID>
<a:Name>物料</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>DF677ECB-5301-4EC1-92DB-0F4A09E21340</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>6C3AC084-E24B-485D-B7DD-874351AA3D3D</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FASSISTPROPERTYID</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>014C6052-57BB-4D24-B18A-46DA70E71EC9</a:ObjectID>
<a:Name>是否赠品</a:Name>
<a:Code>FISPRESENT</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>DFA73099-58D4-4219-9A8A-65A660585B85</a:ObjectID>
<a:Name>物料分类</a:Name>
<a:Code>FMATERIALGROUPID</a:Code>
<a:CreationDate>1397292046</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>0EF964AB-66DD-4702-8EF9-3BBBCC6DD445</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1397293500</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>FDCB7BEA-433D-4388-82B2-FBCF64BE00F4</a:ObjectID>
<a:Name>累进组标识</a:Name>
<a:Code>FGROUPFLAG</a:Code>
<a:CreationDate>1397524462</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397547130</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>nvarchar(150)</a:DataType>
<a:Length>150</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>8226889D-7C24-4021-B605-55D5E09C0AFE</a:ObjectID>
<a:Name>计量单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1397716675</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>8868BE42-6240-4CBD-8ECF-7708E7FBD1AB</a:ObjectID>
<a:Name>参与返利基本数量</a:Name>
<a:Code>FCALCRPBASEQTY</a:Code>
<a:CreationDate>1438310245</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1438310627</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>C81505E6-3434-4090-8800-68A3F7AA81FA</a:ObjectID>
<a:Name>参与返利销售金额</a:Name>
<a:Code>FCALCRPAMOUNT</a:Code>
<a:CreationDate>1438310245</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1438310627</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o19">
<a:ObjectID>BDC2052C-0A61-41C2-940B-729BCFA2F02B</a:ObjectID>
<a:Name>TM_RPM_SALEDATA(返利计算之销售数据统计结果临时表)</a:Name>
<a:Code>TM_RPM_SALEDATA</a:Code>
<a:CreationDate>1396924323</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1568083807</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:Comment>返利计算之销售数据统计结果临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o126">
<a:ObjectID>B9450F31-8CE5-4728-89AF-5069D4448A11</a:ObjectID>
<a:Name>结算组织</a:Name>
<a:Code>FSETTLEORGID</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>C6B60C84-0976-4BD8-83D7-D5D6B5446D0E</a:ObjectID>
<a:Name>返利政策</a:Name>
<a:Code>FRPPOLICYID</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>8DF56773-DA01-4FC3-9F2E-47C807B0CB3C</a:ObjectID>
<a:Name>组号</a:Name>
<a:Code>FGROUPNUMBER</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o129">
<a:ObjectID>EAEAA23F-F5CD-498F-99F0-4C5A9CFF93BB</a:ObjectID>
<a:Name>返利周期</a:Name>
<a:Code>FRPCYCLEID</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o130">
<a:ObjectID>A7A0B8E7-3E15-4303-9BBA-F3547EA2C048</a:ObjectID>
<a:Name>返利对象类型</a:Name>
<a:Code>FCUSTTYPEID</a:Code>
<a:CreationDate>1397442256</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397468132</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o131">
<a:ObjectID>4AB74F58-C9CF-4D42-81B2-06A56D180A45</a:ObjectID>
<a:Name>返利对象</a:Name>
<a:Code>FCUSTOMERID</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o132">
<a:ObjectID>B592EC10-04A3-4F58-8B54-41A89C55841C</a:ObjectID>
<a:Name>购买基本数量</a:Name>
<a:Code>FBASEPURCHASEQTY</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397791484</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o133">
<a:ObjectID>D0182B47-BF57-4B79-915A-CBB389CADD64</a:ObjectID>
<a:Name>购买金额</a:Name>
<a:Code>FPURCHASEAMOUNT</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>681C51B5-B808-4E71-9543-EFE7C761B8FF</a:ObjectID>
<a:Name>指标基本数量</a:Name>
<a:Code>FBASETARGETQTY</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>38D368C0-39A7-49D9-978E-C3F771FCFEC6</a:ObjectID>
<a:Name>指标金额</a:Name>
<a:Code>FTARGETAMOUNT</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>77E962FD-4903-4179-AB81-BD3CF0078DED</a:ObjectID>
<a:Name>指标完成率</a:Name>
<a:Code>FTARGETRATE</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(19,6)</a:DataType>
<a:Length>19</a:Length>
<a:Precision>6</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o137">
<a:ObjectID>4D3B95D5-05E1-4732-B9CF-BE8F3F4AA5EC</a:ObjectID>
<a:Name>实际销售基本数量</a:Name>
<a:Code>FBASEACTUALQTY</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>2946D0E5-2BA6-405B-8977-C34192CED946</a:ObjectID>
<a:Name>实际销售金额</a:Name>
<a:Code>FACTUALAMOUNT</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o139">
<a:ObjectID>5FBD721B-852A-4C9E-A929-8CF0F7282E4C</a:ObjectID>
<a:Name>物料ID</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397524951</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o140">
<a:ObjectID>4CE1B549-91E4-44C8-8306-DDDB773352C8</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o141">
<a:ObjectID>35D11120-C4E0-4E67-A765-9BB2ECD4A409</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FASSISTPROPERTYID</a:Code>
<a:CreationDate>1396924363</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1396949937</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o142">
<a:ObjectID>F95E3A37-4706-4F8D-ADC8-195E162D30A6</a:ObjectID>
<a:Name>是否赠品</a:Name>
<a:Code>FISPRESENT</a:Code>
<a:CreationDate>1397269002</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397291915</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>BA7AAF49-DDD9-4336-AA1E-3FA47B9010A5</a:ObjectID>
<a:Name>物料分类</a:Name>
<a:Code>FMATERIALGROUPID</a:Code>
<a:CreationDate>1397269002</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397291915</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>937DEF48-7C22-4007-A335-624F7E2B8A7B</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1397293388</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397295017</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>4C0C10F6-F069-4210-9306-708F8207F526</a:ObjectID>
<a:Name>计量单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1397716584</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1397728434</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>7CE1B43D-13FF-4A68-852C-09A590E448A0</a:ObjectID>
<a:Name>参与返利基本数量</a:Name>
<a:Code>FCALCRPBASEQTY</a:Code>
<a:CreationDate>1438307010</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1438310282</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>65A560A7-86F2-41F9-A4A1-3A62BEA89187</a:ObjectID>
<a:Name>参与返利销售金额</a:Name>
<a:Code>FCALCRPAMOUNT</a:Code>
<a:CreationDate>1438307010</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1438308085</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>BF4D6892-9E79-4EF6-9563-10AD139F332F</a:ObjectID>
<a:Name>多组号执行策略</a:Name>
<a:Code>FJUDGEMENTMODEL</a:Code>
<a:CreationDate>1438311038</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1438755938</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;2&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o149">
<a:ObjectID>C1689DC1-29EA-4CCA-A6A5-5839D1F27568</a:ObjectID>
<a:Name>返利政策审核日期</a:Name>
<a:Code>FRPPOLICYDATE</a:Code>
<a:CreationDate>1438312472</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1438755938</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o150">
<a:ObjectID>AD981423-1B07-4443-9ED7-6641B6523668</a:ObjectID>
<a:Name>返利政策分录</a:Name>
<a:Code>FRPPOLICYENTRYID</a:Code>
<a:CreationDate>1438829641</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1438829735</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>52314976-962E-4FC0-98E4-186FCA221D64</a:ObjectID>
<a:Name>主键</a:Name>
<a:Code>FRECORDID</a:Code>
<a:CreationDate>1568083680</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1568083807</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o29">
<a:ObjectID>B66E9F3C-C4FD-4309-B08E-A9885F8E307C</a:ObjectID>
<a:Name>TM_SPM_GROUPTEMPTABLE(累计促销统计用的分组临时表)</a:Name>
<a:Code>TM_SPM_GROUPTEMPTABLE</a:Code>
<a:CreationDate>1456737453</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1456737569</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o152">
<a:ObjectID>112907B9-C041-46D6-9D06-5AF2CE2B4E0C</a:ObjectID>
<a:Name>分组ID</a:Name>
<a:Code>FGroupID</a:Code>
<a:CreationDate>1456737490</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1456737569</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>5370B73E-3D32-4F0A-9A27-A0132AFFC679</a:ObjectID>
<a:Name>下级分组ID</a:Name>
<a:Code>FChildGroupID</a:Code>
<a:CreationDate>1456737490</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1456737569</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o31">
<a:ObjectID>75B5AD27-BE5B-4197-B524-32C8B81E1887</a:ObjectID>
<a:Name>TM_SPM_PARAMTEMPTABLE(累计促销使用的参数临时表)</a:Name>
<a:Code>TM_SPM_PARAMTEMPTABLE</a:Code>
<a:CreationDate>1456737455</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1456737674</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o154">
<a:ObjectID>2B20BD58-8082-4A5C-914F-AC888E2EC4A6</a:ObjectID>
<a:Name>销售组织ID</a:Name>
<a:Code>FSaleOrgID</a:Code>
<a:CreationDate>1456737599</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1456737674</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>59492D94-FBE4-4290-AF37-792FF502F512</a:ObjectID>
<a:Name>取数来源</a:Name>
<a:Code>FSumDataSource</a:Code>
<a:CreationDate>1456737599</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1456737674</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>596F46E5-39EA-4AA0-AFC7-99F76F1F83D1</a:ObjectID>
<a:Name>取值设置</a:Name>
<a:Code>FSumDataValue</a:Code>
<a:CreationDate>1456737599</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1456737674</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o33">
<a:ObjectID>52B14D78-6074-4B31-9C12-9A7D3BE86017</a:ObjectID>
<a:Name>TM_SPM_PROMOTIONCOUNT(累计促销统计表)</a:Name>
<a:Code>TM_SPM_PROMOTIONCOUNT</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o157">
<a:ObjectID>D88F97BB-D873-476D-A874-BE2B93B08D75</a:ObjectID>
<a:Name>销售组织</a:Name>
<a:Code>FSALEORGID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>FD0D3434-41CE-463F-A0EA-5684453FC74C</a:ObjectID>
<a:Name>币别</a:Name>
<a:Code>FCURRENCYID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>1BDD356D-AE54-4E8C-9D6C-DA527F596AEF</a:ObjectID>
<a:Name>客户分类</a:Name>
<a:Code>FCUSTOMERTYPEID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(36)</a:DataType>
<a:Length>36</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>2830F202-7467-4370-AD71-5DFC1FCEF4EC</a:ObjectID>
<a:Name>客户ID</a:Name>
<a:Code>FCUSTOMERID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>C83FC4E0-D38C-45FE-8A2D-B0524B9C7183</a:ObjectID>
<a:Name>客户分配内码</a:Name>
<a:Code>FCUSTMASTERID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>6325FACF-CD69-431F-AB4F-ABD1C24C7EF1</a:ObjectID>
<a:Name>促销政策ID</a:Name>
<a:Code>FSPPOLICYID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>487039A2-F85A-4B5F-A16A-F59558582B1F</a:ObjectID>
<a:Name>促销开始时间</a:Name>
<a:Code>FSTARTTIME</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>AE5CFB69-8EC1-431B-A0E7-F26E3281CAB0</a:ObjectID>
<a:Name>促销结束时间</a:Name>
<a:Code>FENDTIME</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o165">
<a:ObjectID>53B386C4-2C6D-4844-B839-FF2FB338C000</a:ObjectID>
<a:Name>初始累计开始时间</a:Name>
<a:Code>FCOUNTSTARTTIME</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o166">
<a:ObjectID>B7DE5409-829A-44A8-9375-28D2609A6979</a:ObjectID>
<a:Name>初始累计结束时间</a:Name>
<a:Code>FCOUNTENDTIME</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o167">
<a:ObjectID>317908FA-5788-4770-8BF6-8265E8BE7100</a:ObjectID>
<a:Name>是否补赠</a:Name>
<a:Code>FISGIVEUP</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458222204</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>FB8E1810-C2BA-445F-BC2B-0103CAC057A2</a:ObjectID>
<a:Name>促销政策参与组织分录ID</a:Name>
<a:Code>FSALEORGENTRYID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>D231DBB9-229A-453A-9DE3-29BAF517ADA5</a:ObjectID>
<a:Name>促销政策参与客户分录ID</a:Name>
<a:Code>FCUSTOMERENTRYID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>5E8B6257-AA43-4783-AD2F-DD1DCDB435CA</a:ObjectID>
<a:Name>促销政策分录ID</a:Name>
<a:Code>FPOLICYENTRYID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o171">
<a:ObjectID>4ECE563C-B7CC-43D2-BF87-DED1181C11EB</a:ObjectID>
<a:Name>组号</a:Name>
<a:Code>FGROUPNUMBER</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o172">
<a:ObjectID>4346CD43-AC8F-47F9-8935-D799D1D63194</a:ObjectID>
<a:Name>物料分组</a:Name>
<a:Code>FMATERIALGROUPID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o173">
<a:ObjectID>22BBA05B-15DB-49CA-B9B3-1A932CF9747F</a:ObjectID>
<a:Name>物料ID</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o174">
<a:ObjectID>AFCA4A8A-2762-441A-8561-FF8E1D4CF3E2</a:ObjectID>
<a:Name>物料分配内码</a:Name>
<a:Code>FMATMASTERID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o175">
<a:ObjectID>626848DC-03AA-43D0-A406-FDAC653280D3</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o176">
<a:ObjectID>23671FCC-7CEA-48A0-AD3C-C229580A2D48</a:ObjectID>
<a:Name>计量单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>B6B26915-5B60-4CA4-AC88-E54B51205C73</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FAUXPROPID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>EF967B0D-51CC-4063-AA60-D002A6CB6F01</a:ObjectID>
<a:Name>初始累计基本数量</a:Name>
<a:Code>FBASEINITQTY</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>2D9387D3-8BEE-4DCF-82FE-E6996DE554C0</a:ObjectID>
<a:Name>初始累计数量</a:Name>
<a:Code>FINITQTY</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>51DA7F70-9BB4-4FDE-9F76-5212EEA51D52</a:ObjectID>
<a:Name>初始累计金额</a:Name>
<a:Code>FINITAMOUNT</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>CC6666D1-2A61-4D7D-BA22-49E4F62F100A</a:ObjectID>
<a:Name>促销累计基本数量</a:Name>
<a:Code>FBASESPMCOUNTQTY</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>9EB1A4A5-01F3-450C-B6F9-F7AA8235CEFB</a:ObjectID>
<a:Name>促销累计数量</a:Name>
<a:Code>FSPMCOUNTQTY</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>DA3E1C97-6751-4C6B-B82E-CEB4068D20C9</a:ObjectID>
<a:Name>促销累计金额</a:Name>
<a:Code>FSPMCOUNTAMOUNT</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>BD01B20B-79E4-473B-8AFB-1581B1BFD8BF</a:ObjectID>
<a:Name>赠品累计赠送基本数量</a:Name>
<a:Code>FBASEPRESENTCOUNTQTY</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>DBF3CDD9-C76C-4E01-A2F9-8D4383CE9CE7</a:ObjectID>
<a:Name>赠品累计赠送数量</a:Name>
<a:Code>FPRESENTCOUNTQTY</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>A2CD23A5-C907-46CF-9E37-7FDA52873654</a:ObjectID>
<a:Name>赠品</a:Name>
<a:Code>FPRESENTCOUNT</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:Comment>1：赠品
2：换赠</a:Comment>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>601534F7-BDA0-4464-94F6-E9F81EFC8546</a:ObjectID>
<a:Name>促销累计总数量</a:Name>
<a:Code>FQTY</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o188">
<a:ObjectID>474A5B39-EADF-4D45-96E1-7ABB4D43E103</a:ObjectID>
<a:Name>促销累计基本总数量</a:Name>
<a:Code>FBASEQTY</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o189">
<a:ObjectID>2B88188B-8402-42C9-A08E-292611F41027</a:ObjectID>
<a:Name>促销累计总金额</a:Name>
<a:Code>FAMOUNT</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o190">
<a:ObjectID>FA2B9CDF-414C-4DF7-8B91-8AE006D62268</a:ObjectID>
<a:Name>统计表ID</a:Name>
<a:Code>FPROMOTIONCOUNTID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o191">
<a:ObjectID>8E4785D4-6CA5-4C87-9E4C-639A023D8C91</a:ObjectID>
<a:Name>统计分录表ID</a:Name>
<a:Code>FPROMOTIONCOUNTENTRYID</a:Code>
<a:CreationDate>1458197938</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1458198146</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o192">
<a:ObjectID>9D37310C-3E73-43E4-B4D4-08933924E317</a:ObjectID>
<a:Name>TM_SPM_MATCHPRESENTCONDITION(促销匹配条件临时表，用于保存赠品记录)</a:Name>
<a:Code>TM_SPM_MATCHPRESENTCONDITION</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o193">
<a:ObjectID>3788E971-42F0-454B-8FA6-265DB46DE8A8</a:ObjectID>
<a:Name>物料</a:Name>
<a:Code>FMATERIALID</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>E69346F6-2D11-4B2F-A235-69590DEA4336</a:ObjectID>
<a:Name>物料内码ＩＤ</a:Name>
<a:Code>FMATMASTERID</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>740A5C47-145C-42A9-ADD1-96DC114E0E2C</a:ObjectID>
<a:Name>计量单位</a:Name>
<a:Code>FUNITID</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>3A8C26A4-05C3-4EFF-AB61-A8B65A5C6569</a:ObjectID>
<a:Name>基本计量单位</a:Name>
<a:Code>FBASEUNITID</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>EBB20198-0E92-4FA0-81EB-154480E685E8</a:ObjectID>
<a:Name>辅助属性</a:Name>
<a:Code>FATTRVALUEID</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>BBDCFD4F-94C4-4E4B-A657-C8ECF348CFFC</a:ObjectID>
<a:Name>购买数量</a:Name>
<a:Code>FPURCHASEQTY</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>5841D5F5-2611-4FFF-B9F3-4591FC93C6E2</a:ObjectID>
<a:Name>购买基本数量</a:Name>
<a:Code>FPURCHASEBASEQTY</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(28,10)</a:DataType>
<a:Length>28</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>2C0829AC-81C0-4A57-AD22-831CC8012DFD</a:ObjectID>
<a:Name>单据ＩＤ</a:Name>
<a:Code>FBILLID</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o201">
<a:ObjectID>7FC6E17D-1337-470A-B103-C18929753631</a:ObjectID>
<a:Name>单据分录ＩＤ</a:Name>
<a:Code>FBILLENTRYID</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o202">
<a:ObjectID>C512E6FA-17E9-41FF-A71D-C688DFCE7F35</a:ObjectID>
<a:Name>表单ID</a:Name>
<a:Code>FFORMID</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(30)</a:DataType>
<a:Length>30</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o203">
<a:ObjectID>E498AF57-1F16-4808-9DA8-84D3B43A4E6C</a:ObjectID>
<a:Name>是否重新匹配</a:Name>
<a:Code>FISREMATCH</a:Code>
<a:CreationDate>1463972306</a:CreationDate>
<a:Creator>RD_shengdong_chen</a:Creator>
<a:ModificationDate>1463972474</a:ModificationDate>
<a:Modifier>RD_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o35">
<a:ObjectID>7911C63B-DFE5-4E44-943B-AD80813ECA79</a:ObjectID>
<a:Name>TM_SPM_INTERMEDIATERESULT(促销匹配中间结果临时表)</a:Name>
<a:Code>TM_SPM_INTERMEDIATERESULT</a:Code>
<a:CreationDate>1550728369</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o204">
<a:ObjectID>DA480354-391B-4FA2-B022-6E48804FAE04</a:ObjectID>
<a:Name>单据ID</a:Name>
<a:Code>FBILLID</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o205">
<a:ObjectID>015728ED-9A92-4B53-9BED-0E0D2E075392</a:ObjectID>
<a:Name>促销政策ID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o206">
<a:ObjectID>4AD378B6-48A6-4C69-B08F-4CD128F23671</a:ObjectID>
<a:Name>促销政策组号</a:Name>
<a:Code>FGROUPNUMBER</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o207">
<a:ObjectID>8953B2FD-F856-421B-B31B-3524492AF11F</a:ObjectID>
<a:Name>组合方式</a:Name>
<a:Code>FCOMPOUNDMODE</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o208">
<a:ObjectID>B164BF4A-F12F-4350-AB4D-B740CE893D80</a:ObjectID>
<a:Name>是否基本单位</a:Name>
<a:Code>FISBASEUNIT</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o209">
<a:ObjectID>C5451384-FEE8-4E95-B69A-8FD1B540803E</a:ObjectID>
<a:Name>是否可用</a:Name>
<a:Code>FISENABLE</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o210">
<a:ObjectID>A3BA2CC8-1445-42DA-9EA2-56C7330C7C92</a:ObjectID>
<a:Name>公除数是否相同</a:Name>
<a:Code>FISSAMEMOD</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o211">
<a:ObjectID>7E06833F-A635-4942-A984-5CB514DCAD66</a:ObjectID>
<a:Name>数量公除数</a:Name>
<a:Code>FQTYDIVISOR</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o212">
<a:ObjectID>C55A404A-203F-4521-AB28-2F77D822E7C2</a:ObjectID>
<a:Name>金额公除数</a:Name>
<a:Code>FAMTDIVISOR</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o213">
<a:ObjectID>59F3F34E-9296-4D3B-8689-6BB26F204089</a:ObjectID>
<a:Name>数量公倍数</a:Name>
<a:Code>FMODEQTY</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o214">
<a:ObjectID>FE106E45-5A4C-4DFD-884E-4511A646620D</a:ObjectID>
<a:Name>金额公倍数</a:Name>
<a:Code>FMODEAMOUNT</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o215">
<a:ObjectID>CB991325-1DED-4703-B547-3B4B5AC35BE4</a:ObjectID>
<a:Name>订单数量</a:Name>
<a:Code>FBILLQTY</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o216">
<a:ObjectID>BDCF5960-1045-471A-BA79-295D47993775</a:ObjectID>
<a:Name>订单基本数量</a:Name>
<a:Code>FBILLBASEQTY</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o217">
<a:ObjectID>DC91DD82-5557-4A14-8171-FF46C2D067F2</a:ObjectID>
<a:Name>订单金额</a:Name>
<a:Code>FBILLAMOUNT</a:Code>
<a:CreationDate>1550728373</a:CreationDate>
<a:Creator>rd_shengdong_chen</a:Creator>
<a:ModificationDate>1550728777</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
</c:Tables>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:DefaultGroups>
<o:Group Id="o218">
<a:ObjectID>197E52B9-2DBD-41AA-BE30-633AF5610AC9</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1382685821</a:CreationDate>
<a:Creator>RD_tipi</a:Creator>
<a:ModificationDate>1382685821</a:ModificationDate>
<a:Modifier>RD_tipi</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o219">
<a:ObjectID>EFB7CCB5-D288-430D-BE12-4938CD87F139</a:ObjectID>
<a:Name>Sybase SQL Anywhere 11</a:Name>
<a:Code>SYASA11</a:Code>
<a:CreationDate>1382685821</a:CreationDate>
<a:Creator>RD_tipi</a:Creator>
<a:ModificationDate>1550805781</a:ModificationDate>
<a:Modifier>rd_shengdong_chen</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/syasa110.xdb</a:TargetModelURL>
<a:TargetModelID>9C2029CC-5AF2-4828-AA6C-11C184EF93D8</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>