#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终的薪资计算部分，找出具体问题
"""

import pyodbc
import pandas as pd

# 数据库连接配置
DB_CONFIG = {
    'server': '192.168.1.250',
    'database': 'HLDB',
    'username': 'hldbuser',
    'password': 'Hldbuser@241031',
    'driver': 'ODBC Driver 17 for SQL Server'
}

def connect_database():
    """连接数据库"""
    try:
        connection_string = (
            f"DRIVER={{{DB_CONFIG['driver']}}};"
            f"SERVER={DB_CONFIG['server']};"
            f"DATABASE={DB_CONFIG['database']};"
            f"UID={DB_CONFIG['username']};"
            f"PWD={DB_CONFIG['password']};"
            "TrustServerCertificate=yes;"
        )
        
        connection = pyodbc.connect(connection_string)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def test_calculation_parts():
    """测试薪资计算的各个部分"""
    connection = connect_database()
    if not connection:
        return
    
    print("🔍 开始测试薪资计算的各个部分...")
    
    # 测试1：先测试不包含薪资计算的完整查询
    print("\n" + "="*60)
    print("测试1：完整查询但不计算薪资")
    print("="*60)
    
    try:
        query1 = """
        SELECT TOP 5
            年月, 厂区, 部门, 工序, 工号, 姓名, 类型, 单价, 班组, 序号,
            产能类型, 产能总和, 绩效分数, status, 绩效占比
        FROM (
            SELECT 
                a.年月, a.厂区, a.部门, a.工序, 
                REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') as 工号, 
                a.姓名, d.类型, t.班组, t.序号, a.status,
                CASE 
                    WHEN ISNULL(d.类型, '') IN ('B', '') THEN '计件产能汇总' 
                    WHEN d.类型 = 'A' THEN '绩效产能汇总' 
                    ELSE '其他' 
                END AS 产能类型,
                -- 保留原来的产能总和计算（包含"休"等字符，但转换为0）
                (CASE WHEN ISNULL(a.[1],'') <> '' AND ISNUMERIC(a.[1]) = 1 AND LEN(a.[1]) <= 10 AND a.[1] NOT LIKE '%[^0-9.-]%' THEN CAST(a.[1] AS FLOAT) ELSE 0 END) +
                (CASE WHEN ISNULL(a.[2],'') <> '' AND ISNUMERIC(a.[2]) = 1 AND LEN(a.[2]) <= 10 AND a.[2] NOT LIKE '%[^0-9.-]%' THEN CAST(a.[2] AS FLOAT) ELSE 0 END) +
                (CASE WHEN ISNULL(a.[3],'') <> '' AND ISNUMERIC(a.[3]) = 1 AND LEN(a.[3]) <= 10 AND a.[3] NOT LIKE '%[^0-9.-]%' THEN CAST(a.[3] AS FLOAT) ELSE 0 END) AS 产能总和,
                d.单价, p.绩效分数, p.绩效占比
            FROM 测试 a 
            LEFT JOIN [dbo].[单价] d ON a.厂区 = d.厂区 AND a.工序 = d.工序 
            LEFT JOIN [dbo].[绩效表] p ON REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') = REPLACE(REPLACE(p.工号, CHAR(13), ''), CHAR(10), '') 
                AND a.年月 = p.年月 AND ISNULL(d.类型, '') IN ('B', '') 
            LEFT JOIN [dbo].[人员信息表] t ON REPLACE(REPLACE(REPLACE(t.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') = REPLACE(REPLACE(REPLACE(a.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') 
                AND t.[工厂] = a.[厂区] and t.[部门] = a.[部门]
            GROUP BY 
                a.年月, a.厂区, a.部门, a.工序, a.status,
                REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), ''), 
                a.姓名, d.类型, d.单价, p.绩效分数, p.绩效占比, t.班组, t.序号, 
                a.[1], a.[2], a.[3]
        ) AS subquery
        """
        df1 = pd.read_sql(query1, connection)
        print("✅ 完整查询（不含薪资计算）成功")
        print(df1.head())
        
        # 检查数据类型
        print("\n数据类型检查:")
        for col in ['产能总和', '单价', '绩效分数', '绩效占比']:
            if col in df1.columns:
                print(f"{col}: {df1[col].dtype}, 示例值: {df1[col].iloc[0] if len(df1) > 0 else 'N/A'}")
        
    except Exception as e:
        print(f"❌ 完整查询失败: {e}")
        return
    
    # 测试2：测试简单的薪资计算
    print("\n" + "="*60)
    print("测试2：简单薪资计算")
    print("="*60)
    
    try:
        query2 = """
        SELECT TOP 5
            年月, 厂区, 部门, 工序, 工号, 姓名,
            产能总和, 单价, 绩效分数, 绩效占比,
            -- 简单的薪资计算
            CASE 
                WHEN 产能类型 = '绩效产能汇总' or 绩效分数 is null 
                THEN 产能总和 * 单价
                ELSE 0
            END AS 简单薪资
        FROM (
            SELECT 
                a.年月, a.厂区, a.部门, a.工序, 
                REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') as 工号, 
                a.姓名,
                CASE 
                    WHEN ISNULL(d.类型, '') IN ('B', '') THEN '计件产能汇总' 
                    WHEN d.类型 = 'A' THEN '绩效产能汇总' 
                    ELSE '其他' 
                END AS 产能类型,
                (CASE WHEN ISNULL(a.[1],'') <> '' AND ISNUMERIC(a.[1]) = 1 AND LEN(a.[1]) <= 10 AND a.[1] NOT LIKE '%[^0-9.-]%' THEN CAST(a.[1] AS FLOAT) ELSE 0 END) AS 产能总和,
                d.单价, p.绩效分数, p.绩效占比
            FROM 测试 a 
            LEFT JOIN [dbo].[单价] d ON a.厂区 = d.厂区 AND a.工序 = d.工序 
            LEFT JOIN [dbo].[绩效表] p ON REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') = REPLACE(REPLACE(p.工号, CHAR(13), ''), CHAR(10), '') 
                AND a.年月 = p.年月 AND ISNULL(d.类型, '') IN ('B', '') 
        ) AS subquery
        """
        df2 = pd.read_sql(query2, connection)
        print("✅ 简单薪资计算成功")
        print(df2.head())
        
    except Exception as e:
        print(f"❌ 简单薪资计算失败: {e}")
        print("问题可能在薪资计算的数学运算中")
    
    # 测试3：测试复杂薪资计算的各个部分
    print("\n" + "="*60)
    print("测试3：分步测试复杂薪资计算")
    print("="*60)
    
    try:
        query3 = """
        SELECT TOP 5
            年月, 厂区, 工序, 工号, 姓名,
            产能总和, 单价, 绩效分数, 绩效占比, 产能类型,
            -- 分步计算
            CASE WHEN 厂区 = '泗阳华茂' and 工序 = '出炉' and 产能总和 > 114 and 绩效分数 is not null THEN 1 ELSE 0 END AS 条件1,
            CASE WHEN 产能类型 = '计件产能汇总' AND ISNULL(绩效分数, 0) <> 0 THEN 1 ELSE 0 END AS 条件2,
            CASE WHEN 产能类型 = '绩效产能汇总' or 绩效分数 is null THEN 1 ELSE 0 END AS 条件3
        FROM (
            SELECT 
                a.年月, a.厂区, a.部门, a.工序, 
                REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') as 工号, 
                a.姓名,
                CASE 
                    WHEN ISNULL(d.类型, '') IN ('B', '') THEN '计件产能汇总' 
                    WHEN d.类型 = 'A' THEN '绩效产能汇总' 
                    ELSE '其他' 
                END AS 产能类型,
                (CASE WHEN ISNULL(a.[1],'') <> '' AND ISNUMERIC(a.[1]) = 1 AND LEN(a.[1]) <= 10 AND a.[1] NOT LIKE '%[^0-9.-]%' THEN CAST(a.[1] AS FLOAT) ELSE 0 END) AS 产能总和,
                d.单价, p.绩效分数, p.绩效占比
            FROM 测试 a 
            LEFT JOIN [dbo].[单价] d ON a.厂区 = d.厂区 AND a.工序 = d.工序 
            LEFT JOIN [dbo].[绩效表] p ON REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') = REPLACE(REPLACE(p.工号, CHAR(13), ''), CHAR(10), '') 
                AND a.年月 = p.年月 AND ISNULL(d.类型, '') IN ('B', '') 
        ) AS subquery
        """
        df3 = pd.read_sql(query3, connection)
        print("✅ 分步薪资计算成功")
        print(df3.head())
        
    except Exception as e:
        print(f"❌ 分步薪资计算失败: {e}")
    
    connection.close()
    print("\n🔒 数据库连接已关闭")

def generate_safe_salary_calculation():
    """生成安全的薪资计算SQL"""
    print("\n" + "="*80)
    print("🔧 生成安全的薪资计算SQL")
    print("="*80)
    
    safe_salary_sql = """
-- 安全的薪资计算（兼容老版本SQL Server）
CASE 
    WHEN 厂区 = '泗阳华茂' and 工序 = '出炉' and 产能总和 > 114 and 绩效分数 is not null 
    THEN 1800 + (产能总和-114)*20 + 产能总和*7.7*ISNULL(CASE WHEN ISNUMERIC(CAST(绩效分数 AS NVARCHAR(50))) = 1 THEN CAST(绩效分数 AS FLOAT) ELSE 0 END, 0)
    
    WHEN 产能类型 = '计件产能汇总' AND ISNULL(CASE WHEN ISNUMERIC(CAST(绩效分数 AS NVARCHAR(50))) = 1 THEN CAST(绩效分数 AS FLOAT) ELSE 0 END, 0) <> 0 
    THEN (产能总和 * ISNULL(CASE WHEN ISNUMERIC(CAST(单价 AS NVARCHAR(50))) = 1 THEN CAST(单价 AS FLOAT) ELSE 0 END, 0)) * 
         ISNULL(CASE WHEN ISNUMERIC(CAST(绩效占比 AS NVARCHAR(50))) = 1 THEN CAST(绩效占比 AS FLOAT) ELSE 0 END, 0) * 
         ISNULL(CASE WHEN ISNUMERIC(CAST(绩效分数 AS NVARCHAR(50))) = 1 THEN CAST(绩效分数 AS FLOAT) ELSE 0 END, 0) + 
         ((产能总和 * ISNULL(CASE WHEN ISNUMERIC(CAST(单价 AS NVARCHAR(50))) = 1 THEN CAST(单价 AS FLOAT) ELSE 0 END, 0)) - 
          (产能总和 * ISNULL(CASE WHEN ISNUMERIC(CAST(单价 AS NVARCHAR(50))) = 1 THEN CAST(单价 AS FLOAT) ELSE 0 END, 0)) * 
          ISNULL(CASE WHEN ISNUMERIC(CAST(绩效占比 AS NVARCHAR(50))) = 1 THEN CAST(绩效占比 AS FLOAT) ELSE 0 END, 0))
    
    WHEN 产能类型 = '绩效产能汇总' or 绩效分数 is null 
    THEN 产能总和 * ISNULL(CASE WHEN ISNUMERIC(CAST(单价 AS NVARCHAR(50))) = 1 THEN CAST(单价 AS FLOAT) ELSE 0 END, 0)
    
    ELSE 0
END AS 薪资
    """
    
    print("复制以下安全的薪资计算SQL:")
    print("-" * 50)
    print(safe_salary_sql)

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 薪资计算问题调试工具")
    print("=" * 80)
    
    test_calculation_parts()
    generate_safe_salary_calculation()

if __name__ == "__main__":
    main()
