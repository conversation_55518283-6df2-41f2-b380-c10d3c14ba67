-- 测试制单人工号查询
-- 用于验证不同方案获取制单人工号的效果

-- 方案测试：查看制单人相关信息
SELECT TOP 10
    h.FBILLNO AS '入库单号',
    h.FCREATORID AS '创建人ID',
    creator.FUSERID AS '用户ID',
    creator.FNAME AS '用户姓名',
    creator.FUSERACCOUNT AS '用户账号',
    creator.FEMPID AS '员工ID',
    
    -- 方案1：操作员分录表工号
    op.FOPERATORID AS '操作员ID',
    op_entry.FNUMBER AS '操作员工号1',
    op_entry.FISUSE AS '是否启用1',
    
    -- 方案2：员工表工号
    staff.FSTAFFID AS '员工表ID',
    staff.FNUMBER AS '员工工号2',
    
    -- 最终选择的工号
    COALESCE(
        op_entry.FNUMBER,           -- 优先：操作员分录表工号
        staff.FNUMBER,              -- 次选：员工表工号
        creator.FUSERACCOUNT,       -- 备用：用户账号
        CAST(creator.FUSERID AS NVARCHAR(50))  -- 最后：用户ID
    ) AS '最终工号'
    
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
LEFT JOIN T_BD_OPERATOR op ON h.FCREATORID = op.FOPERATORID
LEFT JOIN T_BD_OPERATORENTRY op_entry ON op.FOPERATORID = op_entry.FOPERATORID 
    AND op_entry.FISUSE = 1
LEFT JOIN T_BD_STAFF staff ON creator.FEMPID = staff.FSTAFFID
WHERE h.FBILLNO IS NOT NULL
ORDER BY h.FCREATEDATE DESC;

-- 统计各种方案的成功率
SELECT 
    '操作员分录表工号' AS '方案',
    COUNT(*) AS '总记录数',
    COUNT(op_entry.FNUMBER) AS '有工号记录数',
    CAST(COUNT(op_entry.FNUMBER) * 100.0 / COUNT(*) AS DECIMAL(5,2)) AS '成功率%'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
LEFT JOIN T_BD_OPERATOR op ON h.FCREATORID = op.FOPERATORID
LEFT JOIN T_BD_OPERATORENTRY op_entry ON op.FOPERATORID = op_entry.FOPERATORID 
    AND op_entry.FISUSE = 1
WHERE h.FBILLNO IS NOT NULL

UNION ALL

SELECT 
    '员工表工号',
    COUNT(*),
    COUNT(staff.FNUMBER),
    CAST(COUNT(staff.FNUMBER) * 100.0 / COUNT(*) AS DECIMAL(5,2))
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
LEFT JOIN T_BD_STAFF staff ON creator.FEMPID = staff.FSTAFFID
WHERE h.FBILLNO IS NOT NULL

UNION ALL

SELECT 
    '用户账号',
    COUNT(*),
    COUNT(creator.FUSERACCOUNT),
    CAST(COUNT(creator.FUSERACCOUNT) * 100.0 / COUNT(*) AS DECIMAL(5,2))
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER creator ON h.FCREATORID = creator.FUSERID
WHERE h.FBILLNO IS NOT NULL;

-- 查看操作员分录表的详细信息
SELECT TOP 10
    op_entry.FOPERATORID AS '操作员ID',
    op_entry.FNUMBER AS '工号',
    op_entry.FOPERATORTYPE AS '操作员类型',
    op_entry.FISUSE AS '是否启用',
    op_entry.FBIZORGID AS '业务组织',
    op_entry.FSTAFFID AS '关联员工ID'
FROM T_BD_OPERATORENTRY op_entry
WHERE op_entry.FNUMBER IS NOT NULL
ORDER BY op_entry.FOPERATORID;

-- 查看员工表的详细信息
SELECT TOP 10
    staff.FSTAFFID AS '员工ID',
    staff.FNUMBER AS '员工工号',
    staff.FPERSONID AS '人员ID',
    staff.FDOCUMENTSTATUS AS '单据状态',
    staff.FFORBIDSTATUS AS '禁用状态'
FROM T_BD_STAFF staff
WHERE staff.FNUMBER IS NOT NULL
ORDER BY staff.FSTAFFID;
