import pyodbc
import pandas as pd

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

def connect_to_database():
    """连接到SQL Server数据库"""
    connection_string = f"""
    DRIVER={{{DRIVER}}};
    SERVER={SERVER};
    DATABASE={DATABASE};
    UID={USERNAME};
    PWD={PASSWORD};
    TrustServerCertificate=yes;
    """
    try:
        conn = pyodbc.connect(connection_string)
        print("数据库连接成功！")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def main():
    conn = connect_to_database()
    if not conn:
        return
    
    try:
        # 测试1: 查看收款单主表的FSOURCEBILLNUMBER字段
        print("=== 测试1: 查看收款单的源单据号 ===")
        source_query = """
        SELECT TOP 5 FID, FBILLNO, FDATE, FSOURCEBILLNUMBER
        FROM T_AR_RECEIVEBILL
        WHERE FSOURCEBILLNUMBER IS NOT NULL AND FSOURCEBILLNUMBER != ''
        """
        try:
            source_df = pd.read_sql(source_query, conn)
            print(source_df)
        except Exception as e:
            print(f"查询源单据号失败: {e}")
        
        # 测试2: 尝试通过FSOURCEBILLNUMBER关联应收单
        print("\n=== 测试2: 通过源单据号关联应收单和客户 ===")
        join_query1 = """
        SELECT TOP 5 
            r.FID as 收款单ID,
            r.FBILLNO as 收款单号,
            r.FDATE as 收款日期,
            r.FSOURCEBILLNUMBER as 源单据号,
            ar.FBILLNO as 应收单号,
            ar.FCUSTOMERID as 客户ID,
            c_l.FNAME as 客户名称,
            re.FSETTLERECAMOUNT as 收款金额
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        LEFT JOIN T_AR_RECEIVABLE ar ON r.FSOURCEBILLNUMBER = ar.FBILLNO
        LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID  
        LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND r.FDATE >= '2025-05-01'
        """
        try:
            join_df1 = pd.read_sql(join_query1, conn)
            print(join_df1)
            if not join_df1.empty and join_df1['客户名称'].notna().any():
                print("✓ 成功通过源单据号关联到客户！")
            else:
                print("× 通过源单据号关联客户失败")
        except Exception as e:
            print(f"关联查询失败: {e}")
        
        # 测试3: 尝试其他可能的关联方式
        print("\n=== 测试3: 查看收款单分录表是否有客户关联 ===")
        entry_fields_query = """
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILLENTRY'
        AND (COLUMN_NAME LIKE '%CUST%' OR COLUMN_NAME LIKE '%SRC%' OR COLUMN_NAME LIKE '%REF%')
        ORDER BY COLUMN_NAME;
        """
        try:
            entry_fields = pd.read_sql(entry_fields_query, conn)
            print("收款单分录表中可能的关联字段:")
            print(entry_fields)
        except Exception as e:
            print(f"查询分录表字段失败: {e}")
        
        # 测试4: 查看应收单和收款单的实际数据对比
        print("\n=== 测试4: 查看应收单示例数据 ===")
        receivable_sample_query = """
        SELECT TOP 3 FID, FBILLNO, FDATE, FCUSTOMERID
        FROM T_AR_RECEIVABLE
        WHERE FCUSTOMERID IS NOT NULL
        """
        try:
            receivable_sample = pd.read_sql(receivable_sample_query, conn)
            print("应收单示例:")
            print(receivable_sample)
        except Exception as e:
            print(f"查询应收单示例失败: {e}")
        
        # 测试5: 最终的收款单汇总查询（如果关联成功）
        print("\n=== 测试5: 最终收款单汇总查询 ===")
        final_query = """
        SELECT 
            c_l.FNAME AS '客户名称',
            CONVERT(varchar(10), r.FDATE, 120) AS '收款日期',
            SUM(re.FSETTLERECAMOUNT) AS '收款金额'
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        LEFT JOIN T_AR_RECEIVABLE ar ON r.FSOURCEBILLNUMBER = ar.FBILLNO
        LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID  
        LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND r.FCANCELSTATUS = 'A'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        AND c_l.FNAME IS NOT NULL
        GROUP BY 
            c_l.FNAME, 
            CONVERT(varchar(10), r.FDATE, 120)
        ORDER BY 
            CONVERT(varchar(10), r.FDATE, 120) DESC, 
            c_l.FNAME;
        """
        try:
            final_df = pd.read_sql(final_query, conn)
            print("最终收款单汇总结果:")
            print(final_df)
            if not final_df.empty:
                print("✓ 收款单汇总查询成功！")
            else:
                print("× 没有找到2025年5月1日之后的收款数据")
        except Exception as e:
            print(f"最终查询失败: {e}")
            
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
