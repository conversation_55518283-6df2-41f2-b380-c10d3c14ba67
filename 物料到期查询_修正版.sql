-- 物料到期查询 - 修正版（基于深度诊断结果）
-- 使用多种关联方式确保能获取到工号

DECLARE @仓库代码 NVARCHAR(50) = '';  -- 留空查询所有仓库，或输入具体仓库代码
DECLARE @开始日期 DATE = '2023-01-01';
DECLARE @结束日期 DATE = '2026-12-31';

WITH 库存汇总 AS (
    -- SP入库数据
    SELECT 
        e.FMATERIALID,
        e.FSTOCKID,
        e.FLOT,
        e.FPRODUCEDATE,
        e.FEXPIRYDATE,
        e.FBASEQTY AS 数量,
        h.FCREATORID,
        h.FCREATEDATE,
        'SP入库' AS 来源
    FROM T_SP_INSTOCK h
    INNER JOIN T_SP_INSTOCKENTRY e ON h.FID = e.FID
    WHERE (@仓库代码 = '' OR EXISTS (
        SELECT 1 FROM T_BD_STOCK s 
        WHERE s.FSTOCKID = e.FSTOCKID AND s.FNUMBER = @仓库代码
    ))
    
    UNION ALL
    
    -- STK入库数据  
    SELECT 
        e.FMATERIALID,
        e.FSTOCKID,
        e.FLOT,
        e.FPRODUCEDATE,
        e.FEXPIRYDATE,
        e.FBASEQTY AS 数量,
        h.FCREATORID,
        h.FCREATEDATE,
        'STK入库' AS 来源
    FROM T_STK_INSTOCK h
    INNER JOIN T_STK_INSTOCKENTRY e ON h.FID = e.FID
    WHERE (@仓库代码 = '' OR EXISTS (
        SELECT 1 FROM T_BD_STOCK s 
        WHERE s.FSTOCKID = e.FSTOCKID AND s.FNUMBER = @仓库代码
    ))
    
    UNION ALL
    
    -- 调拨入库数据
    SELECT 
        e.FMATERIALID,
        e.FSTOCKID,
        e.FLOT,
        e.FPRODUCEDATE,
        e.FEXPIRYDATE,
        e.FBASEQTY AS 数量,
        h.FCREATORID,
        h.FCREATEDATE,
        '调拨入库' AS 来源
    FROM T_STK_STKTRANSFERIN h
    INNER JOIN T_STK_STKTRANSFERINENTRY e ON h.FID = e.FID
    WHERE (@仓库代码 = '' OR EXISTS (
        SELECT 1 FROM T_BD_STOCK s 
        WHERE s.FSTOCKID = e.FSTOCKID AND s.FNUMBER = @仓库代码
    ))
)

SELECT 
    ROW_NUMBER() OVER (ORDER BY 
        CASE 
            WHEN k.FEXPIRYDATE < GETDATE() THEN 1
            WHEN k.FEXPIRYDATE <= DATEADD(DAY, 30, GETDATE()) THEN 2
            ELSE 3
        END,
        k.FEXPIRYDATE
    ) AS 序号,
    
    -- 物料信息
    ISNULL(m.FNUMBER, '未知物料') AS 物料代码,
    ISNULL(m.FNAME, '未知物料名称') AS 物料名称,
    
    -- 批次和日期信息
    ISNULL(k.FLOT, '') AS 批次号,
    CONVERT(VARCHAR(10), k.FPRODUCEDATE, 120) AS 生产日期,
    CONVERT(VARCHAR(10), k.FEXPIRYDATE, 120) AS 到期日期,
    
    -- 到期状态计算
    CASE 
        WHEN k.FEXPIRYDATE < GETDATE() THEN '已过期'
        WHEN k.FEXPIRYDATE <= DATEADD(DAY, 7, GETDATE()) THEN '7天内到期'
        WHEN k.FEXPIRYDATE <= DATEADD(DAY, 30, GETDATE()) THEN '30天内到期'
        WHEN k.FEXPIRYDATE <= DATEADD(DAY, 90, GETDATE()) THEN '90天内到期'
        ELSE '正常'
    END AS 到期状态,
    
    DATEDIFF(DAY, GETDATE(), k.FEXPIRYDATE) AS 剩余天数,
    
    -- 库存信息
    k.数量 AS 实际数量,
    k.数量 AS 基本数量,
    ISNULL(w.FNAME, '未知仓库') AS 仓库名称,
    
    -- 制单人信息 - 使用多种关联方式
    COALESCE(
        -- 方式1：通过用户ID直接关联操作员分录获取工号
        op1.FNUMBER,
        -- 方式2：通过用户→员工→员工工号
        s1.FNUMBER,
        -- 方式3：通过用户→员工→操作员分录获取工号
        op2.FNUMBER,
        -- 方式4：如果都没有，显示用户账号
        u.FUSERACCOUNT,
        -- 方式5：最后显示用户姓名
        u.FNAME,
        '未知制单人'
    ) AS 制单人工号,
    
    -- 辅助信息用于调试
    u.FNAME AS 制单人姓名,
    CONVERT(VARCHAR(19), k.FCREATEDATE, 120) AS 制单日期,
    k.来源
    
FROM 库存汇总 k
-- 关联物料表
LEFT JOIN T_BD_MATERIAL m ON k.FMATERIALID = m.FMATERIALID
-- 关联仓库表
LEFT JOIN T_BD_STOCK w ON k.FSTOCKID = w.FSTOCKID
-- 关联用户表
LEFT JOIN T_SEC_USER u ON k.FCREATORID = u.FUSERID
-- 方式1：直接通过用户ID关联操作员分录
LEFT JOIN T_BD_OPERATORENTRY op1 ON u.FUSERID = op1.FOPERATORID AND op1.FISUSE = 1
-- 方式2：通过用户的员工ID关联员工表
LEFT JOIN T_BD_STAFF s1 ON u.FEMPID = s1.FSTAFFID
-- 方式3：通过员工ID关联操作员分录
LEFT JOIN T_BD_OPERATORENTRY op2 ON s1.FSTAFFID = op2.FSTAFFID AND op2.FISUSE = 1

WHERE k.FEXPIRYDATE IS NOT NULL
    AND k.FEXPIRYDATE BETWEEN @开始日期 AND @结束日期
    AND k.数量 > 0

ORDER BY 
    CASE 
        WHEN k.FEXPIRYDATE < GETDATE() THEN 1
        WHEN k.FEXPIRYDATE <= DATEADD(DAY, 30, GETDATE()) THEN 2
        ELSE 3
    END,
    k.FEXPIRYDATE;
