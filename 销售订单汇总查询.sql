-- 销售订单按客户名称和日期汇总查询
-- 显示销售组织、客户名称、日期、价税合计四列信息
-- =====================================================

-- 按客户名称和日期汇总价税合计查询（包含销售组织）
SELECT
    org_l.FNAME AS '销售组织',
    c_l.FNAME AS '客户名称',
    CONVERT(varchar(10), o.FDATE, 120) AS '日期',
    SUM(f.FALLAMOUNT) AS '价税合计'
FROM
    T_SAL_ORDER o
    INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
    INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
    INNER JOIN T_BD_CUSTOMER c ON o.FCUSTID = c.FCUSTID
    INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
    LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
    LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
WHERE
    c_l.FLocaleId = 2052  -- 中文
    AND org_l.FLocaleId = 2052  -- 中文组织名称
    --AND o.FDOCUMENTSTATUS = 'C'  -- 已审核单据
    AND CONVERT(varchar(10), o.FDATE, 120) >= '2025-05-01'
    --AND o.FCANCELSTATUS = 'A'    -- 未作废
    -- 只查询指定的销售组织
    AND org_l.FNAME IN (
        '江苏华绿生物科技集团股份有限公司',
        '江苏华骏生物科技有限公司',
        '江苏省华蕈农业发展有限公司',
        '泗阳华盛生物科技有限公司',
        '浙江华实生物科技有限公司',
        '泗阳华茂农业发展有限公司'
    )
GROUP BY
    org_l.FNAME,
    c_l.FNAME,
    CONVERT(varchar(10), o.FDATE, 120)
ORDER BY
    org_l.FNAME,
    CONVERT(varchar(10), o.FDATE, 120) DESC,
    c_l.FNAME;

-- =====================================================
-- 查询说明
-- =====================================================
/*
查询功能：
- 按销售组织、客户名称和日期汇总销售订单的价税合计
- 显示四列：销售组织、客户名称、日期、价税合计

主要表关联：
- T_SAL_ORDER: 销售订单主表
- T_SAL_ORDERENTRY: 销售订单明细表
- T_SAL_ORDERENTRY_F: 销售订单明细财务表
- T_BD_CUSTOMER: 客户主表
- T_BD_CUSTOMER_L: 客户多语言表
- T_ORG_ORGANIZATIONS: 组织主表
- T_ORG_ORGANIZATIONS_L: 组织多语言表

过滤条件：
- 只显示中文客户名称和组织名称 (FLocaleId = 2052)
- 日期范围：2025-05-01及以后
- 可选条件（已注释）：
  * 已审核单据 (FDOCUMENTSTATUS = 'C')
  * 未作废单据 (FCANCELSTATUS = 'A')
  * 组织过滤：可以排除或只包含特定组织

分组汇总：
- 按销售组织、客户名称和日期分组
- 对价税合计字段求和

排序规则：
- 按销售组织排序
- 相同组织内按日期降序排列
- 相同日期按客户名称排序

使用方法：
1. 直接执行查询获取汇总结果
2. 根据需要调整日期范围
3. 取消注释可启用单据状态过滤
4. 修改组织过滤条件来排除不需要的组织

组织过滤示例：
- 排除特定组织：AND org_l.FNAME NOT IN ('组织A', '组织B')
- 只包含特定组织：AND org_l.FNAME IN ('组织C', '组织D')
*/

-- =====================================================
-- 示例结果格式
-- =====================================================
/*
销售组织                        客户名称                        日期          价税合计
青岛海欣农产品有限公司          杭州方帅农业开发有限公司        2025-05-01    1500.0000000000
青岛海欣农产品有限公司          其他客户...                     2025-05-01    其他金额...
其他组织...                     其他客户...                     2025-05-01    其他金额...
*/
