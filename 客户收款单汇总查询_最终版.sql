-- 客户收款单汇总查询 (2025年5月1日之后)
-- 查询每个客户和收款组织的收款信息

SELECT
    c_l.FNAME AS '客户名称',
    org_l.FNAME AS '收款组织',
    CONVERT(varchar(10), r.FDATE, 120) AS '收款日期',
    SUM(DISTINCT re.FSETTLERECAMOUNT) AS '收款金额'
FROM
    T_AR_RECEIVEBILL r                                    -- 收款单主表
    INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID -- 收款单分录表
    LEFT JOIN T_AR_RECEIVEBILLSRCENTRY src ON r.FID = src.FID  -- 收款单源单据表
    LEFT JOIN T_AR_RECEIVABLE ar ON src.FSRCBILLNO = ar.FBILLNO  -- 通过源单据号关联应收单
    LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID      -- 关联客户主表
    LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052  -- 客户多语言表(中文)
    LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID  -- 关联组织表
    LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052  -- 组织多语言表(中文)
WHERE
    r.FDOCUMENTSTATUS = 'C'                              -- 已审核单据
    AND r.FCANCELSTATUS = 'A'                            -- 未作废
    AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'  -- 2025年5月1日之后
    AND c_l.FNAME IS NOT NULL                            -- 确保有客户名称
    AND org_l.FNAME IS NOT NULL                          -- 确保有组织名称
GROUP BY
    c_l.FNAME,
    org_l.FNAME,
    CONVERT(varchar(10), r.FDATE, 120),
    r.FID,
    re.FENTRYID
ORDER BY
    CONVERT(varchar(10), r.FDATE, 120) DESC,             -- 按日期倒序
    c_l.FNAME,                                           -- 按客户名称排序
    org_l.FNAME;                                         -- 按组织名称排序

-- 说明：
-- 1. T_AR_RECEIVEBILL - 收款单主表，包含收款日期、单据状态等
-- 2. T_AR_RECEIVEBILLENTRY - 收款单分录表，包含收款金额(FSETTLERECAMOUNT)
-- 3. T_AR_RECEIVEBILLSRCENTRY - 收款单源单据表，包含源应收单信息
-- 4. T_AR_RECEIVABLE - 应收单表，包含客户ID
-- 5. T_BD_CUSTOMER - 客户基础表
-- 6. T_BD_CUSTOMER_L - 客户多语言表，FLocaleId=2052表示中文
-- 7. T_ORG_ORGANIZATIONS - 组织表，包含组织信息
-- 8. T_ORG_ORGANIZATIONS_L - 组织多语言表，FLocaleId=2052表示中文
-- 9. 关联逻辑：收款单通过源单据表关联应收单，应收单关联客户；收款单通过FPAYORGID关联组织

-- 如果需要查看更详细的信息，可以使用以下查询：
/*
SELECT
    r.FBILLNO AS '收款单号',
    c_l.FNAME AS '客户名称',
    CONVERT(varchar(10), r.FDATE, 120) AS '收款日期',
    re.FSETTLERECAMOUNT AS '收款金额',
    r.FSOURCEBILLNUMBER AS '源单据号',
    ar.FBILLNO AS '应收单号'
FROM
    T_AR_RECEIVEBILL r
    INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
    LEFT JOIN T_AR_RECEIVABLE ar ON r.FSOURCEBILLNUMBER = ar.FBILLNO
    LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID
    LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
WHERE
    r.FDOCUMENTSTATUS = 'C'
    AND r.FCANCELSTATUS = 'A'
    AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
    AND c_l.FNAME IS NOT NULL
ORDER BY
    r.FDATE DESC, c_l.FNAME;
*/
