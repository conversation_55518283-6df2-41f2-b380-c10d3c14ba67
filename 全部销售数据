SELECT 
    org.FNAME AS '销售组织',
    o.FBILLNO AS '订单号',
    CONVERT(varchar(10), o.FDATE, 120) AS '销售日期',
    c_l.FNAME AS '客户名称',
    m.FNUMBER AS '物料编码',
    mg.FNUMBER AS '物料组别编码',           -- 物料组别编码
    mg_l.FNAME AS '物料组别名称',           -- 物料组别名称
    mc.FNUMBER AS '物料类别编码',
    mc_l.FNAME AS '物料类别名称',
    m_l.FNAME AS '物料名称',
    m_l.FSPECIFICATION AS '规格型号',
    e.FQTY AS '数量',
    e.FBASEUNITQTY AS '基础数量',
    e.FUNITID AS '单位',
    f.FPRICE AS '单价',
    f.FTAXPRICE AS '含税单价',
    f.FAMOUNT AS '金额',
    f.FALLAMOUNT AS '价税合计'
FROM 
    T_SAL_ORDER o
    INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
    INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
    INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
    INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
    INNER JOIN T_BD_MATERIALBASE mb ON m.FMATERIALID = mb.FMATERIALID
    INNER JOIN T_BD_MATERIALCATEGORY mc ON mb.FCATEGORYID = mc.FCATEGORYID
    INNER JOIN T_BD_MATERIALCATEGORY_L mc_l ON mc.FCATEGORYID = mc_l.FCATEGORYID
    LEFT JOIN T_BD_MATERIALGROUP mg ON m.FMATERIALGROUP = mg.FID
    LEFT JOIN T_BD_MATERIALGROUP_L mg_l ON mg.FID = mg_l.FID AND mg_l.FLocaleID = 2052  -- 关联物料组别多语言
    INNER JOIN T_BD_CUSTOMER c ON o.FCUSTID = c.FCUSTID
    INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
    INNER JOIN t_org_organizations_l org ON o.FSALEORGID = org.FORGID
WHERE 
    m_l.FLOCALEID = 2052      -- 中文
    AND c_l.FLocaleId = 2052  -- 中文
    AND mc_l.FLOCALEID = 2052 -- 中文
    AND org.FLOCALEID = 2052  -- 中文
    --AND o.FDOCUMENTSTATUS = 'C'  -- 已审核单据
    --AND o.FCLOSESTATUS = 'A'     -- 未关闭
    --AND o.FCANCELSTATUS = 'A'    -- 未作废
ORDER BY 
    o.FDATE DESC, o.FBILLNO, e.FSEQ
    
    
    
