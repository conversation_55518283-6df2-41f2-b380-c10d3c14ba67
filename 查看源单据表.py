import pyodbc
import pandas as pd

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

def connect_to_database():
    """连接到SQL Server数据库"""
    connection_string = f"""
    DRIVER={{{DRIVER}}};
    SERVER={SERVER};
    DATABASE={DATABASE};
    UID={USERNAME};
    PWD={PASSWORD};
    TrustServerCertificate=yes;
    """
    try:
        conn = pyodbc.connect(connection_string)
        print("数据库连接成功！")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def main():
    conn = connect_to_database()
    if not conn:
        return
    
    try:
        # 1. 查看收款单源单据表结构
        print("=== 1. 收款单源单据表 T_AR_RECEIVEBILLSRCENTRY 结构 ===")
        src_structure_query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILLSRCENTRY'
        ORDER BY ORDINAL_POSITION
        """
        try:
            src_structure = pd.read_sql(src_structure_query, conn)
            print(src_structure)
        except Exception as e:
            print(f"查询源单据表结构失败: {e}")
        
        # 2. 查看源单据表示例数据
        print("\n=== 2. 收款单源单据表示例数据 ===")
        src_sample_query = """
        SELECT TOP 10 *
        FROM T_AR_RECEIVEBILLSRCENTRY
        """
        try:
            src_sample = pd.read_sql(src_sample_query, conn)
            print(src_sample)
        except Exception as e:
            print(f"查询源单据表示例数据失败: {e}")
        
        # 3. 查看收款单记录表 T_AR_RECEIVEBILLREC
        print("\n=== 3. 收款单记录表 T_AR_RECEIVEBILLREC 结构 ===")
        rec_structure_query = """
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILLREC'
        ORDER BY ORDINAL_POSITION
        """
        try:
            rec_structure = pd.read_sql(rec_structure_query, conn)
            print(rec_structure)
        except Exception as e:
            print(f"查询记录表结构失败: {e}")
        
        # 4. 查看记录表示例数据
        print("\n=== 4. 收款单记录表示例数据 ===")
        rec_sample_query = """
        SELECT TOP 10 *
        FROM T_AR_RECEIVEBILLREC
        """
        try:
            rec_sample = pd.read_sql(rec_sample_query, conn)
            print(rec_sample)
        except Exception as e:
            print(f"查询记录表示例数据失败: {e}")
        
        # 5. 尝试通过记录表关联客户
        print("\n=== 5. 尝试通过记录表关联客户 ===")
        try:
            rec_customer_query = """
            SELECT TOP 10
                r.FID,
                r.FBILLNO as 收款单号,
                r.FDATE as 收款日期,
                rec.FCUSTOMERID as 客户ID,
                c_l.FNAME as 客户名称,
                org_l.FNAME as 收款组织,
                re.FSETTLERECAMOUNT as 收款金额
            FROM T_AR_RECEIVEBILL r
            INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
            LEFT JOIN T_AR_RECEIVEBILLREC rec ON r.FID = rec.FID
            LEFT JOIN T_BD_CUSTOMER c ON rec.FCUSTOMERID = c.FCUSTID
            LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
            LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID
            LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052
            WHERE r.FDOCUMENTSTATUS = 'C'
            AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
            ORDER BY r.FDATE DESC
            """
            rec_customer = pd.read_sql(rec_customer_query, conn)
            print(rec_customer)
            
            if not rec_customer.empty and rec_customer['客户名称'].notna().any():
                print("✓ 成功通过记录表关联到客户！")
            else:
                print("× 记录表关联客户失败")
                
        except Exception as e:
            print(f"记录表关联客户失败: {e}")
        
        # 6. 查看CRM客户最后收款单视图
        print("\n=== 6. 查看CRM客户最后收款单视图 ===")
        try:
            crm_view_query = """
            SELECT TOP 10 *
            FROM V_CRM_CUST_LASTRECEIVEBILL
            """
            crm_view = pd.read_sql(crm_view_query, conn)
            print("CRM客户最后收款单视图:")
            print(crm_view)
        except Exception as e:
            print(f"查询CRM视图失败: {e}")
        
        # 7. 尝试通过应收单核销关联
        print("\n=== 7. 查看是否有应收核销表能关联客户 ===")
        try:
            # 查找应收相关的核销表
            ar_writeoff_query = """
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME LIKE 'T_AR_%' AND TABLE_NAME LIKE '%WRITEOFF%'
            """
            ar_writeoff_tables = pd.read_sql(ar_writeoff_query, conn)
            print("应收核销相关表:")
            print(ar_writeoff_tables)
        except Exception as e:
            print(f"查询应收核销表失败: {e}")
            
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
