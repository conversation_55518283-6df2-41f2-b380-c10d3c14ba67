-- 客户物料价格查询 - 按客户和物料显示购买价格
-- 查询指定时间范围内每个客户每个物料的购买价格
-- =====================================================

SELECT
    org_l.FNAME AS '销售组织',
    c_l.FNAME AS '客户名称',
    m.FNUMBER AS '物料编码',
    m_l.FNAME AS '物料名称',
    m_l.FSPECIFICATION AS '规格型号',
    mc_l.FNAME AS '物料类别',
    u_l.FNAME AS '计价单位',
    o.FBILLNO AS '订单号',
    CONVERT(varchar(10), o.FDATE, 120) AS '销售日期',
    e.FQTY AS '销售数量',
    f.FPRICE AS '单价',
    f.FTAXPRICE AS '含税单价',
    f.FAMOUNT AS '金额',
    f.FALLAMOUNT AS '价税合计',
    CASE 
        WHEN o.FDOCUMENTSTATUS = 'C' THEN '已审核'
        WHEN o.FDOCUMENTSTATUS = 'B' THEN '已提交'
        WHEN o.FDOCUMENTSTATUS = 'A' THEN '暂存'
        ELSE '其他'
    END AS '单据状态'
FROM
    T_SAL_ORDER o
    INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
    INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
    INNER JOIN T_BD_CUSTOMER c ON o.FCUSTID = c.FCUSTID
    INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
    INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
    INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
    INNER JOIN T_BD_MATERIALBASE mb ON m.FMATERIALID = mb.FMATERIALID
    INNER JOIN T_BD_MATERIALCATEGORY mc ON mb.FCATEGORYID = mc.FCATEGORYID
    INNER JOIN T_BD_MATERIALCATEGORY_L mc_l ON mc.FCATEGORYID = mc_l.FCATEGORYID
    LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
    LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
    LEFT JOIN T_BD_UNIT u ON e.FUNITID = u.FUNITID
    LEFT JOIN T_BD_UNIT_L u_l ON u.FUNITID = u_l.FUNITID
WHERE
    c_l.FLocaleId = 2052        -- 中文客户名称
    AND m_l.FLOCALEID = 2052    -- 中文物料名称
    AND mc_l.FLOCALEID = 2052   -- 中文物料类别
    AND org_l.FLocaleId = 2052  -- 中文组织名称
    AND u_l.FLocaleId = 2052    -- 中文单位名称
    -- 时间范围过滤（请根据需要修改日期）
    AND o.FDATE >= '2025-06-01'
    AND o.FDATE <= '2025-06-10'
    -- 可选的额外过滤条件（根据需要取消注释）
    --AND o.FDOCUMENTSTATUS = 'C'  -- 只查询已审核单据
    --AND o.FCANCELSTATUS = 'A'    -- 排除已作废单据
    --AND f.FPRICE > 0             -- 排除价格为0的记录
ORDER BY
    c_l.FNAME,                  -- 按客户名称排序
    m_l.FNAME,                  -- 按物料名称排序
    o.FDATE DESC,               -- 按日期降序排列
    o.FBILLNO;                  -- 按订单号排序

-- =====================================================
-- 查询说明
-- =====================================================
/*
查询功能：
- 显示指定时间范围内每个客户每个物料的购买价格
- 包含详细的物料信息、客户信息和价格信息
- 支持按客户和物料分组查看价格变化

主要表关联：
- T_SAL_ORDER: 销售订单主表
- T_SAL_ORDERENTRY: 销售订单明细表
- T_SAL_ORDERENTRY_F: 销售订单明细财务表（包含价格信息）
- T_BD_CUSTOMER: 客户主表
- T_BD_CUSTOMER_L: 客户多语言表
- T_BD_MATERIAL: 物料主表
- T_BD_MATERIAL_L: 物料多语言表
- T_BD_MATERIALCATEGORY: 物料类别表
- T_BD_MATERIALCATEGORY_L: 物料类别多语言表
- T_ORG_ORGANIZATIONS: 组织主表
- T_ORG_ORGANIZATIONS_L: 组织多语言表
- T_BD_UNIT: 单位主表
- T_BD_UNIT_L: 单位多语言表

重要字段说明：
- FPRICE: 不含税单价
- FTAXPRICE: 含税单价
- FAMOUNT: 不含税金额
- FALLAMOUNT: 价税合计金额
- FQTY: 销售数量

时间范围设置：
- 默认查询时间：2025年6月1日 - 2025年6月10日
- 可以修改 WHERE 条件中的日期范围来查询其他时间段

使用方法：
1. 直接执行查询获取指定时间范围内的客户物料价格
2. 根据需要修改时间范围
3. 可以取消注释额外的过滤条件来精确筛选数据
4. 结果按客户名称、物料名称、日期排序，便于分析价格变化

排序说明：
- 首先按客户名称排序
- 相同客户内按物料名称排序
- 相同物料按日期降序排列（最新的在前）
- 最后按订单号排序

示例修改：
1. 修改时间范围：
   AND o.FDATE >= '2025-05-01'
   AND o.FDATE <= '2025-05-31'

2. 只查询特定客户：
   AND c_l.FNAME LIKE '%华绿%'

3. 只查询特定物料：
   AND m_l.FNAME LIKE '%蘑菇%'

4. 只查询已审核单据：
   AND o.FDOCUMENTSTATUS = 'C'
*/

-- =====================================================
-- 示例结果格式
-- =====================================================
/*
销售组织                  客户名称              物料编码    物料名称        规格型号    物料类别    计价单位    订单号        销售日期    销售数量    单价        含税单价    金额        价税合计    单据状态
江苏华绿生物科技...      杭州方帅农业...       MAT001     双孢菇          500g       食用菌      箱          SO001        2025-06-01  100        10.00       11.30       1000.00     1130.00     已审核
江苏华绿生物科技...      杭州方帅农业...       MAT002     金针菇          250g       食用菌      箱          SO002        2025-06-02  50         8.50        9.61        425.00      480.50      已审核
*/ 