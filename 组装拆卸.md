create table T_STK_ASSEMBLY(
	FID int not null  comment '内码'
	,FBill<PERSON>o nvarchar(30) not null  comment '编号'
	,FBillTypeID varchar(36) not null  comment '单据类型'
	,FDate datetime not null  comment '日期'
	,FAffairType nvarchar(20) not null  comment '事务类型'
	,Fee decimal(23,10) not null  comment '费用'
	,FCURRENCYID int not null  comment '币别'
	,FBASECURRID int not null  comment '本位币'
	,FEXCHANGETYPEID int not null  comment '汇率类型'
	,FEXCHANGERATE decimal(23,10) not null  comment '汇率'
	,FStockOrgId int not null  comment '库存组织'
	,FDeptID int not null  comment '部门'
	,FSTOCKERGROUPID int not null  comment '库存组'
	,FSTOCKERID int not null  comment '仓管员'
	,FDocumentStatus char(1) not null  comment '状态'
	,FCreatorID int not null  comment '创建人'
	,FCreateDate datetime not null  comment '创建日期'
	,FModifierID int not null  comment '最后修改人'
	,FModifyDate datetime default null  comment '最后修改日期'
	,FAPPROVERID int not null  comment '审核人'
	,FAPPROVEDATE datetime default null  comment '审核日期'
	,FCANCELLERID int not null  comment '作废人'
	,FCANCELDATE datetime default null  comment '作废日期'
	,FCancelStatus char(1) not null  comment '作废状态'
	,FNote nvarchar(255) not null  comment '备注'
	,FOwnerTypeId varchar(36) not null  comment '成品货主类型'
	,FOwnerId int not null  comment '成品货主'
	,FSUBPROOWNTYPEIDH varchar(36) not null  comment '子件货主类型'
	,FSUBPROOWNERIDH int not null  comment '子件货主'
	,FOBJECTTYPEID varchar(36) not null  comment '业务对象类型'
	,primary key (FID)
) comment = '组装单'



create table T_STK_ASSEMBLYPDTSERIAL(
	FDETAILID int not null  comment '内码'
	,FENTRYID int not null  comment '分录内码'
	,FSEQ int not null  comment '序号'
	,FSERIALNO nvarchar(80) not null  comment '序列号'
	,FSERIALID int not null  comment '序列号内码'
	,FSERIALNOTE nvarchar(255) default null  comment '备注'
	,primary key (FDETAILID)
) comment = '组装拆卸单成品序列号'


create table T_STK_ASSEMBLYPRODUCT(
	FEntryID int not null  comment '分录内码'
	,FID int not null  comment '单据内码'
	,FSeq int not null  comment '行号'
	,FRowType nvarchar(20) not null  comment '行类型'
	,FMaterialID int not null  comment '物料'
	,FAuxPropId int not null  comment '辅助属性'
	,FLOT int not null  comment '批号'
	,FLOT_TEXT nvarchar(255) not null  comment '批号文本'
	,FRefBomID int not null  comment '参照bom单'
	,FUnitID int not null  comment '单位'
	,FQty decimal(23,10) not null  comment '数量'
	,FBaseUnitID int not null  comment '基本单位'
	,FBaseQty decimal(23,10) not null  comment '基本单位数量'
	,FBaseDosage decimal(23,10) not null  comment '基本单位用量'
	,FSecUnitID int not null  comment '库存辅助单位'
	,FSecQty decimal(23,10) not null  comment '库存辅助单位数量'
	,FEXTAUXUNITID int not null  comment '辅单位内码'
	,FEXTAUXUNITQTY decimal(23,10) not null  comment '辅单位数量'
	,FExpandType nvarchar(20) not null  comment '展开级次选项'
	,FExpandLevel int not null  comment '展开级次'
	,FPRICE decimal(23,10) not null  comment '单价'
	,FAMOUNT decimal(23,10) not null  comment '金额'
	,Fee decimal(23,10) not null  comment '费用'
	,FStockID int not null  comment '仓库'
	,FStockLocId int not null  comment '仓位'
	,FStockStatusId int not null  comment '库存状态'
	,FStockFlag char(1) not null  comment '已更新库存'
	,FDescription nvarchar(255) not null  comment '描述'
	,FMtoNo nvarchar(255) not null  comment '计划跟踪号'
	,FProjectNo nvarchar(30) not null  comment '项目编号'
	,FOwnerTypeID varchar(36) not null  comment '货主类型'
	,FOwnerID int not null  comment '货主'
	,FKeeperTypeID varchar(36) not null  comment '保管者类型'
	,FKeeperID int not null  comment '保管者'
	,FBomID int not null  comment 'bom单id'
	,FProduceDate datetime default null  comment '生产日期'
	,FExpiryDate datetime default null  comment '到期日'
	,FSNUNITID int not null  comment '序列号单位'
	,FSNQTY int not null  comment '序列号单位数量'
	,FInStockDate datetime default null  comment '入库日期'
	,primary key (FEntryID)
) comment = '组装拆卸单成品表'


create table T_STK_ASSEMBLYPRODUCT_R(
	FENTRYID int not null  comment '分录内码'
	,FID int not null  comment '单据内码'
	,FSrcObjectID varchar(36) not null  comment '源单名称'
	,FSRCBILLTYPEID varchar(36) not null  comment '源单类型'
	,FSRCBILLNO nvarchar(160) not null  comment '源单编号'
	,FSRCSEQ nvarchar(255) not null  comment '源单行号'
	,FSOBomId int not null  comment '销售订单bom'
	,FSaleUnitId int not null  comment '销售单位'
	,FSaleBaseQty decimal(23,10) not null  comment '销售基本数量'
	,FSaleQty decimal(23,10) not null  comment '销售数量'
	,FSrcBizUnitId int not null  comment '携带源单主业务单位'
	,FSrcAuxUnitId int not null  comment '携带源单辅单位'
	,FSrcBizBaseQty decimal(23,10) not null  comment '源单主单位基本数量分子'
	,FSrcAuxBaseQty decimal(23,10) not null  comment '源单辅单位基本数量分母'
	,primary key (FENTRYID)
) comment = '组装拆卸单成品_关联信息'

create table T_STK_ASSEMBLYSUBITEM(
	FDetailID int not null  comment '分录内码'
	,FEntryID int not null  comment '父项分录内码'
	,FSeq int not null  comment '行号'
	,FRowType nvarchar(20) not null  comment '行类型'
	,FMaterialID int not null  comment '物料'
	,FAuxPropId int not null  comment '辅助属性'
	,FLOT int not null  comment '批号'
	,FLOT_TEXT nvarchar(255) not null  comment '批号文本'
	,FUnitID int not null  comment '单位'
	,FQty decimal(23,10) not null  comment '数量'
	,FCostProportion decimal(19,6) not null  comment '拆分成本比例'
	,FBaseUnitID int not null  comment '基本单位'
	,FBaseQty decimal(23,10) not null  comment '基本单位数量'
	,FBaseDosage decimal(23,10) not null  comment '基本单位用量'
	,FSecUnitID int not null  comment '库存辅助单位'
	,FSecQty decimal(23,10) not null  comment '库存辅助单位数量'
	,FEXTAUXUNITID int not null  comment '辅单位内码'
	,FEXTAUXUNITQTY decimal(23,10) not null  comment '辅单位数量'
	,FPRICE decimal(23,10) not null  comment '单价'
	,FAMOUNT decimal(23,10) not null  comment '金额'
	,FStockID int not null  comment '仓库'
	,FStockLocId int not null  comment '仓位'
	,FStockFlag char(1) not null  comment '已更新库存'
	,FStockStatusId int not null  comment '库存状态'
	,FBomID int not null  comment 'bom单id'
	,FDescription nvarchar(512) not null  comment '描述'
	,FMtoNo nvarchar(255) not null  comment '计划跟踪号'
	,FProjectNo nvarchar(30) not null  comment '项目编号'
	,FOwnerTypeID varchar(36) not null  comment '货主类型'
	,FOwnerID int not null  comment '货主'
	,FKeeperTypeID varchar(36) not null  comment '保管者类型'
	,FKeeperID int not null  comment '保管者'
	,FProduceDate datetime default null  comment '生产日期'
	,FExpiryDate datetime default null  comment '到期日'
	,FSNUNITID int not null  comment '序列号单位'
	,FSNQTY int not null  comment '序列号单位数量'
	,FInStockDate datetime default null  comment '入库日期'
	,primary key (FDetailID)
) comment = '组装/拆卸单子件表'