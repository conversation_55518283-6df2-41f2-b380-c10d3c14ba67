<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1546929700" ExtractionBranch="1" ExtractionDate="1546929700" ExtractionId="1188772" ExtractionVersion="7" ID="{F7653CAA-C234-49CB-9EBA-D39F9FA62589}" Label="" LastModificationDate="1512442016" Name="CTRL控件库" Objects="87" RepositoryId="{5C34CC33-BB05-4053-9571-E184DEFF2D78}" Symbols="14" Target="Microsoft SQL Server 2008" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>F7653CAA-C234-49CB-9EBA-D39F9FA62589</a:ObjectID>
<a:Name>CTRL控件库</a:Name>
<a:Code>CTRL</a:Code>
<a:CreationDate>1510881136</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1511405963</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;\ExtendedSubObject &lt;&lt;AggregateParameter&gt;&gt;]
Create=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;ResourcePool&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;WorkloadGroup&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=0
RefrUpdateConstraint=0
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=d:\Program Files (x86)\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=

[ModelOptions\Default Opts\FRMEOBJ&lt;&lt;ResourcePool&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMEOBJ&lt;&lt;WorkloadGroup&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMESOB&lt;&lt;ResourcePool&gt;&gt;]
PhysOpts=

[ModelOptions\Default Opts\FRMESOB&lt;&lt;WorkloadGroup&gt;&gt;]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {5C34CC33-BB05-4053-9571-E184DEFF2D78}
MODEL_ID 1188772
MODEL_VRSN 7
BRANCH_ID 1
EXT_DATE 1546929700
EXT_END 1546929700
OBJECTS 
BEG_BLOCK 
 1188772 {F7653CAA-C234-49CB-9EBA-D39F9FA62589}
 1188773 {83A586CA-5E3B-4BF4-9E19-CC6CA6191FB8}
 1188774 {E7E7644D-DA90-43CD-B9DC-6A9288C1DFDB}
 1188775 {FA8008DC-2762-4DA0-B733-EDE046944C43}
 1188776 {9B2CBA36-CC51-401F-B0DC-3D0B63E81604}
 1188777 {CB67653F-4F87-4F6D-B7D7-6D7E73F0F775}
 1188778 {337F988D-0164-4638-A6A3-A928A6F3561D}
 1190286 {D639B6AD-E49A-4F86-8124-D3D8FC845452}
 1190287 {8AD22FCA-D57F-42CB-8139-66A1199F59AC}
 1190288 {653D69C8-ACC2-4CE0-879F-4E0F88EA644D}
 1190289 {06AB5CCB-9D7C-47FD-A45E-5390C56C8BA7}
 1190290 {98479FF7-5C49-4EE0-92D9-4E00763A6F25}
 1190291 {3AAF4D25-DE9F-47FD-BECE-541E632D0283}
 1190292 {EFED7073-7A49-41E7-A60F-DF1083F26808}
 1190293 {437F0953-B291-4806-96FA-C7030A58BA6A}
 1190294 {111542E8-8DB1-4DF6-A931-C6D9276697C4}
 1190295 {34D34D43-721A-4137-B57E-B4EC8AD13824}
 1190296 {B386B245-E8A6-4101-BF30-EEE52FD7B3DF}
 1190297 {EF8C1254-6773-4C55-AC62-59676CCABBA9}
 1190298 {527E8736-DC12-4110-9526-1754DD3BE421}
 1190299 {CB80942B-8643-4A24-B5A8-39B87EC6EEA4}
 1190300 {AF319DCE-C83C-4DA9-B5DA-E24924DBCF14}
 1190301 {956FB5A4-02F1-4229-AE03-9C42BDE22A6B}
 1190302 {5BBCB624-BCE8-457B-85D6-19174B1F3B58}
 1190303 {1B81816D-2100-490B-AA23-46A215664CF9}
 1190304 {05B89449-C7FB-4745-9472-2E8EC35D9B5A}
 1190305 {0F73DEC8-965E-4E65-9E1B-2E77726A2A34}
 1190306 {E4429A0D-4C3C-49EA-B1E7-8E68B5600A24}
 1190307 {8EBACBDD-2DC7-4937-88E6-A0CCCC0A6F1C}
 1190308 {E89ADB48-5290-4FC3-AC01-51B93979189F}
 1190309 {62D135B0-4868-4363-8730-C7AD589F69E7}
 1190310 {FD398A16-C0FC-461E-AE20-AEECEF87C1AE}
 1190311 {E58866C3-B4F2-4C5A-BCEC-B28E4F686A6E}
 1190312 {133A63EA-8FBD-487C-90C4-90BAD5F800A9}
 1190313 {A6631AC2-9AE0-4AFF-B025-22C1FFBF2A6A}
 1190314 {93F0119D-6920-46F1-B2BE-C9A7CA0089AA}
 1190315 {7E40A1F4-5E2B-40E7-B30C-86D76B8EF492}
 1190316 {D5ADA48F-E915-462F-BA1E-E17DAA1FBA99}
 1190317 {AE0FC02F-7138-4BA1-B361-1C4F9C201D9D}
 1190428 {8B52F617-EAA7-46F8-9A67-E17A8BE18EB8}
 1194719 {156866F0-C78E-4A57-900B-10820070992E}
 1194720 {2F155C42-0ED9-429B-9DAA-D1CFC57ECE33}
 1194721 {0E2EC48E-0D2A-4234-AD42-7BB9A6A1F926}
 1194722 {F4519A73-132D-4EE3-8832-FB54401D7F34}
 1194723 {1920D2D4-F892-4B1E-8CA0-54E39BBCCFB5}
 1194724 {FDB1105E-CB00-466B-8359-E6E46ABB1B7E}
 1194725 {229FB517-856C-4027-A33C-8E992CA567E9}
 1194726 {71993D19-E6B1-40BE-B38C-1EAA0709ABC8}
 1194727 {F2397B20-5470-458A-A367-EF0A16137DE4}
 1194728 {5FE30A93-CCD3-4CD7-86C3-9FE9A305DD5E}
 1194729 {21118388-F43A-4890-9A08-FD6721E015ED}
 1194730 {93F524E0-E5F6-4F3C-9C5F-25D1FE056AF5}
 1194731 {4288C567-1FB2-4189-9BAE-BF2BB308989A}
 1194732 {B6AC9383-274B-4F51-9AE3-188B273DCB72}
 1194733 {E5407880-4CDD-430A-ADB7-993C285FD22D}
 1194734 {19167C17-57FA-423E-9973-E3EEDE7213DA}
 1194735 {B3787B8E-4DDE-457C-9D2E-DB102014537A}
 1194736 {E94E77D1-460B-4877-8015-3D1423485EA2}
 1194737 {72040ACD-928F-40F8-A007-54581C7B6C64}
 1194738 {71460749-3E2E-4925-96BE-4AA0F77DCBF9}
 1194739 {DD810D22-8BF7-4AEA-8EB5-8772C7D92686}
 1194740 {2793E586-FF7C-4D52-AD68-FD2277BE1434}
 1194741 {740F4DA2-5ABE-4D48-A8CE-301735878C85}
 1194742 {1232C1B9-F4BA-4552-B2AD-5DDE5038E539}
 1194743 {221B8AB0-2DE9-4A68-A77A-8EC0CE6E9B87}
 1194744 {1C3D6BA2-AE5B-4FA6-BA80-5529B1736BDD}
 1194745 {3F531934-7BE9-4018-A479-95A86B321F64}
 1194746 {96BFB99B-EEE3-444D-B2C2-96197DF811C0}
 1194747 {C0F8F51C-936F-45BC-B8C7-2EFE0B767870}
 1194748 {1117E65C-3552-4A44-B664-E27860B61E04}
 1194749 {652AC003-C62F-4B20-A61C-EBADC59418E4}
 1194750 {8B41901B-CFB4-40FA-8977-2BFB6E622569}
 1194751 {E5C2FED2-0E9B-4920-AB89-FC0625FBD850}
 1194752 {4B64B1CB-AF06-43DA-8BC6-E22CC950C068}
 1194753 {D8E27322-F865-4E7A-B1F6-D08D95AE418B}
 1194754 {17500089-EE5C-448D-BE79-3133CC808DDB}
 1194755 {16B5C769-82B3-4320-87FF-4612DF9C1121}
 1194756 {4379EE48-EB02-4A6E-B025-E9A7D7DBEAB8}
 1194757 {3DBF7D1D-F9BF-4EA3-A555-B9777E148254}
 1194758 {71D20AB0-1149-49D0-B30D-053E8C6FF3F3}
 1194759 {7F391083-7E7B-4F7B-99FA-4D3AADCCF4BE}
 1194760 {F751C1A2-7B80-4C35-A3A5-1BFEFE3A80B6}
 1194761 {02C0E47B-4403-4A56-8482-2A36CB05FFC0}
 1194762 {5218EC12-F19B-4860-9C9B-814910CD4D51}
 1194763 {1D4A8390-25D9-4439-9C24-7D10DD0747E0}
 1194764 {D90838D7-09F3-41F1-8AF0-95FBA5A7CBCE}
 1194765 {6C1D99A0-FF39-4ADB-B348-3247F480C5A6}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>83A586CA-5E3B-4BF4-9E19-CC6CA6191FB8</a:ObjectID>
<a:Name>Microsoft SQL Server 2008</a:Name>
<a:Code>MSSQLSRV2008</a:Code>
<a:CreationDate>1510881136</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1510881136</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F5C20738-B05A-4F70-BC90-9B5EB9437766</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>E7E7644D-DA90-43CD-B9DC-6A9288C1DFDB</a:ObjectID>
<a:Name>CTRL控件库_ALL</a:Name>
<a:Code>CTRL_ALL</a:Code>
<a:CreationDate>1510881136</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1511503618</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:TextSymbol Id="o5">
<a:CreationDate>1511231954</a:CreationDate>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Rect>((-47850,-32137), (47850,32137))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>Arial,8,N</a:FontName>
</o:TextSymbol>
<o:TextSymbol Id="o6">
<a:CreationDate>1511232509</a:CreationDate>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Rect>((-47850,-29026), (47850,35248))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>Arial,8,N</a:FontName>
</o:TextSymbol>
<o:TextSymbol Id="o7">
<a:CreationDate>1511232818</a:CreationDate>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Rect>((-47850,-29026), (47850,35248))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>Arial,8,N</a:FontName>
</o:TextSymbol>
<o:TextSymbol Id="o8">
<a:Text>FATTRTYPE</a:Text>
<a:CreationDate>1512439043</a:CreationDate>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Rect>((-47850,-32137), (47850,32137))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>16777215</a:FillColor>
<a:ShadowColor>16777215</a:ShadowColor>
<a:FontName>Arial,8,N</a:FontName>
</o:TextSymbol>
<o:TableSymbol Id="o9">
<a:CreationDate>1511232362</a:CreationDate>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-21910,21529), (-6439,29545))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Shortcut Ref="o10"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o11">
<a:CreationDate>1511232701</a:CreationDate>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((4952,23102), (16948,30144))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Shortcut Ref="o12"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o13">
<a:CreationDate>1512438883</a:CreationDate>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-8667,-46500), (8659,-37136))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o14"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o15">
<a:CreationDate>1512439702</a:CreationDate>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-8163,-58180), (8159,-48816))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o16"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o17">
<a:CreationDate>1512440564</a:CreationDate>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-7506,-69860), (7504,-60496))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o18"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o19">
<a:CreationDate>1512441332</a:CreationDate>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-6889,-81053), (6885,-72663))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o20"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o21">
<a:ObjectID>FA8008DC-2762-4DA0-B733-EDE046944C43</a:ObjectID>
<a:Name>CTRL控件库</a:Name>
<a:Code>CTRL控件库</a:Code>
<a:CreationDate>1510881345</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1510882310</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:Comment>用于构建前端控件描述文档和前端用例执行</a:Comment>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o22">
<a:ObjectID>337F988D-0164-4638-A6A3-A928A6F3561D</a:ObjectID>
<a:Name>CTRL控件库</a:Name>
<a:Code>CTRL</a:Code>
<a:CreationDate>1510881345</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1511503603</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=0
Trunc Length=80
Word Length=80
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=Yes
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=Arial,8,N
CENTERFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=Yes
Keep center=Yes
Keep size=No
Width=2400
Height=2400
Brush color=255 255 255
Fill Color=No
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=-1

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=Arial,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=Arial,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=Arial,8,N
TableFkColumnsFont color=0 0 0
KeysFont=Arial,8,N
KeysFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
TriggersFont=Arial,8,N
TriggersFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=210 137 2
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=Arial,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=Arial,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=Arial,8,N
IndexesFont color=0 0 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=233 202 131
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=Arial,8,N
STRNFont color=0 0 0
DISPNAMEFont=Arial,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=Arial,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=Arial,8,N
OBJXSTRFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 0 64
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=Arial,8,N
Free TextFont color=0 0 0
Line style=2
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 64 0
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o22"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o23">
<a:ObjectID>B386B245-E8A6-4101-BF30-EEE52FD7B3DF</a:ObjectID>
<a:Name>t_Ctrl_CtrlType</a:Name>
<a:Code>t_Ctrl_CtrlType</a:Code>
<a:CreationDate>1511231897</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件类型表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o24">
<a:ObjectID>EF8C1254-6773-4C55-AC62-59676CCABBA9</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1511231897</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o25">
<a:ObjectID>527E8736-DC12-4110-9526-1754DD3BE421</a:ObjectID>
<a:Name>FNUMBER</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1511231897</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o26">
<a:ObjectID>CB80942B-8643-4A24-B5A8-39B87EC6EEA4</a:ObjectID>
<a:Name>FDOCUMENTSTATUS</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1511231897</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39;C&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o27">
<a:ObjectID>AF319DCE-C83C-4DA9-B5DA-E24924DBCF14</a:ObjectID>
<a:Name>FFORBIDSTATUS</a:Name>
<a:Code>FFORBIDSTATUS</a:Code>
<a:CreationDate>1511231897</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39;A&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o28">
<a:ObjectID>956FB5A4-02F1-4229-AE03-9C42BDE22A6B</a:ObjectID>
<a:Name>FSeq</a:Name>
<a:Code>FSeq</a:Code>
<a:CreationDate>1511231897</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o29">
<a:ObjectID>5BBCB624-BCE8-457B-85D6-19174B1F3B58</a:ObjectID>
<a:Name>PK_T_CTRL_CTRLTYPE</a:Name>
<a:Code>PK_T_CTRL_CTRLTYPE</a:Code>
<a:CreationDate>1511232003</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o24"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o30">
<a:ObjectID>1B81816D-2100-490B-AA23-46A215664CF9</a:ObjectID>
<a:Name>IDX_T_CTRLTYPE</a:Name>
<a:Code>IDX_T_CTRLTYPE</a:Code>
<a:CreationDate>1511232079</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o31">
<a:ObjectID>05B89449-C7FB-4745-9472-2E8EC35D9B5A</a:ObjectID>
<a:CreationDate>1511232293</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o28"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o29"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o29"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o32">
<a:ObjectID>0F73DEC8-965E-4E65-9E1B-2E77726A2A34</a:ObjectID>
<a:Name>t_Ctrl_CtrlType_L</a:Name>
<a:Code>t_Ctrl_CtrlType_L</a:Code>
<a:CreationDate>1511232428</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件类型多语言表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o33">
<a:ObjectID>E4429A0D-4C3C-49EA-B1E7-8E68B5600A24</a:ObjectID>
<a:Name>FPKID</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1511232428</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o34">
<a:ObjectID>8EBACBDD-2DC7-4937-88E6-A0CCCC0A6F1C</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1511232472</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o35">
<a:ObjectID>E89ADB48-5290-4FC3-AC01-51B93979189F</a:ObjectID>
<a:Name>FLocaleID</a:Name>
<a:Code>FLocaleID</a:Code>
<a:CreationDate>1511232472</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o36">
<a:ObjectID>62D135B0-4868-4363-8730-C7AD589F69E7</a:ObjectID>
<a:Name>FNAME</a:Name>
<a:Code>FNAME</a:Code>
<a:CreationDate>1511232472</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(2000)</a:DataType>
<a:Length>2000</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o37">
<a:ObjectID>FD398A16-C0FC-461E-AE20-AEECEF87C1AE</a:ObjectID>
<a:Name>pk_t_Ctrl_CtrlType_L</a:Name>
<a:Code>pk_t_Ctrl_CtrlType_L</a:Code>
<a:CreationDate>1511232548</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o33"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o38">
<a:ObjectID>E58866C3-B4F2-4C5A-BCEC-B28E4F686A6E</a:ObjectID>
<a:Name>IDX_t_Ctrl_CtrlType_L</a:Name>
<a:Code>IDX_t_Ctrl_CtrlType_L</a:Code>
<a:CreationDate>1511232643</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o39">
<a:ObjectID>133A63EA-8FBD-487C-90C4-90BAD5F800A9</a:ObjectID>
<a:CreationDate>1511232654</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o35"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o40">
<a:ObjectID>A6631AC2-9AE0-4AFF-B025-22C1FFBF2A6A</a:ObjectID>
<a:CreationDate>1511232654</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o36"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o41">
<a:ObjectID>93F0119D-6920-46F1-B2BE-C9A7CA0089AA</a:ObjectID>
<a:CreationDate>1511232675</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o34"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o37"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o37"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o42">
<a:ObjectID>D639B6AD-E49A-4F86-8124-D3D8FC845452</a:ObjectID>
<a:Name>t_Ctrl_CtrlDes</a:Name>
<a:Code>t_Ctrl_CtrlDes</a:Code>
<a:CreationDate>1511232713</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o43">
<a:ObjectID>8AD22FCA-D57F-42CB-8139-66A1199F59AC</a:ObjectID>
<a:Name>FEntryID</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1511232713</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o44">
<a:ObjectID>653D69C8-ACC2-4CE0-879F-4E0F88EA644D</a:ObjectID>
<a:Name>FID</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1511232713</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o45">
<a:ObjectID>06AB5CCB-9D7C-47FD-A45E-5390C56C8BA7</a:ObjectID>
<a:Name>FCtrlName</a:Name>
<a:Code>FCtrlName</a:Code>
<a:CreationDate>1511232713</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o46">
<a:ObjectID>98479FF7-5C49-4EE0-92D9-4E00763A6F25</a:ObjectID>
<a:Name>FCtrlDes</a:Name>
<a:Code>FCtrlDes</a:Code>
<a:CreationDate>1511232713</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(2000)</a:DataType>
<a:Length>2000</a:Length>
</o:Column>
<o:Column Id="o47">
<a:ObjectID>3AAF4D25-DE9F-47FD-BECE-541E632D0283</a:ObjectID>
<a:Name>FCtrlSeq</a:Name>
<a:Code>FCtrlSeq</a:Code>
<a:CreationDate>1511232713</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o48">
<a:ObjectID>8B52F617-EAA7-46F8-9A67-E17A8BE18EB8</a:ObjectID>
<a:Name>FCtrlPrxy</a:Name>
<a:Code>FCtrlPrxy</a:Code>
<a:CreationDate>1511245559</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511245625</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o49">
<a:ObjectID>EFED7073-7A49-41E7-A60F-DF1083F26808</a:ObjectID>
<a:Name>PK_t_Ctrl_CtrlDes</a:Name>
<a:Code>PK_t_Ctrl_CtrlDes</a:Code>
<a:CreationDate>1511232823</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o43"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o50">
<a:ObjectID>437F0953-B291-4806-96FA-C7030A58BA6A</a:ObjectID>
<a:Name>IDX_t_Ctrl_CtrlDes</a:Name>
<a:Code>IDX_t_Ctrl_CtrlDes</a:Code>
<a:CreationDate>1511232823</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o51">
<a:ObjectID>111542E8-8DB1-4DF6-A931-C6D9276697C4</a:ObjectID>
<a:CreationDate>1511232960</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o45"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o52">
<a:ObjectID>34D34D43-721A-4137-B57E-B4EC8AD13824</a:ObjectID>
<a:CreationDate>1511232960</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o47"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o49"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o49"/>
</c:ClusterObject>
</o:Table>
</c:Tables>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:Tables>
<o:Shortcut Id="o10">
<a:ObjectID>D5ADA48F-E915-462F-BA1E-E17DAA1FBA99</a:ObjectID>
<a:Name>t_Ctrl_CtrlType</a:Name>
<a:Code>t_Ctrl_CtrlType</a:Code>
<a:CreationDate>1511232362</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>B386B245-E8A6-4101-BF30-EEE52FD7B3DF</a:TargetID>
<a:TargetClassID>380BAA40-0042-11D2-BD28-00A02478ECC9</a:TargetClassID>
<a:TargetPackagePath>&lt;Model&gt;::CTRL控件库</a:TargetPackagePath>
</o:Shortcut>
<o:Shortcut Id="o12">
<a:ObjectID>AE0FC02F-7138-4BA1-B361-1C4F9C201D9D</a:ObjectID>
<a:Name>t_Ctrl_CtrlType_L</a:Name>
<a:Code>t_Ctrl_CtrlType_L</a:Code>
<a:CreationDate>1511232701</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1511233070</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>0F73DEC8-965E-4E65-9E1B-2E77726A2A34</a:TargetID>
<a:TargetClassID>380BAA40-0042-11D2-BD28-00A02478ECC9</a:TargetClassID>
<a:TargetPackagePath>&lt;Model&gt;::CTRL控件库</a:TargetPackagePath>
</o:Shortcut>
<o:Table Id="o14">
<a:ObjectID>156866F0-C78E-4A57-900B-10820070992E</a:ObjectID>
<a:Name>t_Ctrl_CtrlAttrDetail</a:Name>
<a:Code>t_Ctrl_CtrlAttrDetail</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>属性单据体</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o53">
<a:ObjectID>2F155C42-0ED9-429B-9DAA-D1CFC57ECE33</a:ObjectID>
<a:Name>FAttrDetailID</a:Name>
<a:Code>FAttrDetailID</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>属性</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(150)</a:DataType>
<a:Length>150</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o54">
<a:ObjectID>0E2EC48E-0D2A-4234-AD42-7BB9A6A1F926</a:ObjectID>
<a:Name>FEntryID</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件描述ID</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(500)</a:DataType>
<a:Length>500</a:Length>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>F4519A73-132D-4EE3-8832-FB54401D7F34</a:ObjectID>
<a:Name>FATTRID</a:Name>
<a:Code>FATTRID</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o56">
<a:ObjectID>1920D2D4-F892-4B1E-8CA0-54E39BBCCFB5</a:ObjectID>
<a:Name>FATTRSEQ</a:Name>
<a:Code>FATTRSEQ</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>次序</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o57">
<a:ObjectID>FDB1105E-CB00-466B-8359-E6E46ABB1B7E</a:ObjectID>
<a:Name>FATTRNAME</a:Name>
<a:Code>FATTRNAME</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>属性名称</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>nvarchar(1000)</a:DataType>
<a:Length>1000</a:Length>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>229FB517-856C-4027-A33C-8E992CA567E9</a:ObjectID>
<a:Name>FATTRTYPE</a:Name>
<a:Code>FATTRTYPE</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>类型</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>71993D19-E6B1-40BE-B38C-1EAA0709ABC8</a:ObjectID>
<a:Name>FATTRDEFALUTVALUE</a:Name>
<a:Code>FATTRDEFALUTVALUE</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>默认值</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>F2397B20-5470-458A-A367-EF0A16137DE4</a:ObjectID>
<a:Name>FATTRDES</a:Name>
<a:Code>FATTRDES</a:Code>
<a:CreationDate>1512438883</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>属性描述</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o61">
<a:ObjectID>5FE30A93-CCD3-4CD7-86C3-9FE9A305DD5E</a:ObjectID>
<a:Name>pk_Ctrl_CtrlAttrDetail</a:Name>
<a:Code>pk_Ctrl_CtrlAttrDetail</a:Code>
<a:CreationDate>1512439075</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o53"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o62">
<a:ObjectID>21118388-F43A-4890-9A08-FD6721E015ED</a:ObjectID>
<a:Name>idx_Ctrl_CtrlAttrDetail</a:Name>
<a:Code>idx_Ctrl_CtrlAttrDetail</a:Code>
<a:CreationDate>1512439498</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o63">
<a:ObjectID>93F524E0-E5F6-4F3C-9C5F-25D1FE056AF5</a:ObjectID>
<a:CreationDate>1512439533</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o54"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o61"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o61"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o20">
<a:ObjectID>4288C567-1FB2-4189-9BAE-BF2BB308989A</a:ObjectID>
<a:Name>t_Ctrl_CtrlEvent</a:Name>
<a:Code>t_Ctrl_CtrlEvent</a:Code>
<a:CreationDate>1512441332</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件事件</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o64">
<a:ObjectID>B6AC9383-274B-4F51-9AE3-188B273DCB72</a:ObjectID>
<a:Name>FEntryID</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1512441430</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件ID</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>E5407880-4CDD-430A-ADB7-993C285FD22D</a:ObjectID>
<a:Name>FEventDetailID</a:Name>
<a:Code>FEventDetailID</a:Code>
<a:CreationDate>1512441358</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件事件ID</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>19167C17-57FA-423E-9973-E3EEDE7213DA</a:ObjectID>
<a:Name>FEEVENTSEQ</a:Name>
<a:Code>FEEVENTSEQ</a:Code>
<a:CreationDate>1512441358</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>事件次序</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>B3787B8E-4DDE-457C-9D2E-DB102014537A</a:ObjectID>
<a:Name>FEVENTID</a:Name>
<a:Code>FEVENTID</a:Code>
<a:CreationDate>1512441358</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>事件ID</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>E94E77D1-460B-4877-8015-3D1423485EA2</a:ObjectID>
<a:Name>FEVENTNAME</a:Name>
<a:Code>FEVENTNAME</a:Code>
<a:CreationDate>1512441358</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>事件名称</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>72040ACD-928F-40F8-A007-54581C7B6C64</a:ObjectID>
<a:Name>FEVENTARGS</a:Name>
<a:Code>FEVENTARGS</a:Code>
<a:CreationDate>1512441358</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>事件参数</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>71460749-3E2E-4925-96BE-4AA0F77DCBF9</a:ObjectID>
<a:Name>FEVENTDES</a:Name>
<a:Code>FEVENTDES</a:Code>
<a:CreationDate>1512441358</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>事件描述</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o71">
<a:ObjectID>DD810D22-8BF7-4AEA-8EB5-8772C7D92686</a:ObjectID>
<a:Name>pk_CtrlEvent</a:Name>
<a:Code>pk_CtrlEvent</a:Code>
<a:CreationDate>1512441678</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o65"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o72">
<a:ObjectID>2793E586-FF7C-4D52-AD68-FD2277BE1434</a:ObjectID>
<a:Name>idx_CtrlEvent</a:Name>
<a:Code>idx_CtrlEvent</a:Code>
<a:CreationDate>1512441942</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o73">
<a:ObjectID>740F4DA2-5ABE-4D48-A8CE-301735878C85</a:ObjectID>
<a:CreationDate>1512441956</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o64"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o71"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o71"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o16">
<a:ObjectID>1232C1B9-F4BA-4552-B2AD-5DDE5038E539</a:ObjectID>
<a:Name>t_Ctrl_CtrlFunDetail</a:Name>
<a:Code>t_Ctrl_CtrlFunDetail</a:Code>
<a:CreationDate>1512439702</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件方法</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o74">
<a:ObjectID>221B8AB0-2DE9-4A68-A77A-8EC0CE6E9B87</a:ObjectID>
<a:Name>FEntryID</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1512439823</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件ID</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>1C3D6BA2-AE5B-4FA6-BA80-5529B1736BDD</a:ObjectID>
<a:Name>FFunDetailId</a:Name>
<a:Code>FFunDetailId</a:Code>
<a:CreationDate>1512439729</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment> 控件方法ID</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>3F531934-7BE9-4018-A479-95A86B321F64</a:ObjectID>
<a:Name>FFUNSEQ</a:Name>
<a:Code>FFUNSEQ</a:Code>
<a:CreationDate>1512439729</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>次序</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>96BFB99B-EEE3-444D-B2C2-96197DF811C0</a:ObjectID>
<a:Name>FFUNID</a:Name>
<a:Code>FFUNID</a:Code>
<a:CreationDate>1512439729</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>方法</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o78">
<a:ObjectID>C0F8F51C-936F-45BC-B8C7-2EFE0B767870</a:ObjectID>
<a:Name>FFUNNAME</a:Name>
<a:Code>FFUNNAME</a:Code>
<a:CreationDate>1512439729</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>方法名称</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>1117E65C-3552-4A44-B664-E27860B61E04</a:ObjectID>
<a:Name>FFUNRETURNVALUE</a:Name>
<a:Code>FFUNRETURNVALUE</a:Code>
<a:CreationDate>1512439729</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>方法返回值</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
<o:Column Id="o80">
<a:ObjectID>652AC003-C62F-4B20-A61C-EBADC59418E4</a:ObjectID>
<a:Name>FFUNDEFAULTVALUE</a:Name>
<a:Code>FFUNDEFAULTVALUE</a:Code>
<a:CreationDate>1512439729</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>方法默认值</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
<o:Column Id="o81">
<a:ObjectID>8B41901B-CFB4-40FA-8977-2BFB6E622569</a:ObjectID>
<a:Name>FFUNDES</a:Name>
<a:Code>FFUNDES</a:Code>
<a:CreationDate>1512439729</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>方法描述</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o82">
<a:ObjectID>E5C2FED2-0E9B-4920-AB89-FC0625FBD850</a:ObjectID>
<a:Name>pk_CtrlFunDetail</a:Name>
<a:Code>pk_CtrlFunDetail</a:Code>
<a:CreationDate>1512439901</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o75"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o83">
<a:ObjectID>4B64B1CB-AF06-43DA-8BC6-E22CC950C068</a:ObjectID>
<a:Name>idx_CtrlFunDetail</a:Name>
<a:Code>idx_CtrlFunDetail</a:Code>
<a:CreationDate>1512440126</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o84">
<a:ObjectID>D8E27322-F865-4E7A-B1F6-D08D95AE418B</a:ObjectID>
<a:CreationDate>1512440146</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o74"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o82"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o82"/>
</c:ClusterObject>
</o:Table>
<o:Table Id="o18">
<a:ObjectID>17500089-EE5C-448D-BE79-3133CC808DDB</a:ObjectID>
<a:Name>t_Ctrl_FunArgs</a:Name>
<a:Code>t_Ctrl_FunArgs</a:Code>
<a:CreationDate>1512440564</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件方法参数</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o85">
<a:ObjectID>16B5C769-82B3-4320-87FF-4612DF9C1121</a:ObjectID>
<a:Name>FFunArgDetailID</a:Name>
<a:Code>FFunArgDetailID</a:Code>
<a:CreationDate>1512440585</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件方法参数ID</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>4379EE48-EB02-4A6E-B025-E9A7D7DBEAB8</a:ObjectID>
<a:Name>FFunDetailId</a:Name>
<a:Code>FFunDetailId</a:Code>
<a:CreationDate>1512440585</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>控件方ID</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>3DBF7D1D-F9BF-4EA3-A555-B9777E148254</a:ObjectID>
<a:Name>FFUNARGSSEQ</a:Name>
<a:Code>FFUNARGSSEQ</a:Code>
<a:CreationDate>1512440585</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>次序</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>71D20AB0-1149-49D0-B30D-053E8C6FF3F3</a:ObjectID>
<a:Name>FFUNARGID</a:Name>
<a:Code>FFUNARGID</a:Code>
<a:CreationDate>1512440585</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>参数</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>7F391083-7E7B-4F7B-99FA-4D3AADCCF4BE</a:ObjectID>
<a:Name>FFUNARGNAME</a:Name>
<a:Code>FFUNARGNAME</a:Code>
<a:CreationDate>1512440585</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>参数名称</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>F751C1A2-7B80-4C35-A3A5-1BFEFE3A80B6</a:ObjectID>
<a:Name>FFUNARGTYPE</a:Name>
<a:Code>FFUNARGTYPE</a:Code>
<a:CreationDate>1512440585</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>参数类型</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>02C0E47B-4403-4A56-8482-2A36CB05FFC0</a:ObjectID>
<a:Name>FFUNARGDEFVAL</a:Name>
<a:Code>FFUNARGDEFVAL</a:Code>
<a:CreationDate>1512440585</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>参数默认值</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>5218EC12-F19B-4860-9C9B-814910CD4D51</a:ObjectID>
<a:Name>FFUNARGDES</a:Name>
<a:Code>FFUNARGDES</a:Code>
<a:CreationDate>1512440585</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<a:Comment>方法参数描述</a:Comment>
<a:DefaultValue>&#39; &#39;</a:DefaultValue>
<a:DataType>varchar(1500)</a:DataType>
<a:Length>1500</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o93">
<a:ObjectID>1D4A8390-25D9-4439-9C24-7D10DD0747E0</a:ObjectID>
<a:Name>pk_Ctrl_FunArgs</a:Name>
<a:Code>pk_Ctrl_FunArgs</a:Code>
<a:CreationDate>1512440990</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o85"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o94">
<a:ObjectID>D90838D7-09F3-41F1-8AF0-95FBA5A7CBCE</a:ObjectID>
<a:Name>idx_Ctrl_FunArgs</a:Name>
<a:Code>idx_Ctrl_FunArgs</a:Code>
<a:CreationDate>1512441293</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o95">
<a:ObjectID>6C1D99A0-FF39-4ADB-B348-3247F480C5A6</a:ObjectID>
<a:CreationDate>1512441312</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1512442016</a:ModificationDate>
<a:Modifier>rd_wenjun_wang</a:Modifier>
<c:Column>
<o:Column Ref="o86"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:PrimaryKey>
<o:Key Ref="o93"/>
</c:PrimaryKey>
<c:ClusterObject>
<o:Key Ref="o93"/>
</c:ClusterObject>
</o:Table>
</c:Tables>
<c:DefaultGroups>
<o:Group Id="o96">
<a:ObjectID>9B2CBA36-CC51-401F-B0DC-3D0B63E81604</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1510881136</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1510881136</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o97">
<a:ObjectID>CB67653F-4F87-4F6D-B7D7-6D7E73F0F775</a:ObjectID>
<a:Name>Microsoft SQL Server 2008</a:Name>
<a:Code>MSSQLSRV2008</a:Code>
<a:CreationDate>1510881136</a:CreationDate>
<a:Creator>rd_szchaoming_huang</a:Creator>
<a:ModificationDate>1510881136</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/sqlsv2k8.xdb</a:TargetModelURL>
<a:TargetModelID>F5C20738-B05A-4F70-BC90-9B5EB9437766</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
<o:TargetModel Id="o98">
<a:ObjectID>7E40A1F4-5E2B-40E7-B30C-86D76B8EF492</a:ObjectID>
<a:Name>CTRL控件库</a:Name>
<a:Code>CTRL</a:Code>
<a:RepositoryID>{5C34CC33-BB05-4053-9571-E184DEFF2D78}</a:RepositoryID>
<a:ExtractionID>1188772</a:ExtractionID>
<a:CreationDate>1511233075</a:CreationDate>
<a:Creator>rd_wenjun_wang</a:Creator>
<a:ModificationDate>1546929710</a:ModificationDate>
<a:Modifier>rd_szchaoming_huang</a:Modifier>
<a:TargetModelURL>file:///.</a:TargetModelURL>
<a:TargetModelID>F7653CAA-C234-49CB-9EBA-D39F9FA62589</a:TargetModelID>
<a:TargetModelClassID>CDE44E21-9669-11D1-9914-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o10"/>
<o:Shortcut Ref="o12"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>