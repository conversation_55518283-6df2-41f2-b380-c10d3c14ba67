
SELECT 
    年月, 厂区, 部门, 工序, 工号, 姓名, 类型, 单价, 班组, 序号,
    [1], [2], [3], [4], [5], [6], [7], [8], [9], [10], [11], [12], [13], [14], [15], 
    [16], [17], [18], [19], [20], [21], [22], [23], [24], [25], [26], [27], [28], [29], [30], [31],
    产能类型, 产能总和, 绩效分数, status, 绩效占比,
    CASE 
        WHEN 厂区 = '泗阳华茂' and 工序 = '出炉' and 产能总和 > 114 and 绩效分数 is not null 
        THEN 1800+(产能总和-114)*20+产能总和*7.7*绩效分数
        WHEN 产能类型 = '计件产能汇总' AND ISNULL(绩效分数, 0) <> 0 
        THEN (产能总和 * 单价) * 绩效占比 * 绩效分数 + ((产能总和 * 单价) - (产能总和 * 单价) * 绩效占比)
        WHEN 产能类型 = '绩效产能汇总' or 绩效分数 is null 
        THEN 产能总和 * 单价
        ELSE 0
    END AS 薪资
FROM (
    SELECT 
        a.年月, a.厂区, a.部门, a.工序, 
        REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') as 工号, 
        a.姓名, d.类型, t.班组, t.序号, a.status,
        a.[1], a.[2], a.[3], a.[4], a.[5], a.[6], a.[7], a.[8], a.[9], a.[10], 
        a.[11], a.[12], a.[13], a.[14], a.[15], a.[16], a.[17], a.[18], a.[19], a.[20], 
        a.[21], a.[22], a.[23], a.[24], a.[25], a.[26], a.[27], a.[28], a.[29], a.[30], a.[31],
        CASE 
            WHEN ISNULL(d.类型, '') IN ('B', '') THEN '计件产能汇总' 
            WHEN d.类型 = 'A' THEN '绩效产能汇总' 
            ELSE '其他' 
        END AS 产能类型,
        
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[1]) = 1 
            AND LEN(a.[1]) <= 15 
            AND a.[1] NOT LIKE '%.%.%' 
            AND a.[1] NOT LIKE '%e%' 
            AND a.[1] NOT LIKE '%E%'
            THEN TRY_CAST(a.[1] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[2]) = 1 
            AND LEN(a.[2]) <= 15 
            AND a.[2] NOT LIKE '%.%.%' 
            AND a.[2] NOT LIKE '%e%' 
            AND a.[2] NOT LIKE '%E%'
            THEN TRY_CAST(a.[2] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[3]) = 1 
            AND LEN(a.[3]) <= 15 
            AND a.[3] NOT LIKE '%.%.%' 
            AND a.[3] NOT LIKE '%e%' 
            AND a.[3] NOT LIKE '%E%'
            THEN TRY_CAST(a.[3] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[4]) = 1 
            AND LEN(a.[4]) <= 15 
            AND a.[4] NOT LIKE '%.%.%' 
            AND a.[4] NOT LIKE '%e%' 
            AND a.[4] NOT LIKE '%E%'
            THEN TRY_CAST(a.[4] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[5]) = 1 
            AND LEN(a.[5]) <= 15 
            AND a.[5] NOT LIKE '%.%.%' 
            AND a.[5] NOT LIKE '%e%' 
            AND a.[5] NOT LIKE '%E%'
            THEN TRY_CAST(a.[5] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[6]) = 1 
            AND LEN(a.[6]) <= 15 
            AND a.[6] NOT LIKE '%.%.%' 
            AND a.[6] NOT LIKE '%e%' 
            AND a.[6] NOT LIKE '%E%'
            THEN TRY_CAST(a.[6] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[7]) = 1 
            AND LEN(a.[7]) <= 15 
            AND a.[7] NOT LIKE '%.%.%' 
            AND a.[7] NOT LIKE '%e%' 
            AND a.[7] NOT LIKE '%E%'
            THEN TRY_CAST(a.[7] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[8]) = 1 
            AND LEN(a.[8]) <= 15 
            AND a.[8] NOT LIKE '%.%.%' 
            AND a.[8] NOT LIKE '%e%' 
            AND a.[8] NOT LIKE '%E%'
            THEN TRY_CAST(a.[8] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[9]) = 1 
            AND LEN(a.[9]) <= 15 
            AND a.[9] NOT LIKE '%.%.%' 
            AND a.[9] NOT LIKE '%e%' 
            AND a.[9] NOT LIKE '%E%'
            THEN TRY_CAST(a.[9] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[10]) = 1 
            AND LEN(a.[10]) <= 15 
            AND a.[10] NOT LIKE '%.%.%' 
            AND a.[10] NOT LIKE '%e%' 
            AND a.[10] NOT LIKE '%E%'
            THEN TRY_CAST(a.[10] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[11]) = 1 
            AND LEN(a.[11]) <= 15 
            AND a.[11] NOT LIKE '%.%.%' 
            AND a.[11] NOT LIKE '%e%' 
            AND a.[11] NOT LIKE '%E%'
            THEN TRY_CAST(a.[11] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[12]) = 1 
            AND LEN(a.[12]) <= 15 
            AND a.[12] NOT LIKE '%.%.%' 
            AND a.[12] NOT LIKE '%e%' 
            AND a.[12] NOT LIKE '%E%'
            THEN TRY_CAST(a.[12] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[13]) = 1 
            AND LEN(a.[13]) <= 15 
            AND a.[13] NOT LIKE '%.%.%' 
            AND a.[13] NOT LIKE '%e%' 
            AND a.[13] NOT LIKE '%E%'
            THEN TRY_CAST(a.[13] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[14]) = 1 
            AND LEN(a.[14]) <= 15 
            AND a.[14] NOT LIKE '%.%.%' 
            AND a.[14] NOT LIKE '%e%' 
            AND a.[14] NOT LIKE '%E%'
            THEN TRY_CAST(a.[14] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[15]) = 1 
            AND LEN(a.[15]) <= 15 
            AND a.[15] NOT LIKE '%.%.%' 
            AND a.[15] NOT LIKE '%e%' 
            AND a.[15] NOT LIKE '%E%'
            THEN TRY_CAST(a.[15] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[16]) = 1 
            AND LEN(a.[16]) <= 15 
            AND a.[16] NOT LIKE '%.%.%' 
            AND a.[16] NOT LIKE '%e%' 
            AND a.[16] NOT LIKE '%E%'
            THEN TRY_CAST(a.[16] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[17]) = 1 
            AND LEN(a.[17]) <= 15 
            AND a.[17] NOT LIKE '%.%.%' 
            AND a.[17] NOT LIKE '%e%' 
            AND a.[17] NOT LIKE '%E%'
            THEN TRY_CAST(a.[17] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[18]) = 1 
            AND LEN(a.[18]) <= 15 
            AND a.[18] NOT LIKE '%.%.%' 
            AND a.[18] NOT LIKE '%e%' 
            AND a.[18] NOT LIKE '%E%'
            THEN TRY_CAST(a.[18] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[19]) = 1 
            AND LEN(a.[19]) <= 15 
            AND a.[19] NOT LIKE '%.%.%' 
            AND a.[19] NOT LIKE '%e%' 
            AND a.[19] NOT LIKE '%E%'
            THEN TRY_CAST(a.[19] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[20]) = 1 
            AND LEN(a.[20]) <= 15 
            AND a.[20] NOT LIKE '%.%.%' 
            AND a.[20] NOT LIKE '%e%' 
            AND a.[20] NOT LIKE '%E%'
            THEN TRY_CAST(a.[20] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[21]) = 1 
            AND LEN(a.[21]) <= 15 
            AND a.[21] NOT LIKE '%.%.%' 
            AND a.[21] NOT LIKE '%e%' 
            AND a.[21] NOT LIKE '%E%'
            THEN TRY_CAST(a.[21] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[22]) = 1 
            AND LEN(a.[22]) <= 15 
            AND a.[22] NOT LIKE '%.%.%' 
            AND a.[22] NOT LIKE '%e%' 
            AND a.[22] NOT LIKE '%E%'
            THEN TRY_CAST(a.[22] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[23]) = 1 
            AND LEN(a.[23]) <= 15 
            AND a.[23] NOT LIKE '%.%.%' 
            AND a.[23] NOT LIKE '%e%' 
            AND a.[23] NOT LIKE '%E%'
            THEN TRY_CAST(a.[23] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[24]) = 1 
            AND LEN(a.[24]) <= 15 
            AND a.[24] NOT LIKE '%.%.%' 
            AND a.[24] NOT LIKE '%e%' 
            AND a.[24] NOT LIKE '%E%'
            THEN TRY_CAST(a.[24] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[25]) = 1 
            AND LEN(a.[25]) <= 15 
            AND a.[25] NOT LIKE '%.%.%' 
            AND a.[25] NOT LIKE '%e%' 
            AND a.[25] NOT LIKE '%E%'
            THEN TRY_CAST(a.[25] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[26]) = 1 
            AND LEN(a.[26]) <= 15 
            AND a.[26] NOT LIKE '%.%.%' 
            AND a.[26] NOT LIKE '%e%' 
            AND a.[26] NOT LIKE '%E%'
            THEN TRY_CAST(a.[26] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[27]) = 1 
            AND LEN(a.[27]) <= 15 
            AND a.[27] NOT LIKE '%.%.%' 
            AND a.[27] NOT LIKE '%e%' 
            AND a.[27] NOT LIKE '%E%'
            THEN TRY_CAST(a.[27] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[28]) = 1 
            AND LEN(a.[28]) <= 15 
            AND a.[28] NOT LIKE '%.%.%' 
            AND a.[28] NOT LIKE '%e%' 
            AND a.[28] NOT LIKE '%E%'
            THEN TRY_CAST(a.[28] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[29]) = 1 
            AND LEN(a.[29]) <= 15 
            AND a.[29] NOT LIKE '%.%.%' 
            AND a.[29] NOT LIKE '%e%' 
            AND a.[29] NOT LIKE '%E%'
            THEN TRY_CAST(a.[29] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[30]) = 1 
            AND LEN(a.[30]) <= 15 
            AND a.[30] NOT LIKE '%.%.%' 
            AND a.[30] NOT LIKE '%e%' 
            AND a.[30] NOT LIKE '%E%'
            THEN TRY_CAST(a.[30] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) +
    
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[31]) = 1 
            AND LEN(a.[31]) <= 15 
            AND a.[31] NOT LIKE '%.%.%' 
            AND a.[31] NOT LIKE '%e%' 
            AND a.[31] NOT LIKE '%E%'
            THEN TRY_CAST(a.[31] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0) AS 产能总和,
        d.单价, p.绩效分数, p.绩效占比
    FROM 测试 a 
    LEFT JOIN [dbo].[单价] d ON a.厂区 = d.厂区 AND a.工序 = d.工序 
    LEFT JOIN [dbo].[绩效表] p ON REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') = REPLACE(REPLACE(p.工号, CHAR(13), ''), CHAR(10), '') 
        AND a.年月 = p.年月 AND ISNULL(d.类型, '') IN ('B', '') 
    LEFT JOIN [dbo].[人员信息表] t ON REPLACE(REPLACE(REPLACE(t.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') = REPLACE(REPLACE(REPLACE(a.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') 
        AND t.[工厂] = a.[厂区] and t.[部门] = a.[部门]
    GROUP BY 
        a.年月, a.厂区, a.部门, a.工序, a.status,
        REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), ''), 
        a.姓名, d.类型, d.单价, p.绩效分数, p.绩效占比, t.班组, t.序号, 
        a.[1], a.[2], a.[3], a.[4], a.[5], a.[6], a.[7], a.[8], a.[9], a.[10], 
        a.[11], a.[12], a.[13], a.[14], a.[15], a.[16], a.[17], a.[18], a.[19], a.[20], 
        a.[21], a.[22], a.[23], a.[24], a.[25], a.[26], a.[27], a.[28], a.[29], a.[30], a.[31]
) AS subquery
