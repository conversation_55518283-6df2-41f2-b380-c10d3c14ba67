-- 深度诊断工号关联问题
-- 针对具体的入库单进行详细分析

-- 诊断1：查看具体入库单的制单人信息
SELECT TOP 5
    '诊断1：入库单制单人详细信息' AS '诊断类型',
    h.FBILLNO AS '入库单号',
    h.FCREATORID AS '制单人ID',
    h.FCREATEDATE AS '制单日期',
    -- 用户表信息
    u.FUSERID AS '用户表_用户ID',
    u.FNAME AS '用户表_姓名',
    u.FUSERACCOUNT AS '用户表_账号',
    u.FEMPID AS '用户表_员工ID字段',
    -- 检查这个用户ID是否在操作员分录表中
    op1.FOPERATORID AS '操作员分录_操作员ID',
    op1.FNUMBER AS '操作员分录_工号',
    op1.FSTAFFID AS '操作员分录_员工ID',
    op1.FISUSE AS '操作员分录_是否启用',
    -- 检查通过员工ID关联的情况
    s1.FSTAFFID AS '员工表_员工ID',
    s1.FNUMBER AS '员工表_工号',
    s1.FNAME AS '员工表_姓名'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
LEFT JOIN T_BD_OPERATORENTRY op1 ON h.FCREATORID = op1.FOPERATORID AND op1.FISUSE = 1
LEFT JOIN T_BD_STAFF s1 ON u.FEMPID = s1.FSTAFFID
WHERE h.FBILLNO IS NOT NULL
ORDER BY h.FCREATEDATE DESC;

-- 诊断2：查看操作员分录表中的所有关联方式
SELECT TOP 10
    '诊断2：操作员分录表关联分析' AS '诊断类型',
    op.FOPERATORID AS '操作员ID',
    op.FNUMBER AS '操作员工号',
    op.FSTAFFID AS '关联员工ID',
    op.FISUSE AS '是否启用',
    -- 通过员工ID关联员工表
    s.FSTAFFID AS '员工表ID',
    s.FNUMBER AS '员工工号',
    s.FNAME AS '员工姓名',
    -- 检查是否有用户使用这个操作员ID
    u.FUSERID AS '用户ID',
    u.FNAME AS '用户姓名'
FROM T_BD_OPERATORENTRY op
LEFT JOIN T_BD_STAFF s ON op.FSTAFFID = s.FSTAFFID
LEFT JOIN T_SEC_USER u ON op.FOPERATORID = u.FUSERID
WHERE op.FISUSE = 1 
    AND op.FNUMBER IS NOT NULL
ORDER BY op.FNUMBER;

-- 诊断3：查看用户表中员工ID字段的使用情况
SELECT TOP 10
    '诊断3：用户表员工ID字段分析' AS '诊断类型',
    u.FUSERID AS '用户ID',
    u.FNAME AS '用户姓名',
    u.FUSERACCOUNT AS '用户账号',
    u.FEMPID AS '用户员工ID字段',
    -- 通过员工ID关联员工表
    s.FSTAFFID AS '员工表ID',
    s.FNUMBER AS '员工工号',
    s.FNAME AS '员工姓名',
    -- 检查是否有对应的操作员分录
    op.FOPERATORID AS '操作员分录_操作员ID',
    op.FNUMBER AS '操作员分录_工号'
FROM T_SEC_USER u
LEFT JOIN T_BD_STAFF s ON u.FEMPID = s.FSTAFFID
LEFT JOIN T_BD_OPERATORENTRY op ON s.FSTAFFID = op.FSTAFFID AND op.FISUSE = 1
WHERE u.FEMPID IS NOT NULL
ORDER BY u.FUSERID;

-- 诊断4：查看员工表和操作员分录表的对应关系
SELECT TOP 10
    '诊断4：员工表与操作员分录对应关系' AS '诊断类型',
    s.FSTAFFID AS '员工ID',
    s.FNUMBER AS '员工工号',
    s.FNAME AS '员工姓名',
    -- 对应的操作员分录
    op.FOPERATORID AS '操作员ID',
    op.FNUMBER AS '操作员工号',
    op.FISUSE AS '是否启用',
    -- 对应的用户
    u1.FUSERID AS '用户ID_通过员工ID',
    u1.FNAME AS '用户姓名_通过员工ID',
    u2.FUSERID AS '用户ID_通过操作员ID',
    u2.FNAME AS '用户姓名_通过操作员ID'
FROM T_BD_STAFF s
LEFT JOIN T_BD_OPERATORENTRY op ON s.FSTAFFID = op.FSTAFFID AND op.FISUSE = 1
LEFT JOIN T_SEC_USER u1 ON s.FSTAFFID = u1.FEMPID
LEFT JOIN T_SEC_USER u2 ON op.FOPERATORID = u2.FUSERID
WHERE s.FNUMBER IS NOT NULL
ORDER BY s.FNUMBER;

-- 诊断5：针对具体入库单，尝试所有可能的关联路径
SELECT TOP 5
    '诊断5：具体入库单的所案关联路径' AS '诊断类型',
    h.FBILLNO AS '入库单号',
    h.FCREATORID AS '制单人ID',
    u.FNAME AS '用户姓名',
    -- 路径1：直接用用户ID关联操作员分录
    '路径1' AS '关联路径1',
    op1.FNUMBER AS '路径1_工号',
    -- 路径2：用户ID→员工ID→员工表
    '路径2' AS '关联路径2', 
    s1.FNUMBER AS '路径2_工号',
    -- 路径3：用户ID→员工ID→操作员分录
    '路径3' AS '关联路径3',
    op2.FNUMBER AS '路径3_工号',
    -- 路径4：检查是否有其他字段可以关联
    u.FUSERACCOUNT AS '用户账号',
    CAST(u.FUSERID AS NVARCHAR(50)) AS '用户ID字符串'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
-- 路径1：直接关联操作员分录
LEFT JOIN T_BD_OPERATORENTRY op1 ON u.FUSERID = op1.FOPERATORID AND op1.FISUSE = 1
-- 路径2：通过员工ID关联员工表
LEFT JOIN T_BD_STAFF s1 ON u.FEMPID = s1.FSTAFFID
-- 路径3：通过员工ID关联操作员分录
LEFT JOIN T_BD_OPERATORENTRY op2 ON s1.FSTAFFID = op2.FSTAFFID AND op2.FISUSE = 1
WHERE h.FBILLNO IS NOT NULL
ORDER BY h.FCREATEDATE DESC;
