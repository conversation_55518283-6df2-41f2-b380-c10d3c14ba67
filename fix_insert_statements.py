#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正INSERT语句以符合SQL Server表结构定义
- 将工号字段改为字符串格式（添加引号）
- 将数值字段改为nvarchar格式（添加引号，除了NULL值）
- 添加status字段到INSERT语句中
"""

import re

def fix_insert_statement(line):
    """修正单个INSERT语句"""
    if not line.strip().startswith('INSERT INTO [测试]'):
        return line
    
    # 使用正则表达式找到VALUES部分
    match = re.match(r'(INSERT INTO \[测试\] \([^)]+\)) VALUES \((.+)\);', line)
    if not match:
        return line
    
    insert_part = match.group(1)
    values_part = match.group(2)
    
    # 检查是否已经包含status字段
    if '[status]' not in insert_part:
        # 在INSERT字段列表中添加[status]字段
        insert_part = insert_part.replace(', [31])', ', [31], [status])')
    
    # 分割值部分
    values = []
    current_value = ""
    in_quotes = False
    quote_char = None
    paren_count = 0
    
    for char in values_part:
        if char in ["'", '"'] and not in_quotes:
            in_quotes = True
            quote_char = char
            current_value += char
        elif char == quote_char and in_quotes:
            in_quotes = False
            quote_char = None
            current_value += char
        elif char == ',' and not in_quotes and paren_count == 0:
            values.append(current_value.strip())
            current_value = ""
        elif char == '(' and not in_quotes:
            paren_count += 1
            current_value += char
        elif char == ')' and not in_quotes:
            paren_count -= 1
            current_value += char
        else:
            current_value += char
    
    if current_value.strip():
        values.append(current_value.strip())
    
    # 修正值格式
    fixed_values = []
    for i, value in enumerate(values):
        value = value.strip()
        
        if i == 4:  # 工号字段（第5个字段，索引为4）
            if not value.startswith("'") and value != 'NULL':
                value = f"'{value}'"
        elif i >= 6 and i <= 36:  # 日期字段 [1] 到 [31]（索引6到36）
            if value != 'NULL' and not value.startswith("'"):
                # 如果是数字，添加引号
                try:
                    float(value)
                    value = f"'{value}'"
                except ValueError:
                    # 已经是字符串或其他格式，保持不变
                    pass
        
        fixed_values.append(value)
    
    # 确保有status字段值
    if len(fixed_values) == 37:  # 原来没有status字段
        fixed_values.append('1')
    elif len(fixed_values) == 38:  # 已经有status字段
        pass
    else:
        # 字段数量不对，保持原样
        return line
    
    # 重新组合INSERT语句
    new_values = ', '.join(fixed_values)
    return f"{insert_part} VALUES ({new_values});"

def main():
    """主函数"""
    input_file = 'insert_statements.sql'
    output_file = 'insert_statements_fixed.sql'
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        fixed_lines = []
        for line in lines:
            fixed_line = fix_insert_statement(line)
            fixed_lines.append(fixed_line)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.writelines(fixed_lines)
        
        print(f"修正完成！输出文件：{output_file}")
        
    except Exception as e:
        print(f"处理文件时出错：{e}")

if __name__ == "__main__":
    main() 