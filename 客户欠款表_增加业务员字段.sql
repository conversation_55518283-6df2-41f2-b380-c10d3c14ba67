-- 客户欠款表创建SQL - 增加业务员字段版本
-- =====================================================

-- 删除原表（如果存在）
-- DROP TABLE IF EXISTS T_CUSTOMER_DEBT;

-- =====================================================
-- 客户欠款表 (T_CUSTOMER_DEBT) - 包含业务员字段
-- =====================================================
CREATE TABLE T_CUSTOMER_DEBT (
    FID BIGINT IDENTITY(1,1) PRIMARY KEY,               -- 主键ID (自增长)
    F日期 DATE NOT NULL,                                 -- 日期
    F客户名称 NVARCHAR(255) NOT NULL,                     -- 客户名称
    F业务员 NVARCHAR(100) NULL,                          -- 业务员
    F当前欠款 DECIMAL(18,2) NOT NULL DEFAULT 0           -- 当前欠款金额
);

-- =====================================================
-- 创建索引以提高查询性能
-- =====================================================
CREATE INDEX IX_T_CUSTOMER_DEBT_DATE ON T_CUSTOMER_DEBT(F日期);
CREATE INDEX IX_T_CUSTOMER_DEBT_CUSTOMER ON T_CUSTOMER_DEBT(F客户名称);
CREATE INDEX IX_T_CUSTOMER_DEBT_SALESMAN ON T_CUSTOMER_DEBT(F业务员);
CREATE INDEX IX_T_CUSTOMER_DEBT_AMOUNT ON T_CUSTOMER_DEBT(F当前欠款);

-- =====================================================
-- 如果需要修改现有表结构，使用以下ALTER语句
-- =====================================================
/*
-- 为现有表添加业务员字段
ALTER TABLE T_CUSTOMER_DEBT ADD F业务员 NVARCHAR(100) NULL;

-- 为新字段创建索引
CREATE INDEX IX_T_CUSTOMER_DEBT_SALESMAN ON T_CUSTOMER_DEBT(F业务员);

-- 更新现有数据的业务员字段（示例）
UPDATE T_CUSTOMER_DEBT SET F业务员 = '张三' WHERE F客户名称 LIKE '%杭州%';
UPDATE T_CUSTOMER_DEBT SET F业务员 = '李四' WHERE F客户名称 LIKE '%上海%';
UPDATE T_CUSTOMER_DEBT SET F业务员 = '王五' WHERE F客户名称 LIKE '%北京%';
*/

-- =====================================================
-- 插入示例数据（包含业务员信息）
-- =====================================================
INSERT INTO T_CUSTOMER_DEBT (F日期, F客户名称, F业务员, F当前欠款) VALUES
('2025-04-30', '杭州诚营农业发展有限公司', '张三', -200.00),
('2025-04-30', '杭州方帅农业开发有限公司', '张三', -12296.00),
('2025-04-30', '杭州农副产品物流中心良渚蔬菜批发市场荣叶蔬菜商行', '张三', 316.00),
('2025-04-30', '杭州萧山中正菌业批发部', '张三', 35276.00),
('2025-04-30', '杭州远润农产品有限公司', '张三', 0.00),
('2025-04-30', '杭州琦丰农业发展有限公司', '张三', 30420.00),
('2025-04-30', '嘉兴市闻川旅行社有限公司', '李四', -4310.00),
('2025-04-30', '宁波市鄞州中河锦强食用农产品店', '李四', 0.00),
('2025-04-30', '平湖市吴树叶蔬菜批发部', '李四', -8446.00),
('2025-04-30', '平湖市轶轶食用菌专业合作社', '李四', 8415.00),
('2025-04-30', '衢州市益美农产品有限公司', '王五', 4.00),
('2025-04-30', '泉州市振祥农业科技有限公司', '王五', 0.00),
('2025-04-30', '台州市椒江鑫润农产品有限公司', '王五', 0.00),
('2025-04-30', '上海德添农产品有限公司', '赵六', 0.00),
('2025-04-30', '上海永大菌业有限公司', '赵六', 0.00),
('2025-04-30', '温州市古逸农业科技有限公司', '钱七', 49395.00),
('2025-04-30', '义乌市农业开发有限公司', '钱七', 0.00),
('2025-04-30', '义乌市旭珍蔬菜商行', '钱七', -10088.00),
('2025-04-30', '浙江庆元尚亿农业有限公司', '孙八', -39575.00),
('2025-04-30', '海盐县元通街道益群副食品店', '孙八', 0.00);

-- =====================================================
-- 查询示例
-- =====================================================
-- 查询所有客户欠款（包含业务员）
SELECT
    F日期 AS '日期',
    F客户名称 AS '客户名',
    F业务员 AS '业务员',
    F当前欠款 AS '截至欠款'
FROM T_CUSTOMER_DEBT
ORDER BY F当前欠款 DESC;

-- 按业务员汇总客户欠款
SELECT
    F业务员 AS '业务员',
    COUNT(*) AS '客户数量',
    SUM(F当前欠款) AS '总欠款金额',
    AVG(F当前欠款) AS '平均欠款金额'
FROM T_CUSTOMER_DEBT
WHERE F业务员 IS NOT NULL
GROUP BY F业务员
ORDER BY SUM(F当前欠款) DESC;

-- 查询指定业务员的客户欠款
SELECT
    F日期 AS '日期',
    F客户名称 AS '客户名',
    F业务员 AS '业务员',
    F当前欠款 AS '截至欠款'
FROM T_CUSTOMER_DEBT
WHERE F业务员 = '张三'
ORDER BY F当前欠款 DESC;

-- =====================================================
-- 说明文档
-- =====================================================
/*
表结构说明：

T_CUSTOMER_DEBT - 客户欠款表（增加业务员字段）
- FID: 主键ID (自增长)
- F日期: 日期字段，记录欠款统计日期
- F客户名称: 客户名称，最大255个字符
- F业务员: 业务员名称，最大100个字符，允许为空
- F当前欠款: 当前欠款金额，精度为18位数字，2位小数

索引说明：
- 在日期、客户名称、业务员、欠款金额字段上创建了索引以提高查询性能

新增功能：
1. 可以按业务员查询和汇总客户欠款
2. 可以分析每个业务员的客户数量和欠款情况
3. 支持业务员维度的数据分析

使用说明：
1. 如果是新建表，直接执行CREATE TABLE语句
2. 如果是修改现有表，使用注释中的ALTER TABLE语句
3. 插入数据时需要包含业务员字段
4. 可以使用提供的查询示例进行数据分析
*/
