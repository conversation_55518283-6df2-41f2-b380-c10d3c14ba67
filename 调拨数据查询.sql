    SELECT
        -- 物料信息
        mat_out_base.FNUMBER AS '调出物料编码',
        mat_out.FNAME AS '调出物料名称',
        mat_out.FSPECIFICATION AS '调出物料规格',

        -- 仓库信息
        stock_out.FNAME AS '调出仓库',
        stock_in.FNAME AS '调入仓库',

        -- 数量汇总信息
        SUM(e.FQty) AS '调拨数量汇总',
        SUM(e.FSecQty) AS '辅助单位数量汇总',
        COUNT(*) AS '调拨次数'

    FROM T_STK_STKTRANSFERIN h
    INNER JOIN T_STK_STKTRANSFERINENTRY e ON h.FID = e.FID

    -- 关联调出组织
    LEFT JOIN t_org_orgfunc_l org_out ON h.FStockOutOrgID = org_out.FORGFUNCID
        AND org_out.FLOCALEID = 2052  -- 中文

    -- 关联调入组织
    LEFT JOIN t_org_orgfunc_l org_in ON h.FSTOCKORGID = org_in.FORGFUNCID
        AND org_in.FLOCALEID = 2052  -- 中文

    -- 关联调出物料主表
    LEFT JOIN T_BD_MATERIAL mat_out_base ON e.FSrcMaterialID = mat_out_base.FMATERIALID

    -- 关联调出物料多语言
    LEFT JOIN T_BD_MATERIAL_L mat_out ON e.FSrcMaterialID = mat_out.FMATERIALID
        AND mat_out.FLOCALEID = 2052  -- 中文

    -- 关联调入物料主表
    LEFT JOIN T_BD_MATERIAL mat_in_base ON e.FMaterialID = mat_in_base.FMATERIALID

    -- 关联调入物料多语言
    LEFT JOIN T_BD_MATERIAL_L mat_in ON e.FMaterialID = mat_in.FMATERIALID
        AND mat_in.FLOCALEID = 2052  -- 中文

    -- 关联调出仓库
    LEFT JOIN t_BD_Stock_L stock_out ON e.FSrcStockID = stock_out.FStockId
        AND stock_out.FLocaleId = 2052  -- 中文

    -- 关联调入仓库
    LEFT JOIN t_BD_Stock_L stock_in ON e.FDestStockID = stock_in.FStockId
        AND stock_in.FLocaleId = 2052  -- 中文

    WHERE 1=1
        -- 可以根据需要添加筛选条件
        -- AND h.FDATE >= '2024-01-01'  -- 日期筛选
        AND h.FDOCUMENTSTATUS = 'C'  -- 只查询已审核的单据
        AND stock_out.FNAME LIKE  '%成品%'

    -- 按调出物料和调出仓库分组汇总
    GROUP BY
        mat_out_base.FNUMBER,
        mat_out.FNAME,
        mat_out.FSPECIFICATION,
        stock_out.FNAME,
        stock_in.FNAME

    ORDER BY stock_out.FNAME, mat_out_base.FNUMBER