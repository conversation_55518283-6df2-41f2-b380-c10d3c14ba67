-- 查看收款单相关表的结构
-- 请在数据库中执行以下查询来确定正确的字段名

-- 1. 查看收款单主表结构
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'T_AR_RECEIVEBILL'
ORDER BY ORDINAL_POSITION;

-- 2. 查看收款单分录表结构  
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'T_AR_RECEIVEBILLENTRY'
ORDER BY ORDINAL_POSITION;

-- 3. 查看收款单主表前10条数据（了解数据格式）
SELECT TOP 10 * FROM T_AR_RECEIVEBILL;

-- 4. 查看收款单分录表前10条数据
SELECT TOP 10 * FROM T_AR_RECEIVEBILLENTRY;

-- 5. 查找包含"客户"相关的字段
SELECT COLUMN_NAME, TABLE_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME IN ('T_AR_RECEIVEBILL', 'T_AR_RECEIVEBILLENTRY')
AND (COLUMN_NAME LIKE '%CUST%' OR COLUMN_NAME LIKE '%客户%');

-- 6. 查找包含"金额"相关的字段
SELECT COLUMN_NAME, TABLE_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME IN ('T_AR_RECEIVEBILL', 'T_AR_RECEIVEBILLENTRY')
AND (COLUMN_NAME LIKE '%AMOUNT%' OR COLUMN_NAME LIKE '%金额%' OR COLUMN_NAME LIKE '%AMT%');

-- 7. 查找包含"日期"相关的字段
SELECT COLUMN_NAME, TABLE_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME IN ('T_AR_RECEIVEBILL', 'T_AR_RECEIVEBILLENTRY')
AND (COLUMN_NAME LIKE '%DATE%' OR COLUMN_NAME LIKE '%日期%' OR COLUMN_NAME LIKE '%TIME%');
