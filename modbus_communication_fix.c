/* 修复后的 Modbus 通信代码 */
#include <modbus.h>
#include <errno.h>
#ifdef _WIN32
#include <winsock2.h>
#endif

/* 发送请求/响应 */
static int send_msg(modbus_t *ctx, uint8_t *msg, int msg_length)
{
    int rc;
    int retry_count = 0;
    const int max_retries = 3; // 限制重试次数
    
    msg_length = ctx->backend->send_msg_pre(msg, msg_length);
    
    if (ctx->debug) {
        for (int i = 0; i < msg_length; i++) {
            fprintf(GetFileHandler(), "[%.2X]", msg[i]);
        }
        fprintf(GetFileHandler(), "\n");
    }
    
    /* 在恢复模式下，写命令将被执行直到成功！默认禁用。 */
    do {
        rc = ctx->backend->send(ctx, msg, msg_length);
        if (rc == -1) {
            fprintf(GetFileHandler(), "ctx->backend->send_error\n");
            _error_print(ctx, NULL);
            
            if (ctx->error_recovery & MODBUS_ERROR_RECOVERY_LINK) {
#ifdef _WIN32
                const int wsa_err = WSAGetLastError();
                // 修复：添加缺失的右括号，并改进错误处理
                if (wsa_err == WSAENETRESET || wsa_err == WSAENOTCONN ||
                    wsa_err == WSAENOTSOCK || wsa_err == WSAESHUTDOWN ||
                    wsa_err == WSAEHOSTUNREACH || wsa_err == WSAECONNABORTED ||
                    wsa_err == WSAECONNRESET || wsa_err == WSAETIMEDOUT) {
                    
                    // 安全关闭连接
                    modbus_close(ctx);
                    
                    // 等待一段时间再重新连接
                    _sleep_response_timeout(ctx);
                    
                    // 尝试重新连接
                    int connect_result = modbus_connect(ctx);
                    if (connect_result == -1) {
                        fprintf(GetFileHandler(), "重新连接失败\n");
                        // 如果重新连接失败，跳出循环
                        break;
                    }
                } else {
                    _sleep_response_timeout(ctx);
                    modbus_flush(ctx);
                }
#else
                int saved_errno = errno;
                
                if ((errno == EBADF || errno == ECONNRESET || errno == EPIPE)) {
                    modbus_close(ctx);
                    _sleep_response_timeout(ctx);
                    
                    int connect_result = modbus_connect(ctx);
                    if (connect_result == -1) {
                        fprintf(GetFileHandler(), "重新连接失败\n");
                        break;
                    }
                } else {
                    _sleep_response_timeout(ctx);
                    modbus_flush(ctx);
                }
                
                errno = saved_errno;
#endif
            }
        }
        
        // 增加重试计数器防止无限循环
        retry_count++;
        if (retry_count >= max_retries) {
            fprintf(GetFileHandler(), "达到最大重试次数，退出循环\n");
            break;
        }
        
    } while ((ctx->error_recovery & MODBUS_ERROR_RECOVERY_LINK) && rc == -1);
    
    return rc;
}

/* 改进的连接管理函数 */
int safe_modbus_connect(modbus_t *ctx) {
    if (!ctx) {
        return -1;
    }
    
    // 检查是否已经连接
    if (ctx->s != -1) {
        // 已经有活动连接，先关闭
        modbus_close(ctx);
    }
    
    int result = modbus_connect(ctx);
    if (result == -1) {
        fprintf(GetFileHandler(), "Modbus连接失败: %s\n", modbus_strerror(errno));
    } else {
        fprintf(GetFileHandler(), "Modbus连接成功\n");
    }
    
    return result;
}

/* 改进的错误恢复函数 */
int recover_connection(modbus_t *ctx) {
    if (!ctx) {
        return -1;
    }
    
    fprintf(GetFileHandler(), "开始连接恢复过程...\n");
    
    // 强制关闭当前连接
    modbus_close(ctx);
    
    // 等待一段时间
    _sleep_response_timeout(ctx);
    
    // 尝试重新连接
    return safe_modbus_connect(ctx);
} 