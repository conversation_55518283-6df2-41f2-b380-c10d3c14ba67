import pyodbc
import pandas as pd

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

def connect_to_database():
    """连接到SQL Server数据库"""
    connection_string = f"""
    DRIVER={{{DRIVER}}};
    SERVER={SERVER};
    DATABASE={DATABASE};
    UID={USERNAME};
    PWD={PASSWORD};
    TrustServerCertificate=yes;
    """
    try:
        conn = pyodbc.connect(connection_string)
        print("数据库连接成功！")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def main():
    conn = connect_to_database()
    if not conn:
        return
    
    try:
        # 1. 尝试通过FPAYORGID关联客户
        print("=== 1. 尝试通过FPAYORGID关联客户 ===")
        payorg_query = """
        SELECT TOP 10
            r.FID,
            r.FBILLN<PERSON>,
            r.FDATE,
            r.FPAYORGID,
            c_l.FNAME as 客户名称,
            re.FSETTLERECAMOUNT
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        LEFT JOIN T_BD_CUSTOMER c ON r.FPAYORGID = c.FCUSTID
        LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        ORDER BY r.FDATE DESC
        """
        payorg_df = pd.read_sql(payorg_query, conn)
        print(payorg_df)
        
        # 2. 检查组织表结构
        print("\n=== 2. 检查组织表是否存在 ===")
        org_tables_query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME LIKE '%ORG%' OR TABLE_NAME LIKE '%组织%'
        ORDER BY TABLE_NAME
        """
        org_tables = pd.read_sql(org_tables_query, conn)
        print("组织相关表:")
        print(org_tables.head(20))
        
        # 3. 尝试通过组织表关联
        print("\n=== 3. 尝试通过组织表关联客户 ===")
        try:
            org_query = """
            SELECT TOP 10
                r.FID,
                r.FBILLNO,
                r.FDATE,
                r.FPAYORGID,
                org_l.FNAME as 组织名称,
                re.FSETTLERECAMOUNT
            FROM T_AR_RECEIVEBILL r
            INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
            LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID
            LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052
            WHERE r.FDOCUMENTSTATUS = 'C'
            AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
            ORDER BY r.FDATE DESC
            """
            org_df = pd.read_sql(org_query, conn)
            print(org_df)
        except Exception as e:
            print(f"组织表关联失败: {e}")
        
        # 4. 查看收款单分录表是否有其他关联字段
        print("\n=== 4. 检查收款单分录表的所有字段 ===")
        entry_all_fields_query = """
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILLENTRY'
        ORDER BY ORDINAL_POSITION
        """
        entry_all_fields = pd.read_sql(entry_all_fields_query, conn)
        print("收款单分录表所有字段:")
        print(entry_all_fields)
        
        # 5. 查看是否有收款单和销售订单的关联
        print("\n=== 5. 查看收款单分录表中的源单据信息 ===")
        source_info_query = """
        SELECT TOP 10
            re.FENTRYID,
            re.FID,
            re.FSOURCEBILLNO,
            re.FSOURCEBILLTYPEID,
            re.FSRCROWID,
            re.FSETTLERECAMOUNT
        FROM T_AR_RECEIVEBILLENTRY re
        INNER JOIN T_AR_RECEIVEBILL r ON re.FID = r.FID
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        AND (re.FSOURCEBILLNO IS NOT NULL OR re.FSRCROWID IS NOT NULL)
        ORDER BY re.FID
        """
        try:
            source_info = pd.read_sql(source_info_query, conn)
            print("收款单分录表源单据信息:")
            print(source_info)
        except Exception as e:
            print(f"查询源单据信息失败: {e}")
        
        # 6. 尝试通过分录表的源单据关联
        print("\n=== 6. 尝试通过分录表源单据关联销售订单和客户 ===")
        try:
            sales_query = """
            SELECT TOP 10
                r.FID,
                r.FBILLNO as 收款单号,
                r.FDATE as 收款日期,
                so.FBILLNO as 销售订单号,
                c_l.FNAME as 客户名称,
                re.FSETTLERECAMOUNT as 收款金额
            FROM T_AR_RECEIVEBILL r
            INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
            LEFT JOIN T_SAL_ORDER so ON re.FSOURCEBILLNO = so.FBILLNO
            LEFT JOIN T_BD_CUSTOMER c ON so.FCUSTID = c.FCUSTID
            LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
            WHERE r.FDOCUMENTSTATUS = 'C'
            AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
            ORDER BY r.FDATE DESC
            """
            sales_df = pd.read_sql(sales_query, conn)
            print("通过销售订单关联客户:")
            print(sales_df)
        except Exception as e:
            print(f"销售订单关联失败: {e}")
        
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
