-- 客户回款汇总查询 (2025年5月1日之后)
-- 查询每家客户的收款信息

SELECT 
    c_l.FNAME AS '客户名称',
    CONVERT(varchar(10), r.FDATE, 120) AS '收款日期',
    SUM(re.FRECAMOUNT) AS '收款金额'
FROM 
    T_AR_RECEIVEBILL r
    INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
    INNER JOIN T_BD_CUSTOMER c ON r.FCUSTID = c.FCUSTID
    INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
WHERE 
    c_l.FLocaleId = 2052  -- 中文
    AND r.FDOCUMENTSTATUS = 'C'  -- 已审核单据
    AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
    AND r.FCANCELSTATUS = 'A'    -- 未作废
GROUP BY 
    c_l.FNAME, 
    CONVERT(varchar(10), r.<PERSON>, 120)
ORDER BY 
    CONVERT(varchar(10), r.FDA<PERSON>, 120) DESC, 
    c_l.FNAME;

-- 备注：
-- 1. T_AR_RECEIVEBILL - 收款单主表
-- 2. T_AR_RECEIVEBILLENTRY - 收款单分录表
-- 3. FRECAMOUNT - 收款金额字段
-- 4. 如果字段名称不正确，可能需要根据实际数据库结构调整

-- 可能的替代字段名称（如果上述查询报错）：
-- FRECAMOUNT 可能是 FAMOUNT 或 FALLAMOUNT
-- FCUSTID 可能在分录表中而不是主表中

-- 简化版本（如果需要更简单的结构）：
/*
SELECT 
    c_l.FNAME AS '客户名称',
    CONVERT(varchar(10), r.FDATE, 120) AS '日期',
    SUM(re.FRECAMOUNT) AS '收款金额'
FROM 
    T_AR_RECEIVEBILL r
    INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
    INNER JOIN T_BD_CUSTOMER c ON r.FCUSTID = c.FCUSTID
    INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
WHERE 
    c_l.FLocaleId = 2052
    AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
GROUP BY 
    c_l.FNAME, 
    CONVERT(varchar(10), r.FDATE, 120)
ORDER BY 
    CONVERT(varchar(10), r.FDATE, 120) DESC;
*/
