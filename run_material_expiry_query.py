#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行物料到期查询的简化脚本
"""

import sys
import os

# 确保可以导入主模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from material_expiry_query_auto import MaterialExpiryQueryTool
    print("✅ 成功导入物料到期查询工具")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保安装了所需的依赖包：pip install pyodbc pandas openpyxl")
    sys.exit(1)

def main():
    """主函数"""
    print("=" * 80)
    print("🚀 启动物料到期查询自动化工具")
    print("=" * 80)
    
    # 创建工具实例
    tool = MaterialExpiryQueryTool()
    
    # 运行完整分析
    success = tool.run_complete_analysis()
    
    if success:
        print("\n" + "=" * 80)
        print("🎉 任务执行成功！")
        print("✅ 制单人工号问题已解决")
        print("✅ 查询结果已导出到Excel文件")
        print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ 任务执行失败")
        print("📋 请检查以下内容：")
        print("   1. 数据库连接是否正常")
        print("   2. 查看日志文件 material_expiry_query.log")
        print("   3. 确认数据库表结构是否正确")
        print("=" * 80)

if __name__ == "__main__":
    main()
