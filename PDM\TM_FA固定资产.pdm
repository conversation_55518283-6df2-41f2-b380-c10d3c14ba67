<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ExtractEndDate="1447029653" ExtractionBranch="1" ExtractionDate="1447029653" ExtractionId="733807" ExtractionVersion="14" ID="{64D2E9C0-1C07-475A-BEBC-35E645521C93}" Label="" LastModificationDate="1563758217" Name="TM_FA固定资产" Objects="308" RepositoryId="{5C34CC33-BB05-4053-9571-E184DEFF2D78}" Symbols="4" Target="Microsoft SQL Server 2005" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="15.1.0.2850"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>64D2E9C0-1C07-475A-BEBC-35E645521C93</a:ObjectID>
<a:Name>TM_FA固定资产</a:Name>
<a:Code>TM_FA</a:Code>
<a:CreationDate>1390442572</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390527390</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=Yes
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Event&gt;&gt;]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
EnableRequirements=No
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=0
RefrUpdateConstraint=0
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=30
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=128
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=128
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=128
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE]

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\CUBE\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=D:\Program Files\Sybase\PowerDesigner 15\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Xsm]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryInformation>PD_VER 001
REP_ID {5C34CC33-BB05-4053-9571-E184DEFF2D78}
MODEL_ID 733807
MODEL_VRSN 14
BRANCH_ID 1
EXT_DATE 1447029653
EXT_END 1447029653
OBJECTS 
BEG_BLOCK 
 733807 {64D2E9C0-1C07-475A-BEBC-35E645521C93}
 733817 {A1B1EDC6-5B1C-4E6E-95CA-A4DC2EE8B78C}
 733818 {7F4B5D7B-301B-47A2-86FE-D90D5CB41E4B}
 733819 {86F2395C-4266-4BBA-923D-322CC178C190}
 733820 {D9DE558F-F1C2-4A59-BDC4-2EFC33D1DC14}
 733821 {C846DB0C-379A-4A7E-9837-A6F9926A1F68}
 733822 {9290A673-97AF-4BBE-AC43-FC9B4A33EA68}
 733823 {1506D54E-50A0-42CD-B896-C7B057C8E419}
 733824 {17F4C664-4B4E-4079-9D7C-201CB6D9A3AD}
 733825 {C325732C-711F-40B6-B980-63847EBD13DA}
 733826 {210CA8AA-4959-4CD1-92AE-812762F6AFB6}
 733827 {2E8D0222-9B65-4984-A7A7-084FB689172B}
 733828 {5A3DAF1D-30D4-40F9-8DE2-27A94B07C0C7}
 733829 {CAFF8B96-9DC0-436D-AFDF-5F2F513899BF}
 733830 {8533A1B9-FA2D-4431-A7A4-96373CE9F14B}
 733831 {3D1F03E8-6DFB-4A4A-82A5-C60D025C08F1}
 733832 {ED32F8E5-87F7-4F18-A555-9049BD118831}
 733833 {D82F626F-C363-4A1E-A871-33A0511D8FDD}
 733834 {2C6ACC33-152B-4E8F-8EC7-FDB84130A002}
 733835 {E14A61CB-0573-45D8-A05E-EBD3E5175E4C}
 733836 {C6E906D9-B14C-44EA-86D8-D2122DCFAF43}
 733837 {1700E6CC-8B2A-4A08-83CC-D185C5D6F39F}
 733838 {D2C67D5B-AB78-466C-9CE7-A19E4FDEA329}
 733839 {F08F49B2-DED4-4A35-B9BB-69896AB7207B}
 733840 {6F7E87F2-945F-4289-BC3A-26B349072796}
 733841 {6797BA0F-A5DE-4B71-AA10-BD4FC08DD837}
 733842 {29E84ACA-011E-4B89-97DF-70E2A1C1028E}
 733843 {9FD30D9B-48B2-4CC5-A7F4-BA6ECD7192B7}
 733844 {547676D0-18AF-4F9A-A2A0-363CF329CB33}
 733845 {71DAA6B8-86FC-4FA0-88AE-0C20E230C6DE}
 733846 {D674D81C-9CF5-4EC4-A760-CB03BE6ED691}
 733847 {60D9E4C8-0510-45C8-83DC-4DB2CA23CB4B}
 733848 {DF68D4D4-3BA0-4DA5-B3B4-531FCB7A84CF}
 733849 {91862DF5-7E65-411B-B6C2-21AA05B4D994}
 733850 {3CA756A3-35CB-4BA3-8A12-97995018E4DA}
 733851 {BC7E50E5-88C4-46AA-AD37-1F932022898F}
 733852 {1EDE7C88-E696-4BE9-BE64-D19122A4A5B5}
 733853 {A6AA1802-4867-4489-8303-9EB60A699858}
 733854 {3AB61BDC-A488-4FB6-BECC-F37C6DC80B31}
 733855 {930C2C9E-80A2-460F-A3C1-B069B9741D6E}
 733856 {4B1C1E2F-72CB-47FA-88C0-BBEA9AD24E1B}
 733857 {79C92B8A-6518-498E-BEA8-105FF585C5FC}
 733858 {143C11BC-0BD9-4941-B173-AD92428CD121}
 733859 {F9689E5B-977B-4052-BC0F-67ABA42762F8}
 733860 {BA5B67B6-717F-4C32-B52F-95EB4B474740}
 733861 {423E55DB-52EC-48D6-95DC-47A7493837C9}
 733862 {7C1516C6-A86A-4E69-A10D-F295B98784F9}
 733863 {EA25435F-5DE0-41B9-B996-9F90A211639A}
 733864 {5E7FC86B-ACE0-486D-A6E1-B96DF9EDBA60}
 733865 {F29A52C1-A8A0-4C47-8263-6B7FEF499878}
 733866 {6FC9EA71-7762-4267-BD11-7E3ADBCD0C37}
 733867 {DDB9EBA0-A6CC-44D9-A313-9C0CFCE84062}
 733868 {258E9243-7C2A-45A6-8FC9-56FA95C0C617}
 733869 {AF1A2D92-CC78-4449-BA21-39E711B49B3E}
 733870 {2BEDACF0-318B-4C97-BA4F-F4C6F0C7A771}
 733871 {6AF0CBEA-E7D1-4268-BCE5-F625AF85FA6D}
 733872 {3CD1D177-613F-4A18-9E64-70C0812227D4}
 733873 {B8B50E7B-0093-4F0E-A6F0-F2EB8E72D415}
 733874 {A74C305A-50B4-43B1-AEFD-8242762D04BC}
 733875 {418742D7-9E0D-419D-A560-F902CE490998}
 733876 {F3F495EC-AB48-41AD-92B9-F2E30AEB2F3A}
 733877 {248564E1-A1CB-4F62-96CE-265FE4CE59ED}
 733878 {B46E131D-C5FA-4A4D-AFB4-5996FDB2D519}
 733879 {C2C756D2-8030-42AF-94AD-E481DCC58116}
 733880 {B63EB14D-04AB-4A6D-9A53-CBF712E4DDFC}
 733881 {6F542BC7-2350-4299-B583-425EB4B1E6D5}
 733882 {C56CAC48-0A0E-43EB-A324-B38792B10E9A}
 733883 {E4FFD31E-B803-4E5A-91E1-5C91D118E4B6}
 733884 {F929170A-CD21-489B-995E-F9F2BC6E5FD0}
 733885 {5F026272-8774-4D60-94EE-B271A564FD23}
 733886 {DDEFED2F-B4D9-41E9-A601-964FBD0E5CC9}
 733887 {3C64AA8D-A8EE-475E-BEB3-4A64B888656E}
 733888 {3F1F7A83-CD79-4EB4-9DBB-CED52BB9EFCA}
 733889 {C8A4E7F8-2E59-4235-B728-5C3556D4B6EF}
 733890 {6A3C72CF-A9E6-4879-9000-E49415EB350E}
 733891 {17E69BC8-A0A5-4499-8AE3-0EBE727C01CB}
 733892 {6A02D080-3BD4-402A-83EE-E91211B37B49}
 733893 {706A288A-3C71-47AA-AB04-A9010BF6141F}
 733894 {2D89EA48-ED3E-4783-AED6-2D9DF57B0664}
 733895 {5AF673A2-7B04-47CA-BF12-B67C93526311}
 733896 {07219C3C-511D-44CC-9522-E6792187D8A4}
 733897 {A27B0904-F55F-40C9-BFF6-2173B331F04F}
 733898 {ECDECA33-E48D-4585-848E-9764B038D534}
 733899 {45982A57-6BBB-4742-91F7-80167786BCB3}
 733900 {A0EF42C4-F2DE-4E44-9A79-61401C60C333}
 733901 {06336934-483C-4691-AD50-B35C1AAE4267}
 733902 {74CEC6BE-75FC-4074-8425-F43F597E5070}
 734748 {B07335CA-8337-48E7-A8FB-4B67CA8748D0}
 734749 {C1146CCB-B5E3-44A5-9B86-7B60F4AE714A}
 734750 {85F7DFFA-82E9-43A0-88BF-3315C9EBE797}
 734751 {A7F63623-C1DA-4185-A6BB-5202332B0733}
 734752 {8ADB59E7-3D7E-4288-A692-4D7B05348241}
 734753 {7DF86258-7922-468F-A64B-5CBEB491FD12}
 734754 {D132EE8B-01A2-44A0-9DD0-CAC228DA7F54}
 734755 {E2977B35-B4B9-4F28-89BF-BE04304981BC}
 734756 {00951E73-7742-4887-B9C6-939567200802}
 734757 {591C7E28-6541-4A76-8401-52193C20E0A4}
 734758 {FFEEE253-2F23-4CF6-9819-578F7A5B5CBE}
 734759 {D53AB97B-CBEC-4714-805E-7FDBF292A62B}
 734760 {DF5491F3-C33F-42EB-AF54-A8CDE729FA44}
 734761 {216366C0-D9AD-49CD-A9BC-C2F68C9B0D85}
 734762 {21B3BAFE-A643-4D64-8D15-11C123EFEB3B}
 734763 {32AF9985-CA81-44B6-B9DD-7E4773FA5E4F}
 734764 {960D951C-70E9-4439-A6FA-D7CC65E5759C}
 734765 {BF810D67-6140-4D5F-82B9-FEE3FA34CE61}
 734766 {A6635984-157A-4150-AEA4-00854DEC6BFA}
 734767 {6C2341B7-509B-43B0-9E2D-5A50C1B9CD5F}
 734768 {9E8A060F-AD5C-4EF5-81B3-424D3FBE2A78}
 734769 {C5A7687D-3B9F-40B6-9D01-26BD79FFF5D0}
 734770 {C5971499-8593-4D1B-8E29-A04BE22E9C75}
 734771 {3881D4C4-FD37-456E-B8E8-61405DC70B3A}
 734772 {D25FB3ED-36C9-41A2-B02A-43DF374290E6}
 734773 {FF33CAC5-2F9F-498A-94D2-E182CCF340D4}
 734774 {BD74B5F9-4292-4C44-A67F-6F84502082C6}
 734775 {85C5F169-C10E-4D8A-907B-0AA56243B6B4}
 734776 {2DCAEB00-C38A-4D97-B115-DD700122EE83}
 734777 {34374168-A7FD-4CD5-8CA3-F4600C40F5D6}
 734778 {E0B4BD5B-D7A4-4BD3-B192-E8C4E04CEF94}
 734779 {F02C04F7-D644-49E7-8ABC-82238E94A274}
 734780 {C32F004B-8A40-48F4-9282-E8452B67A77C}
 734781 {1D34E676-9F57-4193-9A84-14E48450A88F}
 734782 {E9EB17AA-FE1C-4105-9B24-C4FAC56B2D1A}
 734783 {B70D496B-76F1-4B8A-885D-BBEE7A0D90DD}
 734784 {2107885C-6447-4A7E-ADBD-D548398EC9AC}
 734785 {D26E77CD-AADE-4C84-9B95-12C93C6D47EF}
 734786 {8A62BE18-1B1B-484F-85AF-48E87A5A3CEA}
 734787 {5C5CDD33-6F12-45F1-A39A-7E48FBB73D6D}
 734788 {E8E1B6B4-5D9C-45A4-B07C-2680C61AA530}
 734789 {89D0D504-6347-4F2B-9C95-7258743CD58F}
 734790 {EBB409F3-CDCD-467D-8EAB-1B320BF2AE82}
 734791 {8CABD81E-003A-4D2F-8A1F-73E716B1FE8F}
 734792 {47EC6673-6A1E-4422-9D73-4AC54E49AFC3}
 734793 {CFBCB9BD-2840-4EDD-82BD-629AF55D9DDE}
 734794 {6BEE3ED8-17CD-4131-BF19-DFAD0F824823}
 734795 {5A706DDE-EF18-46F8-85A2-2BA968FD5CD5}
 734796 {433BE908-0AFD-487E-BDEF-290103D95C7B}
 734797 {E6ABE1B5-4F72-4E18-91FE-C64E4C65FDB4}
 734798 {C4B10E14-2118-4077-9FB9-A6A67A13D541}
 734799 {7793BD52-886C-4604-9103-B8A763644134}
 734800 {2B513664-93DC-47AF-AD35-AFDB878CBB30}
 734801 {769472AF-4603-43B7-BB54-14FD8F7B7372}
 734802 {BDFEF8D9-ED5D-4A3E-AF06-1678A756BD74}
 734803 {A3B05029-67B8-4CBD-96E4-571285E83153}
 734804 {36489076-EBB6-4C75-B378-31B7CD73A29D}
 734805 {9F1AA69C-3922-4920-AE47-A2DF47EC93D6}
 734806 {9A8A2946-038D-4C07-B8B9-4C3663669D0F}
 734807 {A8E0F553-43EC-495B-A1A8-BFB84A3DF67F}
 734808 {68AC5361-015C-4882-9FAE-AE55953AA2C7}
 734809 {BA776E74-EE94-480F-B65B-4B3A7F01F068}
 734810 {C649C0F1-3F33-4C82-BF8D-B91B155D16CF}
 734811 {60CC0920-DD44-4BD5-99E4-BE658D86BBC1}
 734812 {5020F0D4-CA15-47FB-AF8F-31FE587E32F8}
 734813 {50CEE8D1-8651-4C0F-9C37-F1DF7BF44B1F}
 734814 {E1444681-52EF-4A22-946B-B3BEC6B9D4E7}
 734815 {811D5BE4-0A07-4AFE-B9A9-9D6A06297FB4}
 734816 {0D4F08C2-E787-4B27-BB93-DE25BFA5D28A}
 734817 {5ABC1D67-E1E3-4AC6-864F-3B5C92A3C560}
 734818 {8C4C78A1-8E53-4E48-AABA-68DAA8900928}
 734819 {A765973A-0846-4FEA-86CE-A696CBD7A87B}
 734820 {70CE9913-A41E-4F4F-BC63-57B821AB930E}
 734821 {9A3F1678-5557-493C-8E3A-BE5E9215E712}
 734822 {39D864E9-D06A-43FC-AA27-F2DA990E6BDB}
 734823 {F538D447-F857-40E7-9643-44020DB1C8D2}
 734824 {A4CB5CC5-EDBE-4AB9-B1CE-B724F09DC4EB}
 734825 {C459EB5A-8D05-4F23-A635-C10FD9AFF6EB}
 734826 {08BCDC1F-740E-404D-8DC3-352052D6CD76}
 734827 {AE684E77-F66B-42AD-91DF-F05BAABD6250}
 734828 {8843960F-09A4-4EE7-BB7A-48E3DFD4389A}
 734829 {3827ED7B-D60A-4EBE-91AA-1BEF5E9743A0}
 734830 {7D90E143-9B78-49C6-BD3B-9133CF1ACBDE}
 734831 {A63D1FAD-1965-428D-A056-99980E529090}
 734832 {7082D999-4812-4F78-95B1-D3BE86EFFB5F}
 734833 {17A3C54C-56BC-468C-BF12-081B3AB0DAB3}
 734834 {BCDCBACA-5B3D-40BA-A608-FE9F887946E3}
 734835 {D785BDA6-CC42-4C97-A629-D69375A3F65F}
 734836 {81B7C2B0-7031-4C80-A81D-DC9819EFFDD5}
 734837 {65ABAACF-7DEF-4585-A4A9-ED2E10C32DF0}
 734838 {69421C96-F0E6-4E29-9806-366A16005987}
 734839 {30356AB5-78E6-4981-AECF-6F272C1AB8C0}
 734840 {F6A0B629-D56A-4503-A225-F1CE96B412C8}
 734841 {B3F7F576-9CDA-433B-AE0C-6401C9CE2254}
 734892 {C1562A99-0A3E-4DCE-9376-DB9BC03FC6B3}
 734893 {A62247D0-F44E-4F23-804A-DB41A118F4A4}
 734894 {0D838B3E-6DF1-4F10-8C10-6D047CD362E9}
 734895 {BBB7FB16-36C6-4B75-8366-AE4994C289D5}
 734896 {D88C9CF5-5F84-422D-82D2-B953CA0CD953}
 734897 {D86E16AD-C7C9-430E-B3E1-6B9E25BB5ECB}
 734898 {2599D53C-967F-4822-8F3F-22B522BF1450}
 734899 {CE510423-8F32-4821-A8DE-7B47762F7C2B}
 734900 {1EBB822A-01B9-40DC-A60F-B245B0C36050}
 734901 {0D2FAF7C-1F68-459A-A0B2-14F70DC294E3}
 734902 {B6C7988F-20DF-4ADC-8BAB-F9160F1C6C52}
 734903 {13F1FE01-6F7E-4B5A-8A80-82C37A180233}
 734904 {7BFB402C-9F2F-4CF7-9248-F5AD598E5AA2}
 734905 {6B41F466-7ED5-49BD-8409-D54BDE5497FC}
 734906 {A62C13F0-257B-4BEC-88E2-64EB3F4ADEC7}
 734907 {97A3A11B-3EAF-45E8-98E5-593B23160F9C}
 734908 {F2D9BBBF-8085-4E4B-9B84-AAF4411893B0}
 734909 {4DE9D822-69CF-4DBA-9C38-26BCF42227A2}
 734910 {6702696C-482B-4A82-87F9-240C49A8A880}
 734911 {DB9DEE76-4A1E-449D-9CE5-E2A8B4FAFB67}
 734912 {30C6B84C-8A21-4563-86C8-3F0A6AB111D6}
 734913 {2E3AAB5A-5665-44DF-9BC1-1120F7F038A4}
 734914 {4A7A1426-AB89-4035-A374-EA8AE58E9ABE}
 734915 {442BD3AD-08AA-4584-A862-24A4E42B743F}
 734916 {F4FCEBFE-E4D2-406C-B1A1-99A1683BEFAC}
 734917 {E6551701-A0DD-4123-9F25-E458DBE665C1}
 734918 {5BCF08FF-4C89-4BFF-9551-8F0735B77A93}
 734919 {7B5BDF40-F7BA-49CC-AC0B-59D3F33F4B16}
 734920 {1D6E4B9D-695B-4E58-B1BB-C1F9424A54D8}
 734921 {856A3EB5-4318-4A50-98F9-A8609A7E152C}
 734922 {8C46981D-697B-4A80-81CB-4F50643F2CF7}
 734923 {F2CB2A10-588A-484C-9C98-BD6C8BBDAF49}
 734924 {B637DA01-04F9-47BD-A3ED-F1EF38CD898E}
 734925 {17556F97-D868-4054-8152-E7FF6A8EBE45}
 734926 {39F38603-E18A-4A98-8DA2-F4C31C804A8E}
 734927 {CE0B411F-7704-4397-BEE2-B2299A27338E}
 734928 {94E7AECA-F151-44F8-B77F-9201B9D90C26}
 735373 {27A4568B-6E8C-4FAD-B853-A62C093E1903}
 735374 {B35D71F1-1CD3-4B2E-98F4-B5E45BE2E97F}
 735375 {AF303647-D8E7-4A7A-8075-E4CBB4645BD3}
 735376 {C1E7C738-B364-4C81-8B49-7FB7D4DBE759}
 735377 {2AB73A5F-A5E1-4797-AD98-E74964D392EB}
 735378 {57A2E32B-C942-4AA8-93AA-DE6DEB244353}
 735379 {ACC52C95-201B-46E3-8A93-DB9C879677B6}
 735380 {625E64A4-D3A2-45BB-BC30-F9685C90C5D9}
 735381 {0EF06CBF-98F1-4153-BFC5-36A69254EC89}
 735382 {481DAA97-0793-407C-A14E-1277B59CF040}
 735383 {9F76CB0A-F661-404A-B335-D8F7FC3E870A}
 735384 {CB9E48D4-F6D9-4E5D-B6B9-04D5AF49778B}
 735385 {3DEBE64F-B19A-443C-AFD2-BB967310384A}
 735386 {2C41BA99-33D6-40EA-AF4B-007D80AE0893}
 735387 {D4BDCDB4-275C-4570-9EB1-F3A32B22F43A}
 735388 {2F0C8C11-F4E3-4EB6-846A-92760DCE6EAA}
 735389 {3F30A64D-3D8C-4058-A92D-3776AD3C0194}
 735390 {574B362E-DAD5-4C92-BE8E-B65244CC82F5}
 735391 {A47763AE-8F97-4AAA-91C6-17863254CBDA}
 735392 {02929D69-8D25-4F0E-8C76-9B9925F45823}
 735393 {76348FEC-B946-4880-A59C-427FE970D4D1}
 735394 {FA42869E-68CD-422B-9431-57715A3AB667}
 735395 {A71AF206-B30A-4EF3-A1BE-95B9DE00F98B}
 735396 {FE09C983-0D65-48C1-AE89-E97B1E50A526}
 735397 {1913174D-6200-4D71-B588-AB2EF50F6B39}
 735398 {58E65F19-F8C4-47D8-8949-411540629152}
 735399 {218EBF14-1402-486F-A0E3-104E32CA270B}
 735400 {5E0A5919-79A9-48BA-BA78-8DEE8628D33B}
 735401 {A80E7EDA-F644-4170-A74A-0AFC5A7B691B}
 735402 {C8C929A0-9B6C-4F79-B074-20B2CF743FCE}
 735403 {42516309-C5E3-4FF6-BE5C-8360A4854233}
 735404 {2A14FA48-6796-4D10-97F0-1C983345DCDF}
 735405 {1625D31F-C640-434A-BD7C-53831652922D}
 735406 {EE1902FA-5EF4-4D56-9D86-2A2054E5F950}
 735407 {D4F95266-8BC3-4EF4-9688-8473674088C0}
 735408 {968CB311-8EE3-4593-B0B3-EE59404C6F7F}
 735409 {81B351B1-056D-478F-8BC2-DDCF4FC119D4}
 735410 {BE3C75D6-C4E3-407D-801A-2725C97B0C63}
 735411 {56F7C08C-BE58-42D8-8E4E-39C38BD2F9A8}
 735412 {64AC1369-AE17-478B-9DF2-9C7C7A685367}
 735413 {BED59EC2-8766-47AB-BF7A-C6B1C629394E}
 735414 {E15DF405-5A57-4F70-81F0-4F4EEC6F37FE}
 735415 {FF8B194F-829A-427B-B0C6-08FE966AF76F}
 735652 {4D5F1DAE-C4FE-437A-899A-6BF966FD188C}
 735653 {771CA9C6-D47E-4A42-A15B-6A48F2762AB4}
 735654 {873DA2F4-7B37-4A33-A17D-A35B7AEDD50A}
 735655 {E77ADC54-9BDF-4877-8438-1CF6305F3D19}
 735656 {E198A9AF-D2BB-48D8-A0A4-76C3430EB955}
 735657 {1A9DABBC-87A7-44F2-9A9F-607F55EFA9CD}
 735658 {AA9151C5-E988-4B75-BC0D-81B8319F71B0}
 735659 {B628D79C-46D8-45A1-B171-62EF75CBBFB9}
 735660 {C409B148-C82B-4962-89F1-FE4B870F19BD}
 735661 {252EDBFA-5462-431C-9470-7ADF5DDF355B}
 735662 {152B9DFC-EB5C-473B-85EE-32BC2CF5D83E}
 735663 {7413C701-00CA-4698-A760-C85918307EFF}
 735664 {D502EA07-AA73-403D-AF94-4B4BB20191D7}
 735665 {C4CB3738-AFA3-473F-AAE8-8C61019457E1}
 735666 {2AA51F0D-B8FF-4E6C-BC32-ADF0586ED00B}
 735667 {CEFBA2C8-67A5-490F-B63C-11C1895C4355}
 735668 {4D9D423F-CC60-4563-A0B2-D07B3BE79CA8}
 735669 {4E7DB703-ACFD-480B-A776-E3570E00164B}
 735670 {DBCA921C-8401-492B-AF1A-F82AADB251D8}
 735671 {48919E24-5D36-4EA7-9598-A460BC007270}
 735672 {41B26A9E-1A10-400B-8550-1A9F926C7EC0}
 735673 {6B3DFADB-42BC-45F5-B380-47645BD665E4}
 735674 {5C762D54-E223-4062-A258-1E7B1AC5C497}
 735675 {680D22FA-F1C4-43DE-83FD-11748DA0EA56}
 735676 {4D7C4238-F3FD-4487-8851-77F456AAFFDF}
 834636 {9C6AEE04-CF5D-47FA-AC66-CC58A112F671}
 857555 {79DB6E29-4B80-4B01-8051-0C41E8E868A7}
 857556 {A1AD09A2-F285-4468-A298-C06507558718}
 857557 {AEE5587F-643B-4AE3-92AC-7F44F765820A}
 857558 {FF403987-EA92-47B0-8E30-C70033E0C705}
 857559 {F898C168-3877-4470-99F2-EA7A658F9E8A}
 857560 {B94C6E3E-4CB3-4808-8FD3-44DBE876627E}
 857561 {D10C712A-7F04-4665-B8B7-1138F15137B1}
 857562 {987D3B5D-DD22-45B3-85CB-453446C39A99}
 857563 {EA68FE9C-99B2-48BF-AE33-C40D312846B0}
 857564 {AB558682-8065-4274-9E0B-91EA9F30E0C2}
 857565 {1D4E4BFE-19F4-44B0-A906-5D2A418967E4}
 857566 {C8DE71BA-22DF-4D4D-AF51-B4B5733685CB}
 857567 {44001634-5D70-4A45-B705-8B6ABF02D9BD}
 857568 {99C2AF31-727E-45F0-84BE-C05FE0E9787E}
 857569 {40866116-1B19-41DB-985D-D914C3A09B02}
 868527 {050EE589-9999-47CD-B56C-B9DA4758F5CA}
END_BLOCK 
</a:RepositoryInformation>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>A1B1EDC6-5B1C-4E6E-95CA-A4DC2EE8B78C</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1390471647</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390471648</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>7F4B5D7B-301B-47A2-86FE-D90D5CB41E4B</a:ObjectID>
<a:Name>固定资产临时表_ALL</a:Name>
<a:Code>固定资产临时表_ALL</a:Code>
<a:CreationDate>1390442572</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390443491</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:Packages>
<o:Package Id="o5">
<a:ObjectID>86F2395C-4266-4BBA-923D-322CC178C190</a:ObjectID>
<a:Name>固定资产临时表</a:Name>
<a:Code>固定资产临时表</a:Code>
<a:CreationDate>1390443735</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390471648</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=Yes
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Grant=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=Yes
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=Yes
Foreign key=Yes
Alternate key=Yes
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Aggregate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Assembly&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;AsymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Certificate&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Contract&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EndPoint&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;EventNotification&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;FullTextCatalog&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;MessageType&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionFunction&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;PartitionScheme&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Queue&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;RemoteServiceBinding&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Route&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;Service&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;SymmetricKey&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\ExtendedObject &lt;&lt;XMLSchemaCollection&gt;&gt;]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes</a:PackageOptionsText>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o6">
<a:ObjectID>9290A673-97AF-4BBE-AC43-FC9B4A33EA68</a:ObjectID>
<a:Name>临时表</a:Name>
<a:Code>临时表</a:Code>
<a:CreationDate>1390443735</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390465873</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;&quot;#$%&amp;&#39;()*+,-./:;&lt;=&gt;?@[\]^_`{|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
PckgShowStrn=Yes
Package.Comment=No
Package.IconPicture=No
Package_SymbolLayout=
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE&amp;quot;&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:TableSymbol Id="o7">
<a:CreationDate>1390443771</a:CreationDate>
<a:ModificationDate>1390471648</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-3191,-23586), (17282,-11438))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o8"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o9">
<a:CreationDate>1390443778</a:CreationDate>
<a:ModificationDate>1390471648</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-23478,-33336), (-6380,6862))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o10"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o11">
<a:CreationDate>1390443782</a:CreationDate>
<a:ModificationDate>1390471648</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-2774,829), (15824,5552))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o12"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o13">
<a:CreationDate>1390443801</a:CreationDate>
<a:ModificationDate>1390471648</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-2738,-7247), (18484,-3248))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>12632256</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o14"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o6"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o15">
<a:ObjectID>C1562A99-0A3E-4DCE-9376-DB9BC03FC6B3</a:ObjectID>
<a:Name>TM_FA_DEPRADJUSTENTRY(固定资产折旧调整明细临时表)</a:Name>
<a:Code>TM_FA_DEPRADJUSTENTRY</a:Code>
<a:CreationDate>1331195862</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:Comment>固定资产折旧调整明细临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o16">
<a:ObjectID>A62247D0-F44E-4F23-804A-DB41A118F4A4</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1392087882</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o17">
<a:ObjectID>0D838B3E-6DF1-4F10-8C10-6D047CD362E9</a:ObjectID>
<a:Name>年度</a:Name>
<a:Code>FYear</a:Code>
<a:CreationDate>1392087882</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o18">
<a:ObjectID>BBB7FB16-36C6-4B75-8366-AE4994C289D5</a:ObjectID>
<a:Name>期间</a:Name>
<a:Code>FPERIOD</a:Code>
<a:CreationDate>1392087882</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o19">
<a:ObjectID>D88C9CF5-5F84-422D-82D2-B953CA0CD953</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FENTRYID</a:Code>
<a:CreationDate>1331195862</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o20">
<a:ObjectID>D86E16AD-C7C9-430E-B3E1-6B9E25BB5ECB</a:ObjectID>
<a:Name>折旧调整内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1331195862</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o21">
<a:ObjectID>2599D53C-967F-4822-8F3F-22B522BF1450</a:ObjectID>
<a:Name>资产类别</a:Name>
<a:Code>FAssetTypeID</a:Code>
<a:CreationDate>1331195862</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o22">
<a:ObjectID>CE510423-8F32-4821-A8DE-7B47762F7C2B</a:ObjectID>
<a:Name>本期卡片部分的折旧额</a:Name>
<a:Code>FCardDepr</a:Code>
<a:CreationDate>1340933030</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o23">
<a:ObjectID>1EBB822A-01B9-40DC-A60F-B245B0C36050</a:ObjectID>
<a:Name>本期折旧额</a:Name>
<a:Code>FDepr</a:Code>
<a:CreationDate>1331195862</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o24">
<a:ObjectID>0D2FAF7C-1F68-459A-A0B2-14F70DC294E3</a:ObjectID>
<a:Name>本期应提折旧额</a:Name>
<a:Code>FShouldDepr</a:Code>
<a:CreationDate>1331195862</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o25">
<a:ObjectID>B6C7988F-20DF-4ADC-8BAB-F9160F1C6C52</a:ObjectID>
<a:Name>未计提折旧</a:Name>
<a:Code>FDeprRemain</a:Code>
<a:CreationDate>1331195862</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o26">
<a:ObjectID>13F1FE01-6F7E-4B5A-8A80-82C37A180233</a:ObjectID>
<a:Name>行号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1331207405</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o27">
<a:ObjectID>7BFB402C-9F2F-4CF7-9248-F5AD598E5AA2</a:ObjectID>
<a:Name>资产原值</a:Name>
<a:Code>FOrgVal</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o28">
<a:ObjectID>6B41F466-7ED5-49BD-8409-D54BDE5497FC</a:ObjectID>
<a:Name>预计净残值</a:Name>
<a:Code>FResidualvalue</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o29">
<a:ObjectID>A62C13F0-257B-4BEC-88E2-64EB3F4ADEC7</a:ObjectID>
<a:Name>预计使用期间数</a:Name>
<a:Code>FLifePeriods</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o30">
<a:ObjectID>97A3A11B-3EAF-45E8-98E5-593B23160F9C</a:ObjectID>
<a:Name>累计折旧</a:Name>
<a:Code>FAccumDepr</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o31">
<a:ObjectID>F2D9BBBF-8085-4E4B-9B84-AAF4411893B0</a:ObjectID>
<a:Name>减值准备</a:Name>
<a:Code>FAccumDevalue</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o32">
<a:ObjectID>4DE9D822-69CF-4DBA-9C38-26BCF42227A2</a:ObjectID>
<a:Name>已折旧期间数</a:Name>
<a:Code>FDeprPeriods</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o33">
<a:ObjectID>6702696C-482B-4A82-87F9-240C49A8A880</a:ObjectID>
<a:Name>年度会计期间数</a:Name>
<a:Code>FPeriodcount</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o34">
<a:ObjectID>DB9DEE76-4A1E-449D-9CE5-E2A8B4FAFB67</a:ObjectID>
<a:Name>剩余使用年限</a:Name>
<a:Code>FRemainYear</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o35">
<a:ObjectID>30C6B84C-8A21-4563-86C8-3F0A6AB111D6</a:ObjectID>
<a:Name>预计使用年限</a:Name>
<a:Code>FLifeYear</a:Code>
<a:CreationDate>1331800373</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o36">
<a:ObjectID>2E3AAB5A-5665-44DF-9BC1-1120F7F038A4</a:ObjectID>
<a:Name>折旧率</a:Name>
<a:Code>FDeprRate</a:Code>
<a:CreationDate>1332227268</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o37">
<a:ObjectID>4A7A1426-AB89-4035-A374-EA8AE58E9ABE</a:ObjectID>
<a:Name>货主组织</a:Name>
<a:Code>FOwnerOrgID</a:Code>
<a:CreationDate>1333260399</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o38">
<a:ObjectID>442BD3AD-08AA-4584-A862-24A4E42B743F</a:ObjectID>
<a:Name>卡片数量</a:Name>
<a:Code>FMQuantity</a:Code>
<a:CreationDate>1333260183</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o39">
<a:ObjectID>F4FCEBFE-E4D2-406C-B1A1-99A1683BEFAC</a:ObjectID>
<a:Name>卡片最新状态</a:Name>
<a:Code>FAssetCurStatus</a:Code>
<a:CreationDate>1336045308</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o40">
<a:ObjectID>E6551701-A0DD-4123-9F25-E458DBE665C1</a:ObjectID>
<a:Name>处置单本期折旧额</a:Name>
<a:Code>FDisposeDepr</a:Code>
<a:CreationDate>1336463437</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o41">
<a:ObjectID>5BCF08FF-4C89-4BFF-9551-8F0735B77A93</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>FAlterID</a:Code>
<a:CreationDate>1331800926</a:CreationDate>
<a:Creator>deguang_liu</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o42">
<a:ObjectID>7B5BDF40-F7BA-49CC-AC0B-59D3F33F4B16</a:ObjectID>
<a:Name>工作量单位</a:Name>
<a:Code>FWorkLoadUnitID</a:Code>
<a:CreationDate>1380112766</a:CreationDate>
<a:Creator>RD_corebo_lb_he</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o43">
<a:ObjectID>1D6E4B9D-695B-4E58-B1BB-C1F9424A54D8</a:ObjectID>
<a:Name>预计工作总量</a:Name>
<a:Code>FPredictWorkLoad</a:Code>
<a:CreationDate>1380112766</a:CreationDate>
<a:Creator>RD_corebo_lb_he</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o44">
<a:ObjectID>856A3EB5-4318-4A50-98F9-A8609A7E152C</a:ObjectID>
<a:Name>已折旧工作量</a:Name>
<a:Code>FDeprWorkLoad</a:Code>
<a:CreationDate>1380112766</a:CreationDate>
<a:Creator>RD_corebo_lb_he</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o45">
<a:ObjectID>8C46981D-697B-4A80-81CB-4F50643F2CF7</a:ObjectID>
<a:Name>折旧方法</a:Name>
<a:Code>FDeprMethod</a:Code>
<a:CreationDate>1380112766</a:CreationDate>
<a:Creator>RD_corebo_lb_he</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o46">
<a:ObjectID>F2CB2A10-588A-484C-9C98-BD6C8BBDAF49</a:ObjectID>
<a:Name>本期折旧工作量</a:Name>
<a:Code>FPeriodWorkLoad</a:Code>
<a:CreationDate>1380418004</a:CreationDate>
<a:Creator>RD_corebo_lb_he</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o47">
<a:ObjectID>27A4568B-6E8C-4FAD-B853-A62C093E1903</a:ObjectID>
<a:Name>TM_FA_ASSERTDEPRENTRMUL(固定资产计提折旧明细多语言临时表)</a:Name>
<a:Code>TM_FA_ASSERTDEPRENTRMUL</a:Code>
<a:CreationDate>1392103059</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:Comment>固定资产计提折旧明细多语言临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o48">
<a:ObjectID>B35D71F1-1CD3-4B2E-98F4-B5E45BE2E97F</a:ObjectID>
<a:Name>主健</a:Name>
<a:Code>FPKID</a:Code>
<a:CreationDate>1392103145</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o49">
<a:ObjectID>AF303647-D8E7-4A7A-8075-E4CBB4645BD3</a:ObjectID>
<a:Name>临时分录号</a:Name>
<a:Code>FTempEntryID</a:Code>
<a:CreationDate>1392103145</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o50">
<a:ObjectID>C1E7C738-B364-4C81-8B49-7FB7D4DBE759</a:ObjectID>
<a:Name>公式内容</a:Name>
<a:Code>FFORMULACONTENT</a:Code>
<a:CreationDate>1392103145</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o51">
<a:ObjectID>2AB73A5F-A5E1-4797-AD98-E74964D392EB</a:ObjectID>
<a:Name>临时主健</a:Name>
<a:Code>FTempPKID</a:Code>
<a:CreationDate>1392103313</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o52">
<a:ObjectID>4D5F1DAE-C4FE-437A-899A-6BF966FD188C</a:ObjectID>
<a:Name>TM_FA_AcctDeprException(固定资产折旧异常临时表)</a:Name>
<a:Code>TM_FA_AcctDeprException</a:Code>
<a:CreationDate>1392106485</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:Comment>(固定资产折旧异常临时表)</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o53">
<a:ObjectID>771CA9C6-D47E-4A42-A15B-6A48F2762AB4</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1392106532</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o54">
<a:ObjectID>873DA2F4-7B37-4A33-A17D-A35B7AEDD50A</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOwnerOrgID</a:Code>
<a:CreationDate>1392106532</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>E77ADC54-9BDF-4877-8438-1CF6305F3D19</a:ObjectID>
<a:Name>会计政策ID</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1392106592</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o56">
<a:ObjectID>E198A9AF-D2BB-48D8-A0A4-76C3430EB955</a:ObjectID>
<a:Name>当前年期</a:Name>
<a:Code>FYearPeriod</a:Code>
<a:CreationDate>1392106612</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o57">
<a:ObjectID>1A9DABBC-87A7-44F2-9A9F-607F55EFA9CD</a:ObjectID>
<a:Name>资产类别</a:Name>
<a:Code>FAssetTypeID</a:Code>
<a:CreationDate>1392106689</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>AA9151C5-E988-4B75-BC0D-81B8319F71B0</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>FAlterID</a:Code>
<a:CreationDate>1392106727</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>B628D79C-46D8-45A1-B171-62EF75CBBFB9</a:ObjectID>
<a:Name>卡片编码</a:Name>
<a:Code>FNumber</a:Code>
<a:CreationDate>1392106758</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>C409B148-C82B-4962-89F1-FE4B870F19BD</a:ObjectID>
<a:Name>异常原因</a:Name>
<a:Code>FErrorDescription</a:Code>
<a:CreationDate>1392106758</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>252EDBFA-5462-431C-9470-7ADF5DDF355B</a:ObjectID>
<a:Name>年度</a:Name>
<a:Code>FCurrentYear</a:Code>
<a:CreationDate>1392106880</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>152B9DFC-EB5C-473B-85EE-32BC2CF5D83E</a:ObjectID>
<a:Name>期间</a:Name>
<a:Code>FCurrentPeriod</a:Code>
<a:CreationDate>1392106939</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>7413C701-00CA-4698-A760-C85918307EFF</a:ObjectID>
<a:Name>临时编号</a:Name>
<a:Code>FTempid</a:Code>
<a:CreationDate>1392106952</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>D502EA07-AA73-403D-AF94-4B4BB20191D7</a:ObjectID>
<a:Name>表单号</a:Name>
<a:Code>FBillno</a:Code>
<a:CreationDate>1392106952</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o8">
<a:ObjectID>1506D54E-50A0-42CD-B896-C7B057C8E419</a:ObjectID>
<a:Name>TM_FA_PreOneAlterCardDate(固定资产卡片变动数据临时表)</a:Name>
<a:Code>TM_FA_PreOneAlterCardDate</a:Code>
<a:CreationDate>1390443771</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390462518</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o65">
<a:ObjectID>6A3C72CF-A9E6-4879-9000-E49415EB350E</a:ObjectID>
<a:Name>固定资产内码</a:Name>
<a:Code>FAssetID</a:Code>
<a:CreationDate>1390443859</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317197</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>17E69BC8-A0A5-4499-8AE3-0EBE727C01CB</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>FAlterID</a:Code>
<a:CreationDate>1390462085</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317200</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>6A02D080-3BD4-402A-83EE-E91211B37B49</a:ObjectID>
<a:Name>资产原值</a:Name>
<a:Code>FOrgVal</a:Code>
<a:CreationDate>1390462159</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317204</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>706A288A-3C71-47AA-AB04-A9010BF6141F</a:ObjectID>
<a:Name>累计减值准备</a:Name>
<a:Code>FAccumDevalue</a:Code>
<a:CreationDate>1390462159</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317208</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>2D89EA48-ED3E-4783-AED6-2D9DF57B0664</a:ObjectID>
<a:Name>累计折旧额</a:Name>
<a:Code>FAccumDepr</a:Code>
<a:CreationDate>1390462159</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317212</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>5AF673A2-7B04-47CA-BF12-B67C93526311</a:ObjectID>
<a:Name>预计残值</a:Name>
<a:Code>FResidualvalue</a:Code>
<a:CreationDate>1390462159</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317217</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o71">
<a:ObjectID>07219C3C-511D-44CC-9522-E6792187D8A4</a:ObjectID>
<a:Name>折旧方法</a:Name>
<a:Code>FDeprMethod</a:Code>
<a:CreationDate>1390462253</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317302</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o72">
<a:ObjectID>A27B0904-F55F-40C9-BFF6-2173B331F04F</a:ObjectID>
<a:Name>预计使用期间</a:Name>
<a:Code>FLifePeriods</a:Code>
<a:CreationDate>1390462253</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317229</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o73">
<a:ObjectID>ECDECA33-E48D-4585-848E-9764B038D534</a:ObjectID>
<a:Name>累计折旧期间</a:Name>
<a:Code>FDeprPeriods</a:Code>
<a:CreationDate>1390462253</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317233</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>45982A57-6BBB-4742-91F7-80167786BCB3</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1390462253</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317237</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>A0EF42C4-F2DE-4E44-9A79-61401C60C333</a:ObjectID>
<a:Name>是否采用动态折旧方法</a:Name>
<a:Code>FISMODIFYDeprfac</a:Code>
<a:CreationDate>1390462253</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317252</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>06336934-483C-4691-AD50-B35C1AAE4267</a:ObjectID>
<a:Name>是否折旧要素变动</a:Name>
<a:Code>FIsDeprFactoryModify</a:Code>
<a:CreationDate>1390462253</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317258</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>74CEC6BE-75FC-4074-8425-F43F597E5070</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQuantity</a:Code>
<a:CreationDate>1390462253</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317274</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(19,6)</a:DataType>
<a:Length>19</a:Length>
<a:Precision>6</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o14">
<a:ObjectID>210CA8AA-4959-4CD1-92AE-812762F6AFB6</a:ObjectID>
<a:Name>TM_FA_AssetOrgAndProlicy(计提折旧所属组织会计政策临时表)</a:Name>
<a:Code>TM_FA_AssetOrgAndProlicy</a:Code>
<a:CreationDate>1390443801</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390462679</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o78">
<a:ObjectID>2E8D0222-9B65-4984-A7A7-084FB689172B</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOWNERORGID</a:Code>
<a:CreationDate>1390462614</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317427</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o79">
<a:ObjectID>5A3DAF1D-30D4-40F9-8DE2-27A94B07C0C7</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1390462614</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317431</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o80">
<a:ObjectID>D132EE8B-01A2-44A0-9DD0-CAC228DA7F54</a:ObjectID>
<a:Name>TM_FA_BALANCETEMPTABLE(固定资产余额结帐计算临时表)</a:Name>
<a:Code>TM_FA_BALANCETEMPTABLE</a:Code>
<a:CreationDate>1392022681</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o81">
<a:ObjectID>E2977B35-B4B9-4F28-89BF-BE04304981BC</a:ObjectID>
<a:Name>是否最新期间</a:Name>
<a:Code>FISNEWREC</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312740</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o82">
<a:ObjectID>00951E73-7742-4887-B9C6-939567200802</a:ObjectID>
<a:Name>内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312732</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o83">
<a:ObjectID>591C7E28-6541-4A76-8401-52193C20E0A4</a:ObjectID>
<a:Name>固定资产内码</a:Name>
<a:Code>FASSETID</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393315154</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>FFEEE253-2F23-4CF6-9819-578F7A5B5CBE</a:ObjectID>
<a:Name>会计政策ID</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312696</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>D53AB97B-CBEC-4714-805E-7FDBF292A62B</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOWNERORGID</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312700</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>DF5491F3-C33F-42EB-AF54-A8CDE729FA44</a:ObjectID>
<a:Name>当期年度</a:Name>
<a:Code>FYEAR</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312704</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>216366C0-D9AD-49CD-A9BC-C2F68C9B0D85</a:ObjectID>
<a:Name>当期期间</a:Name>
<a:Code>FPERIOD</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312710</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>21B3BAFE-A643-4D64-8D15-11C123EFEB3B</a:ObjectID>
<a:Name>期初原值</a:Name>
<a:Code>FORGVALP</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312544</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>32AF9985-CA81-44B6-B9DD-7E4773FA5E4F</a:ObjectID>
<a:Name>原值调增</a:Name>
<a:Code>FORGVALINC</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312550</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>960D951C-70E9-4439-A6FA-D7CC65E5759C</a:ObjectID>
<a:Name>原值调减</a:Name>
<a:Code>FORGVALDEC</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312554</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>BF810D67-6140-4D5F-82B9-FEE3FA34CE61</a:ObjectID>
<a:Name>期初减值准备</a:Name>
<a:Code>FDECPREP</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312559</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>A6635984-157A-4150-AEA4-00854DEC6BFA</a:ObjectID>
<a:Name>减值准备调增</a:Name>
<a:Code>FDECPREINC</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312563</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>6C2341B7-509B-43B0-9E2D-5A50C1B9CD5F</a:ObjectID>
<a:Name>减值准备调减</a:Name>
<a:Code>FDECPREDEC</a:Code>
<a:CreationDate>1392022699</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312568</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o94">
<a:ObjectID>9E8A060F-AD5C-4EF5-81B3-424D3FBE2A78</a:ObjectID>
<a:Name>本年原值累计调增</a:Name>
<a:Code>FYTDORGVALINC</a:Code>
<a:CreationDate>1392022847</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312573</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o95">
<a:ObjectID>C5A7687D-3B9F-40B6-9D01-26BD79FFF5D0</a:ObjectID>
<a:Name>期初累计折旧</a:Name>
<a:Code>FACCUMDEPRP</a:Code>
<a:CreationDate>1392022847</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312576</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>C5971499-8593-4D1B-8E29-A04BE22E9C75</a:ObjectID>
<a:Name>累计折旧调增</a:Name>
<a:Code>FACCUMDEPRINC</a:Code>
<a:CreationDate>1392022847</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312581</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>3881D4C4-FD37-456E-B8E8-61405DC70B3A</a:ObjectID>
<a:Name>累计折旧调减</a:Name>
<a:Code>FACCUMDEPRDEC</a:Code>
<a:CreationDate>1392022847</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312585</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>D25FB3ED-36C9-41A2-B02A-43DF374290E6</a:ObjectID>
<a:Name>本期应提折旧额备份</a:Name>
<a:Code>FSHOULDDEPRBAK</a:Code>
<a:CreationDate>1392022847</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312589</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>FF33CAC5-2F9F-498A-94D2-E182CCF340D4</a:ObjectID>
<a:Name>本期计提折旧额</a:Name>
<a:Code>FDEPR</a:Code>
<a:CreationDate>1392023768</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312594</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>BD74B5F9-4292-4C44-A67F-6F84502082C6</a:ObjectID>
<a:Name>本期计提折旧率</a:Name>
<a:Code>FDEPRRATE</a:Code>
<a:CreationDate>1392023035</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312597</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>85C5F169-C10E-4D8A-907B-0AA56243B6B4</a:ObjectID>
<a:Name>已提折旧期间</a:Name>
<a:Code>FDEPRPERIODS</a:Code>
<a:CreationDate>1392022847</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312609</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>2DCAEB00-C38A-4D97-B115-DD700122EE83</a:ObjectID>
<a:Name>剩余未折旧额</a:Name>
<a:Code>FDEPRREMAIN</a:Code>
<a:CreationDate>1392023100</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393315192</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>34374168-A7FD-4CD5-8CA3-F4600C40F5D6</a:ObjectID>
<a:Name>本年减值准备调增</a:Name>
<a:Code>FYTDDECPREINC</a:Code>
<a:CreationDate>1392023100</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312602</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o104">
<a:ObjectID>E0B4BD5B-D7A4-4BD3-B192-E8C4E04CEF94</a:ObjectID>
<a:Name>本年减值准备调减</a:Name>
<a:Code>FYTDDECPREDEC</a:Code>
<a:CreationDate>1392023100</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312620</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>F02C04F7-D644-49E7-8ABC-82238E94A274</a:ObjectID>
<a:Name>本年累计折旧调增</a:Name>
<a:Code>FYTDDEPRINC</a:Code>
<a:CreationDate>1392023100</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312624</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>C32F004B-8A40-48F4-9282-E8452B67A77C</a:ObjectID>
<a:Name>本年累计折旧调减</a:Name>
<a:Code>FYTDDEPRDEC</a:Code>
<a:CreationDate>1392023100</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312628</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>1D34E676-9F57-4193-9A84-14E48450A88F</a:ObjectID>
<a:Name>本期应提折旧额</a:Name>
<a:Code>FSHOULDDEPR</a:Code>
<a:CreationDate>1392022847</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312632</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o108">
<a:ObjectID>E9EB17AA-FE1C-4105-9B24-C4FAC56B2D1A</a:ObjectID>
<a:Name>本年原值累计调减</a:Name>
<a:Code>FYTDORGVALDEC</a:Code>
<a:CreationDate>1392023100</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312636</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>B70D496B-76F1-4B8A-885D-BBEE7A0D90DD</a:ObjectID>
<a:Name>最后一次变动日期</a:Name>
<a:Code>FLASTALTDATE</a:Code>
<a:CreationDate>1392023229</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>2107885C-6447-4A7E-ADBD-D548398EC9AC</a:ObjectID>
<a:Name>卡片当前状态</a:Name>
<a:Code>FASSETCURSTATUS</a:Code>
<a:CreationDate>1392023229</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312648</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>D26E77CD-AADE-4C84-9B95-12C93C6D47EF</a:ObjectID>
<a:Name>年初折旧期间</a:Name>
<a:Code>FYTDDEPRPERIOD</a:Code>
<a:CreationDate>1392023229</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>8A62BE18-1B1B-484F-85AF-48E87A5A3CEA</a:ObjectID>
<a:Name>是否新折旧年度第一期</a:Name>
<a:Code>FISNEXTFIRSTPERIOD</a:Code>
<a:CreationDate>1392023229</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>5C5CDD33-6F12-45F1-A39A-7E48FBB73D6D</a:ObjectID>
<a:Name>表头ID</a:Name>
<a:Code>FMAINID</a:Code>
<a:CreationDate>1392023229</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>E8E1B6B4-5D9C-45A4-B07C-2680C61AA530</a:ObjectID>
<a:Name>本年累计折旧</a:Name>
<a:Code>FCURYEARDEPR</a:Code>
<a:CreationDate>1392023229</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312673</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>89D0D504-6347-4F2B-9C95-7258743CD58F</a:ObjectID>
<a:Name>当前业务操作</a:Name>
<a:Code>FCUROPERATION</a:Code>
<a:CreationDate>1392023229</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>EBB409F3-CDCD-467D-8EAB-1B320BF2AE82</a:ObjectID>
<a:Name>当前业务操作单据编号</a:Name>
<a:Code>FCUROPERATIONBILLNO</a:Code>
<a:CreationDate>1392023229</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>8CABD81E-003A-4D2F-8A1F-73E716B1FE8F</a:ObjectID>
<a:Name>已折旧工作量</a:Name>
<a:Code>FDEPRWORKLOAD</a:Code>
<a:CreationDate>1392023439</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312680</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>47EC6673-6A1E-4422-9D73-4AC54E49AFC3</a:ObjectID>
<a:Name>预计残值</a:Name>
<a:Code>FResidualvalue</a:Code>
<a:CreationDate>1392023439</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312687</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>CFBCB9BD-2840-4EDD-82BD-629AF55D9DDE</a:ObjectID>
<a:Name>顺序号</a:Name>
<a:Code>FSeq</a:Code>
<a:CreationDate>1392023439</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o120">
<a:ObjectID>6BEE3ED8-17CD-4131-BF19-DFAD0F824823</a:ObjectID>
<a:Name>当前年度</a:Name>
<a:Code>FCurrentYear</a:Code>
<a:CreationDate>1392023439</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o121">
<a:ObjectID>5A706DDE-EF18-46F8-85A2-2BA968FD5CD5</a:ObjectID>
<a:Name>当前期间</a:Name>
<a:Code>FCurrentPeriod</a:Code>
<a:CreationDate>1392023439</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>433BE908-0AFD-487E-BDEF-290103D95C7B</a:ObjectID>
<a:Name>下一期年度</a:Name>
<a:Code>FNextYear</a:Code>
<a:CreationDate>1392023552</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>E6ABE1B5-4F72-4E18-91FE-C64E4C65FDB4</a:ObjectID>
<a:Name>下一期期间</a:Name>
<a:Code>FNextPeriod</a:Code>
<a:CreationDate>1392023552</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>C4B10E14-2118-4077-9FB9-A6A67A13D541</a:ObjectID>
<a:Name>结束年</a:Name>
<a:Code>FLastYear</a:Code>
<a:CreationDate>1392023552</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>7793BD52-886C-4604-9103-B8A763644134</a:ObjectID>
<a:Name>结束期</a:Name>
<a:Code>FLastPeriod</a:Code>
<a:CreationDate>1392023552</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>2B513664-93DC-47AF-AD35-AFDB878CBB30</a:ObjectID>
<a:Name>是否本期部分清理的资产</a:Name>
<a:Code>FIsCurrClear</a:Code>
<a:CreationDate>1392023439</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312538</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o12">
<a:ObjectID>C325732C-711F-40B6-B980-63847EBD13DA</a:ObjectID>
<a:Name>TM_FA_AssetSystemProfile(计提折旧系统参数临时表)</a:Name>
<a:Code>TM_FA_AssetSystemProfile</a:Code>
<a:CreationDate>1390443782</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390462903</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o127">
<a:ObjectID>CAFF8B96-9DC0-436D-AFDF-5F2F513899BF</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOrgId</a:Code>
<a:CreationDate>1390462695</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390462903</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>8533A1B9-FA2D-4431-A7A4-96373CE9F14B</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>Facctpolicyid</a:Code>
<a:CreationDate>1390462695</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390462791</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o129">
<a:ObjectID>3D1F03E8-6DFB-4A4A-82A5-C60D025C08F1</a:ObjectID>
<a:Name>当前年度</a:Name>
<a:Code>FCurrentYear</a:Code>
<a:CreationDate>1390462739</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397616059</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o130">
<a:ObjectID>ED32F8E5-87F7-4F18-A555-9049BD118831</a:ObjectID>
<a:Name>当前期间</a:Name>
<a:Code>FCurrentPeriod</a:Code>
<a:CreationDate>1390462739</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390462791</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o131">
<a:ObjectID>B07335CA-8337-48E7-A8FB-4B67CA8748D0</a:ObjectID>
<a:Name>TM_FA_AcctPolicy(固定资产会计政策分组表)</a:Name>
<a:Code>TM_FA_AcctPolicy</a:Code>
<a:CreationDate>1392024062</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o132">
<a:ObjectID>C1146CCB-B5E3-44A5-9B86-7B60F4AE714A</a:ObjectID>
<a:Name>顺序号</a:Name>
<a:Code>FSeq</a:Code>
<a:CreationDate>1392024073</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615732</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o133">
<a:ObjectID>85F7DFFA-82E9-43A0-88BF-3315C9EBE797</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOwnerOrgID</a:Code>
<a:CreationDate>1392024090</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615693</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>A7F63623-C1DA-4185-A6BB-5202332B0733</a:ObjectID>
<a:Name>会计政策ID</a:Name>
<a:Code>FAcctPolicyID</a:Code>
<a:CreationDate>1392024090</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615702</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>8ADB59E7-3D7E-4288-A692-4D7B05348241</a:ObjectID>
<a:Name>当期期间</a:Name>
<a:Code>fperiod</a:Code>
<a:CreationDate>1392024090</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615715</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>7DF86258-7922-468F-A64B-5CBEB491FD12</a:ObjectID>
<a:Name>当期年度</a:Name>
<a:Code>FYear</a:Code>
<a:CreationDate>1392024090</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615719</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o137">
<a:ObjectID>769472AF-4603-43B7-BB54-14FD8F7B7372</a:ObjectID>
<a:Name>TM_FA_CardCheckTable(固定资产卡片检查临时表)</a:Name>
<a:Code>TM_FA_CardCheckTable</a:Code>
<a:CreationDate>1392024175</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:Comment>固定资产卡片反审核、变更、撤消检查临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o138">
<a:ObjectID>BDFEF8D9-ED5D-4A3E-AF06-1678A756BD74</a:ObjectID>
<a:Name>货主组织</a:Name>
<a:Code>FOwnerOrgId</a:Code>
<a:CreationDate>1392024194</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397616105</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o139">
<a:ObjectID>A3B05029-67B8-4CBD-96E4-571285E83153</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>Falterid</a:Code>
<a:CreationDate>1392024211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397616111</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o140">
<a:ObjectID>36489076-EBB6-4C75-B378-31B7CD73A29D</a:ObjectID>
<a:Name>卡片内码</a:Name>
<a:Code>FAssetID</a:Code>
<a:CreationDate>1392024211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397616116</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o141">
<a:ObjectID>9F1AA69C-3922-4920-AE47-A2DF47EC93D6</a:ObjectID>
<a:Name>卡片编码</a:Name>
<a:Code>FNumber</a:Code>
<a:CreationDate>1392024211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o142">
<a:ObjectID>9A8A2946-038D-4C07-B8B9-4C3663669D0F</a:ObjectID>
<a:Name>是否最新变动</a:Name>
<a:Code>FIsNewRec</a:Code>
<a:CreationDate>1392024211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>A8E0F553-43EC-495B-A1A8-BFB84A3DF67F</a:ObjectID>
<a:Name>卡片当前状态</a:Name>
<a:Code>FAssetCurStatus</a:Code>
<a:CreationDate>1392024263</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>68AC5361-015C-4882-9FAE-AE55953AA2C7</a:ObjectID>
<a:Name>卡片来源</a:Name>
<a:Code>FSource</a:Code>
<a:CreationDate>1392024263</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>BA776E74-EE94-480F-B65B-4B3A7F01F068</a:ObjectID>
<a:Name>数据状态</a:Name>
<a:Code>FDocumentStatus</a:Code>
<a:CreationDate>1392024263</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>C649C0F1-3F33-4C82-BF8D-B91B155D16CF</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>Facctpolicyid</a:Code>
<a:CreationDate>1392024211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>60CC0920-DD44-4BD5-99E4-BE658D86BBC1</a:ObjectID>
<a:Name>会计政策名称</a:Name>
<a:Code>FAcctPolicyName</a:Code>
<a:CreationDate>1392024211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>5020F0D4-CA15-47FB-AF8F-31FE587E32F8</a:ObjectID>
<a:Name>入账日期</a:Name>
<a:Code>FAcctDate</a:Code>
<a:CreationDate>1392024211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o149">
<a:ObjectID>50CEE8D1-8651-4C0F-9C37-F1DF7BF44B1F</a:ObjectID>
<a:Name>变动年度</a:Name>
<a:Code>FYear</a:Code>
<a:CreationDate>1392024211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o150">
<a:ObjectID>E1444681-52EF-4A22-946B-B3BEC6B9D4E7</a:ObjectID>
<a:Name>变动期间</a:Name>
<a:Code>FPeriod</a:Code>
<a:CreationDate>1392024346</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>811D5BE4-0A07-4AFE-B9A9-9D6A06297FB4</a:ObjectID>
<a:Name>固定资产余额当期年度</a:Name>
<a:Code>FCurYear</a:Code>
<a:CreationDate>1392024346</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o152">
<a:ObjectID>0D4F08C2-E787-4B27-BB93-DE25BFA5D28A</a:ObjectID>
<a:Name>固定资产余额当期期间</a:Name>
<a:Code>FCurPeriod</a:Code>
<a:CreationDate>1392024346</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>5ABC1D67-E1E3-4AC6-864F-3B5C92A3C560</a:ObjectID>
<a:Name>结过账状态</a:Name>
<a:Code>FAcctStatus</a:Code>
<a:CreationDate>1392024346</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o154">
<a:ObjectID>8C4C78A1-8E53-4E48-AABA-68DAA8900928</a:ObjectID>
<a:Name>资产是否处置</a:Name>
<a:Code>FIsDisposed</a:Code>
<a:CreationDate>1392024346</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>A765973A-0846-4FEA-86CE-A696CBD7A87B</a:ObjectID>
<a:Name>资产处置单号</a:Name>
<a:Code>FDisposeBillNO</a:Code>
<a:CreationDate>1392024346</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>70CE9913-A41E-4F4F-BC63-57B821AB930E</a:ObjectID>
<a:Name>资产是否变更</a:Name>
<a:Code>FIsChanged</a:Code>
<a:CreationDate>1392024468</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o157">
<a:ObjectID>9A3F1678-5557-493C-8E3A-BE5E9215E712</a:ObjectID>
<a:Name>资产变更单号</a:Name>
<a:Code>FChangeBillNO</a:Code>
<a:CreationDate>1392024468</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>39D864E9-D06A-43FC-AA27-F2DA990E6BDB</a:ObjectID>
<a:Name>资产是否批次变更</a:Name>
<a:Code>FIsBatChanged</a:Code>
<a:CreationDate>1392024468</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>F538D447-F857-40E7-9643-44020DB1C8D2</a:ObjectID>
<a:Name>资产是否调出</a:Name>
<a:Code>FIsDelivery</a:Code>
<a:CreationDate>1392024532</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>A4CB5CC5-EDBE-4AB9-B1CE-B724F09DC4EB</a:ObjectID>
<a:Name>资产调出单号</a:Name>
<a:Code>FDeliveryBillNO</a:Code>
<a:CreationDate>1392024468</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>C459EB5A-8D05-4F23-A635-C10FD9AFF6EB</a:ObjectID>
<a:Name>是否盘点</a:Name>
<a:Code>FIsInvent</a:Code>
<a:CreationDate>1392024562</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>08BCDC1F-740E-404D-8DC3-352052D6CD76</a:ObjectID>
<a:Name>资产盘点单号</a:Name>
<a:Code>FInventBillNO</a:Code>
<a:CreationDate>1392024562</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>AE684E77-F66B-42AD-91DF-F05BAABD6250</a:ObjectID>
<a:Name>是否折旧调整</a:Name>
<a:Code>FIsDeprAdjust</a:Code>
<a:CreationDate>1392024562</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>8843960F-09A4-4EE7-BB7A-48E3DFD4389A</a:ObjectID>
<a:Name>折旧调整单号</a:Name>
<a:Code>FDeprAdjustBillNO</a:Code>
<a:CreationDate>1392024562</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o165">
<a:ObjectID>3827ED7B-D60A-4EBE-91AA-1BEF5E9743A0</a:ObjectID>
<a:Name>TM_FA_PloicyTempTable(固定资产会计政策临时表)</a:Name>
<a:Code>TM_FA_PloicyTempTable</a:Code>
<a:CreationDate>1392022462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o166">
<a:ObjectID>7D90E143-9B78-49C6-BD3B-9133CF1ACBDE</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOwnerOrgId</a:Code>
<a:CreationDate>1392022471</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397616163</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o167">
<a:ObjectID>A63D1FAD-1965-428D-A056-99980E529090</a:ObjectID>
<a:Name>会计政策ID</a:Name>
<a:Code>FAcctPolicyId</a:Code>
<a:CreationDate>1392022492</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397616167</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>7082D999-4812-4F78-95B1-D3BE86EFFB5F</a:ObjectID>
<a:Name>结束年</a:Name>
<a:Code>FLastYear</a:Code>
<a:CreationDate>1392022508</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>17A3C54C-56BC-468C-BF12-081B3AB0DAB3</a:ObjectID>
<a:Name>下一期期间</a:Name>
<a:Code>FNextPeriod</a:Code>
<a:CreationDate>1392022508</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>BCDCBACA-5B3D-40BA-A608-FE9F887946E3</a:ObjectID>
<a:Name>下一期年度</a:Name>
<a:Code>FNextYear</a:Code>
<a:CreationDate>1392022508</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o171">
<a:ObjectID>D785BDA6-CC42-4C97-A629-D69375A3F65F</a:ObjectID>
<a:Name>当前期间</a:Name>
<a:Code>FCurrentPeriod</a:Code>
<a:CreationDate>1392022508</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o172">
<a:ObjectID>81B7C2B0-7031-4C80-A81D-DC9819EFFDD5</a:ObjectID>
<a:Name>当前年度</a:Name>
<a:Code>FCurrentYear</a:Code>
<a:CreationDate>1392022508</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o173">
<a:ObjectID>65ABAACF-7DEF-4585-A4A9-ED2E10C32DF0</a:ObjectID>
<a:Name>结束期</a:Name>
<a:Code>FLastPeriod</a:Code>
<a:CreationDate>1392022527</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o174">
<a:ObjectID>69421C96-F0E6-4E29-9806-366A16005987</a:ObjectID>
<a:Name>开始年</a:Name>
<a:Code>FStartYear</a:Code>
<a:CreationDate>1392022527</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o175">
<a:ObjectID>30356AB5-78E6-4981-AECF-6F272C1AB8C0</a:ObjectID>
<a:Name>开始期</a:Name>
<a:Code>FStartPeriod</a:Code>
<a:CreationDate>1392022527</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o176">
<a:ObjectID>F6A0B629-D56A-4503-A225-F1CE96B412C8</a:ObjectID>
<a:Name>当前期的开始日期</a:Name>
<a:Code>FStartDate</a:Code>
<a:CreationDate>1392022527</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>B3F7F576-9CDA-433B-AE0C-6401C9CE2254</a:ObjectID>
<a:Name>当前期的结束日期</a:Name>
<a:Code>FEndDate</a:Code>
<a:CreationDate>1392022527</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392087343</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>datetime</a:DataType>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o178">
<a:ObjectID>57A2E32B-C942-4AA8-93AA-DE6DEB244353</a:ObjectID>
<a:Name>TM_FA_ASSERTDEPRENTRY(固定资产计提折旧明细临时表)</a:Name>
<a:Code>TM_FA_ASSERTDEPRENTRY</a:Code>
<a:CreationDate>1390443778</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o179">
<a:ObjectID>ACC52C95-201B-46E3-8A93-DB9C879677B6</a:ObjectID>
<a:Name>分录内码</a:Name>
<a:Code>FEntryID</a:Code>
<a:CreationDate>1392099705</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615793</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>625E64A4-D3A2-45BB-BC30-F9685C90C5D9</a:ObjectID>
<a:Name>资产盘点表内码</a:Name>
<a:Code>FID</a:Code>
<a:CreationDate>1392099705</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615797</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>0EF06CBF-98F1-4153-BFC5-36A69254EC89</a:ObjectID>
<a:Name>序号</a:Name>
<a:Code>FSEQ</a:Code>
<a:CreationDate>1392099705</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615802</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>481DAA97-0793-407C-A14E-1277B59CF040</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOWNERORGID</a:Code>
<a:CreationDate>1390462844</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615806</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>9F76CB0A-F661-404A-B335-D8F7FC3E870A</a:ObjectID>
<a:Name>资产类别</a:Name>
<a:Code>FASSETTYPEID</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615810</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>CB9E48D4-F6D9-4E5D-B6B9-04D5AF49778B</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>FALTERID</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615815</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>3DEBE64F-B19A-443C-AFD2-BB967310384A</a:ObjectID>
<a:Name>变动年度</a:Name>
<a:Code>FYEAR</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615822</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>2C41BA99-33D6-40EA-AF4B-007D80AE0893</a:ObjectID>
<a:Name>变动期间</a:Name>
<a:Code>FPERIOD</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615827</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>D4BDCDB4-275C-4570-9EB1-F3A32B22F43A</a:ObjectID>
<a:Name>固定资产折旧余额</a:Name>
<a:Code>FDEPRREMAIN</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615834</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o188">
<a:ObjectID>2F0C8C11-F4E3-4EB6-846A-92760DCE6EAA</a:ObjectID>
<a:Name>本期应提折旧额备份</a:Name>
<a:Code>FSHOULDDEPR</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615837</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o189">
<a:ObjectID>3F30A64D-3D8C-4058-A92D-3776AD3C0194</a:ObjectID>
<a:Name>本期计提折旧额</a:Name>
<a:Code>FDEPR</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615842</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o190">
<a:ObjectID>574B362E-DAD5-4C92-BE8E-B65244CC82F5</a:ObjectID>
<a:Name>本期计提折旧率</a:Name>
<a:Code>FDEPRRATE</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615847</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o191">
<a:ObjectID>A47763AE-8F97-4AAA-91C6-17863254CBDA</a:ObjectID>
<a:Name>临时分录内码</a:Name>
<a:Code>FTempEntryID</a:Code>
<a:CreationDate>1392100184</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o192">
<a:ObjectID>02929D69-8D25-4F0E-8C76-9B9925F45823</a:ObjectID>
<a:Name>处置单本期折旧额</a:Name>
<a:Code>FDisposeDepr</a:Code>
<a:CreationDate>1392100070</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615860</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o193">
<a:ObjectID>76348FEC-B946-4880-A59C-427FE970D4D1</a:ObjectID>
<a:Name>手工调整折旧额</a:Name>
<a:Code>FMANUALDEPR</a:Code>
<a:CreationDate>1392100070</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615864</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>FA42869E-68CD-422B-9431-57715A3AB667</a:ObjectID>
<a:Name>本期卡片部分的折旧额</a:Name>
<a:Code>FCardDepr</a:Code>
<a:CreationDate>1392100070</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615869</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>A71AF206-B30A-4EF3-A1BE-95B9DE00F98B</a:ObjectID>
<a:Name>新增、清理折旧政策</a:Name>
<a:Code>FCLEARDEPRPOLICY</a:Code>
<a:CreationDate>1390463271</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397616026</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>FE09C983-0D65-48C1-AE89-E97B1E50A526</a:ObjectID>
<a:Name>卡片当前状态</a:Name>
<a:Code>FASSETCURSTATUS</a:Code>
<a:CreationDate>1390463271</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397616034</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>1913174D-6200-4D71-B588-AB2EF50F6B39</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FMQuantity</a:Code>
<a:CreationDate>1390463347</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615920</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>58E65F19-F8C4-47D8-8949-411540629152</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1390463397</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615916</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>218EBF14-1402-486F-A0E3-104E32CA270B</a:ObjectID>
<a:Name>当前年度</a:Name>
<a:Code>FCURRENTYEAR</a:Code>
<a:CreationDate>1390463397</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>5E0A5919-79A9-48BA-BA78-8DEE8628D33B</a:ObjectID>
<a:Name>当前期间</a:Name>
<a:Code>FCURRENTPERIOD</a:Code>
<a:CreationDate>1390463397</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o201">
<a:ObjectID>A80E7EDA-F644-4170-A74A-0AFC5A7B691B</a:ObjectID>
<a:Name>资产原值</a:Name>
<a:Code>FORGVAL</a:Code>
<a:CreationDate>1390463462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615931</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o202">
<a:ObjectID>C8C929A0-9B6C-4F79-B074-20B2CF743FCE</a:ObjectID>
<a:Name>累计减值准备</a:Name>
<a:Code>FACCUMDEVALUE</a:Code>
<a:CreationDate>1390463462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615936</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o203">
<a:ObjectID>42516309-C5E3-4FF6-BE5C-8360A4854233</a:ObjectID>
<a:Name>累计折旧额</a:Name>
<a:Code>FACCUMDEPR</a:Code>
<a:CreationDate>1390463462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615940</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>2A14FA48-6796-4D10-97F0-1C983345DCDF</a:ObjectID>
<a:Name>预计残值</a:Name>
<a:Code>FRESIDUALVALUE</a:Code>
<a:CreationDate>1390463462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615946</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o205">
<a:ObjectID>1625D31F-C640-434A-BD7C-53831652922D</a:ObjectID>
<a:Name>预计使用期间</a:Name>
<a:Code>FLIFEPERIODS</a:Code>
<a:CreationDate>1390463598</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o206">
<a:ObjectID>EE1902FA-5EF4-4D56-9D86-2A2054E5F950</a:ObjectID>
<a:Name>累计折旧期间</a:Name>
<a:Code>FDEPRPERIODS</a:Code>
<a:CreationDate>1390463598</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o207">
<a:ObjectID>D4F95266-8BC3-4EF4-9688-8473674088C0</a:ObjectID>
<a:Name>折旧方法</a:Name>
<a:Code>FDEPRMETHOD</a:Code>
<a:CreationDate>1390465375</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o208">
<a:ObjectID>968CB311-8EE3-4593-B0B3-EE59404C6F7F</a:ObjectID>
<a:Name>会计日历期间数</a:Name>
<a:Code>FPERIODCOUNT</a:Code>
<a:CreationDate>1390463818</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o209">
<a:ObjectID>81B351B1-056D-478F-8BC2-DDCF4FC119D4</a:ObjectID>
<a:Name>剩余使用年限</a:Name>
<a:Code>FREMAINYEAR</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o210">
<a:ObjectID>BE3C75D6-C4E3-407D-801A-2725C97B0C63</a:ObjectID>
<a:Name>预计使用期间比例</a:Name>
<a:Code>FLIFEYEAR</a:Code>
<a:CreationDate>1390463818</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o211">
<a:ObjectID>56F7C08C-BE58-42D8-8E4E-39C38BD2F9A8</a:ObjectID>
<a:Name>公式内容</a:Name>
<a:Code>FFORMULACONTENT</a:Code>
<a:CreationDate>1390464062</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(400)</a:DataType>
<a:Length>400</a:Length>
</o:Column>
<o:Column Id="o212">
<a:ObjectID>64AC1369-AE17-478B-9DF2-9C7C7A685367</a:ObjectID>
<a:Name>预计工作总量</a:Name>
<a:Code>FPredictWorkLoad</a:Code>
<a:CreationDate>1390464285</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615959</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o213">
<a:ObjectID>BED59EC2-8766-47AB-BF7A-C6B1C629394E</a:ObjectID>
<a:Name>本期工作量</a:Name>
<a:Code>FPeriodWorkLoad</a:Code>
<a:CreationDate>1390464285</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1397615954</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o214">
<a:ObjectID>E15DF405-5A57-4F70-81F0-4F4EEC6F37FE</a:ObjectID>
<a:Name>工作量单位</a:Name>
<a:Code>FWorkLoadUnitID</a:Code>
<a:CreationDate>1390464415</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o215">
<a:ObjectID>FF8B194F-829A-427B-B0C6-08FE966AF76F</a:ObjectID>
<a:Name>已折旧工作量</a:Name>
<a:Code>FDeprWorkLoad</a:Code>
<a:CreationDate>1390464433</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103757</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o216">
<a:ObjectID>B637DA01-04F9-47BD-A3ED-F1EF38CD898E</a:ObjectID>
<a:Name>TM_FA_DEPRADJUSTPERIOD(固定资产折旧调整期间临时表)</a:Name>
<a:Code>TM_FA_DEPRADJUSTPERIOD</a:Code>
<a:CreationDate>1392087524</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1399255354</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o217">
<a:ObjectID>17556F97-D868-4054-8152-E7FF6A8EBE45</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOWNERORGID</a:Code>
<a:CreationDate>1392087612</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o218">
<a:ObjectID>39F38603-E18A-4A98-8DA2-F4C31C804A8E</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1392087645</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o219">
<a:ObjectID>CE0B411F-7704-4397-BEE2-B2299A27338E</a:ObjectID>
<a:Name>当前年度</a:Name>
<a:Code>FCurrentYear</a:Code>
<a:CreationDate>1392087665</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o220">
<a:ObjectID>94E7AECA-F151-44F8-B77F-9201B9D90C26</a:ObjectID>
<a:Name>当前期间</a:Name>
<a:Code>FCurrentPeriod</a:Code>
<a:CreationDate>1392087665</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392088248</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o221">
<a:ObjectID>79DB6E29-4B80-4B01-8051-0C41E8E868A7</a:ObjectID>
<a:Name>TM_FA_ASSETANDALTERID(卡片ID临时表)</a:Name>
<a:Code>TM_FA_ASSETANDALTERID</a:Code>
<a:CreationDate>1428029600</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:Comment>记录卡片的FASSETID和FALTERID临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o222">
<a:ObjectID>A1AD09A2-F285-4468-A298-C06507558718</a:ObjectID>
<a:Name>卡片内码</a:Name>
<a:Code>FASSETID</a:Code>
<a:CreationDate>1428029747</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o223">
<a:ObjectID>AEE5587F-643B-4AE3-92AC-7F44F765820A</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>FALTERID</a:Code>
<a:CreationDate>1428029747</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o224">
<a:ObjectID>FF403987-EA92-47B0-8E30-C70033E0C705</a:ObjectID>
<a:Name>IDX_FA_ASSETANDALTERID</a:Name>
<a:Code>IDX_FA_ASSETANDALTERID</a:Code>
<a:CreationDate>1428029991</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o225">
<a:ObjectID>F898C168-3877-4470-99F2-EA7A658F9E8A</a:ObjectID>
<a:CreationDate>1428030070</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o222"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o226">
<a:ObjectID>B94C6E3E-4CB3-4808-8FD3-44DBE876627E</a:ObjectID>
<a:CreationDate>1428030070</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o223"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o227">
<a:ObjectID>D10C712A-7F04-4665-B8B7-1138F15137B1</a:ObjectID>
<a:Name>TM_FA_CARDPOLICY(卡片会计政策临时表)</a:Name>
<a:Code>TM_FA_CARDPOLICY</a:Code>
<a:CreationDate>1428030297</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:Comment>卡片会计政策临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o228">
<a:ObjectID>987D3B5D-DD22-45B3-85CB-453446C39A99</a:ObjectID>
<a:Name>货主组织</a:Name>
<a:Code>FOWNERORGID</a:Code>
<a:CreationDate>1428030297</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o229">
<a:ObjectID>EA68FE9C-99B2-48BF-AE33-C40D312846B0</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1428030297</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o230">
<a:ObjectID>AB558682-8065-4274-9E0B-91EA9F30E0C2</a:ObjectID>
<a:Name>卡片内码</a:Name>
<a:Code>FASSETID</a:Code>
<a:CreationDate>1428030297</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o231">
<a:ObjectID>1D4E4BFE-19F4-44B0-A906-5D2A418967E4</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>FALTERID</a:Code>
<a:CreationDate>1428030602</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o232">
<a:ObjectID>C8DE71BA-22DF-4D4D-AF51-B4B5733685CB</a:ObjectID>
<a:Name>IDX_FA_CARDPOLICY</a:Name>
<a:Code>IDX_FA_CARDPOLICY</a:Code>
<a:CreationDate>1428030735</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o233">
<a:ObjectID>44001634-5D70-4A45-B705-8B6ABF02D9BD</a:ObjectID>
<a:CreationDate>1428030781</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o228"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o234">
<a:ObjectID>99C2AF31-727E-45F0-84BE-C05FE0E9787E</a:ObjectID>
<a:CreationDate>1428030781</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o229"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o235">
<a:ObjectID>40866116-1B19-41DB-985D-D914C3A09B02</a:ObjectID>
<a:CreationDate>1428030781</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1428031179</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o231"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
<o:Table Id="o236">
<a:ObjectID>C4CB3738-AFA3-473F-AAE8-8C61019457E1</a:ObjectID>
<a:Name>TM_FA_ASSERTDEPRDETAIL(固定资产计提折旧子分录临时表)</a:Name>
<a:Code>TM_FA_ASSERTDEPRDETAIL</a:Code>
<a:CreationDate>1392104038</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:Comment>固定资产计提折旧子分录临时表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o237">
<a:ObjectID>2AA51F0D-B8FF-4E6C-BC32-ADF0586ED00B</a:ObjectID>
<a:Name>子分录内码</a:Name>
<a:Code>FDetailID</a:Code>
<a:CreationDate>1392104064</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o238">
<a:ObjectID>CEFBA2C8-67A5-490F-B63C-11C1895C4355</a:ObjectID>
<a:Name>临时分录ID</a:Name>
<a:Code>FTempEntryID</a:Code>
<a:CreationDate>1392104151</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o239">
<a:ObjectID>4D9D423F-CC60-4563-A0B2-D07B3BE79CA8</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>FAlterID</a:Code>
<a:CreationDate>1392104178</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o240">
<a:ObjectID>4E7DB703-ACFD-480B-A776-E3570E00164B</a:ObjectID>
<a:Name>使用部门</a:Name>
<a:Code>FUseDeptID</a:Code>
<a:CreationDate>1392104211</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o241">
<a:ObjectID>DBCA921C-8401-492B-AF1A-F82AADB251D8</a:ObjectID>
<a:Name>使用人</a:Name>
<a:Code>FUserID</a:Code>
<a:CreationDate>1392104220</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o242">
<a:ObjectID>48919E24-5D36-4EA7-9598-A460BC007270</a:ObjectID>
<a:Name>分配比例</a:Name>
<a:Code>FAllocRatio</a:Code>
<a:CreationDate>1392104278</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o243">
<a:ObjectID>41B26A9E-1A10-400B-8550-1A9F926C7EC0</a:ObjectID>
<a:Name>成本项目</a:Name>
<a:Code>FCostItemID</a:Code>
<a:CreationDate>1392104353</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o244">
<a:ObjectID>6B3DFADB-42BC-45F5-B380-47645BD665E4</a:ObjectID>
<a:Name>临时子分录内码</a:Name>
<a:Code>FTempDetailID</a:Code>
<a:CreationDate>1392104392</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o245">
<a:ObjectID>5C762D54-E223-4062-A258-1E7B1AC5C497</a:ObjectID>
<a:Name>实物资产编码</a:Name>
<a:Code>FAssetNO</a:Code>
<a:CreationDate>1392104392</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o246">
<a:ObjectID>680D22FA-F1C4-43DE-83FD-11748DA0EA56</a:ObjectID>
<a:Name>本期计提折旧额</a:Name>
<a:Code>FDEPR</a:Code>
<a:CreationDate>1392104451</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o247">
<a:ObjectID>4D7C4238-F3FD-4487-8851-77F456AAFFDF</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FMquantity</a:Code>
<a:CreationDate>1392104537</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392108899</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o248">
<a:ObjectID>050EE589-9999-47CD-B56C-B9DA4758F5CA</a:ObjectID>
<a:Name>分配分录ID</a:Name>
<a:Code>FALLOCATEID</a:Code>
<a:CreationDate>1431065048</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1431065199</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
</o:Table>
<o:Table Id="o10">
<a:ObjectID>17F4C664-4B4E-4079-9D7C-201CB6D9A3AD</a:ObjectID>
<a:Name>TM_FA_ASSERTDEPR(固定资产计提折旧计算临时表)</a:Name>
<a:Code>TM_FA_ASSERTDEPR</a:Code>
<a:CreationDate>1390443778</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1563758217</a:ModificationDate>
<a:Modifier>rd_junchao_he</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o249">
<a:ObjectID>D82F626F-C363-4A1E-A871-33A0511D8FDD</a:ObjectID>
<a:Name>卡片编码</a:Name>
<a:Code>FNUMBER</a:Code>
<a:CreationDate>1390462844</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393312351</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;&#39;</a:DefaultValue>
<a:DataType>varchar(80)</a:DataType>
<a:Length>80</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o250">
<a:ObjectID>2C6ACC33-152B-4E8F-8EC7-FDB84130A002</a:ObjectID>
<a:Name>货主组织ID</a:Name>
<a:Code>FOWNERORGID</a:Code>
<a:CreationDate>1390462844</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393316969</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o251">
<a:ObjectID>E14A61CB-0573-45D8-A05E-EBD3E5175E4C</a:ObjectID>
<a:Name>资产类别</a:Name>
<a:Code>FASSETTYPEID</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393316973</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o252">
<a:ObjectID>C6E906D9-B14C-44EA-86D8-D2122DCFAF43</a:ObjectID>
<a:Name>固定资产内码</a:Name>
<a:Code>FASSETID</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393316979</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o253">
<a:ObjectID>1700E6CC-8B2A-4A08-83CC-D185C5D6F39F</a:ObjectID>
<a:Name>卡片变动内码</a:Name>
<a:Code>FALTERID</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393316984</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o254">
<a:ObjectID>D2C67D5B-AB78-466C-9CE7-A19E4FDEA329</a:ObjectID>
<a:Name>变动年度</a:Name>
<a:Code>FYEAR</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393316988</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o255">
<a:ObjectID>F08F49B2-DED4-4A35-B9BB-69896AB7207B</a:ObjectID>
<a:Name>变动期间</a:Name>
<a:Code>FPERIOD</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393316993</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o256">
<a:ObjectID>6F7E87F2-945F-4289-BC3A-26B349072796</a:ObjectID>
<a:Name>固定资产折旧余额</a:Name>
<a:Code>FDEPRREMAIN</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393316997</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o257">
<a:ObjectID>6797BA0F-A5DE-4B71-AA10-BD4FC08DD837</a:ObjectID>
<a:Name>本期应提折旧额备份</a:Name>
<a:Code>FSHOULDDEPR</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317002</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o258">
<a:ObjectID>29E84ACA-011E-4B89-97DF-70E2A1C1028E</a:ObjectID>
<a:Name>本期计提折旧额</a:Name>
<a:Code>FDEPR</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317010</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o259">
<a:ObjectID>9FD30D9B-48B2-4CC5-A7F4-BA6ECD7192B7</a:ObjectID>
<a:Name>本期计提折旧率</a:Name>
<a:Code>FDEPRRATE</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317014</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o260">
<a:ObjectID>547676D0-18AF-4F9A-A2A0-363CF329CB33</a:ObjectID>
<a:Name>是否最后一期</a:Name>
<a:Code>ISLASTPERIOD</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317021</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o261">
<a:ObjectID>71DAA6B8-86FC-4FA0-88AE-0C20E230C6DE</a:ObjectID>
<a:Name>新增、清理折旧政策</a:Name>
<a:Code>FCLEARDEPRPOLICY</a:Code>
<a:CreationDate>1390463271</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317034</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o262">
<a:ObjectID>D674D81C-9CF5-4EC4-A760-CB03BE6ED691</a:ObjectID>
<a:Name>变动当期即影响折旧</a:Name>
<a:Code>FISAFFECTDEPR</a:Code>
<a:CreationDate>1390463271</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317040</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o263">
<a:ObjectID>60D9E4C8-0510-45C8-83DC-4DB2CA23CB4B</a:ObjectID>
<a:Name>卡片当前状态</a:Name>
<a:Code>FASSETCURSTATUS</a:Code>
<a:CreationDate>1390463271</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317044</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o264">
<a:ObjectID>DF68D4D4-3BA0-4DA5-B3B4-531FCB7A84CF</a:ObjectID>
<a:Name>数量</a:Name>
<a:Code>FQUANTITY</a:Code>
<a:CreationDate>1390463347</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317050</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o265">
<a:ObjectID>91862DF5-7E65-411B-B6C2-21AA05B4D994</a:ObjectID>
<a:Name>会计政策</a:Name>
<a:Code>FACCTPOLICYID</a:Code>
<a:CreationDate>1390463397</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317057</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o266">
<a:ObjectID>3CA756A3-35CB-4BA3-8A12-97995018E4DA</a:ObjectID>
<a:Name>当前年度</a:Name>
<a:Code>FCURRENTYEAR</a:Code>
<a:CreationDate>1390463397</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317061</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o267">
<a:ObjectID>BC7E50E5-88C4-46AA-AD37-1F932022898F</a:ObjectID>
<a:Name>当前期间</a:Name>
<a:Code>FCURRENTPERIOD</a:Code>
<a:CreationDate>1390463397</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317068</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o268">
<a:ObjectID>1EDE7C88-E696-4BE9-BE64-D19122A4A5B5</a:ObjectID>
<a:Name>资产原值</a:Name>
<a:Code>FORGVAL</a:Code>
<a:CreationDate>1390463462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317072</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o269">
<a:ObjectID>A6AA1802-4867-4489-8303-9EB60A699858</a:ObjectID>
<a:Name>累计减值准备</a:Name>
<a:Code>FACCUMDEVALUE</a:Code>
<a:CreationDate>1390463462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317078</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o270">
<a:ObjectID>3AB61BDC-A488-4FB6-BECC-F37C6DC80B31</a:ObjectID>
<a:Name>累计折旧额</a:Name>
<a:Code>FACCUMDEPR</a:Code>
<a:CreationDate>1390463462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317083</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o271">
<a:ObjectID>C7FC68E1-284E-4CCB-BC4B-30671FB02E90</a:ObjectID>
<a:Name>上一期累计折旧额</a:Name>
<a:Code>FPREACCUMDEPR</a:Code>
<a:CreationDate>1459302533</a:CreationDate>
<a:Creator>RD_xiaohui_liu</a:Creator>
<a:ModificationDate>1459302548</a:ModificationDate>
<a:Modifier>RD_xiaohui_liu</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o272">
<a:ObjectID>930C2C9E-80A2-460F-A3C1-B069B9741D6E</a:ObjectID>
<a:Name>预计残值</a:Name>
<a:Code>FRESIDUALVALUE</a:Code>
<a:CreationDate>1390463462</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317087</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o273">
<a:ObjectID>4B1C1E2F-72CB-47FA-88C0-BBEA9AD24E1B</a:ObjectID>
<a:Name>预计使用期间</a:Name>
<a:Code>FLIFEPERIODS</a:Code>
<a:CreationDate>1390463598</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390463691</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o274">
<a:ObjectID>79C92B8A-6518-498E-BEA8-105FF585C5FC</a:ObjectID>
<a:Name>累计折旧期间</a:Name>
<a:Code>FDEPRPERIODS</a:Code>
<a:CreationDate>1390463598</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390463691</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o275">
<a:ObjectID>143C11BC-0BD9-4941-B173-AD92428CD121</a:ObjectID>
<a:Name>是否计算</a:Name>
<a:Code>FISCAL</a:Code>
<a:CreationDate>1390463598</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390463691</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o276">
<a:ObjectID>F9689E5B-977B-4052-BC0F-67ABA42762F8</a:ObjectID>
<a:Name>折旧依据</a:Name>
<a:Code>FDEPROPTION</a:Code>
<a:CreationDate>1390463271</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390463817</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o277">
<a:ObjectID>BA5B67B6-717F-4C32-B52F-95EB4B474740</a:ObjectID>
<a:Name>是否采用动态折旧方法</a:Name>
<a:Code>FISMODIFYDEPRFAC</a:Code>
<a:CreationDate>1390463271</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390463817</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o278">
<a:ObjectID>423E55DB-52EC-48D6-95DC-47A7493837C9</a:ObjectID>
<a:Name>是否折旧要素变动</a:Name>
<a:Code>FISDEPRFACTORYMODIFY</a:Code>
<a:CreationDate>1390463271</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390463817</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o279">
<a:ObjectID>7C1516C6-A86A-4E69-A10D-F295B98784F9</a:ObjectID>
<a:Name>折旧方法</a:Name>
<a:Code>FDEPRMETHOD</a:Code>
<a:CreationDate>1390465375</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390465431</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o280">
<a:ObjectID>EA25435F-5DE0-41B9-B996-9F90A211639A</a:ObjectID>
<a:Name>会计日历期间数</a:Name>
<a:Code>FPERIODCOUNT</a:Code>
<a:CreationDate>1390463818</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390463872</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o281">
<a:ObjectID>5E7FC86B-ACE0-486D-A6E1-B96DF9EDBA60</a:ObjectID>
<a:Name>剩余使用年限</a:Name>
<a:Code>FREMAINYEAR</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390544007</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o282">
<a:ObjectID>F29A52C1-A8A0-4C47-8263-6B7FEF499878</a:ObjectID>
<a:Name>预计使用期间比例</a:Name>
<a:Code>FLIFEYEAR</a:Code>
<a:CreationDate>1390463818</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390544007</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o283">
<a:ObjectID>6FC9EA71-7762-4267-BD11-7E3ADBCD0C37</a:ObjectID>
<a:Name>是否最近两年</a:Name>
<a:Code>FISLASTTWOYEAR</a:Code>
<a:CreationDate>1390465566</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390465679</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o284">
<a:ObjectID>DDB9EBA0-A6CC-44D9-A313-9C0CFCE84062</a:ObjectID>
<a:Name>错误描述</a:Name>
<a:Code>FERRORDESCRIPTION</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390544113</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o285">
<a:ObjectID>258E9243-7C2A-45A6-8FC9-56FA95C0C617</a:ObjectID>
<a:Name>公式内容</a:Name>
<a:Code>FFORMULACONTENT</a:Code>
<a:CreationDate>1390464062</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1392103669</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(400)</a:DataType>
<a:Length>400</a:Length>
</o:Column>
<o:Column Id="o286">
<a:ObjectID>AF1A2D92-CC78-4449-BA21-39E711B49B3E</a:ObjectID>
<a:Name>折旧调整单状态</a:Name>
<a:Code>FDOCUMENTSTATUS</a:Code>
<a:CreationDate>1390464062</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390544113</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>nvarchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o287">
<a:ObjectID>2BEDACF0-318B-4C97-BA4F-F4C6F0C7A771</a:ObjectID>
<a:Name>年初折旧期间</a:Name>
<a:Code>FYTDDEPRPERIOD</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390544007</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o288">
<a:ObjectID>6AF0CBEA-E7D1-4268-BCE5-F625AF85FA6D</a:ObjectID>
<a:Name>是否新折旧年度第一期</a:Name>
<a:Code>FISNEXTFIRSTPERIOD</a:Code>
<a:CreationDate>1390464062</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390464230</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o289">
<a:ObjectID>3CD1D177-613F-4A18-9E64-70C0812227D4</a:ObjectID>
<a:Name>币别金额精度</a:Name>
<a:Code>FAmountDigits</a:Code>
<a:CreationDate>1390462926</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317098</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o290">
<a:ObjectID>B8B50E7B-0093-4F0E-A6F0-F2EB8E72D415</a:ObjectID>
<a:Name>是否当期新增</a:Name>
<a:Code>FIsCurrPeriodAdd</a:Code>
<a:CreationDate>1390463879</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390464266</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o291">
<a:ObjectID>A74C305A-50B4-43B1-AEFD-8242762D04BC</a:ObjectID>
<a:Name>是否计算修改</a:Name>
<a:Code>FIsCumputAlter</a:Code>
<a:CreationDate>1390464266</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390464285</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o292">
<a:ObjectID>418742D7-9E0D-419D-A560-F902CE490998</a:ObjectID>
<a:Name>预计工作总量</a:Name>
<a:Code>FPredictWorkLoad</a:Code>
<a:CreationDate>1390464285</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317103</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o293">
<a:ObjectID>F3F495EC-AB48-41AD-92B9-F2E30AEB2F3A</a:ObjectID>
<a:Name>历史已折旧工作量</a:Name>
<a:Code>FTotalWorkLoad</a:Code>
<a:CreationDate>1390464285</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317108</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o294">
<a:ObjectID>248564E1-A1CB-4F62-96CE-265FE4CE59ED</a:ObjectID>
<a:Name>本期工作量</a:Name>
<a:Code>FPeriodWorkLoad</a:Code>
<a:CreationDate>1390464285</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1393317113</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o295">
<a:ObjectID>B46E131D-C5FA-4A4D-AFB4-5996FDB2D519</a:ObjectID>
<a:Name>工作量单位</a:Name>
<a:Code>FWorkLoadUnitID</a:Code>
<a:CreationDate>1390464415</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390464450</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o296">
<a:ObjectID>C2C756D2-8030-42AF-94AD-E481DCC58116</a:ObjectID>
<a:Name>已折旧工作量</a:Name>
<a:Code>FDeprWorkLoad</a:Code>
<a:CreationDate>1390464433</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390544007</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
</o:Column>
<o:Column Id="o297">
<a:ObjectID>9C6AEE04-CF5D-47FA-AC66-CC58A112F671</a:ObjectID>
<a:Name>年期</a:Name>
<a:Code>FYearPeriod</a:Code>
<a:CreationDate>1418440066</a:CreationDate>
<a:Creator>RD_jihua_wang</a:Creator>
<a:ModificationDate>1418440181</a:ModificationDate>
<a:Modifier>RD_jihua_wang</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int</a:DataType>
</o:Column>
<o:Column Id="o298">
<a:ObjectID>D7787376-42AA-40F1-A77E-E41BDDC430C9</a:ObjectID>
<a:Name>一次性折旧额</a:Name>
<a:Code>FONETIMEDEPR</a:Code>
<a:CreationDate>1555039256</a:CreationDate>
<a:Creator>rd_junchao_he</a:Creator>
<a:ModificationDate>1555039355</a:ModificationDate>
<a:Modifier>rd_junchao_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o299">
<a:ObjectID>7FAA0EE4-51D7-41D2-9C96-549E1AE4CCC5</a:ObjectID>
<a:Name>税法允许一次性折旧</a:Name>
<a:Code>FISDEPRONETIME</a:Code>
<a:CreationDate>1555039256</a:CreationDate>
<a:Creator>rd_junchao_he</a:Creator>
<a:ModificationDate>1555039355</a:ModificationDate>
<a:Modifier>rd_junchao_he</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o300">
<a:ObjectID>A4050165-448D-40D0-936F-AE6BF2E76A56</a:ObjectID>
<a:Name>上期折旧额</a:Name>
<a:Code>FPREDEPR</a:Code>
<a:CreationDate>1562054079</a:CreationDate>
<a:Creator>rd_junchao_he</a:Creator>
<a:ModificationDate>1562054155</a:ModificationDate>
<a:Modifier>rd_junchao_he</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>decimal(23,10)</a:DataType>
<a:Length>23</a:Length>
<a:Precision>10</a:Precision>
<a:Mandatory>1</a:Mandatory>
</o:Column>
<o:Column Id="o301">
<a:ObjectID>A28B7F0D-864F-4F39-89FE-C2B9706F41E5</a:ObjectID>
<a:Name>初始化卡片</a:Name>
<a:Code>FISINIT</a:Code>
<a:CreationDate>1563758168</a:CreationDate>
<a:Creator>rd_junchao_he</a:Creator>
<a:ModificationDate>1563758217</a:ModificationDate>
<a:Modifier>rd_junchao_he</a:Modifier>
<a:DefaultValue>&#39;0&#39;</a:DefaultValue>
<a:DataType>char(1)</a:DataType>
<a:Length>1</a:Length>
<a:Mandatory>1</a:Mandatory>
</o:Column>
</c:Columns>
<c:Indexes>
<o:Index Id="o302">
<a:ObjectID>B63EB14D-04AB-4A6D-9A53-CBF712E4DDFC</a:ObjectID>
<a:Name>IDX_TM_FA_ASSERTDEPR_OWNER</a:Name>
<a:Code>IDX_TM_FA_ASSERTDEPR_OWNER</a:Code>
<a:CreationDate>1390468793</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1436242999</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o303">
<a:ObjectID>3F1F7A83-CD79-4EB4-9DBB-CED52BB9EFCA</a:ObjectID>
<a:CreationDate>1390468852</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390468906</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o250"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o304">
<a:ObjectID>C8A4E7F8-2E59-4235-B728-5C3556D4B6EF</a:ObjectID>
<a:CreationDate>1390468852</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390468906</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o265"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o305">
<a:ObjectID>6F542BC7-2350-4299-B583-425EB4B1E6D5</a:ObjectID>
<a:Name>IDX_TM_FA_ASSERTDEPR_ASEETID</a:Name>
<a:Code>IDX_TM_FA_ASSERTDEPR_ASEETID</a:Code>
<a:CreationDate>1390468852</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1436243858</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o306">
<a:ObjectID>3C64AA8D-A8EE-475E-BEB3-4A64B888656E</a:ObjectID>
<a:CreationDate>1390468927</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390468975</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o253"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o307">
<a:ObjectID>C56CAC48-0A0E-43EB-A324-B38792B10E9A</a:ObjectID>
<a:Name>IDX_TM_FA_ASSERTDEPR_ALTERID</a:Name>
<a:Code>IDX_TM_FA_ASSERTDEPR_ALTERID</a:Code>
<a:CreationDate>1390468852</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1436243858</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o308">
<a:ObjectID>E4FFD31E-B803-4E5A-91E1-5C91D118E4B6</a:ObjectID>
<a:CreationDate>1390468983</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390469037</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o252"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o309">
<a:ObjectID>F929170A-CD21-489B-995E-F9F2BC6E5FD0</a:ObjectID>
<a:CreationDate>1390468983</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390469037</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o265"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o310">
<a:ObjectID>5F026272-8774-4D60-94EE-B271A564FD23</a:ObjectID>
<a:CreationDate>1390468983</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390469037</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o266"/>
</c:Column>
</o:IndexColumn>
<o:IndexColumn Id="o311">
<a:ObjectID>DDEFED2F-B4D9-41E9-A601-964FBD0E5CC9</a:ObjectID>
<a:CreationDate>1390468983</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390469037</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<c:Column>
<o:Column Ref="o267"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
</o:Table>
</c:Tables>
</o:Package>
</c:Packages>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:DefaultGroups>
<o:Group Id="o312">
<a:ObjectID>D9DE558F-F1C2-4A59-BDC4-2EFC33D1DC14</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1390442572</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390442572</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o313">
<a:ObjectID>C846DB0C-379A-4A7E-9837-A6F9926A1F68</a:ObjectID>
<a:Name>Microsoft SQL Server 2005</a:Name>
<a:Code>MSSQLSRV2005</a:Code>
<a:CreationDate>1390471647</a:CreationDate>
<a:Creator>jihua_wang</a:Creator>
<a:ModificationDate>1390471648</a:ModificationDate>
<a:Modifier>jihua_wang</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/sqlsv2k5.xdb</a:TargetModelURL>
<a:TargetModelID>030105E8-1DFA-4990-B2C8-DEB36D9D8D09</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>