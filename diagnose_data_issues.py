#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断数据转换问题的脚本
"""

import pyodbc
import pandas as pd

# 数据库连接配置
DB_CONFIG = {
    'server': '192.168.1.250',
    'database': 'AIS2018101755337',
    'username': 'hldbuser',
    'password': 'Hldbuser@241031',
    'driver': 'ODBC Driver 17 for SQL Server'
}

def connect_database():
    """连接数据库"""
    try:
        connection_string = (
            f"DRIVER={{{DB_CONFIG['driver']}}};"
            f"SERVER={DB_CONFIG['server']};"
            f"DATABASE={DB_CONFIG['database']};"
            f"UID={DB_CONFIG['username']};"
            f"PWD={DB_CONFIG['password']};"
            "TrustServerCertificate=yes;"
        )
        
        connection = pyodbc.connect(connection_string)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def diagnose_numeric_conversion_issues():
    """诊断数值转换问题"""
    
    connection = connect_database()
    if not connection:
        return
    
    print("🔍 开始诊断数据转换问题...")
    
    # 检查每个数字列的问题数据
    day_columns = [f"[{i}]" for i in range(1, 32)]
    
    for col in day_columns:
        print(f"\n=== 检查列 {col} ===")
        
        # 查询1：找出ISNUMERIC=1但转换失败的数据
        query1 = f"""
        SELECT TOP 10
            年月, 厂区, 部门, 工序, 工号, 姓名,
            {col} as 原始值,
            LEN({col}) as 长度,
            ASCII(LEFT({col}, 1)) as 首字符ASCII,
            ASCII(RIGHT({col}, 1)) as 末字符ASCII
        FROM 测试 
        WHERE ISNUMERIC({col}) = 1 
        AND {col} IS NOT NULL 
        AND {col} <> ''
        AND (
            LEN({col}) > 20 OR  -- 长度过长
            {col} LIKE '%.%.%' OR  -- 多个小数点
            {col} LIKE '%e%' OR {col} LIKE '%E%' OR  -- 科学计数法
            {col} LIKE '%+%' OR {col} LIKE '%--%'  -- 多个符号
        )
        """
        
        try:
            df1 = pd.read_sql(query1, connection)
            if len(df1) > 0:
                print(f"❌ 发现 {len(df1)} 条可能有问题的数据:")
                print(df1.to_string(index=False))
            else:
                print(f"✅ 列 {col} 没有发现明显问题")
        except Exception as e:
            print(f"❌ 查询列 {col} 时出错: {e}")
    
    # 检查所有列的数据类型和范围
    print(f"\n=== 检查数据范围和特殊字符 ===")
    
    range_query = """
    SELECT TOP 20
        年月, 厂区, 部门, 工序, 工号, 姓名,
        [1], [2], [3], [4], [5]
    FROM 测试 
    WHERE (
        (ISNUMERIC([1]) = 1 AND LEN([1]) > 15) OR
        (ISNUMERIC([2]) = 1 AND LEN([2]) > 15) OR
        (ISNUMERIC([3]) = 1 AND LEN([3]) > 15) OR
        (ISNUMERIC([4]) = 1 AND LEN([4]) > 15) OR
        (ISNUMERIC([5]) = 1 AND LEN([5]) > 15)
    )
    """
    
    try:
        df_range = pd.read_sql(range_query, connection)
        if len(df_range) > 0:
            print(f"❌ 发现 {len(df_range)} 条长度过长的数据:")
            print(df_range.to_string(index=False))
        else:
            print("✅ 没有发现长度过长的数据")
    except Exception as e:
        print(f"❌ 检查数据范围时出错: {e}")
    
    connection.close()

def generate_safe_sql():
    """生成安全的SQL查询"""
    
    print("\n" + "="*80)
    print("🔧 生成修复后的安全SQL查询")
    print("="*80)
    
    # 生成安全的数值转换逻辑
    safe_conversion_parts = []
    for i in range(1, 32):
        safe_part = f"""
    COALESCE(
        CASE 
            WHEN ISNUMERIC(a.[{i}]) = 1 
            AND LEN(a.[{i}]) <= 15 
            AND a.[{i}] NOT LIKE '%.%.%' 
            AND a.[{i}] NOT LIKE '%e%' 
            AND a.[{i}] NOT LIKE '%E%'
            THEN TRY_CAST(a.[{i}] AS DECIMAL(18,2)) 
            ELSE 0 
        END, 0)"""
        safe_conversion_parts.append(safe_part)
    
    safe_sum = " +\n    ".join(safe_conversion_parts) + " AS 产能总和"
    
    safe_sql = f"""
SELECT 
    年月, 厂区, 部门, 工序, 工号, 姓名, 类型, 单价, 班组, 序号,
    [1], [2], [3], [4], [5], [6], [7], [8], [9], [10], [11], [12], [13], [14], [15], 
    [16], [17], [18], [19], [20], [21], [22], [23], [24], [25], [26], [27], [28], [29], [30], [31],
    产能类型, 产能总和, 绩效分数, status, 绩效占比,
    CASE 
        WHEN 厂区 = '泗阳华茂' and 工序 = '出炉' and 产能总和 > 114 and 绩效分数 is not null 
        THEN 1800+(产能总和-114)*20+产能总和*7.7*绩效分数
        WHEN 产能类型 = '计件产能汇总' AND ISNULL(绩效分数, 0) <> 0 
        THEN (产能总和 * 单价) * 绩效占比 * 绩效分数 + ((产能总和 * 单价) - (产能总和 * 单价) * 绩效占比)
        WHEN 产能类型 = '绩效产能汇总' or 绩效分数 is null 
        THEN 产能总和 * 单价
        ELSE 0
    END AS 薪资
FROM (
    SELECT 
        a.年月, a.厂区, a.部门, a.工序, 
        REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') as 工号, 
        a.姓名, d.类型, t.班组, t.序号, a.status,
        a.[1], a.[2], a.[3], a.[4], a.[5], a.[6], a.[7], a.[8], a.[9], a.[10], 
        a.[11], a.[12], a.[13], a.[14], a.[15], a.[16], a.[17], a.[18], a.[19], a.[20], 
        a.[21], a.[22], a.[23], a.[24], a.[25], a.[26], a.[27], a.[28], a.[29], a.[30], a.[31],
        CASE 
            WHEN ISNULL(d.类型, '') IN ('B', '') THEN '计件产能汇总' 
            WHEN d.类型 = 'A' THEN '绩效产能汇总' 
            ELSE '其他' 
        END AS 产能类型,
        {safe_sum},
        d.单价, p.绩效分数, p.绩效占比
    FROM 测试 a 
    LEFT JOIN [dbo].[单价] d ON a.厂区 = d.厂区 AND a.工序 = d.工序 
    LEFT JOIN [dbo].[绩效表] p ON REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') = REPLACE(REPLACE(p.工号, CHAR(13), ''), CHAR(10), '') 
        AND a.年月 = p.年月 AND ISNULL(d.类型, '') IN ('B', '') 
    LEFT JOIN [dbo].[人员信息表] t ON REPLACE(REPLACE(REPLACE(t.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') = REPLACE(REPLACE(REPLACE(a.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') 
        AND t.[工厂] = a.[厂区] and t.[部门] = a.[部门]
    GROUP BY 
        a.年月, a.厂区, a.部门, a.工序, a.status,
        REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), ''), 
        a.姓名, d.类型, d.单价, p.绩效分数, p.绩效占比, t.班组, t.序号, 
        a.[1], a.[2], a.[3], a.[4], a.[5], a.[6], a.[7], a.[8], a.[9], a.[10], 
        a.[11], a.[12], a.[13], a.[14], a.[15], a.[16], a.[17], a.[18], a.[19], a.[20], 
        a.[21], a.[22], a.[23], a.[24], a.[25], a.[26], a.[27], a.[28], a.[29], a.[30], a.[31]
) AS subquery
"""
    
    # 保存到文件
    with open('fixed_salary_query.sql', 'w', encoding='utf-8') as f:
        f.write(safe_sql)
    
    print("✅ 修复后的SQL已保存到 fixed_salary_query.sql")
    print("\n主要修复点:")
    print("1. 使用 TRY_CAST 替代 CAST，避免转换错误")
    print("2. 增加长度检查 LEN(a.[{i}]) <= 15")
    print("3. 排除多个小数点的数据")
    print("4. 排除科学计数法格式的数据")
    print("5. 所有转换失败的情况都返回 0")

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 数据转换问题诊断工具")
    print("=" * 80)
    
    # 1. 诊断问题数据
    diagnose_numeric_conversion_issues()
    
    # 2. 生成修复后的SQL
    generate_safe_sql()
    
    print("\n" + "=" * 80)
    print("🎯 建议:")
    print("1. 先运行诊断，查看具体的问题数据")
    print("2. 使用生成的 fixed_salary_query.sql 替代原查询")
    print("3. 如果还有问题，可以进一步清理数据")
    print("=" * 80)

if __name__ == "__main__":
    main()
