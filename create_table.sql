-- 创建"测试"表
-- 根据INSERT语句推断的表结构

CREATE TABLE [测试] (
    [年月] varchar(255) NOT NULL,
    [厂区] varchar(255) NOT NULL,
    [部门] varchar(255) NOT NULL,
    [工序] varchar(255) NOT NULL,
    [工号] varchar(255) NOT NULL,
    [姓名] varchar(255) NOT NULL,
    [1] nvarchar(38) NULL,
    [2] nvarchar(38) NULL,
    [3] nvarchar(38) NULL,
    [4] nvarchar(38) NULL,
    [5] nvarchar(38) NULL,
    [6] nvarchar(38) NULL,
    [7] nvarchar(38) NULL,
    [8] nvarchar(38) NULL,
    [9] nvarchar(38) NULL,
    [10] nvarchar(38) NULL,
    [11] nvarchar(38) NULL,
    [12] nvarchar(38) NULL,
    [13] nvarchar(38) NULL,
    [14] nvarchar(38) NULL,
    [15] nvarchar(38) NULL,
    [16] nvarchar(38) NULL,
    [17] nvarchar(38) NULL,
    [18] nvarchar(38) NULL,
    [19] nvarchar(38) NULL,
    [20] nvarchar(38) NULL,
    [21] nvarchar(38) NULL,
    [22] nvarchar(38) NULL,
    [23] nvarchar(38) NULL,
    [24] nvarchar(38) NULL,
    [25] nvarchar(38) NULL,
    [26] nvarchar(38) NULL,
    [27] nvarchar(38) NULL,
    [28] nvarchar(38) NULL,
    [29] nvarchar(38) NULL,
    [30] nvarchar(38) NULL,
    [31] nvarchar(38) NULL,
    [status] int NOT NULL DEFAULT 1,
    
    -- 创建主键约束 (可能是组合主键)
    CONSTRAINT PK_测试 PRIMARY KEY ([年月], [厂区], [部门], [工序], [工号])
);

-- 创建索引以提高查询性能
CREATE INDEX IX_测试_工号 ON [测试] ([工号]);
CREATE INDEX IX_测试_年月 ON [测试] ([年月]); 