#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试组装拆卸单查询 - 验证单号 ZZCX003758
"""

import pyodbc
import pandas as pd

# 数据库连接配置 - 先尝试HLDB数据库
DB_CONFIG = {
    'server': '192.168.1.250',
    'database': 'HLDB',  # 先用HLDB测试
    'username': 'hldbuser',
    'password': 'Hldbuser@241031',
    'driver': 'ODBC Driver 17 for SQL Server'
}

def connect_database():
    """连接数据库"""
    try:
        connection_string = (
            f"DRIVER={{{DB_CONFIG['driver']}}};"
            f"SERVER={DB_CONFIG['server']};"
            f"DATABASE={DB_CONFIG['database']};"
            f"UID={DB_CONFIG['username']};"
            f"PWD={DB_CONFIG['password']};"
            "TrustServerCertificate=yes;"
        )
        
        connection = pyodbc.connect(connection_string)
        print("✅ HLDB数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def test_assembly_query():
    """测试组装拆卸单查询"""
    connection = connect_database()
    if not connection:
        return
    
    print("🔍 开始查询组装拆卸单 ZZCX003758...")
    
    # 0. 首先检查数据库中是否有组装拆卸相关的表
    print("\n" + "="*60)
    print("步骤0：检查组装拆卸相关表是否存在")
    print("="*60)

    try:
        query0 = """
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME LIKE '%ASSEMBLY%' OR TABLE_NAME LIKE '%组装%'
        """
        df0 = pd.read_sql(query0, connection)
        print("✅ 表结构查询成功")
        print("找到的组装拆卸相关表:")
        print(df0)

        if len(df0) == 0:
            print("❌ 当前数据库中没有组装拆卸相关的表！")
            print("可能需要连接到Kingdee数据库 AIS2018101755337")
            return

    except Exception as e:
        print(f"❌ 表结构查询失败: {e}")
        return

    # 1. 验证单据是否存在
    print("\n" + "="*60)
    print("步骤1：验证单据是否存在")
    print("="*60)

    try:
        query1 = """
        SELECT
            COUNT(*) AS 记录数,
            CASE WHEN COUNT(*) > 0 THEN '存在' ELSE '不存在' END AS 状态
        FROM T_STK_ASSEMBLY
        WHERE FBillNo = 'ZZCX003758'
        """
        df1 = pd.read_sql(query1, connection)
        print("✅ 单据验证查询成功")
        print(df1)
        
        if df1.iloc[0]['记录数'] == 0:
            print("❌ 单据 ZZCX003758 不存在！")
            # 查询类似的单据
            query_similar = """
            SELECT TOP 10 FBillNo, FDate, FAffairType, FDocumentStatus
            FROM T_STK_ASSEMBLY 
            WHERE FBillNo LIKE '%ZZCX%' OR FBillNo LIKE '%003758%'
            ORDER BY FCreateDate DESC
            """
            df_similar = pd.read_sql(query_similar, connection)
            print("\n类似的单据:")
            print(df_similar)
            return
            
    except Exception as e:
        print(f"❌ 单据验证失败: {e}")
        return
    
    # 2. 查询主表信息
    print("\n" + "="*60)
    print("步骤2：查询主表信息")
    print("="*60)
    
    try:
        query2 = """
        SELECT 
            FID AS 单据内码,
            FBillNo AS 单据编号,
            FDate AS 单据日期,
            FAffairType AS 事务类型,
            FDocumentStatus AS 单据状态,
            FCancelStatus AS 作废状态,
            FNote AS 备注,
            FCreatorID AS 创建人ID,
            FCreateDate AS 创建日期
        FROM T_STK_ASSEMBLY 
        WHERE FBillNo = 'ZZCX003758'
        """
        df2 = pd.read_sql(query2, connection)
        print("✅ 主表信息查询成功")
        print(df2)
        
    except Exception as e:
        print(f"❌ 主表信息查询失败: {e}")
    
    # 3. 查询成品信息（入库）
    print("\n" + "="*60)
    print("步骤3：查询成品信息（入库）")
    print("="*60)
    
    try:
        query3 = """
        SELECT 
            a.FBillNo AS 单据编号,
            p.FSeq AS 行号,
            m.FNUMBER AS 物料代码,
            m.FNAME AS 物料名称,
            p.FQty AS 数量,
            u.FNAME AS 单位名称,
            s.FNAME AS 仓库名称,
            p.FLOT_TEXT AS 批次号,
            p.FPRICE AS 单价,
            p.FAMOUNT AS 金额
        FROM T_STK_ASSEMBLY a
        INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
        LEFT JOIN T_BD_MATERIAL m ON p.FMaterialID = m.FMATERIALID
        LEFT JOIN T_BD_UNIT u ON p.FUnitID = u.FUNITID
        LEFT JOIN T_BD_STOCK s ON p.FStockID = s.FSTOCKID
        WHERE a.FBillNo = 'ZZCX003758'
        ORDER BY p.FSeq
        """
        df3 = pd.read_sql(query3, connection)
        print("✅ 成品信息查询成功")
        print(f"找到 {len(df3)} 条成品记录")
        if len(df3) > 0:
            print(df3)
        
    except Exception as e:
        print(f"❌ 成品信息查询失败: {e}")
    
    # 4. 查询子件信息（出库）
    print("\n" + "="*60)
    print("步骤4：查询子件信息（出库）")
    print("="*60)
    
    try:
        query4 = """
        SELECT 
            a.FBillNo AS 单据编号,
            p.FSeq AS 成品行号,
            s.FSeq AS 子件行号,
            m.FNUMBER AS 物料代码,
            m.FNAME AS 物料名称,
            s.FQty AS 数量,
            u.FNAME AS 单位名称,
            st.FNAME AS 仓库名称,
            s.FLOT_TEXT AS 批次号,
            s.FPRICE AS 单价,
            s.FAMOUNT AS 金额,
            s.FCostProportion AS 成本分摊比例
        FROM T_STK_ASSEMBLY a
        INNER JOIN T_STK_ASSEMBLYPRODUCT p ON a.FID = p.FID
        INNER JOIN T_STK_ASSEMBLYSUBITEM s ON p.FEntryID = s.FEntryID
        LEFT JOIN T_BD_MATERIAL m ON s.FMaterialID = m.FMATERIALID
        LEFT JOIN T_BD_UNIT u ON s.FUnitID = u.FUNITID
        LEFT JOIN T_BD_STOCK st ON s.FStockID = st.FSTOCKID
        WHERE a.FBillNo = 'ZZCX003758'
        ORDER BY p.FSeq, s.FSeq
        """
        df4 = pd.read_sql(query4, connection)
        print("✅ 子件信息查询成功")
        print(f"找到 {len(df4)} 条子件记录")
        if len(df4) > 0:
            print(df4)
        
    except Exception as e:
        print(f"❌ 子件信息查询失败: {e}")
    
    # 5. 如果单据不存在，查询最近的组装拆卸单
    print("\n" + "="*60)
    print("步骤5：查询最近的组装拆卸单（作为参考）")
    print("="*60)
    
    try:
        query5 = """
        SELECT TOP 5
            FBillNo AS 单据编号,
            FDate AS 单据日期,
            FAffairType AS 事务类型,
            FDocumentStatus AS 单据状态,
            FCreateDate AS 创建日期
        FROM T_STK_ASSEMBLY 
        ORDER BY FCreateDate DESC
        """
        df5 = pd.read_sql(query5, connection)
        print("✅ 最近组装拆卸单查询成功")
        print("最近的5张组装拆卸单:")
        print(df5)
        
    except Exception as e:
        print(f"❌ 最近组装拆卸单查询失败: {e}")
    
    connection.close()
    print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 组装拆卸单查询测试工具")
    print("=" * 80)
    
    test_assembly_query()

if __name__ == "__main__":
    main()
