#!/usr/bin/env python3
"""
查找INSERT语句中的重复主键值
基于错误信息，主键约束为'PK__测试__22446EA908EA5793'
需要找出哪些记录有重复的主键组合
"""

import re
from collections import defaultdict

def analyze_duplicates(sql_file):
    """分析INSERT语句中的重复值"""
    with open(sql_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取所有INSERT语句
    insert_pattern = r"INSERT INTO \[测试\] \([^)]+\) VALUES \(([^)]+)\);"
    matches = re.findall(insert_pattern, content)
    
    print(f"找到 {len(matches)} 个INSERT语句")
    
    # 分析每个INSERT语句的字段值
    records = []
    for i, values_str in enumerate(matches, 1):
        # 解析字段值
        values = parse_values(values_str)
        if len(values) >= 6:  # 确保有足够的字段
            # 提取关键字段：年月, 厂区, 部门, 工序, 工号, 姓名
            key_fields = (values[0], values[1], values[2], values[3], values[4], values[5])
            records.append({
                'line': i,
                'key': key_fields,
                'values': values
            })
    
    # 查找重复的键
    key_counts = defaultdict(list)
    for record in records:
        key_counts[record['key']].append(record)
    
    # 输出重复的记录
    duplicates_found = False
    for key, record_list in key_counts.items():
        if len(record_list) > 1:
            duplicates_found = True
            print(f"\n发现重复主键组合：")
            print(f"年月: {key[0]}")
            print(f"厂区: {key[1]}")
            print(f"部门: {key[2]}")
            print(f"工序: {key[3]}")
            print(f"工号: {key[4]}")
            print(f"姓名: {key[5]}")
            print(f"重复次数: {len(record_list)}")
            print("出现在以下INSERT语句中：")
            for record in record_list:
                print(f"  - 第 {record['line']} 行")
    
    if not duplicates_found:
        print("\n未发现明显的主键重复。")
        print("可能的原因：")
        print("1. 主键包含更多字段（如status字段）")
        print("2. 数据库中已有部分记录")
        print("3. 主键定义与分析假设不同")
    
    # 输出前几个记录的样本
    print(f"\n前5个记录样本：")
    for i, record in enumerate(records[:5]):
        print(f"记录 {i+1}: {record['key']}")

def parse_values(values_str):
    """解析VALUES子句中的值"""
    # 简单的解析，处理引号和逗号
    values = []
    current_value = ""
    in_quotes = False
    quote_char = None
    
    i = 0
    while i < len(values_str):
        char = values_str[i]
        
        if not in_quotes:
            if char in ("'", '"'):
                in_quotes = True
                quote_char = char
                current_value += char
            elif char == ',':
                values.append(current_value.strip())
                current_value = ""
            else:
                current_value += char
        else:
            current_value += char
            if char == quote_char:
                # 检查是否是转义的引号
                if i + 1 < len(values_str) and values_str[i + 1] == quote_char:
                    current_value += quote_char
                    i += 1  # 跳过下一个引号
                else:
                    in_quotes = False
                    quote_char = None
        
        i += 1
    
    # 添加最后一个值
    if current_value.strip():
        values.append(current_value.strip())
    
    return values

if __name__ == "__main__":
    # 分析原始文件
    print("分析原始文件:")
    analyze_duplicates("insert_statements.sql")
    
    print("\n" + "="*60 + "\n")
    
    # 如果有修正后的文件，也分析一下
    try:
        print("分析修正后的文件:")
        analyze_duplicates("insert_statements_simple_fix.sql")
    except FileNotFoundError:
        print("未找到修正后的文件，跳过分析") 