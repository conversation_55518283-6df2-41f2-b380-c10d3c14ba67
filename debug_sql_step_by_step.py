#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐步调试SQL查询，找出真正的问题字段
"""

import pyodbc
import pandas as pd

# 数据库连接配置
DB_CONFIG = {
    'server': '192.168.1.250',
    'database': 'HLDB',
    'username': 'hldbuser',
    'password': 'Hldbuser@241031',
    'driver': 'ODBC Driver 17 for SQL Server'
}

def connect_database():
    """连接数据库"""
    try:
        connection_string = (
            f"DRIVER={{{DB_CONFIG['driver']}}};"
            f"SERVER={DB_CONFIG['server']};"
            f"DATABASE={DB_CONFIG['database']};"
            f"UID={DB_CONFIG['username']};"
            f"PWD={DB_CONFIG['password']};"
            "TrustServerCertificate=yes;"
        )
        
        connection = pyodbc.connect(connection_string)
        print("✅ 数据库连接成功")
        return connection
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def test_query_step_by_step():
    """逐步测试查询的每个部分"""
    connection = connect_database()
    if not connection:
        return
    
    print("🔍 开始逐步测试查询...")
    
    # 步骤1：测试基本查询
    print("\n" + "="*60)
    print("步骤1：测试基本表查询")
    print("="*60)
    
    try:
        query1 = "SELECT TOP 5 年月, 厂区, 部门, 工序, 工号, 姓名 FROM 测试"
        df1 = pd.read_sql(query1, connection)
        print("✅ 基本表查询成功")
        print(df1.head())
    except Exception as e:
        print(f"❌ 基本表查询失败: {e}")
        return
    
    # 步骤2：测试关联查询
    print("\n" + "="*60)
    print("步骤2：测试表关联")
    print("="*60)
    
    try:
        query2 = """
        SELECT TOP 5 
            a.年月, a.厂区, a.部门, a.工序, a.工号, a.姓名,
            d.类型, d.单价
        FROM 测试 a 
        LEFT JOIN [dbo].[单价] d ON a.厂区 = d.厂区 AND a.工序 = d.工序
        """
        df2 = pd.read_sql(query2, connection)
        print("✅ 单价表关联成功")
        print(df2.head())
    except Exception as e:
        print(f"❌ 单价表关联失败: {e}")
    
    # 步骤3：测试绩效表关联
    print("\n" + "="*60)
    print("步骤3：测试绩效表关联")
    print("="*60)
    
    try:
        query3 = """
        SELECT TOP 5 
            a.年月, a.厂区, a.部门, a.工序, a.工号, a.姓名,
            p.绩效分数, p.绩效占比
        FROM 测试 a 
        LEFT JOIN [dbo].[绩效表] p ON REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') = REPLACE(REPLACE(p.工号, CHAR(13), ''), CHAR(10), '') 
            AND a.年月 = p.年月
        """
        df3 = pd.read_sql(query3, connection)
        print("✅ 绩效表关联成功")
        print(df3.head())
    except Exception as e:
        print(f"❌ 绩效表关联失败: {e}")
    
    # 步骤4：测试人员信息表关联
    print("\n" + "="*60)
    print("步骤4：测试人员信息表关联")
    print("="*60)
    
    try:
        query4 = """
        SELECT TOP 5 
            a.年月, a.厂区, a.部门, a.工序, a.工号, a.姓名,
            t.班组, t.序号
        FROM 测试 a 
        LEFT JOIN [dbo].[人员信息表] t ON REPLACE(REPLACE(REPLACE(t.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') = REPLACE(REPLACE(REPLACE(a.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') 
            AND t.[工厂] = a.[厂区] and t.[部门] = a.[部门]
        """
        df4 = pd.read_sql(query4, connection)
        print("✅ 人员信息表关联成功")
        print(df4.head())
    except Exception as e:
        print(f"❌ 人员信息表关联失败: {e}")
    
    # 步骤5：测试数字字段类型
    print("\n" + "="*60)
    print("步骤5：检查可能有问题的数字字段")
    print("="*60)
    
    # 检查单价字段
    try:
        query5a = """
        SELECT TOP 10 
            厂区, 工序, 类型, 单价,
            ISNUMERIC(单价) as 单价是否数字,
            LEN(CAST(单价 AS NVARCHAR(50))) as 单价长度
        FROM [dbo].[单价]
        WHERE 单价 IS NOT NULL
        """
        df5a = pd.read_sql(query5a, connection)
        print("✅ 单价字段检查:")
        print(df5a.head())
    except Exception as e:
        print(f"❌ 单价字段检查失败: {e}")
    
    # 检查绩效分数字段
    try:
        query5b = """
        SELECT TOP 10 
            工号, 年月, 绩效分数, 绩效占比,
            ISNUMERIC(绩效分数) as 绩效分数是否数字,
            ISNUMERIC(绩效占比) as 绩效占比是否数字
        FROM [dbo].[绩效表]
        WHERE 绩效分数 IS NOT NULL OR 绩效占比 IS NOT NULL
        """
        df5b = pd.read_sql(query5b, connection)
        print("✅ 绩效字段检查:")
        print(df5b.head())
    except Exception as e:
        print(f"❌ 绩效字段检查失败: {e}")
    
    # 步骤6：测试简化的GROUP BY
    print("\n" + "="*60)
    print("步骤6：测试简化的GROUP BY查询")
    print("="*60)
    
    try:
        query6 = """
        SELECT 
            a.年月, a.厂区, a.部门, a.工序, 
            REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') as 工号, 
            a.姓名,
            COUNT(*) as 记录数
        FROM 测试 a 
        GROUP BY 
            a.年月, a.厂区, a.部门, a.工序,
            REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), ''), 
            a.姓名
        """
        df6 = pd.read_sql(query6, connection)
        print("✅ 简化GROUP BY成功")
        print(f"返回 {len(df6)} 条记录")
        print(df6.head())
    except Exception as e:
        print(f"❌ 简化GROUP BY失败: {e}")
    
    # 步骤7：测试完整查询但不包含数字计算
    print("\n" + "="*60)
    print("步骤7：测试完整关联但不计算产能总和")
    print("="*60)
    
    try:
        query7 = """
        SELECT 
            a.年月, a.厂区, a.部门, a.工序, 
            REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') as 工号, 
            a.姓名, d.类型, t.班组, t.序号, a.status,
            CASE 
                WHEN ISNULL(d.类型, '') IN ('B', '') THEN '计件产能汇总' 
                WHEN d.类型 = 'A' THEN '绩效产能汇总' 
                ELSE '其他' 
            END AS 产能类型,
            d.单价, p.绩效分数, p.绩效占比
        FROM 测试 a 
        LEFT JOIN [dbo].[单价] d ON a.厂区 = d.厂区 AND a.工序 = d.工序 
        LEFT JOIN [dbo].[绩效表] p ON REPLACE(REPLACE(a.工号, CHAR(13), ''), CHAR(10), '') = REPLACE(REPLACE(p.工号, CHAR(13), ''), CHAR(10), '') 
            AND a.年月 = p.年月 AND ISNULL(d.类型, '') IN ('B', '') 
        LEFT JOIN [dbo].[人员信息表] t ON REPLACE(REPLACE(REPLACE(t.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') = REPLACE(REPLACE(REPLACE(a.[工号], CHAR(13), ''), CHAR(10), ''), ' ', '') 
            AND t.[工厂] = a.[厂区] and t.[部门] = a.[部门]
        """
        df7 = pd.read_sql(query7, connection)
        print("✅ 完整关联查询成功（不含GROUP BY）")
        print(f"返回 {len(df7)} 条记录")
        print(df7.head())
    except Exception as e:
        print(f"❌ 完整关联查询失败: {e}")
        print("这里可能是问题所在！")
    
    connection.close()
    print("\n🔒 数据库连接已关闭")

def generate_safe_original_query():
    """生成安全的原始查询"""
    print("\n" + "="*80)
    print("🔧 生成修复建议")
    print("="*80)
    
    print("""
基于测试结果，问题可能出现在以下几个地方：

1. 绩效分数或绩效占比字段包含非数字值
2. 单价字段包含非数字值  
3. GROUP BY中的字段类型不匹配

建议的修复方案：

-- 在最终计算薪资时，对所有可能的数字字段进行安全转换
CASE 
    WHEN 厂区 = '泗阳华茂' and 工序 = '出炉' and 产能总和 > 114 and 绩效分数 is not null 
    THEN 1800+(产能总和-114)*20+产能总和*7.7*ISNULL(TRY_CAST(绩效分数 AS FLOAT), 0)
    WHEN 产能类型 = '计件产能汇总' AND ISNULL(TRY_CAST(绩效分数 AS FLOAT), 0) <> 0 
    THEN (产能总和 * ISNULL(TRY_CAST(单价 AS FLOAT), 0)) * ISNULL(TRY_CAST(绩效占比 AS FLOAT), 0) * ISNULL(TRY_CAST(绩效分数 AS FLOAT), 0) + ((产能总和 * ISNULL(TRY_CAST(单价 AS FLOAT), 0)) - (产能总和 * ISNULL(TRY_CAST(单价 AS FLOAT), 0)) * ISNULL(TRY_CAST(绩效占比 AS FLOAT), 0))
    WHEN 产能类型 = '绩效产能汇总' or 绩效分数 is null 
    THEN 产能总和 * ISNULL(TRY_CAST(单价 AS FLOAT), 0)
    ELSE 0
END AS 薪资

如果你的SQL Server不支持TRY_CAST，使用：
ISNULL(CASE WHEN ISNUMERIC(字段名) = 1 THEN CAST(字段名 AS FLOAT) ELSE 0 END, 0)
    """)

def main():
    """主函数"""
    print("=" * 80)
    print("🔍 SQL查询逐步调试工具")
    print("=" * 80)
    
    test_query_step_by_step()
    generate_safe_original_query()

if __name__ == "__main__":
    main()
