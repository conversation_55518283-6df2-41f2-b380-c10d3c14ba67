#!/usr/bin/env python3
"""
简单修正INSERT语句脚本
只修正工号字段的数字格式为字符串格式，保持其他一切不变
"""

import re

def fix_insert_statements(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用更简单的方法：直接替换工号字段
    # 查找模式：[工序], 数字, [姓名]
    # 替换为：[工序], '数字', [姓名]
    
    # 这个正则表达式匹配工号字段（在工序和姓名之间的数字）
    pattern = r"([工序]', )(\d+)(, '[^']+', \[1\])"
    
    def replace_worker_id(match):
        prefix = match.group(1)
        worker_id = match.group(2)
        suffix = match.group(3)
        return f"{prefix}'{worker_id}'{suffix}"
    
    # 执行替换
    fixed_content = re.sub(pattern, replace_worker_id, content)
    
    # 写入修正文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"修正完成！原文件：{input_file}，修正文件：{output_file}")

if __name__ == "__main__":
    fix_insert_statements("insert_statements.sql", "insert_statements_simple_fix.sql") 