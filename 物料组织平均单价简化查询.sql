-- 物料组织平均单价简化查询
-- 专注显示指定时间范围内每个物料在各个销售组织的平均单价
-- =====================================================

SELECT
    m.FNUMBER AS '物料编码',
    m_l.FNAME AS '物料名称',
    m_l.FSPECIFICATION AS '规格型号',
    org_l.FNAME AS '销售组织',
    u_l.FNAME AS '单位',
    COUNT(*) AS '交易次数',
    ROUND(AVG(f.FPRICE), 2) AS '平均单价',
    ROUND(AVG(f.FTAXPRICE), 2) AS '平均含税单价',
    SUM(e.FQTY) AS '总销售数量',
    SUM(f.FALLAMOUNT) AS '总销售额'
FROM
    T_SAL_ORDER o
    INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
    INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
    INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
    INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
    LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
    LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
    LEFT JOIN T_BD_UNIT u ON e.FUNITID = u.FUNITID
    LEFT JOIN T_BD_UNIT_L u_l ON u.FUNITID = u_l.FUNITID
WHERE
    m_l.FLOCALEID = 2052        -- 中文物料名称
    AND org_l.FLocaleId = 2052  -- 中文组织名称
    AND u_l.FLocaleId = 2052    -- 中文单位名称
    -- 修改这里的时间范围
    AND o.FDATE >= '2025-06-01'
    AND o.FDATE <= '2025-06-10'
    -- 可选过滤条件
    AND f.FPRICE > 0            -- 排除价格为0的记录
    AND org_l.FNAME IS NOT NULL -- 排除组织名称为空的记录
    --AND o.FDOCUMENTSTATUS = 'C'  -- 取消注释只查询已审核单据
GROUP BY
    m.FNUMBER,
    m_l.FNAME,
    m_l.FSPECIFICATION,
    org_l.FNAME,
    u_l.FNAME
ORDER BY
    m_l.FNAME,                  -- 按物料名称排序
    org_l.FNAME;                -- 按销售组织排序

-- =====================================================
-- 使用说明
-- =====================================================
/*
这个查询专门用于：
1. 快速查看每个物料在各个销售组织的平均单价
2. 对比同一物料在不同组织的价格差异
3. 分析组织间的定价策略

主要字段说明：
- 平均单价：不含税平均价格
- 平均含税单价：含税平均价格
- 交易次数：该物料在该组织的销售明细条数
- 总销售数量：累计销售数量
- 总销售额：价税合计总额

修改时间范围：
修改 WHERE 条件中的日期：
AND o.FDATE >= '开始日期'
AND o.FDATE <= '结束日期'

示例：
AND o.FDATE >= '2025-05-01'
AND o.FDATE <= '2025-05-31'
*/

-- =====================================================
-- 透视表格式查询（可选使用）
-- =====================================================
/*
-- 如果您想要看到物料和组织的交叉表格式，可以使用下面的查询：

SELECT 
    物料编码,
    物料名称,
    规格型号,
    [江苏华绿生物科技集团股份有限公司] AS '华绿平均单价',
    [江苏华骏生物科技有限公司] AS '华骏平均单价',
    [江苏省华蕈农业发展有限公司] AS '华蕈平均单价',
    [泗阳华盛生物科技有限公司] AS '华盛平均单价',
    [浙江华实生物科技有限公司] AS '华实平均单价',
    [泗阳华茂农业发展有限公司] AS '华茂平均单价'
FROM (
    SELECT 
        m.FNUMBER AS 物料编码,
        m_l.FNAME AS 物料名称,
        m_l.FSPECIFICATION AS 规格型号,
        org_l.FNAME AS 销售组织,
        AVG(f.FPRICE) AS 平均单价
    FROM T_SAL_ORDER o
        INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
        INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
        INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
        LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
    WHERE m_l.FLOCALEID = 2052
        AND org_l.FLocaleId = 2052
        AND o.FDATE >= '2025-06-01'
        AND o.FDATE <= '2025-06-10'
        AND f.FPRICE > 0
    GROUP BY m.FNUMBER, m_l.FNAME, m_l.FSPECIFICATION, org_l.FNAME
) AS SourceTable
PIVOT (
    AVG(平均单价)
    FOR 销售组织 IN (
        [江苏华绿生物科技集团股份有限公司],
        [江苏华骏生物科技有限公司],
        [江苏省华蕈农业发展有限公司],
        [泗阳华盛生物科技有限公司],
        [浙江华实生物科技有限公司],
        [泗阳华茂农业发展有限公司]
    )
) AS PivotTable
ORDER BY 物料名称;
*/ 