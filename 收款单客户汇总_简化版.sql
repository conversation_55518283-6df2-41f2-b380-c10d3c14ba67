-- 收款单客户汇总查询 (2025年5月1日之后) - 正确版本
-- 显示往来单位（客户）、收款组织、业务员、收款日期、收款金额

SELECT
    c_l.FNAME AS '往来单位',
    org_l.FNAME AS '收款组织',
    emp_l.FNAME AS '业务员',
    CONVERT(varchar(10), r.FDATE, 120) AS '收款日期',
    re.FSETTLERECAMOUNT AS '收款金额'
FROM
    T_AR_RECEIVEBILL r                                    -- 收款单主表
    INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID -- 收款单分录表
    LEFT JOIN T_BD_CUSTOMER c ON r.FCONTACTUNIT = c.FCUSTID      -- 通过往来单位关联客户
    LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052  -- 客户多语言表(中文)
    LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID  -- 关联收款组织表
    LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052  -- 组织多语言表(中文)
    LEFT JOIN T_BD_STAFF emp ON r.FSALEERID = emp.FSTAFFID  -- 关联业务员表
    LEFT JOIN T_BD_STAFF_L emp_l ON emp.FSTAFFID = emp_l.FSTAFFID AND emp_l.FLocaleId = 2052  -- 业务员多语言表(中文)
WHERE
    r.FDOCUMENTSTATUS = 'C'                              -- 已审核单据
    AND r.FCANCELSTATUS = 'A'                            -- 未作废
    AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'  -- 2025年5月1日之后
    AND c_l.FNAME IS NOT NULL                            -- 确保有客户名称
    AND org_l.FNAME IS NOT NULL                          -- 确保有组织名称
    -- 只查询指定的收款组织
    AND org_l.FNAME IN (
        '江苏华绿生物科技集团股份有限公司',
        '江苏华骏生物科技有限公司',
        '江苏省华蕈农业发展有限公司',
        '泗阳华盛生物科技有限公司',
        '浙江华实生物科技有限公司',
        '泗阳华茂农业发展有限公司'
    )
ORDER BY
    CONVERT(varchar(10), r.FDATE, 120) DESC,             -- 按日期倒序
    c_l.FNAME,                                           -- 按客户名称排序
    org_l.FNAME,                                         -- 按组织名称排序
    emp_l.FNAME;                                         -- 按业务员排序

-- 如果需要按客户和日期汇总，使用以下查询：
/*
SELECT
    c_l.FNAME AS '往来单位',
    CONVERT(varchar(10), r.FDATE, 120) AS '收款日期',
    SUM(re.FSETTLERECAMOUNT) AS '收款金额'
FROM
    T_AR_RECEIVEBILL r
    INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
    LEFT JOIN T_BD_CUSTOMER c ON r.FCONTACTUNIT = c.FCUSTID
    LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
WHERE
    r.FDOCUMENTSTATUS = 'C'
    AND r.FCANCELSTATUS = 'A'
    AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
    AND c_l.FNAME IS NOT NULL
GROUP BY
    c_l.FNAME,
    CONVERT(varchar(10), r.FDATE, 120)
ORDER BY
    CONVERT(varchar(10), r.FDATE, 120) DESC,
    c_l.FNAME;
*/

-- 关键字段说明：
-- T_AR_RECEIVEBILL.FCONTACTUNIT: 往来单位（客户ID）
-- T_AR_RECEIVEBILL.FPAYUNIT: 付款单位（付款方ID）
-- T_AR_RECEIVEBILL.FPAYORGID: 收款组织ID
-- T_AR_RECEIVEBILL.FSALEERID: 业务员ID
-- T_AR_RECEIVEBILLENTRY.FSETTLERECAMOUNT: 收款金额
-- T_BD_CUSTOMER_L.FNAME: 客户名称（中文）
-- T_ORG_ORGANIZATIONS_L.FNAME: 组织名称（中文）
-- T_BD_STAFF_L.FNAME: 业务员名称（中文）
