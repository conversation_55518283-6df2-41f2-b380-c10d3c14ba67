import pyodbc
import pandas as pd

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

def connect_to_database():
    """连接到SQL Server数据库"""
    connection_string = f"""
    DRIVER={{{DRIVER}}};
    SERVER={SERVER};
    DATABASE={DATABASE};
    UID={USERNAME};
    PWD={PASSWORD};
    TrustServerCertificate=yes;
    """
    try:
        conn = pyodbc.connect(connection_string)
        print("数据库连接成功！")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def main():
    conn = connect_to_database()
    if not conn:
        return
    
    try:
        # 1. 通过源单据表关联应收单，再关联客户
        print("=== 1. 通过源单据表关联应收单和客户 ===")
        src_customer_query = """
        SELECT TOP 10
            r.FID,
            r.FBI<PERSON> as 收款单号,
            r.FDATE as 收款日期,
            src.FSRCBILLNO as 源单据号,
            ar.FCUSTOMERID as 客户ID,
            c_l.FNAME as 客户名称,
            org_l.FNAME as 收款组织,
            re.FSETTLERECAMOUNT as 收款金额
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        LEFT JOIN T_AR_RECEIVEBILLSRCENTRY src ON r.FID = src.FID
        LEFT JOIN T_AR_RECEIVABLE ar ON src.FSRCBILLNO = ar.FBILLNO
        LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID
        LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
        LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        ORDER BY r.FDATE DESC
        """
        try:
            src_customer = pd.read_sql(src_customer_query, conn)
            print(src_customer)
            
            if not src_customer.empty and src_customer['客户名称'].notna().any():
                print("✓ 成功通过源单据表关联到客户！")
            else:
                print("× 源单据表关联客户失败")
        except Exception as e:
            print(f"源单据表关联失败: {e}")
        
        # 2. 尝试通过源单据ID关联应收单
        print("\n=== 2. 通过源单据ID关联应收单和客户 ===")
        src_id_query = """
        SELECT TOP 10
            r.FID,
            r.FBILLNO as 收款单号,
            r.FDATE as 收款日期,
            src.FSRCBILLID as 源单据ID,
            ar.FCUSTOMERID as 客户ID,
            c_l.FNAME as 客户名称,
            org_l.FNAME as 收款组织,
            re.FSETTLERECAMOUNT as 收款金额
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        LEFT JOIN T_AR_RECEIVEBILLSRCENTRY src ON r.FID = src.FID
        LEFT JOIN T_AR_RECEIVABLE ar ON src.FSRCBILLID = ar.FID
        LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID
        LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
        LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        ORDER BY r.FDATE DESC
        """
        try:
            src_id_customer = pd.read_sql(src_id_query, conn)
            print(src_id_customer)
            
            if not src_id_customer.empty and src_id_customer['客户名称'].notna().any():
                print("✓ 成功通过源单据ID关联到客户！")
            else:
                print("× 源单据ID关联客户失败")
        except Exception as e:
            print(f"源单据ID关联失败: {e}")
        
        # 3. 查看源单据表中的销售订单信息
        print("\n=== 3. 通过销售订单关联客户 ===")
        sales_order_query = """
        SELECT TOP 10
            r.FID,
            r.FBILLNO as 收款单号,
            r.FDATE as 收款日期,
            src.FSALEORDERID as 销售订单ID,
            so.FBILLNO as 销售订单号,
            so.FCUSTID as 客户ID,
            c_l.FNAME as 客户名称,
            org_l.FNAME as 收款组织,
            re.FSETTLERECAMOUNT as 收款金额
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        LEFT JOIN T_AR_RECEIVEBILLSRCENTRY src ON r.FID = src.FID
        LEFT JOIN T_SAL_ORDER so ON src.FSALEORDERID = so.FID
        LEFT JOIN T_BD_CUSTOMER c ON so.FCUSTID = c.FCUSTID
        LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
        LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        AND src.FSALEORDERID IS NOT NULL AND src.FSALEORDERID > 0
        ORDER BY r.FDATE DESC
        """
        try:
            sales_order_customer = pd.read_sql(sales_order_query, conn)
            print(sales_order_customer)
            
            if not sales_order_customer.empty and sales_order_customer['客户名称'].notna().any():
                print("✓ 成功通过销售订单关联到客户！")
                
                # 如果成功，创建完整的汇总查询
                print("\n=== 4. 完整的收款单客户汇总查询 ===")
                full_query = """
                SELECT 
                    c_l.FNAME as 客户名称,
                    org_l.FNAME as 收款组织,
                    CONVERT(varchar(10), r.FDATE, 120) as 收款日期,
                    SUM(re.FSETTLERECAMOUNT) as 收款金额
                FROM T_AR_RECEIVEBILL r
                INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
                LEFT JOIN T_AR_RECEIVEBILLSRCENTRY src ON r.FID = src.FID
                LEFT JOIN T_SAL_ORDER so ON src.FSALEORDERID = so.FID
                LEFT JOIN T_BD_CUSTOMER c ON so.FCUSTID = c.FCUSTID
                LEFT JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID AND c_l.FLocaleId = 2052
                LEFT JOIN T_ORG_ORGANIZATIONS org ON r.FPAYORGID = org.FORGID
                LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID AND org_l.FLocaleId = 2052
                WHERE r.FDOCUMENTSTATUS = 'C'
                AND r.FCANCELSTATUS = 'A'
                AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
                AND c_l.FNAME IS NOT NULL
                GROUP BY 
                    c_l.FNAME,
                    org_l.FNAME,
                    CONVERT(varchar(10), r.FDATE, 120)
                ORDER BY 
                    CONVERT(varchar(10), r.FDATE, 120) DESC,
                    c_l.FNAME,
                    org_l.FNAME
                """
                full_result = pd.read_sql(full_query, conn)
                print(f"完整查询结果: {len(full_result)} 条记录")
                print(full_result.head(20))
                
            else:
                print("× 销售订单关联客户失败")
        except Exception as e:
            print(f"销售订单关联失败: {e}")
            
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
