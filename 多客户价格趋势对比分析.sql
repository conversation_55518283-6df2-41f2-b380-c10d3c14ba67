-- 多客户价格趋势对比分析
-- 对比多个客户的价格与组织市场均价的趋势分析
-- 支持按天、周、月三种时间维度，不限定特定客户
-- =====================================================

-- 📝 使用说明：
-- 1. 修改时间范围和筛选条件
-- 2. 选择时间维度（取消相应部分的注释）
-- 3. 可以查看所有客户，也可以筛选特定客户群体

-- =====================================================
-- 按天分析（取消下面注释来使用按天分析）
-- =====================================================
/*
WITH DailyData AS (
    SELECT 
        CONVERT(VARCHAR(10), o.FDATE, 120) AS 日期,
        m.FNUMBER AS 物料编码,
        m_l.FNAME AS 物料名称,
        m_l.FSPECIFICATION AS 规格型号,
        org_l.FNAME AS 销售组织,
        c_l.FNAME AS 客户名称,
        f.FPRICE AS 单价,
        e.FQTY AS 数量,
        f.FALLAMOUNT AS 金额
    FROM T_SAL_ORDER o
        INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
        INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
        INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
        INNER JOIN T_BD_CUSTOMER c ON o.FCUSTID = c.FCUSTID
        INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
        LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
    WHERE m_l.FLOCALEID = 2052 AND c_l.FLocaleId = 2052 AND org_l.FLocaleId = 2052
        AND o.FDATE >= '2025-06-01'                    -- 修改开始日期
        AND o.FDATE <= '2025-06-10'                    -- 修改结束日期
        AND org_l.FNAME LIKE '%华绿%'                  -- 修改组织名称
        AND m.FNUMBER LIKE '%'                         -- 修改物料条件，%表示所有物料
        --AND c_l.FNAME LIKE '%杭州%'                  -- 可选：筛选特定客户群体
        AND f.FPRICE > 0
),
MarketAvg AS (
    SELECT 
        日期, 物料编码, 物料名称, 规格型号, 销售组织,
        ROUND(AVG(单价), 2) AS 市场平均单价,
        COUNT(*) AS 市场交易次数,
        SUM(数量) AS 市场总销量
    FROM DailyData
    GROUP BY 日期, 物料编码, 物料名称, 规格型号, 销售组织
)
SELECT 
    d.日期,
    d.物料编码,
    d.物料名称,
    d.规格型号,
    d.销售组织,
    d.客户名称,
    
    -- 客户数据
    ROUND(AVG(d.单价), 2) AS 客户平均单价,
    COUNT(*) AS 客户交易次数,
    SUM(d.数量) AS 客户购买数量,
    SUM(d.金额) AS 客户购买金额,
    
    -- 市场对比数据
    m.市场平均单价,
    m.市场交易次数,
    m.市场总销量,
    
    -- 价格对比分析
    ROUND(AVG(d.单价) - m.市场平均单价, 2) AS 价格差异,
    CASE 
        WHEN m.市场平均单价 > 0 THEN 
            ROUND((AVG(d.单价) - m.市场平均单价) / m.市场平均单价 * 100, 2)
        ELSE 0 
    END AS 价格差异百分比,
    
    CASE 
        WHEN AVG(d.单价) > m.市场平均单价 * 1.05 THEN '高于市场价'
        WHEN AVG(d.单价) < m.市场平均单价 * 0.95 THEN '低于市场价'
        ELSE '接近市场价'
    END AS 价格水平
    
FROM DailyData d
    INNER JOIN MarketAvg m ON d.日期 = m.日期 
        AND d.物料编码 = m.物料编码 
        AND d.销售组织 = m.销售组织
GROUP BY 
    d.日期, d.物料编码, d.物料名称, d.规格型号, d.销售组织, d.客户名称,
    m.市场平均单价, m.市场交易次数, m.市场总销量
ORDER BY d.日期, d.物料名称, d.客户名称;
*/

-- =====================================================
-- 按周分析（取消下面注释来使用按周分析）
-- =====================================================

WITH WeeklyData AS (
    SELECT 
        YEAR(o.FDATE) AS 年份,
        DATEPART(WEEK, o.FDATE) AS 周数,
        '第' + CONVERT(VARCHAR(2), DATEPART(WEEK, o.FDATE)) + '周' AS 周期标识,
        m.FNUMBER AS 物料编码,
        m_l.FNAME AS 物料名称,
        m_l.FSPECIFICATION AS 规格型号,
        org_l.FNAME AS 销售组织,
        c_l.FNAME AS 客户名称,
        f.FPRICE AS 单价,
        e.FQTY AS 数量,
        f.FALLAMOUNT AS 金额
    FROM T_SAL_ORDER o
        INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
        INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
        INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
        INNER JOIN T_BD_CUSTOMER c ON o.FCUSTID = c.FCUSTID
        INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
        LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
    WHERE m_l.FLOCALEID = 2052 AND c_l.FLocaleId = 2052 AND org_l.FLocaleId = 2052
        AND o.FDATE >= '2025-06-01'                    -- 修改开始日期
        AND o.FDATE <= '2025-06-30'                    -- 修改结束日期
        AND org_l.FNAME LIKE '%华绿%'                  -- 修改组织名称
        AND m.FNUMBER LIKE '%'                         -- 修改物料条件，%表示所有物料
        --AND c_l.FNAME LIKE '%杭州%'                  -- 可选：筛选特定客户群体
        AND f.FPRICE > 0
),
MarketAvg AS (
    SELECT 
        年份, 周数, 周期标识, 物料编码, 物料名称, 规格型号, 销售组织,
        ROUND(AVG(单价), 2) AS 市场平均单价,
        COUNT(*) AS 市场交易次数,
        SUM(数量) AS 市场总销量,
        COUNT(DISTINCT 客户名称) AS 客户数量
    FROM WeeklyData
    GROUP BY 年份, 周数, 周期标识, 物料编码, 物料名称, 规格型号, 销售组织
)
SELECT 
    d.年份,
    d.周数,
    d.周期标识,
    d.物料编码,
    d.物料名称,
    d.规格型号,
    d.销售组织,
    d.客户名称,
    
    -- 客户数据
    ROUND(AVG(d.单价), 2) AS 客户平均单价,
    COUNT(*) AS 客户交易次数,
    SUM(d.数量) AS 客户购买数量,
    ROUND(SUM(d.金额), 2) AS 客户购买金额,
    
    -- 市场对比数据
    m.市场平均单价,
    m.市场交易次数,
    m.市场总销量,
    m.客户数量 AS 市场客户数量,
    
    -- 价格对比分析
    ROUND(AVG(d.单价) - m.市场平均单价, 2) AS 价格差异,
    CASE 
        WHEN m.市场平均单价 > 0 THEN 
            ROUND((AVG(d.单价) - m.市场平均单价) / m.市场平均单价 * 100, 2)
        ELSE 0 
    END AS 价格差异百分比,
    
    CASE 
        WHEN AVG(d.单价) > m.市场平均单价 * 1.05 THEN '高于市场价'
        WHEN AVG(d.单价) < m.市场平均单价 * 0.95 THEN '低于市场价'
        ELSE '接近市场价'
    END AS 价格水平,
    
    -- 客户排名（按价格）
    DENSE_RANK() OVER (
        PARTITION BY d.年份, d.周数, d.物料编码, d.销售组织 
        ORDER BY AVG(d.单价) DESC
    ) AS 价格排名
    
FROM WeeklyData d
    INNER JOIN MarketAvg m ON d.年份 = m.年份 AND d.周数 = m.周数
        AND d.物料编码 = m.物料编码 AND d.销售组织 = m.销售组织
GROUP BY 
    d.年份, d.周数, d.周期标识, d.物料编码, d.物料名称, d.规格型号, 
    d.销售组织, d.客户名称,
    m.市场平均单价, m.市场交易次数, m.市场总销量, m.客户数量
ORDER BY d.年份, d.周数, d.物料名称, 价格排名;

-- =====================================================
-- 按月分析（取消下面注释来使用按月分析）
-- =====================================================
/*
WITH MonthlyData AS (
    SELECT 
        YEAR(o.FDATE) AS 年份,
        MONTH(o.FDATE) AS 月份,
        CONVERT(VARCHAR(7), o.FDATE, 120) AS 月期标识,
        m.FNUMBER AS 物料编码,
        m_l.FNAME AS 物料名称,
        m_l.FSPECIFICATION AS 规格型号,
        org_l.FNAME AS 销售组织,
        c_l.FNAME AS 客户名称,
        f.FPRICE AS 单价,
        e.FQTY AS 数量,
        f.FALLAMOUNT AS 金额
    FROM T_SAL_ORDER o
        INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
        INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
        INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
        INNER JOIN T_BD_CUSTOMER c ON o.FCUSTID = c.FCUSTID
        INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
        LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
    WHERE m_l.FLOCALEID = 2052 AND c_l.FLocaleId = 2052 AND org_l.FLocaleId = 2052
        AND o.FDATE >= '2025-01-01'                    -- 修改开始日期
        AND o.FDATE <= '2025-12-31'                    -- 修改结束日期
        AND org_l.FNAME LIKE '%华绿%'                  -- 修改组织名称
        AND m.FNUMBER LIKE '%'                         -- 修改物料条件，%表示所有物料
        --AND c_l.FNAME LIKE '%杭州%'                  -- 可选：筛选特定客户群体
        AND f.FPRICE > 0
),
MarketAvg AS (
    SELECT 
        年份, 月份, 月期标识, 物料编码, 物料名称, 规格型号, 销售组织,
        ROUND(AVG(单价), 2) AS 市场平均单价,
        COUNT(*) AS 市场交易次数,
        SUM(数量) AS 市场总销量,
        COUNT(DISTINCT 客户名称) AS 客户数量
    FROM MonthlyData
    GROUP BY 年份, 月份, 月期标识, 物料编码, 物料名称, 规格型号, 销售组织
)
SELECT 
    d.年份,
    d.月份,
    d.月期标识,
    d.物料编码,
    d.物料名称,
    d.规格型号,
    d.销售组织,
    d.客户名称,
    
    -- 客户数据
    ROUND(AVG(d.单价), 2) AS 客户平均单价,
    COUNT(*) AS 客户交易次数,
    SUM(d.数量) AS 客户购买数量,
    ROUND(SUM(d.金额), 2) AS 客户购买金额,
    
    -- 市场对比数据
    m.市场平均单价,
    m.市场交易次数,
    m.市场总销量,
    m.客户数量 AS 市场客户数量,
    
    -- 价格对比分析
    ROUND(AVG(d.单价) - m.市场平均单价, 2) AS 价格差异,
    CASE 
        WHEN m.市场平均单价 > 0 THEN 
            ROUND((AVG(d.单价) - m.市场平均单价) / m.市场平均单价 * 100, 2)
        ELSE 0 
    END AS 价格差异百分比,
    
    CASE 
        WHEN AVG(d.单价) > m.市场平均单价 * 1.05 THEN '高于市场价'
        WHEN AVG(d.单价) < m.市场平均单价 * 0.95 THEN '低于市场价'
        ELSE '接近市场价'
    END AS 价格水平,
    
    -- 客户排名（按价格）
    DENSE_RANK() OVER (
        PARTITION BY d.年份, d.月份, d.物料编码, d.销售组织 
        ORDER BY AVG(d.单价) DESC
    ) AS 价格排名
    
FROM MonthlyData d
    INNER JOIN MarketAvg m ON d.年份 = m.年份 AND d.月份 = m.月份
        AND d.物料编码 = m.物料编码 AND d.销售组织 = m.销售组织
GROUP BY 
    d.年份, d.月份, d.月期标识, d.物料编码, d.物料名称, d.规格型号, 
    d.销售组织, d.客户名称,
    m.市场平均单价, m.市场交易次数, m.市场总销量, m.客户数量
ORDER BY d.年份, d.月份, d.物料名称, 价格排名;
*/

-- =====================================================
-- 🔧 快速修改指南
-- =====================================================
/*
1. 选择时间维度：
   - 按天分析：取消"按天分析"部分的注释，注释掉其他两个
   - 按周分析：取消"按周分析"部分的注释，注释掉其他两个（默认）
   - 按月分析：取消"按月分析"部分的注释，注释掉其他两个

2. 客户筛选选项：
   -- 所有客户（默认）
   --AND c_l.FNAME LIKE '%杭州%'                  -- 筛选包含"杭州"的客户
   --AND c_l.FNAME IN ('客户A', '客户B', '客户C')  -- 筛选特定客户列表
   --AND c_l.FNAME NOT LIKE '%测试%'              -- 排除测试客户

3. 组织筛选：
   AND org_l.FNAME LIKE '%华绿%'                  -- 包含"华绿"的组织
   AND org_l.FNAME = '具体组织名称'               -- 精确匹配组织

4. 物料筛选：
   AND m.FNUMBER LIKE '%'                         -- 所有物料
   AND m.FNUMBER LIKE 'MAT%'                      -- 以MAT开头的物料
   AND m.FNUMBER = 'MAT001'                       -- 特定物料

5. 结果排序：
   - 默认按价格排名排序（高价格客户在前）
   - 可以修改 ORDER BY 子句来调整排序方式
*/

-- =====================================================
-- 📊 新增字段说明
-- =====================================================
/*
- 市场客户数量：参与该物料交易的客户总数
- 价格排名：客户在该时期该物料的价格排名（1=最高价）
- 客户购买金额：客户在该时期的总采购金额
- 价格差异百分比：正数表示高于市场价，负数表示低于市场价

使用场景：
1. 客户价格竞争力分析：看哪些客户获得了更好的价格
2. 市场价格监控：了解整体市场的价格水平和波动
3. 客户分类管理：根据价格水平对客户进行分类
4. 价格策略优化：为不同客户制定差异化价格策略
*/

-- =====================================================
-- 📈 扩展分析查询示例
-- =====================================================

-- 1. 价格排名前5的客户（去掉注释使用）
/*
SELECT * FROM (
    -- 这里放上面的主查询结果
) t
WHERE t.价格排名 <= 5
ORDER BY t.年份, t.周数, t.物料名称, t.价格排名;
*/

-- 2. 价格差异百分比超过10%的异常客户（去掉注释使用）
/*
SELECT * FROM (
    -- 这里放上面的主查询结果
) t
WHERE ABS(t.价格差异百分比) > 10
ORDER BY t.价格差异百分比 DESC;
*/ 