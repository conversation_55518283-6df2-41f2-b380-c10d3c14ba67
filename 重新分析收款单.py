import pyodbc
import pandas as pd

# 数据库连接配置
SERVER = "192.168.1.250"
DATABASE = "AIS2018101755337"
USERNAME = "hldbuser"
PASSWORD = "Hldbuser@241031"
DRIVER = "ODBC Driver 17 for SQL Server"

def connect_to_database():
    """连接到SQL Server数据库"""
    connection_string = f"""
    DRIVER={{{DRIVER}}};
    SERVER={SERVER};
    DATABASE={DATABASE};
    UID={USERNAME};
    PWD={PASSWORD};
    TrustServerCertificate=yes;
    """
    try:
        conn = pyodbc.connect(connection_string)
        print("数据库连接成功！")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def main():
    conn = connect_to_database()
    if not conn:
        return
    
    try:
        # 1. 检查收款单的实际数量和客户分布
        print("=== 1. 检查收款单基本情况 ===")
        basic_query = """
        SELECT 
            COUNT(*) as 收款单总数,
            COUNT(DISTINCT r.FID) as 不同收款单数,
            COUNT(DISTINCT re.FENTRYID) as 收款分录总数
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        """
        basic_df = pd.read_sql(basic_query, conn)
        print(basic_df)
        
        # 2. 检查收款单分录表中是否有客户信息
        print("\n=== 2. 检查收款单分录表详细结构 ===")
        entry_detail_query = """
        SELECT TOP 10 
            re.FENTRYID,
            re.FID,
            re.FSETTLERECAMOUNT,
            re.FSETTLETYPEID,
            re.FSEQ
        FROM T_AR_RECEIVEBILLENTRY re
        INNER JOIN T_AR_RECEIVEBILL r ON re.FID = r.FID
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        ORDER BY re.FID, re.FSEQ
        """
        entry_detail = pd.read_sql(entry_detail_query, conn)
        print(entry_detail)
        
        # 3. 检查是否存在其他客户关联表
        print("\n=== 3. 查找其他可能的客户关联方式 ===")
        # 查看是否有收款单客户关联表
        customer_link_query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME LIKE '%RECEIVEBILL%' 
        AND TABLE_NAME LIKE '%CUST%'
        """
        try:
            customer_link = pd.read_sql(customer_link_query, conn)
            print("收款单客户关联表:")
            print(customer_link)
        except:
            print("没有找到收款单客户关联表")
        
        # 4. 检查收款单主表是否有其他客户相关字段
        print("\n=== 4. 重新检查收款单主表的所有字段 ===")
        all_columns_query = """
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILL'
        ORDER BY ORDINAL_POSITION
        """
        all_columns = pd.read_sql(all_columns_query, conn)
        print("收款单主表所有字段:")
        print(all_columns)
        
        # 5. 检查收款单是否直接有客户字段（可能字段名不同）
        print("\n=== 5. 查找收款单中可能的客户字段 ===")
        possible_customer_query = """
        SELECT COLUMN_NAME, DATA_TYPE
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'T_AR_RECEIVEBILL'
        AND (COLUMN_NAME LIKE '%PAYORG%' OR COLUMN_NAME LIKE '%SALEORG%' 
             OR COLUMN_NAME LIKE '%DEPT%' OR COLUMN_NAME LIKE '%SALER%')
        """
        possible_customer = pd.read_sql(possible_customer_query, conn)
        print("可能的客户相关字段:")
        print(possible_customer)
        
        # 6. 尝试通过销售组织或销售员关联客户
        print("\n=== 6. 尝试其他关联方式 ===")
        alt_query = """
        SELECT TOP 10
            r.FID,
            r.FBILLNO,
            r.FDATE,
            r.FSALEORGID,
            r.FSALEERID,
            r.FPAYORGID,
            re.FSETTLERECAMOUNT
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) >= '2025-05-01'
        ORDER BY r.FDATE DESC
        """
        alt_df = pd.read_sql(alt_query, conn)
        print("收款单其他字段信息:")
        print(alt_df)
        
        # 7. 检查当前关联查询的问题
        print("\n=== 7. 分析当前关联查询的问题 ===")
        debug_query = """
        SELECT 
            r.FID,
            r.FBILLNO,
            r.FDATE,
            r.FSOURCEBILLNUMBER,
            COUNT(re.FENTRYID) as 分录数量,
            COUNT(DISTINCT ar.FID) as 关联应收单数,
            COUNT(DISTINCT c.FCUSTID) as 关联客户数
        FROM T_AR_RECEIVEBILL r
        INNER JOIN T_AR_RECEIVEBILLENTRY re ON r.FID = re.FID
        LEFT JOIN T_AR_RECEIVABLE ar ON r.FSOURCEBILLNUMBER = ar.FBILLNO
        LEFT JOIN T_BD_CUSTOMER c ON ar.FCUSTOMERID = c.FCUSTID
        WHERE r.FDOCUMENTSTATUS = 'C'
        AND CONVERT(varchar(10), r.FDATE, 120) = '2025-05-01'
        GROUP BY r.FID, r.FBILLNO, r.FDATE, r.FSOURCEBILLNUMBER
        ORDER BY r.FID
        """
        debug_df = pd.read_sql(debug_query, conn)
        print("关联分析:")
        print(debug_df)
        
    finally:
        conn.close()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
