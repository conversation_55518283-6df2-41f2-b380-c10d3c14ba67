#!/usr/bin/env python3
"""
最终修正INSERT语句的脚本
解决两个主要问题：
1. INSERT语句缺少分号分隔符
2. 部分INSERT语句缺少[status]字段
"""

import re

def fix_insert_statements(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 分割内容，保留注释
    lines = content.split('\n')
    
    # 找到注释部分和SQL部分
    sql_start_index = 0
    for i, line in enumerate(lines):
        if line.strip().startswith('INSERT INTO'):
            sql_start_index = i
            break
    
    # 保留注释部分
    comments_part = '\n'.join(lines[:sql_start_index])
    sql_part = '\n'.join(lines[sql_start_index:])
    
    # 使用正则表达式找到所有INSERT语句
    # 这个正则表达式会匹配完整的INSERT语句
    insert_pattern = r'INSERT INTO \[测试\] \([^\)]+\) VALUES \([^\)]+\);?'
    
    # 找到所有INSERT语句
    matches = re.findall(insert_pattern, sql_part, re.DOTALL)
    
    print(f"找到 {len(matches)} 个INSERT语句")
    
    fixed_statements = []
    
    for i, match in enumerate(matches):
        # 移除末尾可能存在的分号
        statement = match.rstrip(';').strip()
        
        # 检查是否包含[status]字段
        if '[status]' not in statement:
            # 需要添加[status]字段
            # 找到列定义部分和VALUES部分
            parts = statement.split(' VALUES ')
            if len(parts) == 2:
                column_part = parts[0]
                values_part = parts[1]
                
                # 在列定义中添加[status]
                column_part = column_part.replace('])', '], [status])')
                
                # 在VALUES中添加1（表示状态为1）
                values_part = values_part.rstrip(')') + ', 1)'
                
                statement = column_part + ' VALUES ' + values_part
                print(f"语句 {i+1}: 添加了[status]字段")
        
        # 确保语句以分号结尾
        if not statement.endswith(';'):
            statement += ';'
        
        fixed_statements.append(statement)
    
    # 重新组合内容
    fixed_content = comments_part + '\n' + '\n'.join(fixed_statements) + '\n'
    
    # 写入修正后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"修正完成！结果已保存到: {output_file}")
    print(f"共处理了 {len(fixed_statements)} 个INSERT语句")

if __name__ == "__main__":
    input_file = "insert_statements_fixed.sql"
    output_file = "insert_statements_final.sql"
    
    fix_insert_statements(input_file, output_file) 