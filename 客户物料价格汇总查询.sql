-- 客户物料价格汇总查询 - 按客户和物料汇总价格统计
-- 查询指定时间范围内每个客户每个物料的价格汇总信息
-- =====================================================

SELECT
    org_l.FNAME AS '销售组织',
    c_l.FNAME AS '客户名称',
    m.FNUMBER AS '物料编码',
    m_l.FNAME AS '物料名称',
    m_l.FSPECIFICATION AS '规格型号',
    mc_l.FNAME AS '物料类别',
    u_l.FNAME AS '计价单位',
    COUNT(*) AS '交易次数',
    SUM(e.FQTY) AS '总销售数量',
    MIN(f.FPRICE) AS '最低单价',
    MAX(f.FPRICE) AS '最高单价',
    ROUND(AVG(f.FPRICE), 4) AS '平均单价',
    MIN(f.FTAXPRICE) AS '最低含税单价',
    MAX(f.FTAXPRICE) AS '最高含税单价',
    ROUND(AVG(f.FTAXPRICE), 4) AS '平均含税单价',
    SUM(f.FAMOUNT) AS '总金额',
    SUM(f.FALLAMOUNT) AS '总价税合计',
    MIN(CONVERT(varchar(10), o.FDATE, 120)) AS '首次交易日期',
    MAX(CONVERT(varchar(10), o.FDATE, 120)) AS '最后交易日期',
    CASE 
        WHEN MIN(f.FPRICE) = MAX(f.FPRICE) THEN '价格稳定'
        WHEN (MAX(f.FPRICE) - MIN(f.FPRICE)) / MIN(f.FPRICE) <= 0.1 THEN '价格微幅波动'
        ELSE '价格波动较大'
    END AS '价格波动情况'
FROM
    T_SAL_ORDER o
    INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
    INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
    INNER JOIN T_BD_CUSTOMER c ON o.FCUSTID = c.FCUSTID
    INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
    INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
    INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
    INNER JOIN T_BD_MATERIALBASE mb ON m.FMATERIALID = mb.FMATERIALID
    INNER JOIN T_BD_MATERIALCATEGORY mc ON mb.FCATEGORYID = mc.FCATEGORYID
    INNER JOIN T_BD_MATERIALCATEGORY_L mc_l ON mc.FCATEGORYID = mc_l.FCATEGORYID
    LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
    LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
    LEFT JOIN T_BD_UNIT u ON e.FUNITID = u.FUNITID
    LEFT JOIN T_BD_UNIT_L u_l ON u.FUNITID = u_l.FUNITID
WHERE
    c_l.FLocaleId = 2052        -- 中文客户名称
    AND m_l.FLOCALEID = 2052    -- 中文物料名称
    AND mc_l.FLOCALEID = 2052   -- 中文物料类别
    AND org_l.FLocaleId = 2052  -- 中文组织名称
    AND u_l.FLocaleId = 2052    -- 中文单位名称
    -- 时间范围过滤（请根据需要修改日期）
    AND o.FDATE >= '2025-06-01'
    AND o.FDATE <= '2025-06-10'
    -- 可选的额外过滤条件（根据需要取消注释）
    --AND o.FDOCUMENTSTATUS = 'C'  -- 只查询已审核单据
    --AND o.FCANCELSTATUS = 'A'    -- 排除已作废单据
    AND f.FPRICE > 0             -- 排除价格为0的记录
GROUP BY
    org_l.FNAME,
    c_l.FNAME,
    m.FNUMBER,
    m_l.FNAME,
    m_l.FSPECIFICATION,
    mc_l.FNAME,
    u_l.FNAME
HAVING
    COUNT(*) >= 1               -- 至少有1次交易记录
ORDER BY
    c_l.FNAME,                  -- 按客户名称排序
    m_l.FNAME,                  -- 按物料名称排序
    SUM(f.FALLAMOUNT) DESC;     -- 按总价税合计降序排列（交易额大的在前）

-- =====================================================
-- 查询说明
-- =====================================================
/*
查询功能：
- 按客户和物料汇总显示价格统计信息
- 显示价格范围、平均价格、交易次数等统计数据
- 包含价格波动情况分析

主要统计指标：
- 交易次数：该客户购买该物料的订单明细数量
- 总销售数量：累计销售数量
- 价格范围：最低单价到最高单价
- 平均价格：加权平均或算数平均价格
- 总交易金额：累计金额和价税合计
- 交易时间范围：首次和最后交易日期
- 价格波动情况：根据价格变化幅度自动判断

价格波动判断标准：
- 价格稳定：最高价格 = 最低价格
- 价格微幅波动：价格变化幅度 <= 10%
- 价格波动较大：价格变化幅度 > 10%

使用场景：
1. 分析客户采购价格趋势
2. 识别价格波动较大的物料
3. 统计客户的采购习惯和交易频次
4. 为价格谈判提供数据支持

排序规则：
- 首先按客户名称排序
- 相同客户内按物料名称排序
- 相同物料按总交易额降序排列

示例用法：
1. 查看特定客户的物料采购价格分析
2. 识别价格变化频繁的物料
3. 分析大客户的采购模式
4. 为销售定价策略提供参考

筛选建议：
- 可以增加 HAVING COUNT(*) >= 5 来只显示交易次数较多的记录
- 可以增加价格范围过滤来关注特定价位的物料
- 可以按交易金额排序来关注重要客户和产品
*/

-- =====================================================
-- 扩展查询示例
-- =====================================================

-- 1. 只显示交易次数较多的记录（去掉注释使用）
/*
SELECT * FROM (
    -- 这里放上面的主查询
) t
WHERE t.交易次数 >= 3
*/

-- 2. 只显示价格波动较大的物料（去掉注释使用）
/*
SELECT * FROM (
    -- 这里放上面的主查询
) t
WHERE t.价格波动情况 = '价格波动较大'
*/

-- 3. 按交易金额排序显示重要客户产品组合（去掉注释使用）
/*
SELECT * FROM (
    -- 这里放上面的主查询
) t
ORDER BY t.总价税合计 DESC
*/ 