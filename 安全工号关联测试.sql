-- 安全工号关联测试
-- 针对具体的入库单测试各种关联方式

-- 测试1：基本关联测试
SELECT TOP 3
    h.FBILLNO AS 入库单号,
    h.FCREATORID AS 制单人ID,
    u.FNAME AS 用户姓名,
    u.FUSERACCOUNT AS 用户账号
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
WHERE h.FBILLNO IS NOT NULL
ORDER BY h.FCREATEDATE DESC;

-- 测试2：检查操作员分录关联
SELECT TOP 3
    h.FBILLNO AS 入库单号,
    h.FCREATORID AS 制单人ID,
    u.FNAME AS 用户姓名,
    op.FOPERATORID AS 操作员ID,
    op.FNUMBER AS 操作员工号,
    op.FISUSE AS 是否启用
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
LEFT JOIN T_BD_OPERATORENTRY op ON u.FUSERID = op.FOPERATORID
WHERE h.FBILLNO IS NOT NULL
ORDER BY h.FCREATEDATE DESC;

-- 测试3：检查员工表关联
SELECT TOP 3
    h.FBILLNO AS 入库单号,
    h.FCREATORID AS 制单人ID,
    u.FNAME AS 用户姓名,
    u.FEMPID AS 用户员工ID,
    s.FSTAFFID AS 员工表ID,
    s.FNUMBER AS 员工工号,
    s.FNAME AS 员工姓名
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
LEFT JOIN T_BD_STAFF s ON u.FEMPID = s.FSTAFFID
WHERE h.FBILLNO IS NOT NULL
ORDER BY h.FCREATEDATE DESC;
