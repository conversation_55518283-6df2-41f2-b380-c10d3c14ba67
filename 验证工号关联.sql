-- 验证工号关联查询
-- 用于找到正确的用户-工号关联方式

-- 测试1：查看用户和操作员分录的关联关系
SELECT TOP 10
    '测试1：用户-操作员分录关联' AS '测试类型',
    u.FUSERID AS '用户ID',
    u.FNAME AS '用户姓名',
    u.FUSERACCOUNT AS '用户账号',
    op_entry.FOPERATORID AS '操作员ID',
    op_entry.FNUMBER AS '操作员工号',
    op_entry.FSTAFFID AS '关联员工ID'
FROM T_SEC_USER u
LEFT JOIN T_BD_OPERATORENTRY op_entry ON u.FUSERID = op_entry.FOPERATORID
WHERE op_entry.FNUMBER IS NOT NULL
    AND op_entry.FISUSE = 1;

-- 测试2：查看用户和员工的关联关系  
SELECT TOP 10
    '测试2：用户-员工关联' AS '测试类型',
    u.FUSERID AS '用户ID',
    u.FNAME AS '用户姓名',
    u.FUSERACCOUNT AS '用户账号',
    u.FEMPID AS '员工ID字段',
    s.FSTAFFID AS '员工表ID',
    s.FNUMBER AS '员工工号'
FROM T_SEC_USER u
LEFT JOIN T_BD_STAFF s ON u.FEMPID = s.FSTAFFID
WHERE s.FNUMBER IS NOT NULL;

-- 测试3：通过操作员分录表的员工ID关联
SELECT TOP 10
    '测试3：操作员分录-员工关联' AS '测试类型',
    op_entry.FOPERATORID AS '操作员ID',
    op_entry.FNUMBER AS '操作员工号',
    op_entry.FSTAFFID AS '分录中员工ID',
    s.FSTAFFID AS '员工表ID',
    s.FNUMBER AS '员工工号'
FROM T_BD_OPERATORENTRY op_entry
LEFT JOIN T_BD_STAFF s ON op_entry.FSTAFFID = s.FSTAFFID
WHERE op_entry.FNUMBER IS NOT NULL
    AND op_entry.FISUSE = 1
    AND s.FNUMBER IS NOT NULL;

-- 测试4：查看入库单制单人的实际关联情况
SELECT TOP 10
    '测试4：入库单制单人关联' AS '测试类型',
    h.FBILLNO AS '入库单号',
    h.FCREATORID AS '制单人ID',
    u.FUSERID AS '用户ID',
    u.FNAME AS '用户姓名',
    u.FEMPID AS '用户员工ID',
    -- 尝试不同的关联方式
    op_entry1.FNUMBER AS '方式1_操作员工号',
    s1.FNUMBER AS '方式2_员工工号',
    op_entry2.FNUMBER AS '方式3_通过员工关联的操作员工号'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
-- 方式1：直接用用户ID关联操作员分录
LEFT JOIN T_BD_OPERATORENTRY op_entry1 ON u.FUSERID = op_entry1.FOPERATORID 
    AND op_entry1.FISUSE = 1
-- 方式2：通过用户的员工ID关联员工表
LEFT JOIN T_BD_STAFF s1 ON u.FEMPID = s1.FSTAFFID
-- 方式3：通过员工ID关联操作员分录
LEFT JOIN T_BD_OPERATORENTRY op_entry2 ON s1.FSTAFFID = op_entry2.FSTAFFID 
    AND op_entry2.FISUSE = 1
WHERE h.FBILLNO IS NOT NULL
ORDER BY h.FCREATEDATE DESC;

-- 测试5：统计各种关联方式的成功率
SELECT 
    '直接用户ID关联操作员分录' AS '关联方式',
    COUNT(*) AS '总记录数',
    COUNT(op_entry1.FNUMBER) AS '成功记录数',
    CAST(COUNT(op_entry1.FNUMBER) * 100.0 / COUNT(*) AS DECIMAL(5,2)) AS '成功率%'
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
LEFT JOIN T_BD_OPERATORENTRY op_entry1 ON u.FUSERID = op_entry1.FOPERATORID 
    AND op_entry1.FISUSE = 1
WHERE h.FBILLNO IS NOT NULL

UNION ALL

SELECT 
    '通过员工ID关联员工表',
    COUNT(*),
    COUNT(s1.FNUMBER),
    CAST(COUNT(s1.FNUMBER) * 100.0 / COUNT(*) AS DECIMAL(5,2))
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
LEFT JOIN T_BD_STAFF s1 ON u.FEMPID = s1.FSTAFFID
WHERE h.FBILLNO IS NOT NULL

UNION ALL

SELECT 
    '通过员工ID关联操作员分录',
    COUNT(*),
    COUNT(op_entry2.FNUMBER),
    CAST(COUNT(op_entry2.FNUMBER) * 100.0 / COUNT(*) AS DECIMAL(5,2))
FROM T_STK_INSTOCK h
LEFT JOIN T_SEC_USER u ON h.FCREATORID = u.FUSERID
LEFT JOIN T_BD_STAFF s1 ON u.FEMPID = s1.FSTAFFID
LEFT JOIN T_BD_OPERATORENTRY op_entry2 ON s1.FSTAFFID = op_entry2.FSTAFFID 
    AND op_entry2.FISUSE = 1
WHERE h.FBILLNO IS NOT NULL;
