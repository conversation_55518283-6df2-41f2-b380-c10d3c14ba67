-- 客户价格趋势对比分析查询
-- 对比特定客户购买价格与组织内销售均价的趋势分析
-- 支持按天、周、月三种时间维度进行分析
-- =====================================================

-- 设置参数区域（修改这里的参数来调整查询条件）
DECLARE @StartDate DATE = '2025-06-01';           -- 开始日期
DECLARE @EndDate DATE = '2025-06-30';             -- 结束日期
DECLARE @CustomerName NVARCHAR(200) = '杭州方帅农业开发有限公司'; -- 客户名称（可使用%通配符）
DECLARE @OrgName NVARCHAR(200) = '江苏华绿生物科技集团股份有限公司'; -- 销售组织名称（可使用%通配符）
DECLARE @MaterialCode NVARCHAR(100) = '%';        -- 物料编码（可使用%通配符，%表示所有物料）
DECLARE @TimeDimension NVARCHAR(10) = 'WEEK';     -- 时间维度：DAY(按天), WEEK(按周), MONTH(按月)

-- =====================================================
-- 主查询：客户价格与组织均价对比分析
-- =====================================================

WITH TimeSeriesData AS (
    -- 生成时间序列数据
    SELECT 
        o.FDATE,
        -- 根据选择的时间维度生成时间标识
        CASE 
            WHEN @TimeDimension = 'DAY' THEN CONVERT(VARCHAR(10), o.FDATE, 120)
            WHEN @TimeDimension = 'WEEK' THEN 
                CONVERT(VARCHAR(4), YEAR(o.FDATE)) + '-W' + 
                RIGHT('0' + CONVERT(VARCHAR(2), DATEPART(WEEK, o.FDATE)), 2)
            WHEN @TimeDimension = 'MONTH' THEN 
                CONVERT(VARCHAR(7), o.FDATE, 120)
        END AS TimePeriod,
        -- 根据时间维度生成期间描述
        CASE 
            WHEN @TimeDimension = 'DAY' THEN CONVERT(VARCHAR(10), o.FDATE, 120)
            WHEN @TimeDimension = 'WEEK' THEN 
                '第' + CONVERT(VARCHAR(2), DATEPART(WEEK, o.FDATE)) + '周 (' +
                CONVERT(VARCHAR(10), DATEADD(DAY, 1-DATEPART(WEEKDAY, o.FDATE), o.FDATE), 120) + '~' +
                CONVERT(VARCHAR(10), DATEADD(DAY, 7-DATEPART(WEEKDAY, o.FDATE), o.FDATE), 120) + ')'
            WHEN @TimeDimension = 'MONTH' THEN 
                CONVERT(VARCHAR(4), YEAR(o.FDATE)) + '年' + 
                CONVERT(VARCHAR(2), MONTH(o.FDATE)) + '月'
        END AS PeriodDescription,
        m.FNUMBER AS MaterialCode,
        m_l.FNAME AS MaterialName,
        m_l.FSPECIFICATION AS MaterialSpec,
        org_l.FNAME AS OrgName,
        c_l.FNAME AS CustomerName,
        e.FQTY,
        f.FPRICE,
        f.FTAXPRICE,
        f.FALLAMOUNT
    FROM T_SAL_ORDER o
        INNER JOIN T_SAL_ORDERENTRY e ON o.FID = e.FID
        INNER JOIN T_SAL_ORDERENTRY_F f ON e.FENTRYID = f.FENTRYID
        INNER JOIN T_BD_MATERIAL m ON e.FMATERIALID = m.FMATERIALID
        INNER JOIN T_BD_MATERIAL_L m_l ON m.FMATERIALID = m_l.FMATERIALID
        INNER JOIN T_BD_CUSTOMER c ON o.FCUSTID = c.FCUSTID
        INNER JOIN T_BD_CUSTOMER_L c_l ON c.FCUSTID = c_l.FCUSTID
        LEFT JOIN T_ORG_ORGANIZATIONS org ON o.FSALEORGID = org.FORGID
        LEFT JOIN T_ORG_ORGANIZATIONS_L org_l ON org.FORGID = org_l.FORGID
    WHERE m_l.FLOCALEID = 2052
        AND c_l.FLocaleId = 2052
        AND org_l.FLocaleId = 2052
        AND o.FDATE >= @StartDate
        AND o.FDATE <= @EndDate
        AND org_l.FNAME LIKE @OrgName
        AND m.FNUMBER LIKE @MaterialCode
        AND f.FPRICE > 0
        --AND o.FDOCUMENTSTATUS = 'C'  -- 取消注释只查询已审核单据
),

-- 计算组织内所有客户的均价（市场均价）
MarketAvgPrice AS (
    SELECT 
        TimePeriod,
        MaterialCode,
        MaterialName,
        MaterialSpec,
        OrgName,
        COUNT(*) AS MarketTransCount,
        ROUND(AVG(FPRICE), 2) AS MarketAvgPrice,
        ROUND(AVG(FTAXPRICE), 2) AS MarketAvgTaxPrice,
        SUM(FQTY) AS MarketTotalQty,
        SUM(FALLAMOUNT) AS MarketTotalAmount
    FROM TimeSeriesData
    GROUP BY TimePeriod, MaterialCode, MaterialName, MaterialSpec, OrgName
),

-- 计算特定客户的均价
CustomerAvgPrice AS (
    SELECT 
        TimePeriod,
        MaterialCode,
        MaterialName,
        MaterialSpec,
        OrgName,
        CustomerName,
        COUNT(*) AS CustomerTransCount,
        ROUND(AVG(FPRICE), 2) AS CustomerAvgPrice,
        ROUND(AVG(FTAXPRICE), 2) AS CustomerAvgTaxPrice,
        SUM(FQTY) AS CustomerTotalQty,
        SUM(FALLAMOUNT) AS CustomerTotalAmount
    FROM TimeSeriesData
    WHERE CustomerName LIKE @CustomerName
    GROUP BY TimePeriod, MaterialCode, MaterialName, MaterialSpec, OrgName, CustomerName
)

-- 最终结果：对比分析
SELECT 
    t.TimePeriod AS '时间期间',
    MAX(t.PeriodDescription) AS '期间描述',
    c.MaterialCode AS '物料编码',
    c.MaterialName AS '物料名称',
    c.MaterialSpec AS '规格型号',
    c.OrgName AS '销售组织',
    c.CustomerName AS '客户名称',
    
    -- 市场数据
    m.MarketTransCount AS '市场交易次数',
    m.MarketAvgPrice AS '市场平均单价',
    m.MarketAvgTaxPrice AS '市场平均含税单价',
    m.MarketTotalQty AS '市场总销量',
    
    -- 客户数据
    c.CustomerTransCount AS '客户交易次数',
    c.CustomerAvgPrice AS '客户平均单价',
    c.CustomerAvgTaxPrice AS '客户平均含税单价',
    c.CustomerTotalQty AS '客户购买数量',
    
    -- 对比分析
    ROUND(c.CustomerAvgPrice - m.MarketAvgPrice, 2) AS '价格差异',
    CASE 
        WHEN m.MarketAvgPrice > 0 THEN 
            ROUND((c.CustomerAvgPrice - m.MarketAvgPrice) / m.MarketAvgPrice * 100, 2)
        ELSE 0 
    END AS '价格差异百分比',
    
    CASE 
        WHEN c.CustomerAvgPrice > m.MarketAvgPrice * 1.05 THEN '高于市场价'
        WHEN c.CustomerAvgPrice < m.MarketAvgPrice * 0.95 THEN '低于市场价'
        ELSE '接近市场价'
    END AS '价格水平',
    
    ROUND(c.CustomerTotalAmount, 2) AS '客户采购金额'
    
FROM TimeSeriesData t
    LEFT JOIN MarketAvgPrice m ON t.TimePeriod = m.TimePeriod 
        AND t.MaterialCode = m.MaterialCode 
        AND t.OrgName = m.OrgName
    LEFT JOIN CustomerAvgPrice c ON t.TimePeriod = c.TimePeriod 
        AND t.MaterialCode = c.MaterialCode 
        AND t.OrgName = c.OrgName
WHERE c.CustomerName IS NOT NULL  -- 只显示有客户交易的记录
GROUP BY 
    t.TimePeriod,
    c.MaterialCode,
    c.MaterialName,
    c.MaterialSpec,
    c.OrgName,
    c.CustomerName,
    m.MarketTransCount,
    m.MarketAvgPrice,
    m.MarketAvgTaxPrice,
    m.MarketTotalQty,
    c.CustomerTransCount,
    c.CustomerAvgPrice,
    c.CustomerAvgTaxPrice,
    c.CustomerTotalQty,
    c.CustomerTotalAmount
ORDER BY 
    t.TimePeriod,
    c.MaterialName,
    c.CustomerAvgPrice DESC;

-- =====================================================
-- 参数使用说明
-- =====================================================
/*
修改查询参数：

1. 时间范围：
   @StartDate = '2025-06-01'    -- 开始日期
   @EndDate = '2025-06-30'      -- 结束日期

2. 客户筛选：
   @CustomerName = '杭州方帅农业开发有限公司'  -- 精确客户名
   @CustomerName = '%杭州%'                    -- 包含"杭州"的客户
   @CustomerName = '%'                         -- 所有客户

3. 组织筛选：
   @OrgName = '江苏华绿生物科技集团股份有限公司'  -- 精确组织名
   @OrgName = '%华绿%'                          -- 包含"华绿"的组织
   @OrgName = '%'                               -- 所有组织

4. 物料筛选：
   @MaterialCode = 'MAT001'     -- 特定物料编码
   @MaterialCode = 'MAT%'       -- 以MAT开头的物料
   @MaterialCode = '%'          -- 所有物料

5. 时间维度：
   @TimeDimension = 'DAY'       -- 按天分析
   @TimeDimension = 'WEEK'      -- 按周分析  
   @TimeDimension = 'MONTH'     -- 按月分析

结果字段说明：
- 价格差异：客户价格 - 市场价格
- 价格差异百分比：(客户价格 - 市场价格) / 市场价格 * 100
- 价格水平：高于/低于/接近市场价的定性判断
*/

-- =====================================================
-- 趋势分析辅助查询
-- =====================================================

-- 1. 客户价格趋势图数据（去掉注释使用）
/*
SELECT 
    时间期间,
    期间描述,
    物料名称,
    客户平均单价,
    市场平均单价,
    价格差异百分比
FROM (
    -- 这里放上面的主查询
) t
ORDER BY 时间期间, 物料名称;
*/

-- 2. 价格波动分析（去掉注释使用）
/*
WITH PriceTrend AS (
    SELECT *,
        LAG(客户平均单价) OVER (PARTITION BY 物料编码 ORDER BY 时间期间) AS 上期客户价格,
        LAG(市场平均单价) OVER (PARTITION BY 物料编码 ORDER BY 时间期间) AS 上期市场价格
    FROM (
        -- 这里放上面的主查询
    ) t
)
SELECT *,
    CASE 
        WHEN 上期客户价格 IS NOT NULL THEN 
            ROUND((客户平均单价 - 上期客户价格) / 上期客户价格 * 100, 2)
        ELSE 0 
    END AS 客户价格变化百分比,
    CASE 
        WHEN 上期市场价格 IS NOT NULL THEN 
            ROUND((市场平均单价 - 上期市场价格) / 上期市场价格 * 100, 2)
        ELSE 0 
    END AS 市场价格变化百分比
FROM PriceTrend
ORDER BY 时间期间, 物料名称;
*/ 