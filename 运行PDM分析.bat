@echo off
chcp 65001
echo 🚀 PDM文件分析器 - 金蝶K/3 Cloud数据字典分析
echo ================================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装，请先安装Python 3.x
    pause
    exit /b 1
)

REM 检查PDM文件夹
if not exist "pdm" (
    echo ❌ PDM文件夹不存在，请确保pdm文件夹在当前目录
    pause
    exit /b 1
)

REM 检查并安装依赖包
echo 📦 检查Python依赖包...
python -c "import pyodbc, pandas, json, re" >nul 2>&1
if errorlevel 1 (
    echo 📥 安装必要的Python包...
    pip install pyodbc pandas
)

echo ✅ 环境检查完成
echo.

REM 运行PDM分析器
echo 🔍 开始分析PDM文件和数据库结构...
python PDM文件分析器.py

echo.
echo 📋 分析完成！请查看生成的文件：
echo    📄 物料到期查询_PDM优化版.sql  (包含制单人工号的优化查询)
echo    📊 PDM分析报告.json           (详细的分析报告)
echo.
echo 💡 使用说明：
echo    1. 查看SQL文件中的制单人工号查询
echo    2. 根据分析报告调整关联方式
echo    3. 如果工号仍为空，请检查数据完整性
echo.

pause
