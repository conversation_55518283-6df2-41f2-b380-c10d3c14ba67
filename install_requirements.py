#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装物料到期查询工具所需的依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 安装物料到期查询工具依赖包")
    print("=" * 60)
    
    # 需要安装的包列表
    packages = [
        "pyodbc",      # SQL Server 数据库连接
        "pandas",      # 数据处理
        "openpyxl",    # Excel 文件操作
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 安装结果: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("🎉 所有依赖包安装完成！")
        print("✅ 现在可以运行物料到期查询工具了")
        print("\n运行命令: python run_material_expiry_query.py")
    else:
        print("⚠️  部分依赖包安装失败")
        print("请手动安装失败的包或检查网络连接")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
